from django.db import models
from django.contrib.auth import get_user_model
# from django.urls import URLPattern, URLResolver
# from django.urls.resolvers import get_resolver
# from django.db.models.signals import post_migrate



User = get_user_model()

# def get_all_endpoints_choices():
#     urlpatterns = get_resolver().url_patterns
#     endpoints = []

#     def traverse_patterns(patterns, parent_pattern=None):
#         for pattern in patterns:
#             if isinstance(pattern, URLResolver):
#                 # Traverse nested URL resolvers
#                 traverse_patterns(
#                     pattern.url_patterns,
#                     parent_pattern + [pattern.pattern] if parent_pattern else [pattern.pattern],
#                 )
#             elif isinstance(pattern, URLPattern):
#                 # Append the endpoint to the list
#                 endpoints.append((parent_pattern + [pattern.pattern]) if parent_pattern else [pattern.pattern])

#     traverse_patterns(urlpatterns)
#     return [(endpoint[0], endpoint[0]) for endpoint in endpoints]


# class EndpointsToWatch(models.Model):
#     # ENDPOINTS_CHOICES = get_all_endpoints_choices()

#     endpoint = models.CharField(max_length=350, choices=[], blank=True)
#     date_created = models.DateTimeField(auto_now_add=True)
#     last_updated = models.DateTimeField(auto_now=True)

    
# def populate_endpoint_choices(sender, **kwargs):
#     EndpointsToWatch._meta.get_field('endpoint').choices = get_all_endpoints_choices()

# post_migrate.connect(populate_endpoint_choices)

    

# class APICall(models.Model):
#     user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
#     url = models.CharField(max_length=100)
#     count = models.PositiveIntegerField(default=0)
#     data = models.TextField(blank=True, null=True)
#     date_created = models.DateTimeField(auto_now_add=True)
#     last_updated = models.DateTimeField(auto_now=True)

#     def __str__(self) -> str:
#         return self.user if self.user else self.url
    
#     @classmethod
#     def create_API_call_instance_count(cls, url, user: User=None):
#         if user and url in EndpointsToWatch.objects.values_list("endpoint", flat=True):

#             api_call, created = cls.objects.get_or_create(
#                 user=user,
#                 url=url,
#             )
#             api_call.count += 1
#             api_call.save()

#             # return True
#         return True



class TrackableEndpoint(models.Model):
    endpoints = models.CharField(max_length=300)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class RequestTiming(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    endpoint = models.CharField(max_length=300)
    status_code = models.IntegerField()
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    duration = models.FloatField()
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

