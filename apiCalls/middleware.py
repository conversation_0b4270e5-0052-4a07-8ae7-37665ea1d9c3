
from django.conf import settings

import json
import datetime
# from django.utils.deprecation import MiddlewareMixin
# from .models import RequestTiming

from .models import *



# class HandleAPIHitsMiddleware:
#     def __init__(self, get_response):
#         self.get_response = get_response
#         # One-time configuration and initialization.

#     def __call__(self, request):

#         url = request.build_absolute_uri()
#         user = request.user if request.user.is_authenticated else None
        

#         APICall.create_API_call_instance_count(url, user)
  

#         # Code to be executed for each request before
#         # the view (and later middleware) are called.

#         response = self.get_response(request)

#         # Code to be executed for each request/response after
#         # the view is called.

#         return response




class RequestTimingMiddleware():
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Record the start time only for specific endpoints
        start_time = datetime.datetime.now()

        # Call the next middleware or view
        response = self.get_response(request)

        try:
            # Get the endpoint URL
            endpoint = request.path
                
            # Save the request timing information to the database
            if self.should_track(endpoint):

                # Calculate the request processing time
                end_time = datetime.datetime.now()
                duration = (end_time - start_time).total_seconds()
                user = request.user

                RequestTiming.objects.create(
                    user=user, endpoint=endpoint, status_code=response.status_code,
                    start_time=start_time, end_time=end_time, duration=duration, 
                )
        except:
            pass
        
        return response
    
    def should_track(self, endpoint):
        # Define the list of endpoints to track
        trackable_endpoints = list(TrackableEndpoint.objects.all().values_list("endpoints", flat=True))
        return endpoint in trackable_endpoints
