from django.contrib import admin
from .models import *
from import_export import resources
from import_export.admin import ImportExportModelAdmin


# class EndpointsToWatchResource(resources.ModelResource):
#     class Meta:
#         model = EndpointsToWatch

# class APICallResource(resources.ModelResource):
#     class Meta:
#         model = APICall

class TrackableEndpointResource(resources.ModelResource):
    class Meta:
        model = TrackableEndpoint

class RequestTimingResource(resources.ModelResource):
    class Meta:
        model = RequestTiming

#######################################################################
# ADMINS

# class EndpointsToWatchResourceAdmin(ImportExportModelAdmin):
#     resource_class = EndpointsToWatchResource
#     # search_fields = ['user_id']
#     # list_filter = ('date_created', 'blacklisted')

#     def get_list_display(self, request):
#         return [field.name for field in self.model._meta.concrete_fields]
    

# class APICallResourceAdmin(ImportExportModelAdmin):
#     resource_class = APICallResource
#     list_filter = ('date_created',)

#     def get_list_display(self, request):
#         return [field.name for field in self.model._meta.concrete_fields]

class TrackableEndpointResourceAdmin(ImportExportModelAdmin):
    resource_class = TrackableEndpointResource
    search_fields = ['endpoints']
    list_filter = ('date_created',)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class RequestTimingResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["user"]
    resource_class = RequestTimingResource
    search_fields = ['user__email', 'endpoint']
    list_filter = ('date_created', 'status_code')

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


# admin.site.register(EndpointsToWatch, EndpointsToWatchResourceAdmin)
# admin.site.register(APICall, APICallResourceAdmin)
admin.site.register(TrackableEndpoint, TrackableEndpointResourceAdmin)
admin.site.register(RequestTiming, RequestTimingResourceAdmin)
