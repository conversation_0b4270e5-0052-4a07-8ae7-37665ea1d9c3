from django.urls import path, include
from send_money import views


app_name = "send_money"

# URLCOnf
send_money_buddy_urls = [
                path('send_money_paybuddy/<str:buddy_phone>/', views.SendMoneyPayBuddyAPIView.as_view()),
                path('send_money_paybuddy/', views.SendMoneyPayBuddyAPIView.as_view(), name="send_money_pay_buddy"),
            ]

send_money_bank_account_urls = [
                path('fetch_account_name/', views.FetchAccountName.as_view()),
                path('send_money_bank_account/', views.SendMoneyBankAccountAPIView.as_view(), name='first'),
                path('internal_send_money_bank_account/', views.InternalSendMoneyBankAccountAPIView.as_view()),
                path('upload_multi_send_file/', views.UploadFileForSendMultipleAPIView.as_view()),
                path('get_upload_send_data/', views.GetUploadSendMoneyData.as_view()),
                path('upload_send_data_list/', views.UploadSendMoneyList.as_view()),
                path('validate_excel/', views.ValidateExcelView.as_view(), name='validate_excel'),
            ]

other_urls = [
                path('check_enough_user_balance/', views.CheckUserBalance.as_view()),
                path('invite_new_user/<str:phone_number>/', views.invite_new_user),
                path('other_services_charge/', views.OtherServicesChargeAgentAPIView.as_view()),
                path('fund_lotto_wallet/', views.FundLottoWalletAPIView.as_view()),
                path('fund_ajo_wallet/', views.FundAjoWalletAPIView.as_view()),
                path('fund_ajo_loans/', views.SendtoAjoLoansAPIView.as_view()),
                path('process_commission_reversal/', views.ProcessCommissionsReversal.as_view()),
                # path('register_lotto_win/', views.RegisterLottoWinAPIView.as_view()),
                # path('debit_other_coll_account/', views.DebitUserOtherAccount.as_view()),
                # path('release_hold_balance/', views.ReleaseHoldBalanceAPIView.as_view()),
            ]

qr_code_urls = [
                path('generate_qr_code/', views.CreateQRCode.as_view()),
                # path('invite_new_user/<str:phone_number>/', views.invite_new_user),
            ]


urlpatterns = [
                *send_money_buddy_urls,
                *send_money_bank_account_urls,
                *other_urls,
                *qr_code_urls,
            ]
