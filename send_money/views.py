from django.conf import settings
from django.db.models import Q
import pandas as pd
from io import StringIO
from django.http import HttpResponse

from rest_framework import status
from rest_framework.decorators import (
    api_view,
    permission_classes,
)
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from rest_framework.views import APIView
from rest_framework.response import Response

from send_money.models import *
from send_money.serializers import *
from send_money.helpers.helper_functions import (
    calculate_transaction_limit, fetch_account_name, detect_duplicate_transactions, fetch_account_name_second_option, fetch_lotto_agent_user, get_false_float_balance_function
)

from accounts.helpers.vfdbank_manager import VFDBank
from accounts.helpers.helper_func import AjoClass
from accounts.models import (
    AccountSystem, OtherCommissionsRecord, SendMoneyDumpData, UploadSendMoneyData, WalletSystem, AllBankList,
    Transaction, OtherServiceAccountSystem, OutOfBookTransfer, QRCode, RequisitionGroup, Escrow
)
from accounts.tasks import process_bank_name_for_upload_send_money_data, send_money_bank_transfer_task, manage_beneficiaries_task, send_money_pay_buddy_task, send_callback_out_for_send_to_lotto, send_callback_out_for_send_to_ajo

from main.models import *
from main.helper.helper_function import send_sms_to_invite_user_to_join, verify_super_token
from main.helper.utils import get_ip_address
from main.permissions import (
    BuddyTransferRegulatorPermission, HasKYC, HasTransactionPin, CanSendMoney, OTPVerified, CheckAccountAvailable,
    CheckWalletAvailable, SendMoneyRegulatorPermission, BlockSendMoneyOnLowBalancePermission, WhitelistPermission,
    LottoUserPermission, FundLottoWalletRegulatorPermission, CheckDynamicAuthentication, AdminLockPermission,
    NonLottoAgentsBlockSendMoneyOnLowBalancePermission, CustomIsAuthenticated, AccountNumberBlacklist,
    V2NonLottoAgentsBlockSendMoneyOnLowBalancePermission
)

from admin_dashboard.permissions import ApproveRequisitionPermission

from datetime import datetime




@api_view()
@permission_classes((CustomIsAuthenticated, HasTransactionPin, HasKYC))
def invite_new_user(request, phone_number):
    validated_invite_phone_number = User.format_number_from_back_add_234(phone_number)
    if validated_invite_phone_number:
        send_sms_to_invite_user_to_join(
            phone_number=validated_invite_phone_number, user=request.user
        )
        response = {"status": "true", "message": "SMS has been sent to new user"}
        return Response(response, status=status.HTTP_200_OK)

    response = {"status": "False", "message": "phone number is incorrect"}
    return Response(response, status=status.HTTP_400_BAD_REQUEST)



def check_buddy_reciever(buddy_user_instance):
    wallet_system_intance = WalletSystem.objects.filter(user=buddy_user_instance)
    if wallet_system_intance:
        # Get all Wallet Instances
        get_all_wallets = WalletSystem.get_uncreated_wallets(user=buddy_user_instance)

        if "SPEND" not in get_all_wallets \
                and "COLLECTION" not in get_all_wallets \
                and "SAVINGS" not in get_all_wallets \
                and "COMMISSIONS" not in get_all_wallets:

            return False
        else:
            return True

    else:
        return False



class CheckUserBalance(APIView):
    serializer_class = CheckUserBalanceSerializer
    permission_classes = [CustomIsAuthenticated, HasTransactionPin, HasKYC]

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        from_wallet_type = serializer.validated_data["from_wallet_type"]
        amount = serializer.validated_data["amount"]

        request_user =  request.user

        account_obj = WalletSystem.get_wallet(from_wallet_type)
        transaction_charge = ConstantTable.calculate_send_money_transaction_charge(user=request_user, amount=amount)
        amount_with_charge = amount + transaction_charge

        check_balance = User.check_sender_balance(
            user=request_user,
            amount_with_charge=amount_with_charge,
            account_obj=account_obj,
        )

        if check_balance:
            response = {"message": "enough balance found"}
            return Response(response, status=status.HTTP_200_OK)
        else:
            response = {"error": "20", "message": "not enough balance"}
        return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)


###########################################################################################
# Send Money PAYBUDDY


class SendMoneyPayBuddyAPIView(APIView):
    get_serializer_class = SendMoneyFindPayBuddySerializer
    post_serializer_class = SendMoneyPayBuddySerializer
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, CanSendMoney, HasKYC, CheckWalletAvailable,
                          BuddyTransferRegulatorPermission]

    # @silk_profile(name='Check Buddy Receiver')
    def check_buddy_reciever(self, buddy_user_instance):
        # wallet_system_intance = getattr(buddy_user_instance, 'wallets', None)

        # if wallet_system_intance.exists():
        #     # Get all Wallet Instances

        #     print(wallet_system_intance)
        #     print(wallet_system_intance.all())
        #     get_all_wallets = list(wallet_system_intance.values_list('wallet_type', flat=True))


        #     print(get_all_wallets)


        #     if "SPEND" not in get_all_wallets \
        #         or "COLLECTION" not in get_all_wallets:
        #             # or "SAVINGS" not in get_all_wallets \
        #             # or "COMMISSIONS" not in get_all_wallets:

        #         return False
        #     else:
        #         return True

        # else:
        #     return False

        wallet_system_intance = WalletSystem.objects.filter(user=buddy_user_instance)
        if wallet_system_intance:
            # Get all Wallet Instances
            get_all_wallets = WalletSystem.get_uncreated_wallets(user=buddy_user_instance)

            if "SPEND" not in get_all_wallets \
                or "COLLECTION" not in get_all_wallets:
                    # or "SAVINGS" not in get_all_wallets \
                    # or "COMMISSIONS" not in get_all_wallets:

                return False
            else:
                return True

        else:
            return False

    # @silk_profile(name='Get Send Buddy')
    def get(self, request, buddy_phone=None):
        if buddy_phone:
            formatted_buddy_phone = User.format_number_from_back_add_234(buddy_phone)

            if not formatted_buddy_phone.isnumeric():
                response = {
                    "status": "error",
                    "message": "Invalid Buddy Phone Number",
                }
                return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

            buddy_user_instance = User.objects.filter(
                Q(phone_number=formatted_buddy_phone) | Q(phone_number=buddy_phone)
            ).last()
            if not buddy_user_instance:
                response = {
                    "status": "error",
                    "message": "Buddy Does Not Exist",
                    "invite_user": f"{settings.BASE_URL}/invite_new_user/{formatted_buddy_phone}/",
                }
                return Response(response, status=status.HTTP_404_NOT_FOUND)
            else:

                check_buddy_wallet = self.check_buddy_reciever(buddy_user_instance=buddy_user_instance)
                if check_buddy_wallet == False:
                    response = {
                        "status": "error",
                        "message": "Buddy has no Wallets Created Yet. Let them know to do so while we also remind them",
                    }
                    return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

                else:
                    serializer = self.get_serializer_class(buddy_user_instance)
                    response = {
                        "message": "buddy found",
                        "data": serializer.data
                    }
                    return Response(response, status=status.HTTP_200_OK)
        else:
            response = {
                "status": "empty",
                "message": "Enter Value (phone or account name) in URL",
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)

    # @silk_profile(name='Post Send Buddy')
    def post(self, request):
        serializer = self.post_serializer_class(data=request.data)
        request_user = request.user

        if serializer.is_valid():

            # DATA/PAYLOAD
            from_wallet_type = serializer.validated_data["from_wallet_type"]
            to_wallet_type = serializer.validated_data["to_wallet_type"]
            main_data = serializer.validated_data["data"]
            transaction_pin = serializer.validated_data["transaction_pin"]
            bulk_id = serializer.validated_data.get("bulk_id")

            # LOGIC
            dump_data = serializer.validated_data.copy()
            del dump_data["transaction_pin"]

            payload_saved = SendMoneyDumpData.objects.create(
                user=request_user,
                transfer_type="BUDDY_TRANSFER",
                dump=json.dumps(dump_data)
            )

            get_all_wallet_types = WalletSystem.get_available_wallet_types()

            if from_wallet_type not in get_all_wallet_types or to_wallet_type not in get_all_wallet_types:
                response = {
                    "error": "895",
                    "message": "either 'from_wallet_type' or 'to_wallet_type' is not a correct wallet type",
                    "wallet_type_list": get_all_wallet_types
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            # Check Transaction Pin
            chcek_pin = User.check_sender_transaction_pin(
                user=request_user, pincode=transaction_pin
            )
            if chcek_pin == False:
                retries = User.count_down_transaction_pin_retries(request_user)

                response = {
                    "error": "error",
                    "message": "Incorrect Pin",
                    "retry_count": retries["retry_count"],
                    "remaining_retries": retries["remaining_retries"],
                }

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)

            else:
                User.reset_transaction_pin_retries(request_user)

            # Calculate Daily Transfer Limits Here

            calculate_daily_limit = calculate_transaction_limit(request_user, request.data)
            if calculate_daily_limit is not None:
                return Response(calculate_daily_limit, status=status.HTTP_400_BAD_REQUEST)

            if len(main_data) == 1:
                check_amount = main_data[0].get('amount')
                check_ben_nuban = User.format_number_from_back_add_234(main_data[0].get('buddy_phone_number'))

                # Check for duplicate transaction
                last_send_money_transaction = detect_duplicate_transactions(
                    user=request_user,
                    transaction_type="SEND_BUDDY",
                    amount=check_amount,
                    beneficiary=check_ben_nuban
                )

                if last_send_money_transaction == True and request_user.bypass_duplicate_trans == False:
                    response = {
                        "error": "143",
                        "message": "Duplicate Transaction. Please try again after 5 minutes!"
                    }
                    return Response(response, status=status.HTTP_403_FORBIDDEN)

            # Get wallet type

            sender_wallet_instance = WalletSystem.get_wallet(
                user=request_user, from_wallet_type=from_wallet_type
            )

            get_total_amount = 0.00

            for data in main_data:
                get_total_amount += data["amount"]

                deduce_user_balance = WalletSystem.check_wallet_balance(
                    amount=get_total_amount,
                    wallet_instance=sender_wallet_instance
                )

                if deduce_user_balance["status"] == False:
                # if sender_wallet_instance.available_balance < get_total_amount:
                    response = {
                        "error": "161",
                        "message": "You do not have sufficient balance to make this transaction",
                    }
                    return Response(response, status=status.HTTP_403_FORBIDDEN)

                if get_total_amount <= 0.0:
                    response = {
                        "error": "162",
                        "message": "amount must be greater than 0",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                else:
                    get_total_amount = get_total_amount

            # Check user's level of KYC and get transaction limit

            check_kyc = request_user.check_kyc.kyc_level

            transfer_limit = ConstantTable.calculate_transfer_limit(kyc_level=check_kyc, user=request_user)
            if get_total_amount > transfer_limit:
                response = {
                    "error": "error",
                    "message": f"You cannot transfer more than ₦{transfer_limit} with KYC Level {check_kyc}",
                }
                return Response(response, status=status.HTTP_403_FORBIDDEN)

            for data in main_data:
                buddy_phone_number = User.format_number_from_back_add_234(
                    data["buddy_phone_number"]
                )

                buddy_user_instance = User.objects.filter(phone_number=buddy_phone_number).last()

                if buddy_user_instance:
                    check_buddy_wallet = self.check_buddy_reciever(buddy_user_instance=buddy_user_instance)

                    if check_buddy_wallet == False:
                        response = {
                            "status": "error",
                            "message": f"Buddy {buddy_user_instance.phone_number} has no Wallets Created Yet. Let them know to do so while we also remind them",
                        }
                        return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

                    if buddy_user_instance == request.user and from_wallet_type == to_wallet_type:

                        response = {
                            "error": "162",
                            "message": "You cannot send money to yourself",
                        }
                        return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

                elif not buddy_user_instance:
                    response = {
                        "status": "error",
                        "message": f"Buddy with Phone Number {buddy_phone_number} Does Not Exist",
                        "invite_user": f"{settings.BASE_URL}/invite_new_user/{buddy_phone_number}/",
                    }
                    return Response(response, status=status.HTTP_404_NOT_FOUND)

                else:
                    pass

            if len(main_data) > 1:
                use_task = True
                if not bulk_id:
                    bulk_id = Transaction.create_liberty_reference(suffix="LGLP_BULK")
            else:
                use_task = False

            message_ids = []
            for data in main_data:
                # Debit Sender with amount due
                amount = data["amount"]

                deduct_balance = WalletSystem.deduct_balance(
                    user=request_user,
                    wallet=sender_wallet_instance,
                    amount=amount,
                    trans_type="SEND_BUDDY"
                )

                buddy_phone_number = User.format_number_from_back_add_234(
                    data["buddy_phone_number"]
                )

                buddy_user_instance = User.objects.filter(phone_number=buddy_phone_number).last()

                receiver_wallet = WalletSystem.get_wallet(
                    user = buddy_user_instance,
                    from_wallet_type = to_wallet_type
                )

                receiver_wallet_id = receiver_wallet.wallet_id

                # receiver_wallet_id = WalletSystem.get_wallet_id_with_phone_number(
                #     to_wallet_type=to_wallet_type, phone_number=buddy_phone_number
                # )

                customer_reference = data.get("customer_reference")
                metadata = data.get("metadata")


                narration = data["narration"]
                is_beneficiary = data["is_beneficiary"]
                is_recurring = data["is_recurring"]
                save_beneficiary = data["save_beneficiary"]
                remove_beneficiary = data["remove_beneficiary"]

                fee = 0.00
                total_amount_charged = amount + fee

                # Manage Beneficiaries
                manage_beneficiaries_task.apply_async(
                    queue="sms_queue",
                    kwargs={
                        "user_id": request_user.id,
                        "save_beneficiary": save_beneficiary,
                        "remove_beneficiary": remove_beneficiary,
                        "buddy_phone_number": buddy_phone_number,
                        "wallet_id": receiver_wallet_id,
                        "account_name": buddy_user_instance.bvn_full_name if buddy_user_instance.bvn_first_name else buddy_user_instance.full_name
                    }
                )



                # Send Money

                # CHECK IF ACCOUNT NUMBER IS IN BENEFICIARY HERE ----- NOTE

                user_balance_before = deduct_balance["balance_before"]

                user_balance_after = WalletSystem.get_balance_after(
                    user=request_user,
                    balance_before=user_balance_before,
                    total_amount=total_amount_charged,
                    is_credit=False
                )

                # Get IP ADDRESS
                address = request.META.get('HTTP_X_FORWARDED_FOR')
                if address:
                    ip_addr = address.split(',')[-1].strip()
                else:
                    ip_addr = request.META.get('REMOTE_ADDR')

                escrow_instance = WalletSystem.move_to_escrow_send_pay_buddy(
                    user=request_user,
                    balance_before=user_balance_before,
                    balance_after=user_balance_after,
                    from_wallet_id=sender_wallet_instance.wallet_id,
                    to_wallet_id=receiver_wallet_id,
                    from_wallet_type=from_wallet_type,
                    to_wallet_type=to_wallet_type,
                    transfer_type="SEND_BUDDY",
                    amount=amount,
                    to_nuban=buddy_phone_number,
                    to_account_name=buddy_user_instance.bvn_full_name if buddy_user_instance.bvn_first_name else buddy_user_instance.full_name,
                    liberty_commission=fee,
                    total_amount_charged=total_amount_charged,
                    narration=narration,
                    is_beneficiary=is_beneficiary,
                    is_recurring=is_recurring,
                    debit_credit_record_id=deduct_balance.get("debit_credit_record_id"),
                    ip_addr=ip_addr,
                    customer_reference=customer_reference,
                    bulk_id=bulk_id,
                    metadata=metadata
                )

                payload_saved.escrow_created = True

                if not use_task:
                    send_money_to_buddy = WalletSystem.fund_wallet_pay_buddy(
                        sender_user_instance=request.user,
                        buddy_user_instance=buddy_user_instance,
                        sender_wallet=sender_wallet_instance,
                        receiver_wallet=receiver_wallet,
                        amount=amount,
                        escrow_id=escrow_instance.escrow_id,
                        customer_reference=customer_reference,
                        metadata=metadata
                    )
                else:
                    message_id = send_money_pay_buddy_task.apply_async(
                        queue="processbulksheet",
                        kwargs={
                            "sender_wallet_id": sender_wallet_instance.wallet_id,
                            "receiver_wallet_id": receiver_wallet_id,
                            "amount": amount,
                            "escrow_id": escrow_instance.escrow_id,
                            "customer_reference": customer_reference,
                            "metadata": metadata

                        }
                    )
                    message_ids.append(message_id.id)

                    get_bulk_data = UploadSendMoneyData.objects.filter(request_id=bulk_id)

                    if bulk_id is not None and get_bulk_data and customer_reference is not None:
                        unique_bulk_instance = get_bulk_data.filter(unique_reference=customer_reference).last()
                        unique_bulk_instance.escrow_id = escrow_instance.escrow_id
                        unique_bulk_instance.is_sent = True
                        unique_bulk_instance.save()


            payload_saved.money_removed = True
            payload_saved.save()

            response = {
                "message": "success",
                "data": {
                    "message": "Transaction completed successfully",
                    "amount_sent": get_total_amount,
                    "escrow_id": escrow_instance.escrow_id,
                    "bulk_id": bulk_id,
                    "message_ids": str(message_ids),
                },
                "date_completed": datetime.now(),
            }
            return Response(response, status=status.HTTP_202_ACCEPTED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


###########################################################################################
# Send Money Via Bank


class FetchAccountName(APIView):
    serializer_class = FetchBankDetailsSerializer
    permission_classes = [CustomIsAuthenticated, HasTransactionPin, HasKYC]

    def post(self, request):
        serializer = self.serializer_class(data=request.data)


        if serializer.is_valid():
            account_number = serializer.validated_data["account_number"]
            bank_code = serializer.validated_data["bank_code"]
            print(account_number, bank_code, "########")

            fetch_account_detail = fetch_account_name(
                account_number=account_number, bank_code=bank_code
            )
            print(fetch_account_detail)

            if not fetch_account_detail["status"] == False:

                response = {
                    "message": "account name found",
                    "data": {
                        "account_number": f"{fetch_account_detail['data']['account_number']}",
                        "account_name": f"{fetch_account_detail['data']['account_name']}",
                    },
                }
                return Response(response, status=status.HTTP_200_OK)


            else:

                get_bank_list = AllBankList.get_all_bank_list()
                try:
                    bank_inst = get_bank_list.get(cbn_code=bank_code)
                    bank_code = bank_inst.bank_code
                except AllBankList.DoesNotExist:
                    pass

                fetch_account_detail = fetch_account_name_second_option(
                    account_number=account_number, bank_code=bank_code
                )

                if fetch_account_detail["status"] == True:

                    response = {
                        "message": "account name found",
                        "data": {
                            "account_number": f"{fetch_account_detail['data']['account_number']}",
                            "account_name": f"{fetch_account_detail['data']['account_name']}",
                        },
                    }
                    return Response(response, status=status.HTTP_200_OK)

                else:
                    response = {"message": "could not fetch account name", "data": []}

                    return Response(response, status=status.HTTP_404_NOT_FOUND)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ValidateExcelView(APIView):
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request, *args, **kwargs):
        file = request.FILES.get('file')
        if not file:
            return Response({"error": "No file provided"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Validate the Excel file
            df = pd.read_excel(file, engine='openpyxl')

            # Check if all required columns are present
            required_columns = ['account_name', 'bank_name', 'bank_code', 'account_number', 'amount', 'narration']
            for col in required_columns:
                if col not in df.columns:
                    raise ValueError(f"Missing required column: {col}")

            # Check for empty cells in required columns
            if df[required_columns].isnull().values.any():
                missing_data = df[required_columns].isnull()
                missing_info = {}
                for col in required_columns:
                    if missing_data[col].any():
                        # Add 1 to each index to make it 1-based
                        missing_info[col] = (missing_data.index[missing_data[col]] + 1).tolist()
                raise ValueError(f"The Excel file contains empty cells in the following columns and rows: {missing_info}")

            # Ensure columns are treated as strings and check for white spaces
            for col in ['account_name', 'bank_name', 'bank_code', 'narration']:
                if df[col].isnull().any():
                    raise ValueError(f"The column '{col}' contains empty cells.")
                df[col] = df[col].astype(str)
                whitespace_rows = (df[df[col].str.strip() == ''].index + 1).tolist()
                if whitespace_rows:
                    raise ValueError(f"The column '{col}' contains cells with only white space at rows: {whitespace_rows}")

            # Check for 10-digit account numbers
            df['account_number'] = df['account_number'].astype(str)
            invalid_account_numbers = (df[~df['account_number'].str.match(r'^\d{10}$')].index + 1).tolist()
            if invalid_account_numbers:
                raise ValueError(f"All 'account_number' entries must be exactly 10 digits. Invalid entries at rows: {invalid_account_numbers}")

            # Check for positive amounts
            invalid_amounts = (df[df['amount'] <= 0].index + 1).tolist()
            if invalid_amounts:
                raise ValueError(f"Amount should be a positive number. Invalid entries at rows: {invalid_amounts}")

            # Convert DataFrame to CSV
            csv_buffer = StringIO()
            df.to_csv(csv_buffer, index=False)
            csv_buffer.seek(0)

            # Create HTTP response with CSV
            response = HttpResponse(csv_buffer, content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename=validated_data.csv'
            return response

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SendMoneyBankAccountAPIView(APIView):
    post_serializer_class = SendMoneyBankTransferSerializer
    permission_classes = [CustomIsAuthenticated, SendMoneyRegulatorPermission, OTPVerified, HasTransactionPin, CanSendMoney,
                          HasKYC, CheckWalletAvailable, CheckAccountAvailable, BlockSendMoneyOnLowBalancePermission, AccountNumberBlacklist,
                          V2NonLottoAgentsBlockSendMoneyOnLowBalancePermission,
                        ]
    # from silk.profiling.profiler import silk_profile
    # @silk_profile(name='Post Send Bank Account')
    def post(self, request):

        trans_started = datetime.now()

        request_user = request.user

        serializer = self.post_serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        # PAYLOAD
        from_wallet_type = serializer.validated_data["from_wallet_type"]
        transaction_mode = serializer.validated_data.get("transaction_mode")
        main_data = serializer.validated_data["data"]
        total_amount = serializer.validated_data.get("total_amount")
        total_amount_with_charge = serializer.validated_data.get("total_amount_with_charge")
        send_by_card_rrn = serializer.validated_data.get("send_by_card_rrn")
        transaction_pin = serializer.validated_data["transaction_pin"]
        bulk_id = serializer.validated_data.get("bulk_id")
        from_account_number = serializer.validated_data.get("from_account_number")

        dump_data = serializer.validated_data.copy()
        del dump_data["transaction_pin"]

        payload_saved = SendMoneyDumpData.objects.create(
            user=request_user,
            transfer_type="BANK_TRANSFER",
            dump=json.dumps(dump_data)
        )

        # Calculate Daily Transfer Limits Here
        calculate_daily_limit = calculate_transaction_limit(request_user, request.data)
        if calculate_daily_limit is not None:
            return Response(calculate_daily_limit, status=status.HTTP_400_BAD_REQUEST)

        if transaction_mode:
            transaction_mode = transaction_mode
        else:
            transaction_mode = "SEND_MONEY_ONLINE"

        # LOGIC
        # Check Transaction Pin
        from_provider_type = AccountSystem.get_provider_type(user=request_user)

        chcek_pin = User.check_sender_transaction_pin(
            user=request_user, pincode=transaction_pin
        )
        if chcek_pin == False:
            retries = User.count_down_transaction_pin_retries(request_user)

            response = {
                "status": "error",
                "error": "344",
                "message": "Incorrect Pin",
                "retry_count": retries["retry_count"],
                "remaining_retries": retries["remaining_retries"],
            }

            return Response(response, status=status.HTTP_401_UNAUTHORIZED)

        else:
            User.reset_transaction_pin_retries(request_user)

            # Get wallet type

            wallet_instance = WalletSystem.get_wallet(
                user=request_user, from_wallet_type=from_wallet_type
            )

            if from_account_number:
                account_instance = AccountSystem.objects.select_related('user', 'wallet').filter(
                    Q(account_number=from_account_number) & Q(account_provider=from_provider_type) & Q(user=request_user)
                ).first()

                if not account_instance:
                    response = {
                        "status": "error",
                        "error": "989",
                        "message": "No From Account Number Found",
                    }

                    return Response(response, status=status.HTTP_400_BAD_REQUEST)
            else:

                account_instance = AccountSystem.objects.filter(
                    Q(wallet=wallet_instance)
                    & Q(account_type=AccountTypes.COLLECTION)
                    & Q(account_provider=from_provider_type)
                ).select_related('user').first()

            if not account_instance:
                response = {
                    "status": "error",
                    "error": "989",
                    "message": "No Account Found",
                }

                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            # Check for duplicate transactions
            if ConstantTable.get_constant_table_instance().block_duplicate_transaction == True and request_user.bypass_duplicate_trans == False:
                if len(main_data) == 1:
                    check_amount = main_data[0].get('amount')
                    check_ben_nuban = main_data[0].get('account_number')

                    # Check for duplicate transaction
                    last_send_money_transaction = detect_duplicate_transactions(
                        user=request_user,
                        transaction_type="SEND_BANK_TRANSFER",
                        amount=check_amount,
                        beneficiary=check_ben_nuban
                    )

                    if last_send_money_transaction == True:
                        response = {
                            "status": "error",
                            "error": "143",
                            "message": "Duplicate Transaction. Please try again after 5 minutes!"
                        }
                        return Response(response, status=status.HTTP_403_FORBIDDEN)

            vfd_account_id = ""
            if from_provider_type == "VFD":
                vfd_account_id = account_instance.vfd_account_id
                if not vfd_account_id:
                    try:
                        vfd_account_id = VFDBank.vfd_account_enquiry(account_number=account_instance.account_number)["data"]["accountId"]
                        account_instance.vfd_account_id = vfd_account_id
                        account_instance.save()
                    except Exception as err:
                        response = {
                            "status": "error",
                            "error": "45",
                            "message": f"Source Account ID not found"
                        }

                        return Response(response, status=status.HTTP_400_BAD_REQUEST)

                if ConstantTable.get_constant_table_instance().enquire_account_on_send_money == True:
                    for data in main_data:
                        account_number = data["account_number"]
                        bank_code = data["bank_code"]

                        if bank_code in ["090110", "999999"]:
                            transfer_type_check = "intra"
                        else:
                            transfer_type_check = "inter"

                        enquire_account = VFDBank.vfd_get_transfer_recipient(
                            transfer_type=transfer_type_check,
                            account_number=account_number,
                            bank_code=bank_code
                        )

                        if not enquire_account.get("status") == "00" and not enquire_account.get(
                                "message") == "Account Found":
                            response = {
                                "status": "error",
                                "error": "44",
                                "message": f"Account Number {account_number} not found with selected bank"
                            }

                            return Response(response, status=status.HTTP_400_BAD_REQUEST)

            elif from_provider_type in ["WEMA", "FIDELITY"]:
                # For WEMA and FIDELITY, we'll use the account number as the vfd_account_id
                # This is just to maintain compatibility with the existing code
                vfd_account_id = account_instance.account_number

            get_total_amount_with_charge = 0.00
            get_total_amount_without_charge = 0.00
            customer_reference = None

            for data in main_data:
                amount = data["amount"]
                check_transaction_charge = ConstantTable.calculate_send_money_transaction_charge(user=request_user, amount=amount)
                check_amount_with_charge = amount + check_transaction_charge
                data["new_amount_with_charge"] = check_amount_with_charge

                # Main Check Balance Before Transfer
                deduce_user_balance = WalletSystem.check_wallet_balance(
                    amount=check_amount_with_charge + get_total_amount_with_charge,
                    wallet_instance=wallet_instance
                )

                if deduce_user_balance["status"] == False:
                    response = {
                        "status": "error",
                        "error": "244",
                        "message": f"You do not have sufficient balance to make this transaction. Please remove {amount}",
                    }

                    return Response(response, status=status.HTTP_403_FORBIDDEN)

                if check_amount_with_charge + get_total_amount_with_charge <= 0.0:
                    response = {
                        "status": "error",
                        "error": "162",
                        "message": "amount must be greater than 0",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                else:
                    get_total_amount_with_charge += check_amount_with_charge
                    get_total_amount_without_charge += amount

            # Check user's level of KYC and get transaction limit
            check_kyc = request_user.check_kyc.kyc_level

            transfer_limit = ConstantTable.calculate_transfer_limit(kyc_level=check_kyc, user=request_user)

            if get_total_amount_without_charge > transfer_limit:
                response = {
                    "status": "error",
                    "error": "871",
                    "message": f"You cannot transfer more than ₦{transfer_limit} with KYC Level {check_kyc}",
                }
                return Response(response, status=status.HTTP_403_FORBIDDEN)

            else:

                if len(main_data) > 1:
                    use_task = True
                    if not bulk_id:
                        bulk_id = Transaction.create_liberty_reference(suffix="LGLP_BULK")
                else:
                    use_task = False

                escrow_id = None

                for data in main_data:

                    new_amount_with_charge = data["new_amount_with_charge"]

                    # Deduct Balance
                    deduct_balance = WalletSystem.deduct_balance(
                        user=request_user,
                        wallet=wallet_instance,
                        amount=new_amount_with_charge,
                        trans_type="SEND_BANK_EXTERNAL"
                    )

                    if from_account_number:
                        AccountSystem.handle_other_account_fund_and_debit(entry="DEBIT", amount=get_total_amount_with_charge, account_inst=account_instance)

                    account_number = data["account_number"]
                    account_name = data["account_name"]
                    bank_code = data["bank_code"]
                    bank_name = data["bank_name"]
                    amount = data["amount"]
                    narration = data["narration"]
                    is_beneficiary = data["is_beneficiary"]
                    is_recurring = data["is_recurring"]
                    save_beneficiary = data["save_beneficiary"]
                    remove_beneficiary = data["remove_beneficiary"]

                    ledger_commission = data.get("ledger_commission")
                    commission_type = data.get("commission_type")
                    customer_reference = data.get("customer_reference")

                    if customer_reference and Escrow.objects.filter(user=request_user, customer_reference=customer_reference).exists():
                        response = {
                            "status": "error",
                            "message": f"Customer reference already exists",
                            "reference": customer_reference,
                            "error": "454"
                        }
                        return Response(response, status=status.HTTP_400_BAD_REQUEST)

                    if ledger_commission is None:
                        ledger_commission = 0.00

                    check_transaction_charge = ConstantTable.calculate_send_money_transaction_charge(user=request_user, amount=amount)
                    fee = check_transaction_charge
                    total_amount_charged = amount + fee

                    # Manage Beneficiaries
                    manage_beneficiaries_task.apply_async(
                        queue="sms_queue",
                        kwargs={
                            "user_id": request_user.id,
                            "save_beneficiary": save_beneficiary,
                            "remove_beneficiary": remove_beneficiary,
                            "account_number": account_number,
                            "account_name": account_name,
                            "bank_name": bank_name,
                            "bank_code": bank_code,
                        }
                    )

                    user_balance_before = deduct_balance["balance_before"]

                    user_balance_after = WalletSystem.get_balance_after(
                        user=request_user,
                        balance_before=user_balance_before,
                        total_amount=total_amount_charged,
                        is_credit=False
                    )

                    # Get IP ADDRESS
                    ip_addr = get_ip_address(request)

                    beneficiary_nuban = account_instance.account_number
                    beneficiary_account_name = account_instance.account_name
                    beneficiary_bank_code = account_instance.bank_code

                    print(beneficiary_account_name, "$$$$$$$")
                    print(account_instance.id, "$$$$$$$")

                    escrow_qs_inst = AccountSystem.move_to_escrow_bank_transfer(
                        user=request_user,
                        from_wallet_id=wallet_instance.wallet_id,
                        from_wallet_type=from_wallet_type,
                        amount=amount,
                        balance_before=user_balance_before,
                        balance_after=user_balance_after,
                        from_provider_type=from_provider_type,
                        liberty_commission=fee,
                        ip_addr=ip_addr,
                        total_amount_charged=total_amount_charged,
                        user_account_number=beneficiary_nuban,
                        user_account_name=beneficiary_account_name,
                        user_bank_name=account_instance.bank_name,
                        user_bank_code=beneficiary_bank_code,
                        user_account_provider=account_instance.account_provider,
                        to_account_name=account_name,
                        to_nuban=account_number,
                        to_bank_name=bank_name,
                        to_bank_code=bank_code,
                        narration=narration,
                        is_beneficiary=is_beneficiary,
                        is_recurring=is_recurring,
                        transaction_mode=transaction_mode,
                        transfer_type="SEND_BANK_TRANSFER",
                        debit_credit_record_id=deduct_balance.get("debit_credit_record_id"),
                        send_by_card_rrn=send_by_card_rrn,
                        customer_reference = customer_reference,
                        bulk_id = bulk_id,
                        pos_charge=ledger_commission if ledger_commission else 0,
                        pos_charge_type=commission_type,
                        vfd_account_id=vfd_account_id
                    )

                    payload_saved.escrow_created = True
                    payload_saved.save()

                    print(escrow_qs_inst.user_account_name, "pppppppppp^^^^^")
                    escrow_id = escrow_qs_inst.escrow_id

                    external_transaction_id = AccountSystem.general_send_money(
                        user=request_user,
                        escrow_instance=escrow_qs_inst,
                        from_provider_type=from_provider_type,
                        trans_started=trans_started
                    )

                    if settings.ENVIRONMENT == "development":
                        send_money_bank_transfer_task(
                            escrow_id=escrow_id,
                            transaction_id=external_transaction_id,
                            amount=amount + fee
                        )

                    else:
                        send_money_bank_transfer_task.apply_async(
                            queue="newsendbanktrans",
                            kwargs={
                                "escrow_id": escrow_id,
                                "transaction_id": external_transaction_id,
                                "amount": amount + fee,
                            }
                        )

                    # if not use_task:
                    #     send_money_bank = AccountSystem.general_send_money(
                    #         user=request_user,
                    #         from_wallet_type=from_wallet_type,
                    #         from_provider_type=from_provider_type,
                    #         amount=amount + fee,
                    #         escrow_id=escrow_id,
                    #         ledger_commission=ledger_commission,
                    #         commission_type=commission_type
                        # )

                    if use_task:
                        # send_money = send_money_bank_transfer_task.delay(
                        #     user_id=request_user.id,
                        #     from_wallet_type=from_wallet_type,
                        #     from_provider_type=from_provider_type,
                        #     amount=amount + fee,
                        #     escrow_id=escrow_id,
                        #     ledger_commission=ledger_commission,
                        #     commission_type=commission_type
                        # )

                        get_bulk_data = UploadSendMoneyData.objects.filter(request_id=bulk_id)

                        if bulk_id is not None and get_bulk_data and customer_reference is not None:
                            unique_bulk_instance = get_bulk_data.filter(unique_reference=customer_reference).last()
                            unique_bulk_instance.escrow_id = escrow_id
                            unique_bulk_instance.is_sent = True
                            unique_bulk_instance.save()

                # ENDFOR

                payload_saved.money_removed = True
                payload_saved.save()

                trans_stopped = datetime.now()
                trans_time = trans_stopped - trans_started

                response = {
                    "status": "success",
                    "message": "success",
                    "data": {
                        "message": "Transaction In Progress",
                        "amount_sent": get_total_amount_without_charge,
                        "escrow_id": escrow_id,
                        "bulk_id": bulk_id,
                        "customer_reference": customer_reference,
                    },
                    "date_completed": datetime.now(),
                }
                return Response(response, status=status.HTTP_202_ACCEPTED)


class InternalSendMoneyBankAccountAPIView(APIView):
    post_serializer_class = InternalSendMoneyBankTransferSerializer
    permission_classes = [
        WhitelistPermission, SendMoneyRegulatorPermission, CheckWalletAvailable,
        CheckAccountAvailable, BlockSendMoneyOnLowBalancePermission
    ]

    def post(self, request):

        serializer = self.post_serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        # PAYLOAD
        user = serializer.validated_data["request_user"]
        from_wallet_type = serializer.validated_data["from_wallet_type"]
        transaction_mode = serializer.validated_data.get("transaction_mode")
        main_data = serializer.validated_data["data"]
        total_amount = serializer.validated_data["total_amount"]
        total_amount_with_charge = serializer.validated_data["total_amount_with_charge"]
        transaction_pin = serializer.validated_data["transaction_pin"]

        request_user = User.objects.filter(email=user).last()

        dump_data = serializer.validated_data.copy()
        del dump_data["transaction_pin"]

        payload_saved = SendMoneyDumpData.objects.create(
            user=request_user,
            transfer_type="BANK_TRANSFER",
            dump=json.dumps(dump_data)
        )

        if transaction_mode:
            transaction_mode = transaction_mode
        else:
            transaction_mode = "SEND_MONEY_ONLINE"

        # LOGIC

        if transaction_pin != request_user.transaction_pin:
            response = {
                "error": "44",
                "message": f"Incorrect Transaction Pin"
            }

            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        from_provider_type = AccountSystem.get_provider_type(user=request_user)

        # Get wallet type

        wallet_instance = WalletSystem.get_wallet(
            user=request_user, from_wallet_type=from_wallet_type
        )

        get_total_amount_with_charge = 0.00
        get_total_amount_without_charge = 0.00

        for data in main_data:
            amount = data["amount"]

            check_transaction_charge = ConstantTable.calculate_send_money_transaction_charge(user=request_user, amount=amount)

            check_amount_with_charge = amount + check_transaction_charge

            # Main Check Balance Before Transfer
            get_user_wallet_balance = WalletSystem.check_wallet_balance(
                amount = check_amount_with_charge + get_total_amount_with_charge,
                wallet_instance = wallet_instance
            )

            if get_user_wallet_balance["status"] == False:
                response = {
                    "error": "244",
                    "message": f"You do not have sufficient balance to make this transaction. Please remove {amount}",
                }

                return Response(response, status=status.HTTP_403_FORBIDDEN)

            else:
                get_total_amount_with_charge += check_amount_with_charge
                get_total_amount_without_charge += amount

        # Check user's level of KYC and get transaction limit

        check_kyc = request_user.check_kyc.kyc_level

        transfer_limit = ConstantTable.calculate_transfer_limit(kyc_level=check_kyc, user=request_user)

        if get_total_amount_without_charge > transfer_limit:
            response = {
                "error": "error",
                "message": f"You cannot transfer more than ₦{transfer_limit} with KYC Level {check_kyc}",
            }
            return Response(response, status=status.HTTP_403_FORBIDDEN)

        else:

            float_user = WalletSystem.get_float_user()

            # Deduct Balance
            deduct_balance = WalletSystem.deduct_balance(
                user=request_user,
                wallet=wallet_instance,
                amount=get_total_amount_with_charge,
                trans_type="SEND_BANK_EXTERNAL"
            )

            # # Deduct Float Balance
            # deduct_float_balance = WalletSystem.deduct_balance(
            #     user=float_user,
            #     wallet=WalletSystem.get_float_wallet(from_wallet_type="FLOAT"),
            #     amount=get_total_amount_with_charge,
            #     trans_type="SEND_BANK_INTERNAL"
            # )

            payload_saved.money_removed = True
            payload_saved.save()

            for data in main_data:
                account_number = data["account_number"]
                account_name = data["account_name"]
                bank_code = data["bank_code"]
                bank_name = data["bank_name"]
                amount = data["amount"]
                narration = data["narration"]
                is_beneficiary = data["is_beneficiary"]
                is_recurring = data["is_recurring"]
                save_beneficiary = data["save_beneficiary"]
                remove_beneficiary = data["remove_beneficiary"]

                ledger_commission = data.get("ledger_commission")
                commission_type = data.get("commission_type")

                if ledger_commission is None:
                    ledger_commission = 0.00

                fee = check_transaction_charge
                total_amount_charged = amount + fee

                # Manage Beneficiaries
                manage_beneficiaries_task.apply_async(
                    queue="sms_queue",
                    kwargs={
                        "user_id": request_user.id,
                        "save_beneficiary": save_beneficiary,
                        "remove_beneficiary": remove_beneficiary,
                        "account_number": account_number,
                        "account_name": account_name,
                        "bank_name": bank_name,
                        "bank_code": bank_code,
                    }
                )


                # Send Money
                account_instance = AccountSystem.objects.filter(
                    Q(wallet_id=wallet_instance) & Q(account_provider=from_provider_type)
                ).first()


                user_balance_before = deduct_balance["balance_before"]

                user_balance_after = WalletSystem.get_balance_after(
                    user=request_user,
                    balance_before=user_balance_before,
                    total_amount=total_amount_charged,
                    is_credit=False
                )

                # Get IP ADDRESS
                address = request.META.get('HTTP_X_FORWARDED_FOR')
                if address:
                    ip_addr = address.split(',')[-1].strip()
                else:
                    ip_addr = request.META.get('REMOTE_ADDR')

                escrow_qs_inst = AccountSystem.move_to_escrow_bank_transfer(
                    user=request_user,
                    from_wallet_id=wallet_instance.wallet_id,
                    from_wallet_type=from_wallet_type,
                    amount=amount,
                    balance_before=user_balance_before,
                    balance_after=user_balance_after,
                    from_provider_type=from_provider_type,
                    liberty_commission=fee,
                    # amount_left_float_bank=amount_left_float_bank,
                    # amount_left_user_wallet=amount_left_user_wallet,
                    total_amount_charged=total_amount_charged,
                    user_account_number=account_instance.account_number,
                    user_account_name=account_instance.account_name,
                    user_bank_name=account_instance.bank_name,
                    user_bank_code=account_instance.bank_code,
                    user_account_provider=account_instance.account_provider,
                    to_account_name=account_name,
                    to_nuban=account_number,
                    to_bank_name=bank_name,
                    to_bank_code=bank_code,
                    narration=narration,
                    is_beneficiary=is_beneficiary,
                    is_recurring=is_recurring,
                    transaction_mode=transaction_mode,
                    transfer_type="SEND_BANK_TRANSFER",
                    debit_credit_record_id=deduct_balance.get("debit_credit_record_id"),
                    ip_addr=ip_addr
                )

                payload_saved.escrow_created = True
                payload_saved.save()

                escrow_id = escrow_qs_inst.escrow_id

                send_money_bank = AccountSystem.general_send_money(
                    user=request_user,
                    from_wallet_type=from_wallet_type,
                    from_provider_type=from_provider_type,
                    amount=amount + fee,
                    escrow_id=escrow_id,
                    ledger_commission=ledger_commission,
                    commission_type=commission_type
                )

            # ENDFOR

            response = {
                "message": "success",
                "data": {
                    "message": "Transaction completed successfully",
                    "amount_sent": get_total_amount_without_charge,
                    # "amount_sent": get_total_amount_with_charge,
                    # "data": send_money_bank
                },
                "date_completed": datetime.now(),
            }
            return Response(response, status=status.HTTP_202_ACCEPTED)


class InternalFetchAccountName:

    def parse_details(self, payload):
        account_number = payload["account_number"]
        bank_code = payload["bank_code"]

        fetch_account_detail = fetch_account_name_second_option(
            account_number=account_number, bank_code=bank_code
        )
        if fetch_account_detail["status"]:
            response = {
                "message": "account name found",
                "data": {
                    "account_number": f"{fetch_account_detail['data']['account_number']}",
                    "account_name": f"{fetch_account_detail['data']['account_name']}",
                },
            }
            return response
        else:
            fetch_account_detail = fetch_account_name(
                account_number=account_number, bank_code=bank_code
            )
            if fetch_account_detail["status"]:
                response = {
                    "message": "account name found",
                    "data": {
                        "account_number": f"{fetch_account_detail['data']['account_number']}",
                        "account_name": f"{fetch_account_detail['data']['account_name']}",
                    },
                }
                return response

            else:
                response = {"message": "could not fetch account name", "data": []}
                return response


class UploadFileForSendMultipleAPIView(APIView):
    parser_classes = [MultiPartParser]
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin,
        CanSendMoney, HasKYC, CheckWalletAvailable, CheckAccountAvailable,
    ]

    def post(self, request, format=None):
        request_user = request.user

        # try:

        xlsx_file = request.FILES["file"]
        title = request.POST.get("title", "Untitled")
        transfer_type = request.POST.get("transfer_type")

        get_req_group = RequisitionGroup.get_approver_or_reviewer(request_user.email, "UPLOADER")
        if get_req_group is not None:
            approver = RequisitionGroup.get_approver_or_reviewer_with_group(request.POST.get("approver", None), "APPROVER", get_req_group)
            reviewer = RequisitionGroup.get_approver_or_reviewer_with_group(request.POST.get("reviewer", None), "REVIEWER", get_req_group)
        else:
            approver = None
            reviewer = None


        print(approver)
        print(reviewer)




        if not transfer_type or transfer_type not in ["BUDDY", "BANK"]:
            response = {
                "error": "989",
                "message":'Transfer type must be either BUDDY or BANK'
            }

            return Response(data=response, status=status.HTTP_400_BAD_REQUEST)

        if not xlsx_file.name.endswith('.xlsx'):
            response = {
                "error": "989",
                "message":'File is not xlsx type'
            }

            return Response(data=response, status=status.HTTP_400_BAD_REQUEST)

        #if file is too large, return
        if xlsx_file.multiple_chunks():
            response = "Uploaded file is too big (%.2f MB)." % (xlsx_file.size/(1000*1000),)
            return Response(status=status.HTTP_406_NOT_ACCEPTABLE)


        # result_id = UploadSendMoneyData.process_data_from_excel(request.user, xlsx_file, title, transfer_type)
        result_id = UploadSendMoneyData.process_data_from_excel_to_instances(request.user, xlsx_file, title, transfer_type)

        if result_id["error"] == True:
            response = {
                "error": "999",
                "request_id": None,
                "message": "Missing columns found",
                "missing_columns": result_id["missing_columns"]
            }
            return Response(data=response, status=status.HTTP_400_BAD_REQUEST)



        # request_id = UploadSendMoneyData.objects.get(request_id=result_id["request_id"])
        request_id = result_id["request_id"]

        process_bank_name_for_upload_send_money_data(request_id, transfer_type)

        # process_bank_name_for_upload_send_money_data.apply_async(
        #     queue="processbulksheet",
        #     kwargs={
        #         "request_id": request_id,
        #         "transfer_type": transfer_type
        #     }
        # )

        response = {
            "request_id": request_id,
            "message": "File uploaded successfully",
            "missing_columns": None
        }

        return Response(data=response, status=status.HTTP_202_ACCEPTED)

        # except Exception as e:

        #     response = {"message":'Processing Error : '+repr(e)}

            # return Response(data=response, status=status.HTTP_400_BAD_REQUEST)


    def get(self, request):
        request_user = request.user

        get_group_members = RequisitionGroup.get_group_members(request_user)
        if get_group_members is None:
            response = {
                "error": "565",
                "message":'User does not belong to a requisition group'
            }
            return Response(data=response, status=status.HTTP_404_NOT_FOUND)

        response = {
            "status": True,
            "data": get_group_members
        }
        return Response(data=response, status=status.HTTP_200_OK)







class ApproveUploadSendMoneyData(APIView):
    permission_classes = [CustomIsAuthenticated, ApproveRequisitionPermission]

    def put(self, request):
        if request.query_params.get('request_id') is None:
            response = {
                "error": "549",
                "message": "No Request ID Attached"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)


        get_data = UploadSendMoneyData.objects.filter(user=request.user, request_id=request.query_params.get('request_id')).last()
        if get_data is None:
            response = {
                "error": "550",
                "message": "Invalid Request ID"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)
        elif get_data.is_processed == False:
            response = {
                "error": "551",
                "message": "Data is being processed"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)

        else:
            get_data.approved = True
            get_data.save()

            response = {
                "total_amount": sum([data["amount"] for data in get_data.good_data]) if get_data.good_data else None,
                "total_recipients": len(get_data.good_data) if get_data.good_data else 0,
                "message": "Data Found",
                "send_money_data": get_data.send_money_data,
                "good_data": get_data.good_data,
                "bad_data": get_data.bad_data,
                "data": get_data.resolved_data,
            }
            return Response(response, status=status.HTTP_200_OK)


class RejectUploadSendMoneyData(APIView):
    permission_classes = [CustomIsAuthenticated, ApproveRequisitionPermission]

    def put(self, request):
        if request.query_params.get('request_id') is None:
            response = {
                "error": "549",
                "message": "No Request ID Attached"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)


        get_data = UploadSendMoneyData.objects.filter(user=request.user, request_id=request.query_params.get('request_id')).last()
        if get_data is None:
            response = {
                "error": "550",
                "message": "Invalid Request ID"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)
        elif get_data.is_processed == False:
            response = {
                "error": "551",
                "message": "Data is being processed"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)

        else:
            get_data.rejected = True
            get_data.save()

            response = {
                "total_amount": sum([data["amount"] for data in get_data.good_data]) if get_data.good_data else None,
                "total_recipients": len(get_data.good_data) if get_data.good_data else 0,
                "message": "Data Found",
                "good_data": get_data.good_data,
                "bad_data": get_data.bad_data,
                "data": get_data.resolved_data,
            }
            return Response(response, status=status.HTTP_200_OK)


class GetUploadSendMoneyData(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin,
        CanSendMoney, HasKYC, CheckWalletAvailable, CheckAccountAvailable,
    ]

    def get(self, request):
        if request.query_params.get('request_id') is None:
            response = {
                "error": "549",
                "message": "No Request ID Attached"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)


        get_data = UploadSendMoneyData.objects.filter(user=request.user, request_id=request.query_params.get('request_id'))
        if get_data is None:
            response = {
                "error": "550",
                "message": "Invalid Request ID"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)
        elif get_data.last().is_processed == False:
            response = {
                "error": "551isnumeric",
                "message": "Data is being processed"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)

        else:
            good_data = get_data.filter(is_good =True)
            bad_data = get_data.filter(is_good =False)

            if get_data.last().transfer_type == "BUDDY":
                the_serializer = GetBuddyUploadDataSerializer
            else:
                the_serializer = GetBankUploadDataSerializer

            response = {
                "bulk_id": good_data.first().request_id,
                "request_type": good_data.first().transfer_type,
                "total_amount": sum([data.amount for data in good_data]),
                "total_recipients": len(good_data),
                "message": "Data Found",
                "send_money_data": the_serializer(good_data, many=True).data,
                # "good_data": GetUploadDataSerializer(good_data, many=True).data,
                "bad_data": the_serializer(bad_data, many=True).data,
            }
            return Response(response, status=status.HTTP_200_OK)

    def post(self, request):
        item_id_raw = request.query_params.get('item_id')
        request_id = request.query_params.get('request_id')


        if item_id_raw is None or request_id is None:
            response = {
                "error": "549",
                "message": "Please pass an item ID or request ID"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)

        if not item_id_raw.isnumeric():
            response = {
                "error": "587",
                "message": "Item ID must be numeric"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)

        item_id = int(item_id_raw)

        serializer = ResolvedDataUploadDataSerializer(data=request.data)
        if serializer.is_valid():
            account_number = serializer.validated_data["account_number"]
            bank_code = serializer.validated_data["bank_code"]
            amount = serializer.validated_data["amount"]

            get_data = UploadSendMoneyData.objects.filter(user=request.user, request_id=request_id).last()

            if get_data:
                raw_data = get_data.resolved_data

                if raw_data:
                    if not raw_data[item_id]:
                        response = {
                            "error": "550",
                            "message": "This item ID does not exist"
                        }
                        return Response(response, status=status.HTTP_404_NOT_FOUND)

                    data = raw_data[item_id]

                    get_account_name = fetch_account_name_second_option(
                        account_number=account_number,
                        bank_code=bank_code
                    )


                    if get_account_name["status"] == True:
                        account_name = get_account_name["data"]["account_name"]
                        data["processed"] = True
                        data["account_number"] = account_number
                        data["bank_code"] = bank_code
                        data["amount"] = amount
                        data["account_name"] = account_name

                        get_data.save()

                        response = {
                            "message": "Account Name Verified Successfully"
                        }

                        return Response(response, status=status.HTTP_202_ACCEPTED)

                    else:
                        account_name = None
                        data["processed"] = False

                        response = {
                            "error": "657",
                            "message": "Account Name could not be verified"
                        }

                        return Response(response, status=status.HTTP_400_BAD_REQUEST)
                else:
                    response = {
                        "error": "550",
                        "message": "Data is still being processed"
                    }
                    return Response(response, status=status.HTTP_404_NOT_FOUND)

            else:
                response = {
                    "error": "551",
                    "message": "Data does not exist"
                }
                return Response(response, status=status.HTTP_404_NOT_FOUND)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request):
        item_id_raw = request.query_params.get('item_id')
        request_id = request.query_params.get('request_id')


        if item_id_raw is None or request_id is None:
            response = {
                "error": "549",
                "message": "Please pass an item ID or request ID"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)

        if not item_id_raw.isnumeric():
            response = {
                "error": "587",
                "message": "Item ID must be numeric"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)

        item_id = int(item_id_raw)


        get_data = UploadSendMoneyData.objects.filter(user=request.user, request_id=request_id).last()
        if get_data:
            raw_data = get_data.resolved_data

            if raw_data:

                if not raw_data[item_id]:
                    response = {
                        "error": "550",
                        "message": "This item ID does not exist"
                    }
                    return Response(response, status=status.HTTP_404_NOT_FOUND)



                for i in range(len(raw_data)):
                    if raw_data[i]['id'] == item_id:
                        del raw_data[i]
                        break

                get_data.save()

                response = {
                    "message": "Item Successfully Deleted"
                }
                return Response(response, status=status.HTTP_200_OK)

            else:
                response = {
                    "error": "550",
                    "message": "Data is still being processed or does not exist"
                }
                return Response(response, status=status.HTTP_404_NOT_FOUND)

        else:
            response = {
                "error": "551",
                "message": "Data does not exist"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)


class UploadSendMoneyList(APIView):
    serializer_class = UploadDataSerializer
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, CanSendMoney, HasKYC,
        CheckWalletAvailable, CheckAccountAvailable
    ]

    def get(self, request):

        get_data = UploadSendMoneyData.objects.filter(user=request.user).order_by("-date_created")
        if get_data is None:
            response = {
                "error": "550",
                "message": "No Files Uploaded Yet"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)
        else:
            serializer = self.serializer_class(get_data, many=True)
            response = {
                "message": "Data Found",
                "data": serializer.data
            }

            return Response(response, status=status.HTTP_200_OK)


class OtherServicesChargeAgentAPIView(APIView):
    permission_classes = [CheckDynamicAuthentication]
    serializer_class = OtherServicesChargeAgentSerializer

    def post(self, request):
        custom_permission_user = [*self.permission_classes][0]().has_permission(request, self.as_view())

        request_user = custom_permission_user
        serializer = self.serializer_class(data=request.data)


        if serializer.is_valid():

            # DATA/PAYLOAD
            service_name = serializer.validated_data["service_name"]
            user_id = serializer.validated_data["user_id"]
            unique_reference = serializer.validated_data["unique_reference"]
            narration = serializer.validated_data["narration"]
            total_amount = serializer.validated_data["total_amount"]
            service_comm = serializer.validated_data["service_comm"]
            agent_comm = serializer.validated_data["agent_comm"]
            transaction_pin = serializer.validated_data["transaction_pin"]

            # LOGIC
            dump_data = serializer.validated_data.copy()
            del dump_data["transaction_pin"]

            payload_saved = SendMoneyDumpData.objects.create(
                user=request_user,
                transfer_type="OTHER_SERVICES_CHARGE_AGENT",
                dump=json.dumps(dump_data)
            )


            # Check Service User
            service_user_check = OtherServiceDetail.objects.filter(user=request_user, service_name=service_name).last()
            if not service_user_check:
                response = {
                    "error": "890",
                    "message": "Incorrect Service User on Agency Banking List"
                }

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)


            # Check Agent Instance
            agent_user = User.objects.filter(id=user_id).last()
            if not agent_user:
                response = {
                    "error": "589",
                    "message": "Agent User does not exist. Please check unique identifier passed"
                }

                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            bypass_pin = False
            super_token = request.headers.get('Super-Token', None)

            # if super_token == settings.OTHERS_SERVICES_SUPERTOKEN:
            if super_token is not None:
                if service_user_check.can_gen_super_token:
                    if verify_super_token(super_token=super_token, service_name=service_name):
                        bypass_pin = True
                    else:
                        response = {
                            "error": "589",
                            "message": "Invalid Super-Token"
                        }

                        return Response(response, status=status.HTTP_400_BAD_REQUEST)
                else:
                    response = {
                        "error": "589",
                        "message": "Not Profiled For Transaction"
                    }

                    return Response(response, status=status.HTTP_400_BAD_REQUEST)


            # Check Transaction Pin
            chcek_pin = User.check_sender_transaction_pin(
                user=agent_user, pincode=transaction_pin
            )
            if chcek_pin == False and bypass_pin == False:
                retries = User.count_down_transaction_pin_retries(request_user)

                response = {
                    "error": "545",
                    "message": "Incorrect Agent Pin",
                    "retry_count": retries["retry_count"],
                    "remaining_retries": retries["remaining_retries"],
                }

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)

            else:
                User.reset_transaction_pin_retries(request_user)


                if service_comm + agent_comm != total_amount:
                    response = {
                        "error": "898",
                        "message": "Service Comm Plus Agent Comm Must be Equal to Total Comm"
                    }

                    return Response(response, status=status.HTTP_401_UNAUTHORIZED)

                service_user = request_user
                from_wallet_type = "COLLECTION"
                to_wallet_type = "COLLECTION"
                fee = 0.00


                service_user_wallet_instance = WalletSystem.get_wallet(
                    user=request_user, from_wallet_type=from_wallet_type
                )

            ########################################################################################################



                agent_wallet_instance = WalletSystem.get_wallet(
                    user=agent_user, from_wallet_type=from_wallet_type
                )

                deduce_user_balance = WalletSystem.check_wallet_balance(
                    amount=total_amount,
                    wallet_instance=agent_wallet_instance
                )

                if deduce_user_balance["status"] == False:
                    response = {
                        "error": "1621",
                        "message": "Agent does not have sufficient balance to make this transaction",
                        "user_balance": agent_wallet_instance.available_balance,
                    }
                    return Response(response, status=status.HTTP_403_FORBIDDEN)


                # Check user's level of KYC and get transaction limit
                check_kyc = agent_user.check_kyc.kyc_level
                if check_kyc == 1:
                    transfer_limit = ConstantTable.calculate_transfer_limit(kyc_level=check_kyc, user=agent_user)
                    if total_amount > transfer_limit:
                        response = {
                            "error": "892",
                            "message": f"Agent cannot pay more than ₦{transfer_limit} without KYC Level 2",
                        }
                        return Response(response, status=status.HTTP_403_FORBIDDEN)

                    check_buddy_wallet = check_buddy_reciever(buddy_user_instance=agent_user)

                    if check_buddy_wallet == False:
                        response = {
                            "status": "893",
                            "message": f"Agent has no Wallets Created Yet.",
                        }
                        return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)



                # Debit Agent with amount due
                deduct_balance = WalletSystem.deduct_balance(
                    user=agent_user,
                    wallet=agent_wallet_instance,
                    amount=total_amount,
                    trans_type=service_name
                )

                payload_saved.money_removed = True
                payload_saved.save()


                # Send Money

                agent_balance_before = deduct_balance["balance_before"]

                agent_balance_after = WalletSystem.get_balance_after(
                    user=agent_user,
                    balance_before=agent_balance_before,
                    total_amount=total_amount,
                    is_credit=False
                )

                # Get IP ADDRESS
                address = request.META.get('HTTP_X_FORWARDED_FOR')
                if address:
                    ip_addr = address.split(',')[-1].strip()
                else:
                    ip_addr = request.META.get('REMOTE_ADDR')

                escrow_instance = WalletSystem.move_to_escrow_send_pay_buddy(
                    user=request_user,
                    balance_before=agent_balance_before,
                    balance_after=agent_balance_after,
                    from_wallet_id=agent_wallet_instance.wallet_id,
                    to_wallet_id=service_user_wallet_instance.wallet_id,
                    from_wallet_type=from_wallet_type,
                    to_wallet_type=to_wallet_type,
                    transfer_type=service_name,
                    amount=total_amount,
                    to_nuban=service_user.phone_number,
                    to_account_name=service_user.bvn_full_name if service_user.bvn_first_name else service_user.full_name,
                    liberty_commission=fee,
                    total_amount_charged=total_amount,
                    narration=narration,
                    is_beneficiary=False,
                    is_recurring=False,
                    debit_credit_record_id=deduct_balance.get("debit_credit_record_id"),
                    customer_reference=unique_reference,
                    ip_addr=ip_addr,
                )

                payload_saved.escrow_created = True
                payload_saved.save()

                send_money_to_buddy = WalletSystem.debit_credit_for_other_services(
                    service_user_instance=service_user,
                    agent_user_instance=agent_user,
                    service_wallet_instance=service_user_wallet_instance,
                    agent_wallet_instance=agent_wallet_instance,
                    total_amount=total_amount,
                    service_comm=service_comm,
                    agent_comm=agent_comm,
                    unique_reference=unique_reference,
                    escrow_id=escrow_instance.escrow_id
                )


                response = {
                    "message": "success",
                    "data": {
                        "message": "Transaction completed successfully",
                        "amount_sent": total_amount,
                        "escrow_id": escrow_instance.escrow_id,
                    },
                    "date_completed": datetime.now(),
                }
                return Response(response, status=status.HTTP_202_ACCEPTED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class RegisterLottoWinAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, LottoUserPermission]
    serializer_class = LottoMirrorFundsSerializer


    def get(self, request):
        user_id = request.query_params.get('user_id')
        if user_id is None:
            response = {
                "error": "549",
                "message": "No Request ID Attached"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)
        else:
            agent_user = User.objects.filter(id=user_id).last()
            from_wallet_type = "COLLECTION"

            if agent_user:
                agent_wallet_instance = WalletSystem.get_wallet(
                    user=agent_user, from_wallet_type=from_wallet_type
                )


                check_buddy_wallet = check_buddy_reciever(buddy_user_instance=agent_user)

                if check_buddy_wallet == False:
                    response = {
                        "error": "893",
                        "message": f"Agent has no Wallets Created Yet."
                    }
                    return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)
                else:
                    hold_balance = agent_wallet_instance.hold_balance
                    response = {
                        "user_id": user_id,
                        "hold_balance": hold_balance
                    }
                    return Response(response, status=status.HTTP_200_OK)

            else:
                response = {
                        "error": "900",
                        "message": "Invalid User"
                    }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        request_user = request.user


        if serializer.is_valid():

            # DATA/PAYLOAD
            user_id = serializer.validated_data["user_id"]
            amount = serializer.validated_data["amount"]
            entry = serializer.validated_data["entry"]
            unique_reference = serializer.validated_data["unique_reference"]

            # LOGIC
            dump_data = serializer.validated_data.copy()
            # del dump_data["transaction_pin"]

            payload_saved = SendMoneyDumpData.objects.create(
                user=request_user,
                transfer_type="MIRRO_LOTTO_ADD_FUNDS",
                dump=json.dumps(dump_data)
            )

            # Check User
            agent_user = User.objects.filter(id=user_id).last()
            from_wallet_type = "COLLECTION"

            if agent_user:
                agent_wallet_instance = WalletSystem.get_wallet(
                    user=agent_user, from_wallet_type=from_wallet_type
                )


                check_buddy_wallet = check_buddy_reciever(buddy_user_instance=agent_user)

                if check_buddy_wallet == False:
                    response = {
                        "error": "893",
                        "message": f"Agent has no Wallets Created Yet."
                    }
                    return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

                else:
                    liberty_reference = Transaction.create_liberty_reference(suffix="LOTTO_WIN")

                    if entry == "debit":
                        if agent_wallet_instance.hold_balance < amount:
                            response = {
                                "error": "901",
                                "message": "Amount greater than lotto balance"
                            }
                            return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)


                        agent_wallet_instance.hold_balance = agent_wallet_instance.hold_balance - amount

                        transaction_instance = Transaction.objects.create(
                            user=agent_user,
                            amount=amount,
                            wallet_id=agent_wallet_instance.wallet_id,
                            wallet_type=agent_wallet_instance.wallet_type,
                            transaction_type="LOTTO_WINNING_DEBIT",
                            narration="LOTTO WINNING",
                            status="SUCCESSFUL",
                            source_account_name="WHISPA WYSE",
                            liberty_reference=liberty_reference,
                            unique_reference=unique_reference,
                            provider_status="SUCCESS",
                        )

                    else:
                        agent_wallet_instance.hold_balance = agent_wallet_instance.hold_balance + amount

                        transaction_instance = Transaction.objects.create(
                            user=agent_user,
                            amount=amount,
                            wallet_id=agent_wallet_instance.wallet_id,
                            wallet_type=agent_wallet_instance.wallet_type,
                            transaction_type="LOTTO_WINNING_CREDIT",
                            narration="LOTTO WINNING",
                            status="SUCCESSFUL",
                            source_account_name="WHISPA WYSE",
                            liberty_reference=liberty_reference,
                            unique_reference=unique_reference,
                            provider_status="SUCCESS",
                        )

                    agent_wallet_instance.save()


                    response = {
                        "status": "success",
                        "message": "Lotto Balance Updated"
                    }

                    return Response(response, status=status.HTTP_200_OK)

            else:

                response = {
                    "error": "900",
                    "message": "Invalid User"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class FundLottoWalletAPIView(APIView):
    get_serializer_class = SendMoneyFindPayBuddySerializer
    post_serializer_class = SendMoneyPayBuddySerializer
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, CanSendMoney, HasKYC, CheckWalletAvailable,
                          FundLottoWalletRegulatorPermission]


    def get(self, request):
        lotto_agent_num = request.query_params.get("lotto_agent_num")
        if lotto_agent_num:
            formatted_buddy_phone = User.format_number_from_back_add_234(lotto_agent_num)

            get_data = fetch_lotto_agent_user(phone_number=formatted_buddy_phone)

            if get_data.get("status") == True:
                response = {
                    "message": "buddy found",
                    "data": {
                        "full_name": get_data.get("data").get("account_name")
                    }
                }
                return Response(response, status=status.HTTP_200_OK)

            else:
                response = {
                    "status": "error",
                    "message": "Lotto Agent Does Not Exist",
                }
                return Response(response, status=status.HTTP_404_NOT_FOUND)

        else:
            response = {
                "error": "658",
                "message": "Enter Value (phone number) in URL",
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)



    def post(self, request):
        serializer = self.post_serializer_class(data=request.data)
        request_user = request.user

        if serializer.is_valid():

            # DATA/PAYLOAD
            from_wallet_type = serializer.validated_data["from_wallet_type"]
            to_wallet_type = serializer.validated_data["to_wallet_type"]
            main_data = serializer.validated_data["data"]
            transaction_pin = serializer.validated_data["transaction_pin"]

            # LOGIC

            dump_data = serializer.validated_data.copy()
            del dump_data["transaction_pin"]

            payload_saved = SendMoneyDumpData.objects.create(
                user=request_user,
                transfer_type="SEND_LOTTO_WALLET",
                dump=json.dumps(dump_data)
            )

            service_user_check = OtherServiceDetail.objects.filter(service_name="LOTTO_PLAY", is_active=True).last()
            if not service_user_check:
                response = {
                    "error": "997",
                    "message": "Sorry, cannot transfer money at this time. Please try again later"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)


            # Check Transaction Pin
            chcek_pin = User.check_sender_transaction_pin(
                user=request_user, pincode=transaction_pin
            )
            if chcek_pin == False:
                retries = User.count_down_transaction_pin_retries(request_user)

                response = {
                    "error": "error",
                    "message": "Incorrect Pin",
                    "retry_count": retries["retry_count"],
                    "remaining_retries": retries["remaining_retries"],
                }

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)

            else:
                User.reset_transaction_pin_retries(request_user)


            # Calculate Daily Transfer Limits Here

            calculate_daily_limit = calculate_transaction_limit(request_user, request.data)
            if calculate_daily_limit is not None:
                return Response(calculate_daily_limit, status=status.HTTP_400_BAD_REQUEST)

            # Get wallet type

            sender_wallet_instance = WalletSystem.get_wallet(
                user=request_user, from_wallet_type=from_wallet_type
            )

            get_total_amount = 0.00

            for data in main_data:
                get_total_amount += data["amount"]

                deduce_user_balance = WalletSystem.check_wallet_balance(
                    amount=get_total_amount,
                    wallet_instance=sender_wallet_instance
                )

                if deduce_user_balance["status"] == False:
                    response = {
                        "error": "161",
                        "message": "You do not have sufficient balance to make this transaction",
                    }
                    return Response(response, status=status.HTTP_403_FORBIDDEN)

                else:
                    get_total_amount = get_total_amount

            # Check user's level of KYC and get transaction limit

            check_kyc = request_user.check_kyc.kyc_level

            transfer_limit = ConstantTable.calculate_transfer_limit(kyc_level=check_kyc, user=request_user)
            if get_total_amount > transfer_limit:
                response = {
                    "error": "error",
                    "message": f"You cannot transfer more than ₦{transfer_limit} with KYC Level {check_kyc}",
                }
                return Response(response, status=status.HTTP_403_FORBIDDEN)

            for data in main_data:
                lotto_agent_phone_number = User.format_number_from_back_add_234(
                    data["buddy_phone_number"]
                )

                lotto_agent_user_instance = User.objects.filter(phone_number=lotto_agent_phone_number).last()

                if lotto_agent_user_instance:
                    check_buddy_wallet = check_buddy_reciever(buddy_user_instance=lotto_agent_user_instance)

                    if check_buddy_wallet == False:
                        response = {
                            "status": "error",
                            "message": f"Lotto Agent with {lotto_agent_user_instance.phone_number} has no Wallets Created Yet. Let them know to do so while we also remind them",
                        }
                        return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

                elif not lotto_agent_user_instance:
                    response = {
                        "status": "error",
                        "message": f"Lotto Agent with Phone Number {lotto_agent_phone_number} Does Not Exist",
                        "invite_user": f"{settings.BASE_URL}/invite_new_user/{lotto_agent_phone_number}/",
                    }
                    return Response(response, status=status.HTTP_404_NOT_FOUND)

                else:
                    pass

            # Debit Sender with amount due
            deduct_balance = WalletSystem.deduct_balance(
                user=request_user,
                wallet=sender_wallet_instance,
                amount=get_total_amount,
                trans_type="SEND_LOTTO_WALLET"
            )

            payload_saved.money_removed = True
            payload_saved.save()

            if len(main_data) > 1:
                use_task = True
            else:
                use_task = False



            for data in main_data:

                buddy_phone_number = service_user_check.user.phone_number

                buddy_user_instance = User.objects.filter(phone_number=buddy_phone_number).last()

                receiver_wallet = WalletSystem.get_wallet(
                    user = buddy_user_instance,
                    from_wallet_type = to_wallet_type
                )

                receiver_wallet_id = receiver_wallet.wallet_id

                # receiver_wallet_id = WalletSystem.get_wallet_id_with_phone_number(
                #     to_wallet_type=to_wallet_type, phone_number=buddy_phone_number
                # )

                amount = data["amount"]
                narration = data["narration"]
                is_beneficiary = data["is_beneficiary"]
                is_recurring = data["is_recurring"]
                save_beneficiary = data["save_beneficiary"]
                remove_beneficiary = data["remove_beneficiary"]

                fee = 0.00
                total_amount_charged = amount + fee


                lotto_agent_phone_number = User.format_number_from_back_add_234(
                    data["buddy_phone_number"]
                )

                # lotto_phone_number = User.format_number_from_back_add_234(
                #     data["buddy_phone_number"]
                # )

                lotto_agent_user_instance = User.objects.filter(phone_number=lotto_agent_phone_number).last()


                # Manage Beneficiaries
                manage_beneficiaries_task.apply_async(
                    queue="sms_queue",
                    kwargs={
                        "user_id": request_user.id,
                        "save_beneficiary": save_beneficiary,
                        "remove_beneficiary": remove_beneficiary,
                        "buddy_phone_number": lotto_agent_phone_number,
                        "wallet_id": receiver_wallet_id,
                        "account_name": lotto_agent_user_instance.bvn_full_name if lotto_agent_user_instance.bvn_first_name else lotto_agent_user_instance.full_name,
                        "trans_type": "FUND_LOTTO_WALLET"
                    }
                )

                # Send Money

                # CHECK IF ACCOUNT NUMBER IS IN BENEFICIARY HERE ----- NOTE

                user_balance_before = deduct_balance["balance_before"]

                user_balance_after = WalletSystem.get_balance_after(
                    user=request_user,
                    balance_before=user_balance_before,
                    total_amount=total_amount_charged,
                    is_credit=False
                )

                # Get IP ADDRESS
                address = request.META.get('HTTP_X_FORWARDED_FOR')
                if address:
                    ip_addr = address.split(',')[-1].strip()
                else:
                    ip_addr = request.META.get('REMOTE_ADDR')


                escrow_instance = WalletSystem.move_to_escrow_send_pay_buddy(
                    user=request_user,
                    balance_before=user_balance_before,
                    balance_after=user_balance_after,
                    from_wallet_id=sender_wallet_instance.wallet_id,
                    to_wallet_id=receiver_wallet_id,
                    from_wallet_type=from_wallet_type,
                    to_wallet_type=to_wallet_type,
                    transfer_type="SEND_LOTTO_WALLET",
                    amount=amount,
                    to_nuban=buddy_phone_number,
                    to_account_name=buddy_user_instance.bvn_full_name if buddy_user_instance.bvn_first_name else buddy_user_instance.full_name,
                    liberty_commission=fee,
                    total_amount_charged=total_amount_charged,
                    narration=narration,
                    is_beneficiary=is_beneficiary,
                    is_recurring=is_recurring,
                    debit_credit_record_id=deduct_balance.get("debit_credit_record_id"),
                    ip_addr=ip_addr,
                    lotto_agent_user_id=lotto_agent_user_instance.id,
                    lotto_agent_user_phone=lotto_agent_user_instance.phone_number
                )

                payload_saved.escrow_created = True
                payload_saved.save()

                # if sender_wallet_instance.available_balance < total_amount_charged:
                #     continue

                # else:

                if not use_task:
                    send_money_to_buddy = WalletSystem.fund_wallet_pay_buddy(
                        sender_user_instance=request.user,
                        buddy_user_instance=buddy_user_instance,
                        sender_wallet=sender_wallet_instance,
                        receiver_wallet=receiver_wallet,
                        amount=amount,
                        escrow_id=escrow_instance.escrow_id
                    )

                    receiver_transaction = send_money_to_buddy.get("receiver_transaction")

                    if isinstance(receiver_transaction, Transaction):

                        send_callback_out = send_callback_out_for_send_to_lotto.apply_async(
                            queue="processbulksheet",
                            kwargs={
                                "instance_id": receiver_transaction.id
                            }
                        )


                else:
                    send_money_to_buddy = send_money_pay_buddy_task.apply_async(
                        queue="sendbulktrans",
                        kwargs={
                            "sender_wallet_id": sender_wallet_instance.wallet_id,
                            "receiver_wallet_id": receiver_wallet_id,
                            "amount": amount,
                            "escrow_id": escrow_instance.escrow_id,

                        }
                    )


            response = {
                "message": "success",
                "data": {
                    "message": "Transaction completed successfully",
                    "amount_sent": get_total_amount,
                    "escrow_id": escrow_instance.escrow_id,
                },
                "date_completed": datetime.now(),
            }
            return Response(response, status=status.HTTP_202_ACCEPTED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class FundAjoWalletAPIView(APIView):
    get_serializer_class = SendMoneyFindPayBuddySerializer
    post_serializer_class = SendMoneyPayBuddySerializer
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, CanSendMoney, HasKYC, CheckWalletAvailable,
                          FundLottoWalletRegulatorPermission]


    def get(self, request):
        agent_phone = request.query_params.get("agent_phone")
        if agent_phone:
            formatted_buddy_phone = User.format_number_from_back_add_234(agent_phone)

            get_data = fetch_lotto_agent_user(phone_number=formatted_buddy_phone)

            if get_data.get("status") == True:
                response = {
                    "message": "buddy found",
                    "data": {
                        "full_name": get_data.get("data").get("account_name")
                    }
                }
                return Response(response, status=status.HTTP_200_OK)

            else:
                response = {
                    "status": "error",
                    "message": "Lotto Agent Does Not Exist",
                }
                return Response(response, status=status.HTTP_404_NOT_FOUND)

        else:
            response = {
                "error": "658",
                "message": "Enter Value (phone number) in URL",
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)



    def post(self, request):
        serializer = self.post_serializer_class(data=request.data)
        request_user = request.user

        if serializer.is_valid():

            # DATA/PAYLOAD
            from_wallet_type = serializer.validated_data["from_wallet_type"]
            to_wallet_type = serializer.validated_data["to_wallet_type"]
            main_data = serializer.validated_data["data"]
            transaction_pin = serializer.validated_data["transaction_pin"]

            # LOGIC

            dump_data = serializer.validated_data.copy()
            del dump_data["transaction_pin"]

            payload_saved = SendMoneyDumpData.objects.create(
                user=request_user,
                transfer_type="SEND_AJO_WALLET",
                dump=json.dumps(dump_data)
            )

            service_user_email = "<EMAIL>" if settings.ENVIRONMENT == "development" else "<EMAIL>"

            service_user_check = OtherServiceDetail.objects.filter(user__email=service_user_email, service_name="SAVINGS", is_active=True).last()
            if not service_user_check:
                response = {
                    "error": "997",
                    "message": "Sorry, cannot transfer money at this time. Please try again later"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)


            # Check Transaction Pin
            chcek_pin = User.check_sender_transaction_pin(
                user=request_user, pincode=transaction_pin
            )
            if chcek_pin == False:
                retries = User.count_down_transaction_pin_retries(request_user)

                response = {
                    "error": "error",
                    "message": "Incorrect Pin",
                    "retry_count": retries["retry_count"],
                    "remaining_retries": retries["remaining_retries"],
                }

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)

            else:
                User.reset_transaction_pin_retries(request_user)


            # Calculate Daily Transfer Limits Here

            calculate_daily_limit = calculate_transaction_limit(request_user, request.data)
            if calculate_daily_limit is not None:
                return Response(calculate_daily_limit, status=status.HTTP_400_BAD_REQUEST)

            # Get wallet type

            sender_wallet_instance = WalletSystem.get_wallet(
                user=request_user, from_wallet_type=from_wallet_type
            )

            get_total_amount = 0.00

            for data in main_data:
                get_total_amount += data["amount"]

                deduce_user_balance = WalletSystem.check_wallet_balance(
                    amount=get_total_amount,
                    wallet_instance=sender_wallet_instance
                )

                if deduce_user_balance["status"] == False:
                    response = {
                        "error": "161",
                        "message": "You do not have sufficient balance to make this transaction",
                    }
                    return Response(response, status=status.HTTP_403_FORBIDDEN)

                else:
                    get_total_amount = get_total_amount

            # Check user's level of KYC and get transaction limit

            check_kyc = request_user.check_kyc.kyc_level

            transfer_limit = ConstantTable.calculate_transfer_limit(kyc_level=check_kyc, user=request_user)
            if get_total_amount > transfer_limit:
                response = {
                    "error": "error",
                    "message": f"You cannot transfer more than ₦{transfer_limit} with KYC Level {check_kyc}",
                }
                return Response(response, status=status.HTTP_403_FORBIDDEN)

            for data in main_data:
                agent_phone_number = User.format_number_from_back_add_234(
                    data["buddy_phone_number"]
                )

                agent_user_instance = User.objects.filter(phone_number=agent_phone_number).last()

                if agent_user_instance:
                    check_buddy_wallet = check_buddy_reciever(buddy_user_instance=agent_user_instance)

                    if check_buddy_wallet == False:
                        response = {
                            "status": "error",
                            "message": f"Ajo Agent with {agent_user_instance.phone_number} has no Wallets Created Yet. Let them know to do so while we also remind them",
                        }
                        return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

                elif not agent_user_instance:
                    response = {
                        "status": "error",
                        "message": f"Ajo Agent with Phone Number {agent_phone_number} Does Not Exist",
                        "invite_user": f"{settings.BASE_URL}/invite_new_user/{agent_phone_number}/",
                    }
                    return Response(response, status=status.HTTP_404_NOT_FOUND)

                else:
                    pass

            # Debit Sender with amount due
            deduct_balance = WalletSystem.deduct_balance(
                user=request_user,
                wallet=sender_wallet_instance,
                amount=get_total_amount,
                trans_type="SEND_AJO_WALLET"
            )

            payload_saved.money_removed = True
            payload_saved.save()

            if len(main_data) > 1:
                use_task = True
            else:
                use_task = False

            for data in main_data:

                buddy_phone_number = service_user_check.user.phone_number

                buddy_user_instance = User.objects.filter(phone_number=buddy_phone_number).last()

                receiver_wallet = WalletSystem.get_wallet(
                    user = buddy_user_instance,
                    from_wallet_type = to_wallet_type
                )

                receiver_wallet_id = receiver_wallet.wallet_id

                # receiver_wallet_id = WalletSystem.get_wallet_id_with_phone_number(
                #     to_wallet_type=to_wallet_type, phone_number=buddy_phone_number
                # )

                amount = data["amount"]
                narration = data["narration"]
                is_beneficiary = data["is_beneficiary"]
                is_recurring = data["is_recurring"]
                save_beneficiary = data["save_beneficiary"]
                remove_beneficiary = data["remove_beneficiary"]

                fee = 0.00
                total_amount_charged = amount + fee


                agent_phone_number = User.format_number_from_back_add_234(
                    data["buddy_phone_number"]
                )

                agent_user_instance = User.objects.filter(phone_number=agent_phone_number).last()


                # Manage Beneficiaries
                manage_beneficiaries_task.apply_async(
                    queue="sms_queue",
                    kwargs={
                        "user_id": request_user.id,
                        "save_beneficiary": save_beneficiary,
                        "remove_beneficiary": remove_beneficiary,
                        "buddy_phone_number": agent_phone_number,
                        "wallet_id": receiver_wallet_id,
                        "account_name": agent_user_instance.bvn_full_name if agent_user_instance.bvn_first_name else agent_user_instance.full_name,
                        "trans_type": "SEND_AJO_WALLET"
                    }
                )


                # Send Money

                # CHECK IF ACCOUNT NUMBER IS IN BENEFICIARY HERE ----- NOTE

                user_balance_before = deduct_balance["balance_before"]

                user_balance_after = WalletSystem.get_balance_after(
                    user=request_user,
                    balance_before=user_balance_before,
                    total_amount=total_amount_charged,
                    is_credit=False
                )

                # Get IP ADDRESS
                address = request.META.get('HTTP_X_FORWARDED_FOR')
                if address:
                    ip_addr = address.split(',')[-1].strip()
                else:
                    ip_addr = request.META.get('REMOTE_ADDR')

                escrow_instance = WalletSystem.move_to_escrow_send_pay_buddy(
                    user=request_user,
                    balance_before=user_balance_before,
                    balance_after=user_balance_after,
                    from_wallet_id=sender_wallet_instance.wallet_id,
                    to_wallet_id=receiver_wallet_id,
                    from_wallet_type=from_wallet_type,
                    to_wallet_type=to_wallet_type,
                    transfer_type="SEND_AJO_WALLET",
                    amount=amount,
                    to_nuban=buddy_phone_number,
                    to_account_name=buddy_user_instance.bvn_full_name if buddy_user_instance.bvn_first_name else buddy_user_instance.full_name,
                    liberty_commission=fee,
                    total_amount_charged=total_amount_charged,
                    narration=narration,
                    is_beneficiary=is_beneficiary,
                    is_recurring=is_recurring,
                    debit_credit_record_id=deduct_balance.get("debit_credit_record_id"),
                    ip_addr=ip_addr,
                    lotto_agent_user_id=agent_user_instance.id,
                    lotto_agent_user_phone=agent_user_instance.phone_number
                )

                payload_saved.escrow_created = True
                payload_saved.save()

                # if sender_wallet_instance.available_balance < total_amount_charged:
                #     continue

                # else:

                if not use_task:
                    send_money_to_buddy = WalletSystem.fund_wallet_pay_buddy(
                        sender_user_instance=request.user,
                        buddy_user_instance=buddy_user_instance,
                        sender_wallet=sender_wallet_instance,
                        receiver_wallet=receiver_wallet,
                        amount=amount,
                        escrow_id=escrow_instance.escrow_id
                    )

                    receiver_transaction = send_money_to_buddy.get("receiver_transaction")

                    if isinstance(receiver_transaction, Transaction):

                        send_callback_out = send_callback_out_for_send_to_ajo.apply_async(
                            queue="processbulksheet",
                            kwargs={
                                "instance_id": receiver_transaction.id
                            }
                        )


                else:
                    send_money_to_buddy = send_money_pay_buddy_task.apply_async(
                        queue="sendbulktrans",
                        kwargs={
                            "sender_wallet_id": sender_wallet_instance.wallet_id,
                            "receiver_wallet_id": receiver_wallet_id,
                            "amount": amount,
                            "escrow_id": escrow_instance.escrow_id,

                        }
                    )


            response = {
                "message": "success",
                "data": {
                    "message": "Transaction completed successfully",
                    "amount_sent": get_total_amount,
                    "escrow_id": escrow_instance.escrow_id,
                },
                "date_completed": datetime.now(),
            }
            return Response(response, status=status.HTTP_202_ACCEPTED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class SendtoAjoLoansAPIView(APIView):
    get_serializer_class = SendMoneyFindPayBuddySerializer
    post_serializer_class = SendMoneyPayBuddySerializer
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, CanSendMoney, HasKYC, CheckWalletAvailable,
                          FundLottoWalletRegulatorPermission]


    # def get(self, request):
    #     agent_phone = request.query_params.get("agent_phone")
    #     if agent_phone:
    #         formatted_buddy_phone = User.format_number_from_back_add_234(agent_phone)

    #         get_data = fetch_lotto_agent_user(phone_number=formatted_buddy_phone)

    #         if get_data.get("status") == True:
    #             response = {
    #                 "message": "buddy found",
    #                 "data": {
    #                     "full_name": get_data.get("data").get("account_name")
    #                 }
    #             }
    #             return Response(response, status=status.HTTP_200_OK)

    #         else:
    #             response = {
    #                 "status": "error",
    #                 "message": "Lotto Agent Does Not Exist",
    #             }
    #             return Response(response, status=status.HTTP_404_NOT_FOUND)

    #     else:
    #         response = {
    #             "error": "658",
    #             "message": "Enter Value (phone number) in URL",
    #         }
    #         return Response(response, status=status.HTTP_404_NOT_FOUND)

    def get(self, request):
        get_loan_amount_tiers = AjoClass.get_ajo_loan_tiers()

        response = {
            "status": "success",
            "message": "data found",
            "data": {
                "tiers": get_loan_amount_tiers,
                # "loan_types": [OtherTrxSubType.AJO_STAFF_LOAN, OtherTrxSubType.AJO_PROSPER_LOAN]
                "loan_types": [OtherTrxSubType.AJO_PROSPER_LOAN]
            }
        }
        return Response(response, status=status.HTTP_200_OK)



    def post(self, request):
        serializer = self.post_serializer_class(data=request.data)
        request_user = request.user

        if serializer.is_valid():

            # DATA/PAYLOAD
            from_wallet_type = serializer.validated_data["from_wallet_type"]
            to_wallet_type = serializer.validated_data["to_wallet_type"]
            main_data = serializer.validated_data["data"]
            transaction_pin = serializer.validated_data["transaction_pin"]

            # LOGIC

            dump_data = serializer.validated_data.copy()
            del dump_data["transaction_pin"]

            payload_saved = SendMoneyDumpData.objects.create(
                user=request_user,
                transfer_type="SEND_AJO_LOANS",
                dump=json.dumps(dump_data)
            )

            service_user_email = "<EMAIL>" if settings.ENVIRONMENT == "development" else "<EMAIL>"

            service_user_check = OtherServiceDetail.objects.filter(user__email=service_user_email, service_name="SAVINGS", is_active=True).last()
            if not service_user_check:
                response = {
                    "error": "997",
                    "message": "Sorry, cannot transfer money at this time. Please try again later"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)


            # Check Transaction Pin
            chcek_pin = User.check_sender_transaction_pin(
                user=request_user, pincode=transaction_pin
            )
            if chcek_pin == False:
                retries = User.count_down_transaction_pin_retries(request_user)

                response = {
                    "error": "error",
                    "message": "Incorrect Pin",
                    "retry_count": retries["retry_count"],
                    "remaining_retries": retries["remaining_retries"],
                }

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)

            else:
                User.reset_transaction_pin_retries(request_user)


            # Calculate Daily Transfer Limits Here

            calculate_daily_limit = calculate_transaction_limit(request_user, request.data)
            if calculate_daily_limit is not None:
                return Response(calculate_daily_limit, status=status.HTTP_400_BAD_REQUEST)

            # Get wallet type

            sender_wallet_instance = WalletSystem.get_wallet(
                user=request_user, from_wallet_type=from_wallet_type
            )

            get_total_amount = 0.00

            for data in main_data:
                get_total_amount += data["amount"]

                deduce_user_balance = WalletSystem.check_wallet_balance(
                    amount=get_total_amount,
                    wallet_instance=sender_wallet_instance
                )

                if deduce_user_balance["status"] == False:
                    response = {
                        "error": "161",
                        "message": "You do not have sufficient balance to make this transaction",
                    }
                    return Response(response, status=status.HTTP_403_FORBIDDEN)

                else:
                    get_total_amount = get_total_amount

            # Check user's level of KYC and get transaction limit

            check_kyc = request_user.check_kyc.kyc_level

            transfer_limit = ConstantTable.calculate_transfer_limit(kyc_level=check_kyc, user=request_user)
            if get_total_amount > transfer_limit:
                response = {
                    "error": "error",
                    "message": f"You cannot transfer more than ₦{transfer_limit} with KYC Level {check_kyc}",
                }
                return Response(response, status=status.HTTP_403_FORBIDDEN)

            for data in main_data:
                agent_phone_number = User.format_number_from_back_add_234(
                    data["buddy_phone_number"]
                )

                agent_user_instance = User.objects.filter(phone_number=agent_phone_number).last()

                if agent_user_instance:
                    check_buddy_wallet = check_buddy_reciever(buddy_user_instance=agent_user_instance)

                    if check_buddy_wallet == False:
                        response = {
                            "status": "error",
                            "message": f"Ajo Agent with {agent_user_instance.phone_number} has no Wallets Created Yet. Let them know to do so while we also remind them",
                        }
                        return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

                elif not agent_user_instance:
                    response = {
                        "status": "error",
                        "message": f"Ajo Agent with Phone Number {agent_phone_number} Does Not Exist",
                        "invite_user": f"{settings.BASE_URL}/invite_new_user/{agent_phone_number}/",
                    }
                    return Response(response, status=status.HTTP_404_NOT_FOUND)

                # Check Loan Amount
                get_loan_amount_tiers = AjoClass.get_ajo_loan_tiers()
                if not get_loan_amount_tiers:
                    response = {
                        "status": "error",
                        "message": "No Loan Tiers Available",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                print(list(get_loan_amount_tiers.values()))

                # all_loan_amount_values = list(get_loan_amount_tiers.values())
                all_loan_amount_values = [tier_data["deposit"] for tier_data in get_loan_amount_tiers.values()]

                if data["amount"] not in all_loan_amount_values:
                    response = {
                        "status": "error",
                        "message": "Invalid Loan Tier Amount",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)



                metadata = data.get("metadata")
                loan_types = [OtherTrxSubType.AJO_STAFF_LOAN, OtherTrxSubType.AJO_PROSPER_LOAN]
                if metadata and isinstance(metadata, dict):
                    input_loan_type = metadata.get("type")
                    if input_loan_type not in loan_types:
                        response = {
                            "status": "error",
                            "message": f"Invalid Metadata",
                        }
                        return Response(response, status=status.HTTP_400_BAD_REQUEST)


            # Debit Sender with amount due
            deduct_balance = WalletSystem.deduct_balance(
                user=request_user,
                wallet=sender_wallet_instance,
                amount=get_total_amount,
                trans_type="SEND_AJO_LOANS"
            )

            payload_saved.money_removed = True
            payload_saved.save()

            if len(main_data) > 1:
                use_task = True
            else:
                use_task = False



            for data in main_data:

                buddy_phone_number = service_user_check.user.phone_number

                buddy_user_instance = User.objects.filter(phone_number=buddy_phone_number).last()

                receiver_wallet = WalletSystem.get_wallet(
                    user = buddy_user_instance,
                    from_wallet_type = to_wallet_type
                )

                receiver_wallet_id = receiver_wallet.wallet_id

                # receiver_wallet_id = WalletSystem.get_wallet_id_with_phone_number(
                #     to_wallet_type=to_wallet_type, phone_number=buddy_phone_number
                # )

                amount = data["amount"]
                narration = data["narration"]
                is_beneficiary = data["is_beneficiary"]
                is_recurring = data["is_recurring"]
                save_beneficiary = data["save_beneficiary"]
                remove_beneficiary = data["remove_beneficiary"]

                fee = 0.00
                total_amount_charged = amount + fee


                agent_phone_number = User.format_number_from_back_add_234(
                    data["buddy_phone_number"]
                )

                agent_user_instance = User.objects.filter(phone_number=agent_phone_number).last()

                # Send Money

                # CHECK IF ACCOUNT NUMBER IS IN BENEFICIARY HERE ----- NOTE

                user_balance_before = deduct_balance["balance_before"]

                user_balance_after = WalletSystem.get_balance_after(
                    user=request_user,
                    balance_before=user_balance_before,
                    total_amount=total_amount_charged,
                    is_credit=False
                )

                # Get IP ADDRESS
                address = request.META.get('HTTP_X_FORWARDED_FOR')
                if address:
                    ip_addr = address.split(',')[-1].strip()
                else:
                    ip_addr = request.META.get('REMOTE_ADDR')


                escrow_instance = WalletSystem.move_to_escrow_send_pay_buddy(
                    user=request_user,
                    balance_before=user_balance_before,
                    balance_after=user_balance_after,
                    from_wallet_id=sender_wallet_instance.wallet_id,
                    to_wallet_id=receiver_wallet_id,
                    from_wallet_type=from_wallet_type,
                    to_wallet_type=to_wallet_type,
                    transfer_type="SEND_AJO_LOANS",
                    amount=amount,
                    to_nuban=buddy_phone_number,
                    to_account_name=buddy_user_instance.bvn_full_name if buddy_user_instance.bvn_first_name else buddy_user_instance.full_name,
                    liberty_commission=fee,
                    total_amount_charged=total_amount_charged,
                    narration=narration,
                    is_beneficiary=is_beneficiary,
                    is_recurring=is_recurring,
                    debit_credit_record_id=deduct_balance.get("debit_credit_record_id"),
                    ip_addr=ip_addr,
                    lotto_agent_user_id=agent_user_instance.id,
                    lotto_agent_user_phone=agent_user_instance.phone_number,
                    metadata=metadata
                )

                payload_saved.escrow_created = True
                payload_saved.save()

                # if sender_wallet_instance.available_balance < total_amount_charged:
                #     continue

                # else:

                if not use_task:
                    send_money_to_buddy = WalletSystem.fund_wallet_pay_buddy(
                        sender_user_instance=request.user,
                        buddy_user_instance=buddy_user_instance,
                        sender_wallet=sender_wallet_instance,
                        receiver_wallet=receiver_wallet,
                        amount=amount,
                        escrow_id=escrow_instance.escrow_id,
                        metadata=metadata
                    )

                    receiver_transaction = send_money_to_buddy.get("receiver_transaction")

                    if isinstance(receiver_transaction, Transaction):

                        send_callback_out = send_callback_out_for_send_to_ajo.apply_async(
                            queue="processbulksheet",
                            kwargs={
                                "instance_id": receiver_transaction.id
                            }
                        )


                else:
                    send_money_to_buddy = send_money_pay_buddy_task.apply_async(
                        queue="sendbulktrans",
                        kwargs={
                            "sender_wallet_id": sender_wallet_instance.wallet_id,
                            "receiver_wallet_id": receiver_wallet_id,
                            "amount": amount,
                            "escrow_id": escrow_instance.escrow_id,
                            "metadata": metadata,

                        }
                    )


            response = {
                "message": "success",
                "data": {
                    "message": "Transaction completed successfully",
                    "amount_sent": get_total_amount,
                    "escrow_id": escrow_instance.escrow_id,
                },
                "date_completed": datetime.now(),
            }
            return Response(response, status=status.HTTP_202_ACCEPTED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class DebitUserOtherAccount(APIView):
    permission_classes = [CustomIsAuthenticated, LottoUserPermission]
    serializer_class = DebitUserOtherAccountSerializer

    def post(self, request):
        request_data = request.data
        service_user = request.user




        serializer = self.serializer_class(data=request_data)

        if serializer.is_valid():
            provider = serializer.validated_data["provider"]
            account_number = serializer.validated_data["account_number"]
            liberty_reference = serializer.validated_data["liberty_reference"]


            dump_data = serializer.validated_data.copy()
            # del dump_data["transaction_pin"]

            payload_saved = SendMoneyDumpData.objects.create(
                user=service_user,
                transfer_type="OTHER_COLLECTION_ACCOUNT",
                dump=json.dumps(dump_data)
            )


            get_account_det = OtherServiceAccountSystem.objects.filter(account_number=account_number).last()

            if request.user != get_account_det.requested_by and service_user.email not in ["<EMAIL>", "<EMAIL>"]:
                response = {
                    "error": "650",
                    "message": "Account Was Not Requested By This User"
                }
                return Response(response, status=status.HTTP_404_NOT_FOUND)


            get_trans_det = Transaction.objects.filter(liberty_reference=liberty_reference, transaction_type="FUND_BANK_TRANSFER").last()
            if not get_trans_det:
                response = {
                    "error": "550",
                    "message": "No Transaction Found"
                }
                return Response(response, status=status.HTTP_404_NOT_FOUND)

            else:
                if get_trans_det.other_account_owner_settled == True:

                    response = {
                        "error": "651",
                        "message": "Transaction has already been settled",
                        "settlement_reference": get_trans_det.oth_acct_num_settlement_ref
                    }
                    return Response(response, status=status.HTTP_404_NOT_FOUND)


                else:
                    # LOGIC

                    agent_user = get_trans_det.user
                    amount = get_trans_det.amount

                    # Get wallet type

                    sender_wallet_instance = WalletSystem.get_wallet(
                        user=agent_user, from_wallet_type="COLLECTION"
                    )

                    receiver_wallet_instance = WalletSystem.get_wallet(
                        user=service_user, from_wallet_type="COLLECTION"
                    )


                    # Main Check Balance Before Transfer
                    get_user_wallet_balance = WalletSystem.check_wallet_balance(
                        amount = amount,
                        wallet_instance = sender_wallet_instance,
                        check_hold = True
                    )

                    if get_user_wallet_balance["status"] == False:
                        payload_saved.money_removed = False
                        payload_saved.save()

                        response = {
                            "error": "161",
                            "message": "User does not have sufficient balance in their hold balance for this transaction",
                        }
                        return Response(response, status=status.HTTP_403_FORBIDDEN)

                    else:

                        # Debit Sender with amount due
                        deduct_balance = WalletSystem.debit_from_hold_balance(
                            user=agent_user,
                            from_wallet_type="COLLECTION",
                            amount=amount,
                            trans_type="SEND_COLLECTION_ACCOUNT"
                        )

                        payload_saved.money_removed = True
                        payload_saved.save()


                        narration = "Collcetion Account Fund"
                        is_beneficiary = False
                        is_recurring = False
                        save_beneficiary = False
                        remove_beneficiary = False

                        fee = 0.00
                        total_amount_charged = amount + fee

                        buddy_user_instance = service_user


                        # Send Money

                        user_balance_before = deduct_balance["balance_before"]

                        user_balance_after = WalletSystem.get_balance_after(
                            user=agent_user,
                            balance_before=user_balance_before,
                            total_amount=total_amount_charged,
                            is_credit=False
                        )

                        # Get IP ADDRESS
                        address = request.META.get('HTTP_X_FORWARDED_FOR')
                        if address:
                            ip_addr = address.split(',')[-1].strip()
                        else:
                            ip_addr = request.META.get('REMOTE_ADDR')


                        escrow_instance = WalletSystem.move_to_escrow_send_pay_buddy(
                            user=agent_user,
                            balance_before=user_balance_before,
                            balance_after=user_balance_after,
                            from_wallet_id=sender_wallet_instance.wallet_id,
                            to_wallet_id=receiver_wallet_instance.wallet_id,
                            from_wallet_type="COLLECTION",
                            to_wallet_type="COLLECTION",
                            transfer_type="SEND_COLLECTION_ACCOUNT",
                            amount=amount,
                            to_nuban=account_number,
                            to_account_name=buddy_user_instance.bvn_full_name if buddy_user_instance.bvn_first_name else buddy_user_instance.full_name,
                            liberty_commission=fee,
                            total_amount_charged=total_amount_charged,
                            narration=narration,
                            is_beneficiary=is_beneficiary,
                            is_recurring=is_recurring,
                            debit_credit_record_id=deduct_balance.get("debit_credit_record_id"),
                            ip_addr=ip_addr,
                        )

                        payload_saved.escrow_created = True
                        payload_saved.save()



                        send_money_to_buddy = WalletSystem.fund_wallet_pay_buddy(
                            sender_user_instance=agent_user,
                            buddy_user_instance=service_user,
                            sender_wallet=sender_wallet_instance,
                            receiver_wallet=receiver_wallet_instance,
                            amount=amount,
                            escrow_id=escrow_instance.escrow_id
                        )

                        # escrow_instance = Escrow.objects.filter(escrow_id=escrow_id).last()

                        get_trans_det.other_account_owner_settled = True
                        get_trans_det.oth_acct_num_settlement_ref = escrow_instance.liberty_reference
                        get_trans_det.save()

                        response = {
                            "message": "success",
                            "data": {
                                "message": "Transaction completed successfully",
                                "amount_sent": amount,
                                "settlement_reference": escrow_instance.liberty_reference
                            },
                            "date_completed": datetime.now(),
                        }
                        return Response(response, status=status.HTTP_202_ACCEPTED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ReleaseHoldBalanceAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, AdminLockPermission]
    serializer_class = ReleaseHoldBalanceSerializer

    def post(self, request):
        # custom_permission_user = [*self.permission_classes][0]().has_permission(request, self.as_view())

        service_user = request.user

        request_data = request.data

        payload_saved = SendMoneyDumpData.objects.create(
            user=service_user,
            transfer_type="RELEASE_HOLD_BALANCE",
            dump=request.data
        )


        serializer = self.serializer_class(data=request_data)

        if serializer.is_valid():
            user_id = serializer.validated_data["user_id"]
            amount = serializer.validated_data["amount"]


            if user_id is None:
                response = {
                    "status": "error",
                    "message": "No user id attached"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            wallet_data_qs = WalletSystem.objects.filter(user__id=user_id, wallet_type="COLLECTION").last()
            if wallet_data_qs:
                # Main Check Balance Before Transfer
                get_user_wallet_balance = WalletSystem.check_wallet_balance(
                    amount = amount,
                    wallet_instance = wallet_data_qs,
                    check_hold = True
                )

                if get_user_wallet_balance["status"] == False:
                    payload_saved.money_removed = False
                    payload_saved.save()

                    response = {
                        "error": "161",
                        "message": "User does not have sufficient balance in their hold balance for this transaction",
                    }
                    return Response(response, status=status.HTTP_403_FORBIDDEN)

                else:
                    agent_user = wallet_data_qs.user

                    # Debit Sender with amount due
                    deduct_balance = WalletSystem.debit_from_hold_balance(
                        agent_user,
                        from_wallet_type="COLLECTION",
                        amount=amount,
                        trans_type="RELEASE_HOLD_BALANCE"
                    )

                    payload_saved.money_removed = True
                    payload_saved.save()


                    narration = "Release Hold Balance"
                    is_beneficiary = False
                    is_recurring = False
                    save_beneficiary = False
                    remove_beneficiary = False

                    fee = 0.00
                    total_amount_charged = amount + fee

                    buddy_user_instance = agent_user


                    # Send Money

                    user_balance_before = deduct_balance["balance_before"]

                    user_balance_after = WalletSystem.get_balance_after(
                        user=agent_user,
                        balance_before=user_balance_before,
                        total_amount=total_amount_charged,
                        is_credit=False
                    )

                    # Get IP ADDRESS
                    address = request.META.get('HTTP_X_FORWARDED_FOR')
                    if address:
                        ip_addr = address.split(',')[-1].strip()
                    else:
                        ip_addr = request.META.get('REMOTE_ADDR')


                    escrow_instance = WalletSystem.move_to_escrow_send_pay_buddy(
                        user=agent_user,
                        balance_before=user_balance_before,
                        balance_after=user_balance_after,
                        from_wallet_id=wallet_data_qs.wallet_id,
                        to_wallet_id=wallet_data_qs.wallet_id,
                        from_wallet_type="COLLECTION",
                        to_wallet_type="COLLECTION",
                        transfer_type="RELEASE_HOLD_BALANCE",
                        amount=amount,
                        to_nuban=agent_user.phone_number,
                        to_account_name=buddy_user_instance.bvn_full_name if buddy_user_instance.bvn_first_name else buddy_user_instance.full_name,
                        liberty_commission=fee,
                        total_amount_charged=total_amount_charged,
                        narration=narration,
                        is_beneficiary=is_beneficiary,
                        is_recurring=is_recurring,
                        debit_credit_record_id=deduct_balance.get("debit_credit_record_id"),
                        ip_addr=ip_addr,
                    )

                    payload_saved.escrow_created = True
                    payload_saved.save()



                    send_money_to_buddy = WalletSystem.release_hold_balance(
                        user_instance=agent_user,
                        wallet_instance=wallet_data_qs,
                        amount=amount,
                        escrow_id=escrow_instance.escrow_id
                    )


                    response = {
                        "message": "success",
                        "data": {
                            "message": "Transaction completed successfully",
                            "amount_sent": amount,
                            "settlement_reference": escrow_instance.liberty_reference
                        },
                        "date_completed": datetime.now(),
                    }
                    return Response(response, status=status.HTTP_202_ACCEPTED)

            else:
                response = {
                    "status": "error",
                    "message": "user with ID does not exist"
                }
                return Response(response, status=status.HTTP_404_NOT_FOUND)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ProcessCommissionsReversal(APIView):

    permission_classes = [CustomIsAuthenticated, AdminLockPermission]
    serializer_class = GetEscrowSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid():
            escrow_id = serializer.validated_data["escrow_id"]
            expected_error_code = serializer.validated_data["expected_error_code"]

            escrow_qs = Transaction.objects.filter(escrow_id=escrow_id)
            if escrow_qs:
                get_commissions = escrow_qs.filter(transaction_type="SEND_LIBERTY_COMMISSION")
                if get_commissions:
                    get_internal_trans_id = escrow_qs.filter(transaction_type="SEND_BANK_TRANSFER", transaction_leg="INTERNAL").last()
                    get_external_trans_id = escrow_qs.filter(transaction_type="SEND_BANK_TRANSFER", transaction_leg="EXTERNAL").last()
                    if get_external_trans_id:
                        main_comms_record = OtherCommissionsRecord.objects \
                            .filter(transaction_id=get_external_trans_id.transaction_id)

                        if main_comms_record.filter(entry="DEBIT").exists():
                            response = {
                                "error": "516",
                                "message": "This transaction has a reversal already with ID does not exist"
                            }
                            return Response(response, status=status.HTTP_404_NOT_FOUND)

                        check_liberty_refernce = VFDBank.vfd_transaction_verification_handler(reference=get_external_trans_id.liberty_reference)

                        if check_liberty_refernce["status"] != "00":
                            response = {
                                "error": "848",
                                "message": "Could not verify liberty reference"
                            }
                            return Response(response, status=status.HTTP_404_NOT_FOUND)

                        get_real_transfer_status = check_liberty_refernce["data"].get("transactionStatus")

                        if get_real_transfer_status != expected_error_code:
                            response = {
                                "error": "545",
                                "message": f"VFD Transaction status is not {expected_error_code}"
                            }
                            return Response(response, status=status.HTTP_404_NOT_FOUND)

                        else:
                            # Process Reversal
                            get_first_comm = get_commissions.filter(transaction_sub_type="MAIN_TRSF_COMM").first()

                            liberty_commission_bank_account = AccountSystem.get_float_account(
                                from_wallet_type="COMMISSIONS", from_provider_type="VFD"
                            )
                            beneficiary_bank_code = "999999"

                            if get_first_comm and not OutOfBookTransfer.objects.filter(escrow_id=escrow_id, trans_id=get_first_comm.transaction_id, is_active=True).exists():

                                beneficiary_account_name = get_internal_trans_id.beneficiary_account_name
                                beneficiary_nuban = get_internal_trans_id.beneficiary_nuban

                                first_comm_liberty_ref = Transaction.create_liberty_reference(suffix="LGLP-COMMRVRSL-VFD")

                                record_first_com = OutOfBookTransfer.objects.create(
                                    type_of_transfer = "COMMISSION_REVERSAL",
                                    provider = "VFD",
                                    amount = get_first_comm.amount,
                                    from_account = liberty_commission_bank_account.account_number,
                                    to_account = beneficiary_nuban,
                                    created_liberty_reference = first_comm_liberty_ref,
                                    trans_id = get_first_comm.transaction_id,
                                    escrow_id = escrow_id,
                                )

                                reverse_comm = VFDBank.initiate_payout(
                                    beneficiary_account_name=beneficiary_account_name,
                                    beneficiary_nuban=beneficiary_nuban,
                                    beneficiary_bank_code=beneficiary_bank_code,
                                    source_account=liberty_commission_bank_account.account_number,
                                    narration=f"COMMISSION REVERSAL FOR {get_external_trans_id.liberty_reference}",
                                    amount=get_first_comm.amount,
                                    transfer_type="intra",
                                    user_bvn=get_external_trans_id.user.check_kyc.bvn_rel.bvn_number,
                                    reference=first_comm_liberty_ref,
                                )

                                record_first_com.send_payload = reverse_comm
                                record_first_com.save()

                            # Verify trans
                            # verify_first_comm = VFDBank.vfd_transaction_verification_handler(reference=first_comm_liberty_ref)
###############################################################################################################################################################################
###############################################################################################################################################################################


                            get_sub_comm = get_commissions.filter(transaction_sub_type="NO_RO_EXT_COMM").first()

                            print("get_sub_type", get_sub_comm)

                            if get_sub_comm and not OutOfBookTransfer.objects.filter(escrow_id=escrow_id, trans_id=get_sub_comm.transaction_id, is_active=True).exists():
                                liberty_float_bank_account = AccountSystem.get_float_account(
                                    from_wallet_type="FLOAT", from_provider_type="VFD"
                                )

                                liberty_beneficiary_account_name = liberty_float_bank_account.account_name
                                liberty_beneficiary_nuban = liberty_float_bank_account.account_number

                                sub_comm_liberty_ref = Transaction.create_liberty_reference(suffix="LGLP-COMMRVRSL-VFD")

                                record_sub_com = OutOfBookTransfer.objects.create(
                                    type_of_transfer = "NO_RO_EXT_COMM",
                                    provider = "VFD",
                                    amount = get_sub_comm.amount,
                                    from_account = liberty_commission_bank_account.account_number,
                                    to_account = liberty_beneficiary_nuban,
                                    created_liberty_reference = sub_comm_liberty_ref,
                                    trans_id = get_sub_comm.transaction_id,
                                    escrow_id = escrow_id,
                                )

                                reverse_sub_comm = VFDBank.initiate_payout(
                                    beneficiary_account_name=liberty_beneficiary_account_name,
                                    beneficiary_nuban=liberty_beneficiary_nuban,
                                    beneficiary_bank_code=beneficiary_bank_code,
                                    source_account=liberty_commission_bank_account.account_number,
                                    narration=f"SUB COMMISSION REVERSAL FOR {get_external_trans_id.liberty_reference}",
                                    amount=get_sub_comm.amount,
                                    transfer_type="intra",
                                    user_bvn=get_external_trans_id.user.check_kyc.bvn_rel.bvn_number,
                                    reference=sub_comm_liberty_ref,
                                )

                                record_sub_com.send_payload = reverse_sub_comm
                                record_sub_com.save()


                            for comm in get_commissions:
                                comm.status = "FAILED"
                                comm.save()


                            response = {
                                "status": "success",
                                "message": " Commission Processed"
                            }
                            return Response(response, status=status.HTTP_200_OK)

                    else:
                        response = {
                            "error": "516",
                            "message": " No External Transaction Exists"
                        }
                        return Response(response, status=status.HTTP_404_NOT_FOUND)


                else:
                    response = {
                        "error": "516",
                        "message": "NO Liberty Commission Exists"
                    }
                    return Response(response, status=status.HTTP_404_NOT_FOUND)
            else:
                response = {
                    "error": "516",
                    "message": "Escrow ID does not exists"
                }
                return Response(response, status=status.HTTP_404_NOT_FOUND)


        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)





class CreateQRCode(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, HasKYC, CheckWalletAvailable,
                        ]
    serializer_class = CreateQRCodeSerializer

    def post(self, request):

        request_user = request.user

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        # PAYLOAD
        amount = serializer.validated_data["amount"]
        narration = serializer.validated_data.get("narration")
        transaction_pin = serializer.validated_data["transaction_pin"]


        dump_data = serializer.validated_data.copy()
        del dump_data["transaction_pin"]

        payload_saved = SendMoneyDumpData.objects.create(
            user=request_user,
            transfer_type="QR_GENERATE",
            dump=json.dumps(dump_data)
        )

        # Check Transaction Pin
        chcek_pin = User.check_sender_transaction_pin(
            user=request_user, pincode=transaction_pin
        )

        if chcek_pin == False:
            retries = User.count_down_transaction_pin_retries(request_user)

            response = {
                "error": "error",
                "message": "Incorrect Pin",
                "retry_count": retries["retry_count"],
                "remaining_retries": retries["remaining_retries"],
            }

            return Response(response, status=status.HTTP_401_UNAUTHORIZED)


        else:
            User.reset_transaction_pin_retries(request_user)

            if amount < 100:
                response = {
                    "error": "554",
                    "message": "Amount must be greater than N100",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)



            # Create QR Instance
            qr_code_instance = QRCode.create_qr_code_instance(
                user = request_user,
                amount = amount,
                narration = narration
            )

            if qr_code_instance.status == "FAILED":
                response = {
                    "error": "554",
                    "message": "Could not create QR Code. Please try again later",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
            elif qr_code_instance.status == "SUCCESSFUL":
                response = {
                    "status": "success",
                    "message": "QR code successfully created",
                    "code_url": qr_code_instance.qr_code_url,
                }
                return Response(response, status=status.HTTP_200_OK)

            else:
                response = {
                    "status": "pending",
                    "message": "QR code creation pending",
                }
                return Response(response, status=status.HTTP_200_OK)




