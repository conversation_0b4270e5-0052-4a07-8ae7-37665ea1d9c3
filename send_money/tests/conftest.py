from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
import pytest

User = get_user_model()

@pytest.fixture
def api_client():
    return APIClient()

@pytest.fixture
def authenticate(api_client):
    
    def do_authenticate(
        is_staff=False, 
        transaction_pin=None):
        
        return api_client.force_authenticate(user=User(
            is_staff=is_staff,
            transaction_pin=transaction_pin))
        
    return do_authenticate