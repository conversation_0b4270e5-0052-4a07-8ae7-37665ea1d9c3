from rest_framework import status
import pytest
# from .models import Collection
# from model_bakery import baker

@pytest.fixture
def create_pin(api_client):
    def do_create_pin(pincode):
        return api_client.post('/agency/create_transaction_pin/', pincode)
    return do_create_pin



@pytest.mark.django_db
class TestTransactionPIN:
    def test_if_user_is_anonymous_returns_401(self, create_pin):
        response = create_pin({'title':'a'})

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    # def test_if_user_is_not_admin_returns_403(self, authenticate, create_pin):
    #     authenticate()

    #     response = create_pin({'title':'a'})

    #     assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_if_data_is_invalid_returns_400(self, authenticate, create_pin):
        authenticate()

        response = create_pin({'title':''})

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data['transaction_pin'] is not None

    def test_if_data_is_valid_returns_201(self, authenticate, create_pin):
        authenticate()

        response = create_pin({'transaction_pin':'1234'})
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['status'] == 'pin created'
        
    def test_if_user_has_pin_on_creation_returns_406(self, authenticate, create_pin):
        authenticate(transaction_pin="1234")

        response = create_pin({'transaction_pin':'1234'})
        assert response.status_code == status.HTTP_406_NOT_ACCEPTABLE
        assert response.data['status'] == 'user transaction pin exists'

    def test_disable_after_six_tries():
        pass
    
    
# @pytest.mark.django_db
# class TestRetrieveCollection:
#     def test_if_collection_exists_return_200(self, api_client):
#         collection = baker.make(Collection)
      
#         response = api_client.get(f'/store/collections/{collection.id}/')

#         assert response.status_code == status.HTTP_200_OK
#         assert response.data == {
#             'id': collection.id,
#             'title': collection.title,
#             'products_count': 0
#         }