from django.contrib.auth import get_user_model
from rest_framework import serializers
from accounts.models import UploadSendMoneyData
from main.models import OtherServiceDetail
from main.model_choices import *
from send_money.models import *


User = get_user_model()

#####################################################################
# Check Balance Serializer
class CheckUserBalanceSerializer(serializers.Serializer):
    from_wallet_type = serializers.CharField()
    amount = serializers.FloatField()


#####################################################################
# Send Money Buddy Serializer


class SendMoneyFindPayBuddySerializer(serializers.ModelSerializer):
    full_name = serializers.SerializerMethodField()

    def get_full_name(self, user: User):
        return user.bvn_full_name if user.bvn_first_name else user.full_name

    class Meta:
        model = User
        fields = ["full_name", "phone_number"]

    # def get_account_id(self, user: User):
    #     return f"{user.collection_account.account_id}"


class MetaDataSerializer(serializers.Serializer):
    type = serializers.ChoiceField(choices=OtherTrxSubType.choices)
    user_id = serializers.IntegerField()

class BuddyDetailSerializer(serializers.Serializer):
    buddy_phone_number = serializers.CharField()
    amount = serializers.FloatField(min_value=30)
    narration = serializers.CharField(max_length=60)
    customer_reference = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    is_beneficiary = serializers.BooleanField()
    save_beneficiary = serializers.BooleanField()
    remove_beneficiary = serializers.BooleanField()
    is_recurring = serializers.BooleanField()
    metadata = MetaDataSerializer(required=False, allow_null=True)


class SendMoneyPayBuddySerializer(serializers.Serializer):
    from_wallet_type = serializers.CharField()
    to_wallet_type = serializers.CharField()
    data = serializers.ListSerializer(child=BuddyDetailSerializer())
    bulk_id = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    transaction_pin = serializers.CharField()


###################################################################################################
# SEND TO BANK SERIALIZERS
class FetchBankDetailsSerializer(serializers.Serializer):
    account_number = serializers.CharField()
    bank_code = serializers.CharField()


class AccountDetailSerializer(serializers.Serializer):
    COMMISSION_TYPE_CHOICES = [
        ("BANK", "BANK"),
        ("CASH", "CASH")
    ]
    account_number = serializers.CharField()
    account_name = serializers.CharField()
    bank_code = serializers.CharField()
    bank_name = serializers.CharField()
    amount = serializers.FloatField(min_value=10)
    narration = serializers.CharField(max_length=60)
    customer_reference = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    is_beneficiary = serializers.BooleanField()
    save_beneficiary = serializers.BooleanField()
    is_recurring = serializers.BooleanField()
    remove_beneficiary = serializers.BooleanField()
    ledger_commission = serializers.FloatField(required=False, allow_null=True)
    commission_type = serializers.ChoiceField(choices= COMMISSION_TYPE_CHOICES, required=False, allow_null=True, allow_blank=True)
    

class SendMoneyBankTransferSerializer(serializers.Serializer):
    send_by_card_rrn = serializers.CharField(required=False, allow_null=True)
    from_wallet_type = serializers.CharField(default="COLLECTION")
    from_account_number = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    transaction_mode = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    data = serializers.ListSerializer(child=AccountDetailSerializer())
    bulk_id = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    total_amount = serializers.FloatField(required=False, allow_null=True)
    total_amount_with_charge = serializers.FloatField(required=False, allow_null=True)
    transaction_pin = serializers.CharField()


class InternalSendMoneyBankTransferSerializer(serializers.Serializer):
    request_user = serializers.EmailField(required=True, allow_blank=False, allow_null=False)
    from_wallet_type = serializers.CharField()
    transaction_mode = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    data = serializers.ListSerializer(child=AccountDetailSerializer())
    total_amount = serializers.FloatField()
    total_amount_with_charge = serializers.FloatField()
    transaction_pin = serializers.CharField()


class UploadDataSerializer(serializers.ModelSerializer):

    # def get_customer_reference(self, bulk: UploadSendMoneyData):
    #     return bulk.unique_reference
    
    class Meta:
        model = UploadSendMoneyData
        fields = ["id", "title", "firstname", "lastname", "amount", "account_name", "bank_code", "bank_name", "account_number", "narration", "buddy_phone_number", "transfer_type", "escrow_id", "is_sent"]
    # class Meta:
    #     model = UploadSendMoneyData
    #     fields = ["request_id", "title", "resolved_data", "date_created"]

class GetBuddyUploadDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = UploadSendMoneyData
        fields = ["buddy_phone_number", "amount", "narration"]


    def to_representation(self, instance):
        representation = super().to_representation(instance)
        # representation["bulk_id"] = instance.unique_reference
        # if instance.transfer_type == "BANK":
        #     self.Meta.fields += ('account_number', 'account_name', 'bank_code', 'bank_name')
        #     representation["ledger_commission"] = 0
        #     representation["commission_type"] = None
        # else:
        #     self.Meta.fields += ('buddy_phone_number')

        representation["is_recurring"] = False
        representation["is_beneficiary"] = False
        representation["save_beneficiary"] = False
        representation["remove_beneficiary"] = False


        return representation

class GetBankUploadDataSerializer(serializers.ModelSerializer):
    customer_reference = serializers.SerializerMethodField()

    def get_customer_reference(self, bulk: UploadSendMoneyData):
        return bulk.unique_reference
    
    class Meta:
        model = UploadSendMoneyData
        fields = ["amount", "account_name", "bank_code", "bank_name", "account_number", "narration", "customer_reference"]



    def to_representation(self, instance):
        representation = super().to_representation(instance)
        # representation["bulk_id"] = instance.unique_reference
        representation["is_recurring"] = False
        representation["is_beneficiary"] = False
        representation["save_beneficiary"] = False
        representation["remove_beneficiary"] = False
        representation["ledger_commission"] = 0
        representation["commission_type"] = None


        return representation



class ResolvedDataUploadDataSerializer(serializers.Serializer):
    account_number = serializers.CharField(required=True)
    bank_code = serializers.CharField(required=True)
    amount = serializers.FloatField(required=True)



class OtherServicesChargeAgentSerializer(serializers.Serializer):

    serial_choices = OtherServiceDetail.objects.filter(is_active=True).distinct().values_list('service_name', 'service_name')
    OTHER_SERVICES_CHOICES = tuple(serial_choices)

    service_name = serializers.ChoiceField(required=True, choices=OTHER_SERVICES_CHOICES)
    user_id = serializers.IntegerField(required=True)
    unique_reference = serializers.CharField(required=True)
    narration = serializers.CharField(required=True, max_length=100)
    total_amount = serializers.FloatField(required=True, min_value=2.99)
    service_comm = serializers.FloatField(required=True)
    agent_comm = serializers.FloatField(required=True)
    transaction_pin = serializers.CharField()


class LottoMirrorFundsSerializer(serializers.Serializer):
    ENTRY_CHOICES = (
        ("debit", "debit"),
        ("credit", "credit"),
    )
    user_id = serializers.IntegerField(required=True)
    amount = serializers.FloatField(required=True, min_value=30)
    unique_reference = serializers.CharField(required=True)
    entry = serializers.ChoiceField(required=True, choices=ENTRY_CHOICES)


class DebitUserOtherAccountSerializer(serializers.Serializer):
    provider = serializers.CharField(required=True)
    account_number = serializers.CharField(required=True)
    liberty_reference = serializers.CharField(required=True)


class ReleaseHoldBalanceSerializer(serializers.Serializer):
    user_id = serializers.IntegerField(required=True)
    amount = serializers.FloatField(required=True, min_value=30)

class GetEscrowSerializer(serializers.Serializer):
    escrow_id = serializers.CharField(required=True)
    expected_error_code = serializers.CharField()



def validate_two_decimal_places(value):
    decimal_places = len(str(value).split('.')[1])
    if decimal_places > 2:
        raise serializers.ValidationError('Amount cannot have more than 2 decimal places')

# def minimum_amount(value):
#     decimal_places = len(str(value).split('.')[1])
#     if value  2:
#         raise serializers.ValidationError('Amount cannot have more than 2 decimal places')


class CreateQRCodeSerializer(serializers.Serializer):
    amount = serializers.FloatField(required=True, validators=[validate_two_decimal_places], min_value=30)
    narration = serializers.CharField(required=True, max_length=60)
    transaction_pin = serializers.CharField()


