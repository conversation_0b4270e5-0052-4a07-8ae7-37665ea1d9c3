from django.db import transaction
from django.http import QueryDict
from main.models import User, ConstantTable

from datetime import datetime



def get_retry_pin_response(user, message=None) -> dict:
    """ "
    This function manages retry pin and retry pin countdown
    """
    if message == "success":
        user.pin_retries = 0
        user.pin_remaining_retries = 6
        user.save(update_fields=["pin_retries", "pin_remaining_retries"])
        response = {"data": "successfully sent"}
    else:
        user.pin_retries += 1
        user.pin_remaining_retries -= 1
        user.save(update_fields=["pin_retries", "pin_remaining_retries"])

        response = {
            "status_code": "004",
            "status": "error",
            "message": "Incorrect Pin",
            "retry_count": user.pin_retries,
            "remaining_retries": user.pin_remaining_retries,
            "transaction_id": "63hgjhndddsa",
            "date_completed": datetime.now(),
        }

    return response


def send_money_pay_buddy(
    user, pincode, amount_with_charge, paybuddy_account_id, account_obj
) -> dict:
    """ "
    Checks if pin is correct, checks if amount is in sender's account balance, then sends
    """
    chcek_pin = User.check_sender_transaction_pin(user=user, pincode=pincode)
    if chcek_pin == True:
        check_balance = User.check_sender_balance(user, amount_with_charge, account_obj)
        if check_balance:
            try:
                get_paybuddy = account_obj.objects.get(account_id=paybuddy_account_id)
                with transaction.atomic():
                    # If transaction fails, everything reverts
                    retry_resp = get_retry_pin_response(user, "success")

                    response = {
                        "status_code": "003",
                        "status": "success",
                        "data": {
                            "message": "Transaction completed successfully",
                            "transaction_id": "63hgjhndddsa",
                            "sender_wallet_id": "heyy",
                            "amount_sent": amount_with_charge,
                        },
                        "date_completed": datetime.now(),
                    }
            except account_obj.DoesNotExist:
                response = {
                    "status_code": "002",
                    "status": "error",
                    "message": "Paybuddy User does not exist",
                    "transaction_id": "63hgjhndddsa",
                    "date_completed": datetime.now(),
                }
        else:
            response = {
                "status_code": "001",
                "status": "error",
                "message": "You do not have sufficient balance to make this transaction",
                "transaction_id": "63hgjhndddsa",
                "date_completed": datetime.now(),
            }
    else:
        response = get_retry_pin_response(user=user)

    return response


def send_money_bank_transfer(
    user, pincode, amount_with_charge, paybuddy_account_id, account_obj
) -> dict:

    """ "
    Checks if pin is correct and checks if amount is in sender's account balance, then sends
    """
    chcek_pin = User.check_sender_pin(user=user, pincode=pincode)
    if chcek_pin == True:
        check_balance = User.check_sender_balance(user, amount_with_charge, account_obj)
        if check_balance:
            try:
                get_paybuddy = account_obj.objects.get(account_id=paybuddy_account_id)
                with transaction.atomic():
                    # If transaction fails, everything reverts
                    retry_resp = get_retry_pin_response(user, "success")

                    response = {
                        "status_code": "003",
                        "status": "success",
                        "data": {
                            "message": "Transaction completed successfully",
                            "transaction_id": "63hgjhndddsa",
                            "sender_wallet_id": "heyy",
                            "amount_sent": amount_with_charge,
                        },
                        "date_completed": datetime.now(),
                    }
            except account_obj.DoesNotExist:
                response = {
                    "status_code": "002",
                    "status": "error",
                    "message": "Wallert Type Does Not Exist",
                    "transaction_id": "63hgjhndddsa",
                    "date_completed": datetime.now(),
                }
        else:
            response = {
                "status_code": "001",
                "status": "error",
                "message": "You do not have sufficient balance to make this transaction",
                "transaction_id": "63hgjhndddsa",
                "date_completed": datetime.now(),
            }
    else:
        response = get_retry_pin_response(user=user)

    return response
