from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.core.cache import cache

from singlelogin.models import BlacklistConstant


# Signal receiver to clear the cache when the instance or model is updated
@receiver(post_save, sender=BlacklistConstant)
def clear_constant_table_cache(sender, instance, **kwargs):
    cache_key = 'blacklist_constant_table_instance_v1'
    cache.delete(cache_key)


@receiver(post_delete, sender=BlacklistConstant)
def clear_blacklist_list_cache(sender, instance: BlacklistConstant, created, **kwargs):
    cache_key = 'blacklist_constant_table_instance_v1'
    cache.delete(cache_key)