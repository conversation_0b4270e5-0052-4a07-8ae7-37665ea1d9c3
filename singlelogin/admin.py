from django.contrib import admin
from singlelogin.models import BlackListedJWT, BlacklistConstant
from import_export import resources
from import_export.admin import ImportExportModelAdmin

# Register your models here.

class BlackListedJWTResource(resources.ModelResource):
    class Meta:
        model = BlackListedJWT

class BlacklistConstantResource(resources.ModelResource):
    class Meta:
        model = BlacklistConstant

#######################################################################
# ADMINS


class BlackListedJWTResourceAdmin(ImportExportModelAdmin):
    resource_class = BlackListedJWTResource
    search_fields = ['user_id', 'email', 'serial_no']
    list_filter = ('date_created', 'blacklisted')

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        list_of_exluded_user = BlacklistConstant.get_constant_table_instance().whitelisted_ids
        return queryset.exclude(user_id__in=list_of_exluded_user)
    

class BlacklistConstantResourceAdmin(ImportExportModelAdmin):
    resource_class = BlacklistConstantResource
    list_filter = ('date_created',)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(BlackListedJWT, BlackListedJWTResourceAdmin)
admin.site.register(BlacklistConstant, BlacklistConstantResourceAdmin)
