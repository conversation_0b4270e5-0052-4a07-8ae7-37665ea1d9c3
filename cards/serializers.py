from rest_framework import serializers
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator

from cards.models import *
from main.models import DeliveryAddressData

        
class CreatePhysicalVirtualCardSerializer(serializers.Serializer):
    CARD_TYPE_CHOICES = ( 
        ("VIRTU<PERSON>", "VIRTUAL"),
        ("PHY<PERSON><PERSON>L", "PHYSICAL")
    )

    CARD_BRAND_CHOICES = [
        (1, "Verve"),
        (2, "Visa"),
        (3, "MasterCard"),
    ]

    card_type = serializers.ChoiceField(required=True, choices=CARD_TYPE_CHOICES)
    delivery_address_id = serializers.IntegerField(required=True, allow_null=True)
    card_brand = serializers.ChoiceField(required=False, choices=CARD_BRAND_CHOICES, allow_null=True)
    from_wallet_type = serializers.CharField()
    transaction_pin = serializers.CharField()
    unique_reference = serializers.Char<PERSON>ield(required=False, allow_null=True, allow_blank=True)

class NewPhysicalCardMappingViewSerializer(serializers.Serializer):
    card_pan = serializers.CharField()
    card_expiry_month = serializers.CharField()
    card_expiry_year = serializers.CharField()
    unique_reference = serializers.CharField(allow_blank=True, allow_null=True)



class UserTransIDSerializer(serializers.Serializer):
    trans_id = serializers.IntegerField(required=True)

class CardSettingSerializer(serializers.Serializer):
    freeze = serializers.BooleanField(required=False, allow_null=True)
    cancel = serializers.BooleanField(required=False, allow_null=True)
    new_limit = serializers.FloatField()

class ViewCardListDetailSerializer(serializers.ModelSerializer):

    full_name = serializers.SerializerMethodField()
    billing_address = serializers.SerializerMethodField()

    def get_full_name(self, card: CustomerCardDetail):
        return card.customer.get_customer_full_name
    
    def get_billing_address(self, card: CustomerCardDetail):
        billing_address = None
        card_raw_data = card.customer.user.raw_card_customer_data.last()
        if card_raw_data:
            payload = json.loads(card_raw_data.data)
            billing_address = payload.get("data", {}).get("data", {}).get("billingAddress")

        return billing_address
    
    class Meta:
        model = CustomerCardDetail
        fields = [
            "id", "card_type", "full_name", "card_brand", "card_masked_pan",
            "card_expiry_month", "card_expiry_year", "card_id", "date_created", "pin_changed",
            "is_active", "is_frozen", "is_canceled", "daily_limit", "billing_address"
        ]


class ViewRequestCardSerializer(serializers.ModelSerializer):

    delivery_address = serializers.SerializerMethodField()
    
    def get_delivery_address(self, req: RequestCardData):
        try:
            return DeliveryAddressData.objects.get(id=req.delivery_address_id).full_address
        except:
            return None

    
    class Meta:
        model = RequestCardData
        fields = ["id", "card_type", "card_brand", "delivery_address", "unique_reference", "date_created"]



class ListCardsIDSerializer(serializers.Serializer):
    card_ids = serializers.ListField(required=True)

class CardIDSerializer(serializers.Serializer):
    card_id = serializers.CharField(required=True)


class FormatCardDataSerializer(serializers.Serializer):
    raw_id = serializers.PrimaryKeyRelatedField(queryset=RawSudoCardCallback.objects.all())
