# from django.conf import settings
# from cards.models import SudoCustomer, CustomerCardDetail, RawVirtualPhysicalCardData, PhysicalCard, RawCustomerData
# from main.models import DeliverySystem, ConstantTable
# from kyc_app.models import BVNDetail
# from datetime import datetime
# import requests
# import json

# SUDO_API_KEY = f"{settings.SUDO_API_KEY}"
# SUDO_FUNDING_SOURCE_ID = f"{settings.SUDO_FUNDING_SOURCE_ID}"
# SUDO_DEBIT_ACCOUNT_ID = f"{settings.SUDO_DEBIT_ACCOUNT_ID}"

# class SudoHelper:

#     url = settings.SUDO_LIVE_BASE_URL
#     headers = {
#         "Accept": "application/json",
#         "Content-Type": "application/json",
#         "Authorization": f"Bearer {SUDO_API_KEY}"
#     }
    
#     @classmethod
#     def device_ip(cls, request):
#         x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR',None)
#         if x_forwarded_for:
#             ip = x_forwarded_for.split(',')[-1].strip()
#         else:
#             ip = request.META.get('REMOTE_ADDR', None)
#         return ip

#     @classmethod
#     def format_birthdate(cls, birthdate):
#         if birthdate:
#             date = birthdate.split("-")
#             if len(date[0]) == 4:
#                 return birthdate
#             elif len(date[2]) == 4:
#                 date = datetime.strptime(birthdate, '%d-%m-%Y')
#                 year = date.year
#                 month = date.month
#                 day = date.day
#                 return f"{year}-{month}-{day}"
#             else:
#                 return ""
#         else:
#             return ""

#     @classmethod
#     def create_new_customer_card(cls, user, card_type, ip_address, delivery_address_id):
#         card_type = card_type.lower()
#         bvn_instance = BVNDetail.objects.filter(kyc__user=user, is_verified=True).last()
        
#         if bvn_instance:
#             bvn_name = user.bvn_full_name
#             bvn_phone = user.phone_number
#             bvn_first_name = bvn_instance.bvn_first_name
#             bvn_last_name = bvn_instance.bvn_last_name
#             bvn_middle_name = bvn_instance.bvn_middle_name if bvn_instance.bvn_middle_name is not None else ""
#             bvn_number = bvn_instance.bvn_number
#             bvn_birthdate = cls.format_birthdate(bvn_instance.bvn_birthdate)
#             bvn_email = user.email
#             # address_1 = bvn_instance.bvn_residential_address if bvn_instance.bvn_residential_address else "27 Alara Street" #Address This
#             # city = user.nearest_landmark if user.nearest_landmark else "Sabo-Yaba" #Address This
#             address_1 = "27 Alara Street, Sabo, Yaba, Lagos" #Address This
#             city = "Yaba" #Address This
#             state = "Lagos"
#             country = "NG"
#             postal_code = "101212"

#             if not bvn_first_name or not bvn_last_name or not bvn_number or not bvn_email or not bvn_phone: 
#                 print(f"cannot get {bvn_first_name}, {bvn_last_name}, {bvn_number}, {city}, {bvn_email}, {bvn_phone}")
#                 return None
#             else:
#                 print(f"{bvn_name}, {bvn_first_name}, {bvn_last_name}, {bvn_middle_name}, {bvn_birthdate}, {bvn_number}, {address_1}, {bvn_email}, {bvn_phone}")
#                 url = f"{cls.url}/customers"
#                 payload = {
#                     "type": "individual",
#                     "name": bvn_name,
#                     "phoneNumber": bvn_phone,
#                     "emailAddress": bvn_email,
#                     "individual": {
#                         "firstName": bvn_first_name,
#                         "lastName": bvn_last_name, 
#                         "otherNames": bvn_middle_name,
#                         "dob": bvn_birthdate,   
#                         "identity":{
#                             "type":"BVN",
#                             "number": bvn_number,
#                         },
#                         "documents":{}
#                     },
#                     "status": "active",
#                     "billingAddress":{
#                         "line1": address_1,
#                         "line2":"",
#                         "city": city,
#                         "state": state,
#                         "country": country,
#                         "postalCode": postal_code
#                     }
                    
#                 }

#                 try:
#                     response = requests.post(url, json=payload, headers=cls.headers)
#                     resp = {
#                         "status": "success",
#                         "data": response.json()
#                     }


#                 except requests.exceptions.RequestException as err:
#                     resp = {
#                         "status": "success",
#                         "data": f"{err}"
#                     }

                
#                 print(resp)

#                 if resp["status"] == "success":
#                     raw_cust_status = "success"
#                 else:
#                     raw_cust_status = "failed"

#                 customer_detail = RawCustomerData.objects.create(
#                     user=user,
#                     initial_payload=payload,
#                     status=raw_cust_status,
#                     data=json.dumps(resp)
#                 )
                    

#                 if resp["status"] == "success":
#                     customer_data = resp["data"]

#                     if "statusCode" in customer_data.keys():
#                         if customer_data["statusCode"] == 200:
#                             customer_business_id = customer_data["data"]["business"]
#                             customer_type = customer_data["data"]["type"]
#                             customer_phone_number = customer_data["data"]["phoneNumber"]
#                             customer_name = customer_data["data"]["name"]
#                             customer_email = customer_data["data"]["emailAddress"]
#                             customer_status = customer_data["data"]["status"]
#                             customer_first_name = customer_data["data"]["individual"]["firstName"]
#                             customer_last_name = customer_data["data"]["individual"]["lastName"]
#                             customer_other_name = customer_data["data"]["individual"]["otherNames"]
#                             customer_date_of_birth = customer_data["data"]["individual"]["dob"]
#                             customer_bvn_number = customer_data["data"]["individual"]["identity"]["number"]
#                             customer_identity_id = customer_data["data"]["individual"]["identity"]["_id"]
#                             customer_individual_id = customer_data["data"]["individual"]["_id"]
#                             customer_document_id = customer_data["data"]["individual"]["documents"]["_id"]
#                             customer_address_1 = customer_data["data"]["billingAddress"]["line1"]
#                             customer_city = customer_data["data"]["billingAddress"]["city"]
#                             customer_state = customer_data["data"]["billingAddress"]["state"]
#                             customer_country = customer_data["data"]["billingAddress"]["country"]
#                             customer_postal_zipcode = customer_data["data"]["billingAddress"]["postalCode"]
#                             customer_billing_address_id = customer_data["data"]["billingAddress"]["_id"]
#                             customer_created_at = customer_data["data"]["createdAt"]
#                             customer_id = customer_data["data"]["_id"]
#                             customer_v = customer_data["data"]["__v"]

#                             database_customer = SudoCustomer.objects.create(
#                                 user=user,
#                                 customer_id=customer_id,
#                                 customer_business_id=customer_business_id,
#                                 customer_type=customer_type,
#                                 customer_phone_number=f"234{customer_phone_number[-10:]}",
#                                 customer_name=customer_name,
#                                 customer_email=customer_email,
#                                 customer_status=customer_status,
#                                 customer_first_name=customer_first_name,
#                                 customer_last_name=customer_last_name,
#                                 customer_other_name=customer_other_name,
#                                 customer_date_of_birth=customer_date_of_birth,
#                                 customer_bvn_number=customer_bvn_number,
#                                 customer_identity_id=customer_identity_id,
#                                 customer_individual_id=customer_individual_id,
#                                 customer_document_id=customer_document_id,
#                                 customer_address_1=customer_address_1,
#                                 customer_city=customer_city,
#                                 customer_state=customer_state,
#                                 customer_country=customer_country,
#                                 customer_postal_zipcode=customer_postal_zipcode,
#                                 customer_billing_address_id=customer_billing_address_id,
#                                 customer_created_at=customer_created_at,
#                                 customer_v=customer_v,
#                                 customer_ip_address=ip_address
#                             )

#                             print("customer created and added to database")

#                             create_card = SudoHelper().map_customer_to_card(
#                                 customer=database_customer,
#                                 funding_source_id=SUDO_FUNDING_SOURCE_ID,
#                                 card_type=card_type,
#                                 ip_address=ip_address,
#                                 delivery_address_id=delivery_address_id,
#                             )
                            
#                             if create_card is not None:
#                                 print("card created for new customer")
#                                 return create_card
#                             else:
#                                 return None
                            

#                         else:
#                             print("could not create customer")
#                             return None
#                     else:
#                         print("an error occured")
#                         return None  
#                 else:
#                     print("second error occured")
#                     return None  

#         else:
#             return None

        

        
#     @classmethod
#     def map_customer_to_card(cls, customer, funding_source_id, card_type, ip_address, delivery_address_id, card_pan_number=None):
#         url = f"{cls.url}/cards"
#         customer_id = customer.customer_id

#         payload = {
#             "customerId": customer_id,
#             "type": card_type,
#             "currency": "NGN",
#             "issuerCountry": "NGA",
#             "status": "active",
#             "brand":"Verve",
#             "spendingControls": {   
#                 "allowedCategories":[],
#                 "blockedCategories":[],
#                 "channels": {
#                     "atm": True,
#                     "pos": True,
#                     "web": True,
#                     "mobile": True
#                 },
#                 "spendingLimits": [
#                     {
#                         "interval":"daily",
#                         "amount": ConstantTable.get_constant_table_instance().default_card_spending_limit
#                     }
#                 ]
#             },
#             "debitAccountId":"",
#             "sendPINSMS": False,
#             "debitAccountId": SUDO_DEBIT_ACCOUNT_ID,
#             "fundingSourceId": funding_source_id
#         }

#         if card_type == "virtual":
#             is_physical = False
#         else:
#             physical_card = PhysicalCard.objects.filter(card_status="NOT_ASSIGNED", customer__isnull=True).order_by("id").first()
#             if physical_card:
#                 physical_card.card_status = "PENDING"
#                 physical_card.save()

#                 payload["number"] = physical_card.pan_number
#                 is_physical = True
#             else:
#                 print("no physical card available")
#                 return None
            

#         try:
#             response = requests.post(url, json=payload, headers=cls.headers)
#             resp  = {
#                 "status": "success",
#                 "data": response.json()
#             }

#             card_detail = RawVirtualPhysicalCardData.objects.create(
#                 initial_payload = payload,
#                 customer = customer,
#                 card_type = card_type,
#                 data = json.dumps(resp),
#             )

#         except requests.exceptions.RequestException as err:
#             resp = {
#                 "status": "success",
#                 "data": f"{err}"
#             }

#             card_detail = RawVirtualPhysicalCardData.objects.create(
#                 initial_payload = payload,
#                 customer = customer,
#                 card_type = card_type,
#                 data = json.dumps(resp),
#                 status = "FAILED"
#             ) 

#         print(resp)
#         if resp["status"] == "success":
#             map_card = resp["data"]

#             if map_card["statusCode"] == 200:
#                 if is_physical == True:
#                     physical_card.customer = customer
#                     physical_card.card_status = "ASSIGNED"
#                     physical_card.date_assigned = datetime.now()
#                     physical_card.save()

#                 CustomerCardDetail.create_customer_card(
#                     customer, map_card, ip_address, delivery_address_id
#                 )

#                 DeliverySystem.create_delivery_instance(
#                     user=customer.user,
#                     delivery_address_id=delivery_address_id,
#                     delivery_object="CARD"
#                 )

#                 return map_card
#             else:
#                 if is_physical == True:
#                     physical_card.card_status = "NOT_ASSIGNED"
#                     physical_card.save()

#                 return None
#         else:
#             return None



#     @classmethod
#     def create_old_new_card(cls, request_user, card_type, ip_addr, delivery_address_id=None):
#         sudo_user_exist = SudoCustomer.objects.filter(user=request_user).last()
#         if sudo_user_exist:



#         else:
#             customer_card = SudoHelper.create_new_customer_card(
#                 user = request_user,
#                 card_type = card_type,
#                 ip_address = ip_addr,
#                 delivery_address_id = delivery_address_id
#             )

#         return customer_card



#     @classmethod
#     def send_default_card_pin(cls, card_id):
#         url = f"{cls.url}/cards/{card_id}/send-pin"

#         try:
#             response = requests.put(url, headers=cls.headers)
#             resp = response.json()
#             print(resp)

#             if response.status_code and resp.get("statusCode") == 200:
#                 resp = {
#                     "status": True,
#                     "data": f"{resp}",
#                 }
#             else:
#                 resp = {
#                     "status": False,
#                     "data": f"{resp}",
#                 }


#         except requests.exceptions.RequestException as err:
#             resp = {
#                 "status": False,
#                 "data": f"{err}"
#             }

#         return resp


#     @classmethod
#     def change_card_pin(cls, card_id, old_pin, new_pin):  
        
#         url = f"{cls.url}/cards/{card_id}/pin"
#         payload = {
#             "oldPin": old_pin,
#             "newPin": new_pin
#         }

#         try:
#             response = requests.put(url, json=payload, headers=cls.headers)
#             resp = response.json()
#             print(resp)
#             print(response.status_code)

#             if response.status_code and resp.get("statusCode") == 200:
#                 resp = {
#                     "status": True,
#                     "data": f"{resp}",
#                 }
#             else:
#                 resp = {
#                     "status": False,
#                     "data": f"{resp}",
#                 }

#         except requests.exceptions.RequestException as err:
#             resp = {
#                 "status": False,
#                 "data": f"{err}"
#             }
        
#         return resp


#     @classmethod
#     def get_single_authorization(cls, auth_id):  
        
#         url = f"{cls.url}/cards/authorizations/{auth_id}"

#         print(url)

#         try:
#             response = requests.get(url, headers=cls.headers)
#             resp = response.json()

#             if response.status_code and resp.get("statusCode") == 200:
#                 resp = {
#                     "status": True,
#                     "data": resp
#                 }
#             else:
#                 resp = {
#                     "status": False,
#                     "data": resp
#                 }

#         except requests.exceptions.RequestException as err:
#             resp = {
#                 "status": False,
#                 "data": f"{err}"
#             }
        
#         return resp



# ####################################################################################################################################################
# ####################################################################################################################################################

#     @classmethod
#     def get_all_customers(cls, page, limit):
#         url = f"{cls.url}/customers?page={page}&limit={limit}"
#         response = requests.get(url, headers=cls.headers)
#         return response.json()
    
#     @classmethod
#     def get_single_customer(cls, customer_id):
#         url = f"{cls.url}/customers/{customer_id}"
#         response = requests.get(url, headers=cls.headers)
#         return response.json()

#     @classmethod
#     def get_all_accounts(cls, page, limit, currency, type):
#         url = f"{cls.url}/accounts?page={page}&limit={limit}&currency={currency}&type={type}"
#         response = requests.get(url, headers=cls.headers)
#         return response.json()
    
#     @classmethod
#     def get_all_cards(cls, page, limit):
#         url = f"{cls.url}/cards?page={page}&limit={limit}"
#         response = requests.get(url, headers=cls.headers)
#         return response.json()
    
#     @classmethod
#     def get_single_user_cards(cls, customer_id, page, limit):
#         url = f"{cls.url}/cards/customer/{customer_id}?page={page}&limit={limit}"
#         response = requests.get(url, headers=cls.headers)
#         return response.json()
   
#     @classmethod
#     def get_single_card(cls, card_id):
#         url = f"{cls.url}/cards/{card_id}"
#         response = requests.get(url, headers=cls.headers)
#         return response.json() 
  
#     @classmethod
#     def reveal_or_hide_card_details(cls, card_id):
#         url = f"{cls.url}/cards/{card_id}?reveal=true"
#         response = requests.get(url, headers=cls.headers)
#         return response.json()
  
