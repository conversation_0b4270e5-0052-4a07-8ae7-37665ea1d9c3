from django.urls import path, include
from cards import views

urlpatterns = [
    path("card_pricing/", views.CardPricingAPIView.as_view()),
    path("create_physical_virtual_card/", views.CreatePhysicalVirtualCardView.as_view()),
    path("map_physical_card/", views.NewPhysicalCardMappingView.as_view()),
    path("send_default_pin/", views.SendDefaultCardPinView.as_view()),
    path("send_many_default_pin/", views.SendManyDefaultCardPinView.as_view()),
    path("change_card_pin/", views.ChangeCardPinView.as_view()),
    
    path("list_cards/", views.ViewCardListDetailsAPIView.as_view()),
    path("authorize_gateway/", views.SudoGatewayView.as_view()),
    path("card_settings/", views.CardSettingsAPIView.as_view()),

    path("refund_failed_card_create/", views.RefundFailedCardCreation.as_view()),
    path("get_card_token/", views.GetCardTokenAPIView.as_view()),
    path("get_card_requests/", views.ViewRequestCardListDetailsAPIView.as_view()),
    path("format_card_payload/", views.FormatPayloadView.as_view()),

    # path("get_all_cards/", views.GetAllCardsView.as_view(), name=""),
    # path("get_single_user_cards/", views.GetSingleUserCardsView.as_view(), name=""),
    # path("get_single_card/", views.GetSingleCardView.as_view(), name=""),
    # path("reveal_hide_card_details/", views.RevealOrHideCardDetailsView.as_view(), name=""),
    # path("get_all_delivery_address/", views.ViewAllDeliveryAddress.as_view(), name=""),
    # path("create_delivery_address/", views.NewDeliveryAddressPhysicalCardView.as_view(), name=""),
    # path("edit_user_address/", views.EditDeliveryAddressView.as_view(), name=""),
]