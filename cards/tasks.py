from celery import shared_task

from cards.models import SudoCardTransaction, RawSudoCardCallback, SudoHelper

import json


@shared_task
def receive_trans_created(request_payload, raw_data_id, request_type, use_auth=False):

    raw_sudo_card = RawSudoCardCallback.objects.filter(id=raw_data_id).first()
    if raw_sudo_card:
        if use_auth == True:
            raw_sudo_card_inst = raw_sudo_card
        else:
            raw_sudo_card_inst = None

        if not isinstance(request_payload, dict):
            try:
                request_payload = eval(request_payload)
            except:
                request_payload = json.loads(request_payload)



        response = SudoCardTransaction.finish_transaction(request_payload=request_payload)
        print(response)

        if use_auth == True:
            raw_sudo_card.new_returned_response=json.dumps(response)
        else:
            raw_sudo_card.returned_response=json.dumps(response)

        raw_sudo_card.request_type=request_type
        raw_sudo_card.save()
    else:
        pass

    return "DONE"



# def eager_receive_trans_created(request_payload, raw_data_id, request_type, use_auth=False):

#     raw_sudo_card = RawSudoCardCallback.objects.filter(id=raw_data_id).first()
#     if raw_sudo_card:
#         if use_auth == True:
#             raw_sudo_card_inst = raw_sudo_card
#         else:
#             raw_sudo_card_inst = None

#         if not isinstance(request_payload, dict):
#             try:
#                 request_payload = eval(request_payload)
#             except:
#                 request_payload = json.loads(request_payload)


#         response = SudoCardTransaction.finish_transaction(request_payload=request_payload, raw_sudo_card_inst=None)

#         if use_auth == True:
#             raw_sudo_card.new_returned_response=json.dumps(response)
#         else:
#             raw_sudo_card.returned_response=json.dumps(response)

#         raw_sudo_card.request_type=request_type
#         raw_sudo_card.save()
#     else:
#         pass

#     return "DONE"


@shared_task
def send_default_pin_task(card_id):

    send_out_default_pin = SudoHelper.send_default_card_pin(card_id=card_id)

    return "DONE"



@shared_task
def process_refund_trans_task(request_payload, raw_data_id, escape_limit=False):

    raw_sudo_card = RawSudoCardCallback.objects.filter(id=raw_data_id).first()
    if raw_sudo_card:
        # raw_data_request_payload = json.loads(raw_sudo_card.payload)
        if not isinstance(request_payload, dict):
            try:
                request_payload = eval(request_payload)
            except:
                request_payload = json.loads(request_payload)




        response = SudoCardTransaction.process_transaction_refund(request_payload=request_payload, escape_limit=escape_limit)
        print(response)

        raw_sudo_card.returned_response=json.dumps(response)
        raw_sudo_card.other_service_response=response.get("others_data")
        raw_sudo_card.save()
    else:
        pass

    return "DONE"


# def eager_process_refund_trans_task(request_payload, raw_data_id, escape_limit=False):
#     print(escape_limit)

#     raw_sudo_card = RawSudoCardCallback.objects.filter(id=raw_data_id).first()
#     if raw_sudo_card:
#         # raw_data_request_payload = json.loads(raw_sudo_card.payload)
#         if not isinstance(request_payload, dict):
#             try:
#                 request_payload = eval(request_payload)
#             except:
#                 request_payload = json.loads(request_payload)



#         response = SudoCardTransaction.process_transaction_refund(request_payload=request_payload, escape_limit=escape_limit)
#         print(response)

#         raw_sudo_card.returned_response=json.dumps(response)
#         raw_sudo_card.save()
#     else:
#         pass

#     return "DONE"


