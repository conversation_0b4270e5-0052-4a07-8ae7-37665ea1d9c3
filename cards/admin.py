from django.contrib import admin
from django.conf import settings
from import_export import resources
from import_export.admin import ImportExportMixin, ImportExportModelAdmin
from cards.models import SudoCardAuthorization, RawCustomerData, SudoCardTransaction, RawSudoCardCallback, SudoCardAuthorizationBegin, PhysicalCard, SudoCustomer, CustomerCardDetail, RawVirtualPhysicalCardData, RequestCardData, OtherServiceUsersGateway
# Register your models here.


class SudoCardAuthorizationResource(resources.ModelResource):
    
    class Meta:
        model = SudoCardAuthorization

class RawCustomerDataResource(resources.ModelResource):
    class Meta: 
        model = RawCustomerData
        
class SudoCardTransactionResource(resources.ModelResource):
    class Meta: 
        model = SudoCardTransaction
        
class RawSudoCardCallbackResource(resources.ModelResource):
    class Meta: 
        model = RawSudoCardCallback
        
class SudoCardAuthorizationBeginResource(resources.ModelResource):
    class Meta: 
        model = SudoCardAuthorizationBegin
        
# class RawSudoCardTransactionResource(resources.ModelResource):
#     class Meta: 
#         model = RawSudoCardTransaction
        
class PhysicalCardResource(resources.ModelResource):
    class Meta: 
        model = PhysicalCard
        
class RequestCardDataResource(resources.ModelResource):
    class Meta: 
        model = RequestCardData
        
class SudoCustomerResource(resources.ModelResource):
    class Meta: 
        model = SudoCustomer
class CustomerCardDetailResource(resources.ModelResource):
    class Meta: 
        model = CustomerCardDetail
        
    
class RawVirtualPhysicalCardDataResource(resources.ModelResource):
    class Meta: 
        model = RawVirtualPhysicalCardData

class OtherServiceUsersGatewayResource(resources.ModelResource):
    class Meta: 
        model = OtherServiceUsersGateway

##### Admin 
class SudoCardAuthorizationResourceAdmin(ImportExportModelAdmin):
    resource_class = SudoCardAuthorizationResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class RawCustomerDataResourceAdmin(ImportExportModelAdmin):
    resource_class = RawCustomerDataResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class SudoCardTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = SudoCardTransactionResource
    search_fields = ['customer_id', 'card_id', 'authorization_id', 'merchant_id', 'reference']
  

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class RawSudoCardCallbackResourceAdmin(ImportExportModelAdmin):
    def resend_callback(self, request, queryset):

        from cards.tasks import receive_trans_created, process_refund_trans_task

        for query in queryset:

            # Resend Callback

            request_payload = query.payload
            request_type = query.request_type

            
            if request_type == "transaction.created":

                resend = receive_trans_created(
                    request_payload=request_payload,
                    raw_data_id=query.id,
                    request_type=request_type,
                    use_auth=True
                )
            
            elif request_type == "transaction.refund":
                make_refund = process_refund_trans_task(
                    escape_limit=True,
                    request_payload=request_payload,
                    raw_data_id=query.id
                )





        self.message_user(request, "Successfully Resent Transactions")
    
    resend_callback.short_description = "Resend Transactions field for selected items"


    resource_class = RawSudoCardCallbackResource
    search_fields = ['payload']
    list_filter = (
        ('request_type', 'date_added')
    )
    actions = [resend_callback]



    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class SudoCardAuthorizationBeginResourceAdmin(ImportExportModelAdmin):
    resource_class = SudoCardAuthorizationBeginResource
    search_fields = ['customer_id', 'card_id', 'authorization_id', 'reference']
    list_filter = (
        ('card_brand','card_type')
    )
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
# class RawSudoCardTransactionResourceAdmin(ImportExportModelAdmin):
#     resource_class = RawSudoCardTransactionResource

#     def get_list_display(self, request):
#         return [field.name for field in self.model._meta.concrete_fields]
    
class PhysicalCardResourceAdmin(ImportExportModelAdmin):

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = super().get_readonly_fields(request, obj)

        if obj and obj.is_delivered and obj.date_delivered:
                self.readonly_fields += ['is_delivered', 'date_delivered']
        
        if settings.ENVIRONMENT != "development":
            self.readonly_fields += ['card_status', 'customer', 'pan_number', 'card_first_six', 'card_last_four']

        return readonly_fields


    autocomplete_fields = ['customer']
    resource_class = PhysicalCardResource
    readonly_fields = ['date_assigned',]


    search_fields = ["customer__user__email", "pan_number"]
    list_filter = ["card_status", "date_assigned", "date_created"]


    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class RequestCardDataResourceAdmin(ImportExportModelAdmin):

    resource_class = RequestCardDataResource
    # readonly_fields = ['assigned_card', 'date_requested', 'customer']

    search_fields = ["customer__user__email", "assigned_card__pan_number"]
    list_filter = ["assigned", "date_requested", "date_created"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class SudoCustomerResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user']
    resource_class = SudoCustomerResource
    search_fields = ['customer_id', 'customer_type', 'customer_phone_number', 'customer_name', 'customer_email', 'customer_bvn_number']
    list_filter = (
        ('customer_type','customer_status')
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    
class CustomerCardDetailResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['customer']
    resource_class = CustomerCardDetailResource
    search_fields = ['customer__user__email', 'account', 'funding_source', 'card_type', 'card_id', 'card_status']
    list_filter = (
        ('card_type', 'account', 'card_status')
    )
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

class RawVirtualPhysicalCardDataResourceAdmin(ImportExportModelAdmin):
    resource_class = RawVirtualPhysicalCardDataResource
    # list_filter = (
    #     ('address_type',)
    # )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
# class PhysicalCardDeliveryDataResourceAdmin(ImportExportModelAdmin):
#     resource_class = PhysicalCardDeliveryDataResource
#     search_fields = ['user__email', 'state', 'lga', 'nearest_landmark', 'street', 'office']
#     list_filter = (
#         ('address_type',)
#     )

#     def get_list_display(self, request):
#         return [field.name for field in self.model._meta.concrete_fields]
    

class OtherServiceUsersGatewayResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user']
    resource_class = OtherServiceUsersGatewayResource
    search_fields = ['user__email', 'gateway']
    list_filter = (
        ('date_created', "is_active")
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

admin.site.register(SudoCardAuthorization, SudoCardAuthorizationResourceAdmin)
admin.site.register(RawCustomerData, RawCustomerDataResourceAdmin)
admin.site.register(SudoCardTransaction, SudoCardTransactionResourceAdmin)
admin.site.register(RawSudoCardCallback, RawSudoCardCallbackResourceAdmin)
admin.site.register(SudoCardAuthorizationBegin, SudoCardAuthorizationBeginResourceAdmin)
# admin.site.register(RawSudoCardTransaction, RawSudoCardTransactionResourceAdmin)
admin.site.register(PhysicalCard, PhysicalCardResourceAdmin)
admin.site.register(RequestCardData, RequestCardDataResourceAdmin)
admin.site.register(SudoCustomer, SudoCustomerResourceAdmin)
admin.site.register(CustomerCardDetail, CustomerCardDetailResourceAdmin)
admin.site.register(RawVirtualPhysicalCardData, RawVirtualPhysicalCardDataResourceAdmin)
admin.site.register(OtherServiceUsersGateway, OtherServiceUsersGatewayResourceAdmin)
