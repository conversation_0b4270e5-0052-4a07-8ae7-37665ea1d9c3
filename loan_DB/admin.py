from django.contrib import admin
from .models import Bvn_check, Liberty_Loan_database
from import_export.admin import ImportExportMixin


class Liberty_Loan_databaseAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ['loandisk_id','Remita_Mandate_id','Full_Name', 'Borrower_Mobile', 'Borrower_Age','Borrower_Address','Borrower_city',
        'Borrower_Gender',
        'BVN',
        'Loan_Duration',
        'Days_Past_Maturity',
        'Last_Repayment',
        'Principal_Amount',
        'Balance_Amount',
        'Loan_Status_Name',
        'date_created',
        'date_updated',
        'Branch_name',
        'update_failed_raw_data'

    ]
    search_fields = ['Borrower_Mobile','BVN','loandisk_id','Remita_Mandate_id']
admin.site.register(Liberty_Loan_database, Liberty_Loan_databaseAdmin)
