from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .serializers import bvn_checkserializer
from .models import *
import requests
import json




class LoanDiskWebhookView(APIView):
    
    # serializer_class = loanserializer
    def post(self, request):
        response = dict(request.data)
        try:
            branch_id = response.get('branch_id')[0]
            req_type = response['type'][0]
            print(branch_id)

            response_raw = response.values()
            response_list = list(response_raw)

            len_of_updated_loans = len(response_list[2:])


            for x in response_list[2:]:
                loan_id = x[0]
                url = f"https://api-main.loandisk.com/{settings.LOANDISK_PUBLIC_KEY}/{branch_id}/loan/{loan_id}"

                headers = {
                    'Authorization': f'Basic {settings.LOANDISK_AUTH_TOKEN}',
                    'Content-Type': 'application/json'
                }
                
                raw_get_loan_request = requests.request("GET", url=url, headers=headers)
                get_loan_request = json.loads(raw_get_loan_request.text)

                if get_loan_request:
                   
                    loan_status_id = get_loan_request['response']['Results'][0].get('loan_status_id', None)
                    last_repayment_amount = get_loan_request['response']['Results'][0].get('last_repayment_amount', None)
                    loan_principal_amount = get_loan_request['response']['Results'][0].get('loan_principal_amount', None)
                    balance_amount = get_loan_request['response']['Results'][0].get('balance_amount', None)
                    loan_disk_id = get_loan_request['response']['Results'][0].get('loan_id', None)
                    loan_full_name = get_loan_request['response']['Results'][0].get('borrower_fullname',None)
                    loan_mobile = get_loan_request['response']['Results'][0].get('borrower_mobile',None)
                    loan_age = get_loan_request['response']['Results'][0].get('borrower_dob', None)
                    loan_address = get_loan_request['response']['Results'][0].get('borrower_address',None)
                    loan_city = get_loan_request['response']['Results'][0].get('borrower_city', None)
                    loan_gender = get_loan_request['response']['Results'][0].get('borrower_gender', None)
                    loan_bvn = get_loan_request['response']['Results'][0].get('custom_field_4361', None)
                    loan_duration = get_loan_request['response']['Results'][0].get('loan_duration', None)
                    loan_past_maturity = get_loan_request['response']['Results'][0].get('loan_status_id', None)
                    mandate_id = get_loan_request['response']['Results'][0].get('custom_field_5262', None)

                    loan_instance = Liberty_Loan_database.objects.filter(loandisk_id=loan_id).first()
                    if loan_instance:
                        
                        if loan_status_id:
                            loan_instance.Loan_Status_Name = loan_status_id
                            loan_instance.save()

                        if last_repayment_amount:
                            loan_instance.Last_Repayment = last_repayment_amount
                            loan_instance.save()

                        if loan_principal_amount:
                            loan_instance.Principal_Amount = loan_principal_amount
                            loan_instance.save()

                        if balance_amount:
                            loan_instance.Balance_Amount = balance_amount
                            loan_instance.save()
                        
                        loan_instance.Branch_name = branch_id
                        loan_instance.save()
                        

                    else:
                        Liberty_Loan_database.objects.create(
                            
                            loandisk_id = loan_id,
                            Remita_Mandate_id = mandate_id,
                            Full_Name = loan_full_name,
                            Borrower_Mobile = loan_mobile,
                            Borrower_Age = loan_age,
                            Borrower_Address = loan_address,
                            Borrower_city = loan_city,
                            Borrower_Gender = loan_gender,
                            BVN =loan_bvn,
                            Loan_Duration = loan_duration,
                            Days_Past_Maturity = loan_past_maturity,
                            Last_Repayment = last_repayment_amount,
                            Principal_Amount = loan_principal_amount,
                            Balance_Amount = balance_amount,
                            Loan_Status_Name = loan_status_id,
                            Branch_name = branch_id
                        )
            
        except KeyError as e:
            print(e)
            Liberty_Loan_database.objects.create(update_failed_raw_data = response)

        return Response({"Success": "Response Received"})



class internal_check(APIView):
    def get (self, request):
        serializer = bvn_checkserializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        bvn = serializer.validated_data.get('bvn')

        get_loans = Liberty_Loan_database.objects.filter(BVN=bvn)
        loan_data = []
        if get_loans:
            for bvn in get_loans:
                loan_data.append({
                    "Full_Name":bvn.Full_Name,
                    "bvn":bvn.BVN,
                    "Age":bvn.Borrower_Age,
                    "loan_status":bvn.Loan_Status_Name,
                    "Balance_Amount":bvn.Balance_Amount,
                    "Last_Repayment":bvn.Last_Repayment,
                    "Borrower_Mobile":bvn.Borrower_Mobile,
                    "Loan_Duration":bvn.Loan_Duration,
                    "Days_Past_Maturity": bvn.Days_Past_Maturity
                })
                           
            data = {
                "status": status.HTTP_200_OK,
                "data": loan_data
            }
            
            return Response(data, status=status.HTTP_200_OK)

        else: 
            response = {
                        "status": status.HTTP_404_NOT_FOUND,
                        "message": "BVN not found on this database try check blacklist for update"  
            }
            
            return Response(response)


       