from django.db import models    

class Liberty_Loan_database(models.Model):

    LOAN_STATUS_CHOICES = (

            ("8","Processiong"),
            ("3","Defaulted"),
            ("1","Open"),
            ("2","Fully Paid"),
            ("6","Past Maturity")
    )

    LOAN_BRANCH_NAME = (

            ("6937","Headquarter"),
            ("16857","Ltech_External Branch"),
            ("28262","Ltech_Internal Branch"),
            ("10680","Liberty Cred"),
            ("17807","Liberty IPPIS"),
            ("16140", "Liberty Quickcash"),
            ("9952", "Liberty Rebirth"),
            ("17511", "Liberty_Q2")
    )

    loandisk_id             = models.CharField(max_length=50, null=True)
    Remita_Mandate_id       = models.CharField(max_length=50, null=True, blank=True)
    Full_Name	            = models.CharField(max_length=60, null=True)
    Borrower_Mobile         = models.Char<PERSON>ield(max_length=50, null=True)
    Borrower_Age		    = models.CharField(max_length=50, null=True)
    Borrower_Address        = models.CharField(max_length=200, null=True)
    Borrower_city           = models.CharField(max_length=50, null=True)
    Borrower_Gender         = models.CharField(max_length=10, null=True)
    BVN	                    = models.CharField(max_length=15, null=True)	
    Loan_Duration	        = models.CharField(max_length=50, null=True)
    Days_Past_Maturity	    = models.CharField(max_length=15, null=True)
    Last_Repayment	        = models.CharField(max_length=40,  blank=True,null=True)
    Principal_Amount	    = models.FloatField(blank=True,null=True)
    Balance_Amount	        = models.FloatField(blank=True,null=True)
    Loan_Status_Name	    = models.CharField(max_length=30, choices=LOAN_STATUS_CHOICES, null=True)
    Branch_name             = models.CharField(max_length=100, choices=LOAN_BRANCH_NAME, null=True)
    date_created            = models.DateTimeField(auto_now_add=True)
    date_updated             = models.DateTimeField(auto_now=True)
    update_failed_raw_data  = models.TextField(null=True)

    def __str__(self):
        if self.BVN:
            return f"{self.BVN}"
        else:
            return "Failed Data Update"

    class Meta:
        ordering = ["-date_updated"]

class Bvn_check(models.Model):
    bvn = models.CharField(max_length=15)


    def __str__(self):
        return self.bvn