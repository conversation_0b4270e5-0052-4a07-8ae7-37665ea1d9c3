from django.urls import path
from pos_onboarding import views

app_name = "onboarding"


urlpatterns = [
#     path("filter_pos_user/", views.get_agent_existing_data_with_phone_no, name="get_user"),
#     path("update_email_on_sign_up/", views.UpdateEmailAPIView.as_view(), name="update_user_email"),
#     path("create_user_detail/", views.CreateUserDetailAPIView.as_view(), name="update_profile"),
#     path("verify_passcode/", views.VerifyEmailAPIView.as_view(), name="verify_passcode"),
#     path("resend_passcode/", views.ResendPasscodeAPIView.as_view()),
]
