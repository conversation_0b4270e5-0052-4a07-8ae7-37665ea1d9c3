import time
import requests
import cryptocode
import os
import base64

from django.conf import settings
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO


def get_access_token_of_user(user_email, user_password):

    url = f"{settings.BASE_URL}/auth_token/jwt/create/"

    payload = {
        "email": user_email,
        "password": user_password
    }

    response = requests.request("POST", url, json=payload)
    res = response.json()
    return res.get("access")


def send_pay_buddy_redirect(send_money_pay_buddy_payload, user_access_token):

    url = f"{settings.BASE_URL}/send/send_money_paybuddy/"

    payload = send_money_pay_buddy_payload

    headers = {
        "Authorization": f"Bearer {user_access_token}",
    }

    response = requests.request("POST", url, headers=headers, json=payload)
    res = response.json()

    return res


def fetch_bank_account_name(account_number, bank_code, user_access_token):

    url = f"{settings.BASE_URL}/send/fetch_account_name/"

    payload = {
        "account_number": account_number,
        "bank_code": bank_code
    }
    headers = {
        "Authorization": f"Bearer {user_access_token}"
    }

    response = requests.request("POST", url, headers=headers, json=payload)
    res = response.json()
    return res


def send_bank_transfer_redirect(send_money_bank_transfer_payload, user_access_token):

    url = f"{settings.BASE_URL}/send/send_money_bank_account/"
    payload = send_money_bank_transfer_payload
    headers = {
        "Authorization": f"Bearer {user_access_token}",
    }

    response = requests.request("POST", url, headers=headers, json=payload)
    res = response.json()

    return res


def get_card_bank(pan):
    url = f"https://api.paystack.co/decision/bin/{pan}"
    response = requests.request("GET", url=url, headers="")
    payload = response.json()

    bank = payload.get("data").get("bank")
    brand = payload.get("data").get("brand")
    data = {
        "bank": bank.title() if bank else bank,
        "brand": brand.title() if brand else brand
    }
    return data


def check_terminal_id_exist(user):
    if user.terminal_id is not None:
        return True
    else:
        return False

    # response_code = serializer.validated_data["responseCode"]
    # if response_code == "00":
    #     resp_status = True
    # else:
    #     resp_status = False

    # for k, v in serializer.validated_data.items():
    #     if serializer.validated_data[k] == "":
    #         serializer.validated_data[k] = "N/A"

    # response = {
    #     "resultCode": 0 if resp_status else 99,
    #     "reslutTxt": serializer.validated_data["responseDescription"],
    #     "authCode": serializer.validated_data["authCode"],
    #     "amount": serializer.validated_data["amount"],
    #     "mID": serializer.validated_data["merchantId"],
    #     "tID": serializer.validated_data["terminalId"],
    #     "merchantName": serializer.validated_data["merchantName"],
    #     "merchantAddress": serializer.validated_data["merchantAddress"],
    #     "mCC": "N/A",
    #     "acquirerName": "N/A",
    #     "hostRespCode": response_code,
    #     "hostRespMsg": serializer.validated_data["responseDescription"],
    #     "traceNum": "N/A",
    #     "batchNo": "N/A",
    #     "timestamp": serializer.validated_data["transactionTime"],
    #     "appLabel": "N/A",
    #     "pAN": serializer.validated_data["PAN"],
    #     "expireDate": "N/A",
    #     "holderName": "N/A",
    #     "stan": serializer.validated_data["STAN"],
    #     "verifyType": "N/A",
    #     "refNum": serializer.validated_data["rrn"],
    #     "ptspName": "N/A",
    #     "ptspContact": "N/A",
    #     "deviceSN": "N/A",
    #     "baseAppVer": "N/A",
    #     "ledger_commission": 0,
    #     "commission_type": "CASH",
    #     "send_money_by_card": {
    #         "is_send_money_trans": 0,
    #         "data": []
    #     },
    #     "signature_token": Command().handle()
    # }


def format_hapticks_data(data):
    response_code = data["hostRespCode"]
    if response_code == "00":
        resp_status = True
    else:
        resp_status = False

    for k, v in data.items():
        if data[k] == "":
            data[k] = "N/A"

    # get_user = User.objects.filter(terminal_id=data["tID"]).last()
    # if get_user:

    response = {
        "MTI":"0200",
        "amount":data["amount"],
        "terminalId":data["tID"],
        "responseCode":response_code,
        "responseDescription":data["hostRespMsg"],
        "PAN":data["pAN"],
        "STAN":data["stan"],
        "authCode":data["authCode"],
        "transactionTime":data["timestamp"],
        "reversal":False,
        "merchantId":data["mID"],
        "merchantName":data["merchantName"],
        "merchantAddress":data["merchantAddress"],
        "rrn":data["refNum"]
    }
    #     response = {
    #         "admin_password": settings.EMEKA_ADMIN_PASSWORD,
    #         "user_by_admin": get_user.email,
    #         "resultCode": 0 if resp_status else 99,
    #         "reslutTxt": data["responseDescription"],
    #         "authCode": data["authCode"],
    #         "amount": data["amount"],
    #         "mID": data["merchantId"],
    #         "tID": data["terminalId"],
    #         "merchantName": data["merchantName"],
    #         "merchantAddress": data["merchantAddress"],
    #         "mCC": "N/A",
    #         "acquirerName": "N/A",
    #         "hostRespCode": response_code,
    #         "hostRespMsg": data["responseDescription"],
    #         "traceNum": "N/A",
    #         "batchNo": "N/A",
    #         "timestamp": data["transactionTime"],
    #         "appLabel": "N/A",
    #         "pAN": data["PAN"],
    #         "expireDate": "N/A",
    #         "holderName": "N/A",
    #         "stan": data["STAN"],
    #         "verifyType": "N/A",
    #         "refNum": data["rrn"],
    #         "ptspName": "N/A",
    #         "ptspContact": "N/A",
    #         "deviceSN": "N/A",
    #         "baseAppVer": "N/A",
    #         "ledger_commission": 0,
    #         "commission_type": "CASH",
    #         "send_money_by_card": {
    #             "is_send_money_trans": 0,
    #             "data": []
    #         },
    #         "signature_token": Command().handle()
    #     }

    # else:
    #     response = {
    #         "error": "User with terminal id does not exist"
    #     }

    return response


def refire_cash_out_callback(data):
    url = f"{settings.BASE_URL}/horizon/api/resend_hapticks_data/"
    payload = data
    headers = {
        "Authorization": f"Hook {settings.EMEKA_ADMIN_PASSWORD}",
    }
    response = requests.request("POST", url, headers=headers, json=payload)
    res = response.json()
    return res


def create_cashout_rrn(tID):
    epoch = int(time.time())
    tID_last_two = tID[-2:]
    rrn = str(tID_last_two) + str(epoch)[-10:]
    return rrn


def encrypt_trans_pin(pin):
    encoded = cryptocode.encrypt(pin, settings.EMEKA_ADMIN_PASSWORD)
    return encoded


def decrypt_trans_pin(pin):
    decoded = cryptocode.decrypt(pin, settings.EMEKA_ADMIN_PASSWORD)
    return decoded


def login_terminal_cards_app():
    # Login
    login_url = f"{settings.CARDS_APP_BASE_URL}/login"
    username = settings.CARDS_APP_LOGIN_USERNAME
    password = settings.CARDS_APP_LOGIN_PASSWORD

    login_body = {
        "userName": username,
        "password": password

    }

    try:
        login_response = requests.request("POST", login_url, json=login_body)
        resp = login_response.json()
        bearer_token = resp.get("data")
    except:
        bearer_token = None

    return bearer_token


def register_terminal_cards_app(data):
    login_data = login_terminal_cards_app()
    if login_data is not None:
        url = f"{settings.CARDS_APP_BASE_URL}/pos-server/api/v1/terminals/insert/"
        payload = data
        headers = {
            "Authorization": f"Bearer {login_data}",
        }
        response = requests.request("POST", url, headers=headers, json=payload)
        res = response.json()
    else:
        res = {}
    return res


def generate_receipt_base64(data, username, address):
    base_url = os.path.dirname(os.path.abspath(__file__))
    font_url = os.path.join(base_url, "../../media/Poppins-Regular.ttf")
    logo_url = os.path.join(base_url, "../../media/liberty_pay_logo.png")
    font_path = os.path.abspath(font_url)
    logo_path = os.path.abspath(logo_url)
    logo = Image.open(logo_path).convert("RGBA")
    logo_size = (30, 30)
    logo.thumbnail(logo_size)

    # font_path = "../../media/Poppins-Regular.ttf"
    font = ImageFont.truetype(font_path, 10)
    title_font = ImageFont.truetype(font_path, 20)

    try:
        width, height = 420, 700
        background_color = "white"

        image = Image.new("RGB", (width, height), background_color)
        draw = ImageDraw.Draw(image)
        image.paste(logo, (120, 30), logo)
        # image.paste(logo)

        draw.text((width // 2.6, 30), "Liberty Pay", fill="blue", font=title_font)
        # draw.text((start_x + logo.width // 2, 35), "Liberty Pay", fill="blue", font=title_font)
        draw.text((width // 2.3, 60), username, fill="blue", font=font)
        draw.text((width // 2.5, 80), address, fill="blue", font=font)
        draw.line([(20, 98), (400, 98)], fill="black", width=2)
        draw.text((width // 3.6, 100), "CASH WITHDRAWAL", fill="blue", font=title_font)
        draw.line([(20, 130), (400, 130)], fill="black", width=2)

        start_y = 150
        line_spacing = 32
        left_x = 20
        right_x = 400

        for key, value in data.items():
            text_font = font
            if key == "Amount":
                text_font = title_font
            draw.text((left_x, start_y), f"{key}:", fill="black", font=font)
            value_width = draw.textlength(str(value), font=text_font)
            value_x = right_x - value_width
            draw.text((value_x, start_y), str(value), fill="black", font=text_font)
            start_y += line_spacing

        footer_text = "*** FAILED ***"
        draw.text((width // 2.8, height - (start_y - 200)), footer_text, fill="red", font=title_font)

        # Convert the image to grayscale before encoding
        grayscale_image = image.convert("L")

        buffered = BytesIO()
        grayscale_image.save(buffered, format="JPEG")
        base64_string = base64.b64encode(buffered.getvalue()).decode()

        return base64_string

    except Exception as e:
        return str(e)
