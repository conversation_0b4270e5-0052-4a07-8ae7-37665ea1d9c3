from datetime import datetime, timedelta, date
from accounts.models import AllBankList, TransferVerificationObject, DailyUptimeDowntimeRecord
from horizon_pay.models import CardIssuerTable, CardTransaction
from django.core.management.base import BaseCommand
import requests
import os, pytz
from django.conf import settings
import ast
import json


class Command(BaseCommand):
    help = "Generate card issuer performance for every three hours"
    url = "https://api.paystack.co/decision/bin/"
    headers = {
        "Authorization": f"Bearer {os.environ.get('PAYSTACK_API_SECRET')}"
    }
    timezone = pytz.timezone(settings.TIME_ZONE)

    def resolve_card(self, pan):
        bin = pan[:6]
        url = self.url + bin
        response = requests.get(url, self.headers)
        data = response.json()
        if data['status'] == True:
            bank = data.get("data").get("bank")
            brand = data.get("data").get("brand")

            new_data = {
                "bank": bank.title() if bank else bank,
                "brand": brand.title() if brand else brand,
                "card_bin": bin
            }
            return new_data
            
        raise Exception("Card could not be resolved!")

    def calculate_performance(self):
        for data in CardIssuerTable.objects.all():
            data.previous_performance = data.performance
            data.overall_previous_performance = data.overall_performance
            data.front_view_previous_performance = data.front_view_performance

            if data.daily_count_total < 1:
                data.performance = 100
            elif data.daily_count_pass < 1:
                data.performance = 0
            else:
                data.performance = (data.daily_count_pass/data.daily_count_total) * 100
                

            if data.overall_count_total < 1:
                data.performance = 100
            elif data.overall_count_pass < 1:
                data.overall_performance = 0
            else:
                data.overall_performance = (data.overall_count_pass/data.overall_count_total) * 100

            # if data.front_view_count_total > 0:
            # # if data.front_view_count_total > 1:
            #     data.front_view_performance = ((data.daily_count_pass/5)/(data.daily_count_total/5)) * 100

            data.save()

        return True

    def parse_transactions(self):
        today = datetime.now().date()

        # all_card_transactions = CardTransaction.objects \
        #     .filter(date_created__date=today).order_by("id")[:10]

        all_card_transactions = CardTransaction.objects \
            .filter(date_created__date=today, bank_performance_checked=False).order_by("id")[:50]

        for i in all_card_transactions:
            i.bank_performance_checked = True

        CardTransaction.objects.bulk_update(all_card_transactions, fields=["bank_performance_checked"])

        if len(all_card_transactions) > 0:
            for transaction in all_card_transactions:
                pan = transaction.pan_number
                result_code = transaction.host_resp_code

                # fetch bank
                data = self.resolve_card(pan)
                bank_name = data['bank']
                brand = data['brand']

                bank_data, created = CardIssuerTable.objects.get_or_create(
                    performance_type = "CASH_OUT",
                    bank=bank_name,
                    brand=brand,
                    card_bin = data["card_bin"]
                )

                if result_code == '00':
                            
                    bank_data.daily_count_pass += 1
                    bank_data.overall_count_pass += 1

                    if bank_data.daily_count_pass % 5 == 0 and bank_data.daily_count_pass > 0:
                        bank_data.front_view_count_pass += 1

                    if transaction.amount > 8000:
                        get_interswitch_bank_name = CardIssuerTable.objects.filter(id=1961).last()
                        if get_interswitch_bank_name:
                            get_interswitch_bank_name.performance = 100
                            get_interswitch_bank_name.save()
                        

                elif result_code in ['91', '06']:
                    bank_data.daily_count_fail += 1
                    bank_data.overall_count_fail += 1

                    if bank_data.daily_count_fail % 5 == 0 and bank_data.daily_count_fail > 0:
                        bank_data.front_view_count_fail += 1
                    
                
                elif result_code == "02":
                    if transaction.amount > 8000:
                        get_interswitch_bank_name = CardIssuerTable.objects.filter(id=1961).last()
                        if get_interswitch_bank_name:
                            get_interswitch_bank_name.performance = 20
                            get_interswitch_bank_name.save()

                bank_data.save()


            self.calculate_performance()

        return "Finished"


    def calulate_and_parse_bank_transactions(self):
        today = datetime.now().date()


        # all_bank_transactions = TransferVerificationObject.objects \
        #     .filter(bank_performance_checked=False).order_by("-id")[:2]

        all_bank_transactions = TransferVerificationObject.objects \
            .filter(date_added__date=today, bank_performance_checked=False, transaction_leg="EXTERNAL", is_finished_verification=True, verification_payload__isnull=False).order_by("id")[:10]

        for i in all_bank_transactions:
            i.bank_performance_checked = True

        TransferVerificationObject.objects.bulk_update(all_bank_transactions, fields=["bank_performance_checked"])

        if len(all_bank_transactions) > 0:
            for transaction in all_bank_transactions:

                try:
                    dict_payload = eval(transaction.verification_payload)
                except TypeError:
                    dict_payload = json.loads(transaction.verification_payload)
                except:
                    dict_payload = None



                if dict_payload is not None:



                    bank_code = dict_payload.get("data", {}).get("toBank")
                    result_code = transaction.trans_status_code if transaction.trans_status_code else dict_payload.get("data", {}).get("transactionStatus")

                    if bank_code is not None and result_code is not None:

                        # fetch bank
                        
                        bank_name = AllBankList.objects.filter(bank_code=bank_code).last()

                        bank_data, created = CardIssuerTable.objects.get_or_create(
                            performance_type = "TRANSFER",
                            bank=bank_name.name
                        )

                        if result_code == '00':
                            bank_data.daily_count_pass += 1
                            bank_data.overall_count_pass += 1

                            if bank_data.daily_count_pass % 5 == 0 and bank_data.daily_count_pass > 0:
                                bank_data.front_view_count_pass += 1



                        elif result_code == '91':
                            bank_data.daily_count_fail += 1
                            bank_data.overall_count_fail += 1

                            if bank_data.daily_count_fail % 5 == 0 and bank_data.daily_count_fail > 0:
                                bank_data.front_view_count_fail += 1

                        bank_data.save()


            self.calculate_performance()

        return "Finished"
    

    def handle(self, *args, **kwargs):
        base = self.parse_transactions()
        bank_transfer_performance = self.calulate_and_parse_bank_transactions()
        # print(bank_transfer_performance)










    # def parse_transactions(self):
    #     d_now = datetime.now()
    #     d_prev = d_now - timedelta(hours=3)
    #     now = self.timezone.localize(d_now)
    #     prev = self.timezone.localize(d_prev)

    #     all_card_transactions = CardTransaction.objects.filter(date_created__lte=now).filter(date_created__gte=prev)
    #     base = {}

    #     if len(all_card_transactions) > 0:
    #         for transaction in all_card_transactions:
    #             try:
    #                 pan = transaction.pan_number
    #                 result_code = transaction.host_resp_code

    #                 # fetch bank
    #                 data = self.resolve_card(pan)
    #                 bank_name = data['bank']
    #                 brand = data['brand']
                    
    #                 if result_code == '00':
    #                     if bank_name in base.keys():
    #                         if brand in base[bank_name].keys():
    #                             if 'success' in base[bank_name][brand].keys():
    #                                 base[bank_name][brand]['success'] += 1
    #                             else:
    #                                 base[bank_name][brand]['success'] = 1
    #                         else:
    #                             base[bank_name][brand] = {'success': 1}
    #                     else:
    #                         base[bank_name] = {brand: {'success': 1}}
    #                 elif result_code == '91':
    #                     if bank_name in base.keys():
    #                         if brand in base[bank_name].keys():
    #                             if 'failure' in base[bank_name][brand].keys():
    #                                 base[bank_name][brand]['failure'] += 1
    #                             else:
    #                                 base[bank_name][brand]['failure'] = 1
    #                         else:
    #                             base[bank_name][brand] = {'failure': 1}
    #                     else:
    #                         base[bank_name] = {brand: {'failure': 1}}
    #             except Exception as e:
    #                 pass
    #     return base
    
    # def calculate_performance(self, base):
    #     for k, v in base.items():
    #         for brand, value in base[k].items():
    #             success_rate  = None
    #             if "success" in value.keys():
    #                 if 'failure' in value.keys():
    #                     total = value['success'] + value['failure']
    #                     success_rate = ((value['success'])/total) * 100
    #                 else:
    #                     success_rate = 100
    #             if 'failure' in value.keys():
    #                 if 'success' in value.keys():
    #                     pass
    #                 else:
    #                     success_rate = 0
    #             card_issuer = None
    #             card_issuers = CardIssuerTable.objects.filter(
    #                 bank=k
    #             )
    #             if len(card_issuers) > 0:
    #                 for ci in card_issuers:
    #                     if ci.brand == brand:
    #                         card_issuer = ci
    #                         break
    #                 if not card_issuer:
    #                     card_issuer = CardIssuerTable.objects.create(
    #                         bank=k,
    #                         brand=brand
    #                     )
    #             else:
    #                 card_issuer = CardIssuerTable.objects.create(
    #                     bank=k,
    #                     brand=brand
    #                 )
    #             card_issuer.previous_performance = card_issuer.performance
    #             card_issuer.performance = success_rate
    #             card_issuer.save()
    #     return 1

    # def handle(self, *args, **kwargs):
    #     base = self.parse_transactions()
    #     if len(base) > 0:
    #         self.calculate_performance(base)
        