from horizon_pay.models import CardIssuerTable
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = "Reset card issuer performance to 100 at the beginning of the day"

    def handle(self, *args, **kwargs):
        CardIssuerTable.objects.all().update(
            performance = 100,
            overall_performance = 100,
            front_view_performance = 100,
            previous_performance = 100,
            front_view_previous_performance = 100,
            daily_count_pass = 0,
            daily_count_fail = 0,
            daily_count_total = 0,
            front_view_count_pass = 0,
            front_view_count_fail = 0,
            front_view_count_total = 0
        )