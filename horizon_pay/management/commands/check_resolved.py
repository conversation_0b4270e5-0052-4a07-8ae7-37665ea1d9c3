from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from django.db.models import Q
from accounts.models import Transaction
from horizon_pay.models import HorizonPayTable
from main.helper.send_emails import send_email_with_parameters
from datetime import datetime, timedelta
import pandas as pd



class Command(BaseCommand):
    help = ""

    def add_arguments(self, parser):
        parser.add_argument('--day', dest='arg1', type=str, help="By Default, this runs for last 2 days. But passing day as DD-MM-YYYY will ensure it runs for the set date.")

    def handle(self, *args, **options):
        custom_day = options.get('arg1')

        # Your custom logic goes here
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=2)


        if custom_day:
            try:
                end_date = datetime.strptime(custom_day, "%d-%m-%Y").date()
                start_date = end_date

            except ValueError:
                raise CommandError('Invalid Date Format. Use DD-MM-YYYY')
            


        filter_data = Q(date_created__date__lte=end_date, date_created__date__gte=start_date)
        cash_out_data = HorizonPayTable.objects.filter(is_resolved=False).filter(filter_data).order_by("id")[:500]
        


        print(start_date)
        print(end_date)

        print(cash_out_data)
        for data in cash_out_data:
            

            get_trans = Transaction.objects.select_related("user").filter(transaction_type__in=["CARD_TRANSACTION_FUND", "CARD_TRANSACTION_FUND_TRANSFER"], unique_reference=data.rrn).first()

            print(get_trans)
            print(data.rrn)

            if get_trans:
                print(get_trans.status)
                if get_trans.status in ["SUCCESSFUL", "FAILED"]:
                    print("herrrrr")
                    data.is_resolved = True
                    data.save()



        

        pass

