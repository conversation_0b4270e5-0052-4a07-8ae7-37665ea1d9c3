from django.core.management import BaseCommand
from faker import Faker
from accounts.models import TransferVerificationObject
from horizon_pay.models import CardIssuerTable, HorizonPayTable, StartCashOutTran
from horizon_pay.helpers.helper_function import login_terminal_cards_app
from datetime import datetime
import json

class Command(BaseCommand):

    def handle(self, *args, **kwargs):
        # today = datetime.now().date()

        for data in StartCashOutTran.objects.filter(rrn__in=["3689803","3689802","3689801","3689800","3689799","3689798","3689797","3689795","3689793","3689790","3689789","3689788","3689786","3689785","3689784","3689782","3689781","3689780","3689779","3689778","3689776","3689773","3689772","3689771","3689770","3689769","3689767","3689766","3689765","3689764","3689762","3689760","3689758","3689755","3689753","3689752","3689749","3689748","3689747","3689745","3689744","3689743","3689740","3689739","3689738","3689737","3689733","3689729","3689721","3689720","3689718","3689717","3689716","3689715","3689714","3689713","3689711","3689710","3689709","3689708","3689707","3689706","3689704","3689703","3689702","3689701","3689700","3689699","3689698","3689697","3689696","3689695","3689694","3689692","3689691","3689689","3689688","3689687","3689686","3689685","3689684","3689683"]):
            data.trans_complete = False
            data.save()


            get_horizon = HorizonPayTable.objects.filter(rrn=data.rrn).first()

            get_horizon.is_resolved = False
            get_horizon.save()

        pass


        # login_data = login_terminal_cards_app()


        # for data in HorizonPayTable.objects.filter(date_created__date=today, is_resolved=True):
        #     formatted_payload = json.loads(data.payload)


        #     data.rrn = formatted_payload.get("rrn")
        #     data.terminal_id = formatted_payload.get("terminalId")
        #     data.amount = formatted_payload.get("amount")
        #     data.response_code = formatted_payload.get("responseCode")
        #     data.save()
            
    
   
        # all_card_transactions = CardTransaction.objects \
        #     .filter(date_created__date=today, bank_performance_checked=True)
        
        
        # TransferVerificationObject.objects.bulk_update(all_bank_transactions, fields=["bank_performance_checked"])
        # CardTransaction.objects.bulk_update(all_card_transactions, fields=["bank_performance_checked"])

        # queryset = HorizonPayTable.objects.filter(rrn__in=["************","************","************","************","************","************","************","************","************","************","************","************","************","************","************","************","************","************","************","************","************","************","************","************","************","************","************","************","************","************","************","************","************","************"])


        # for query in queryset:
        #     from horizon_pay.tasks import accept_cashout_resolve_task

        #     print("------------------")
        #     print(query)
        #     print("------------------")
        #     # Verify trans

        #     rrn = query.rrn
        #     terminal_id = query.terminal_id
        #     amount = query.amount
        #     response_json = json.loads(query.payload)

        #     print(rrn)
        #     print(terminal_id)
        #     print(amount)
        #     print(response_json)

        #     accept_cashout_resolve_task.apply_async(
        #         queue="resolvecashout",
        #         kwargs={
        #             "rrn": rrn,
        #             "terminal_id": terminal_id,
        #             "amount": amount,
        #             "response_json": response_json
        #         }
        #     )