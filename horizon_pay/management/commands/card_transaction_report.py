from horizon_pay.models import CardTransaction
from accounts.models import Transaction
from django.core.management.base import BaseCommand
from horizon_pay.serializers import CardTransactionReportSerializer
from main.helper.send_emails import send_single_sendgrid_email_with_attachment
from accounts.serializers import TransactionSerializerCard<PERSON>ransactionReport
import pytz, base64
from datetime import datetime, timedelta
from django.conf import settings
import pandas as pd
from sendgrid import SendGridAPIClient

class Command(BaseCommand):
    help: str = "To get a report of all card transactions in the last 24 hours in an excl sheet!"
    timezone = pytz.timezone(settings.TIME_ZONE)
    file_path = "generated/card_transaction_report.xlsx"

    def get_info(self):
        d_now = datetime.now()
        d_yesterday = d_now - timedelta(days=1)
        now = self.timezone.localize(d_now)
        yesterday = self.timezone.localize(d_yesterday)

        card_transactions = CardTransaction.objects.filter(date_created__lte=now).filter(date_created__gte=yesterday)
        # card_transactions = CardTransaction.objects.all()

        if len(card_transactions) > 0:
            cardTransaction_serializer = CardTransactionReportSerializer(card_transactions, many=True)
            all_transctions = []
            for value in cardTransaction_serializer.data:
                transaction = {k: v for k, v in value['transaction'].items() if k != 'user'}
                user_info = {k: v for k, v in value['transaction']['user'].items()}
                card_info = {k: v for k, v in value.items() if k != "transaction"}
                trn = dict(transaction, **card_info, **user_info)
                all_transctions.append(trn)

            # convert to pd dataframe
            df = pd.DataFrame.from_dict(all_transctions)
            df.to_excel(self.file_path)
        else:
            with open(self.file_path, 'w') as f:
                f.write("")

    def send_email(self):
        # {'email': '<EMAIL>'}, {'email': '<EMAIL>'}]
        receiver = [{'email': '<EMAIL>'}, {'email': '<EMAIL>'}]
        subject = 'Sending Transaction Records for the last 24 hours'
        with open(self.file_path, 'rb') as f:
            data = f.read()
        encoded = base64.b64encode(data).decode()
        resp = send_single_sendgrid_email_with_attachment(receiver, '', subject, encoded, self.file_path)

    def handle(self, *args, **kwargs):
        self.get_info()
        self.send_email()