from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.models import Q

from accounts.models import WalletSystem, DebitCreditRecordOnAccount
from horizon_pay.models import CardTransaction, StartCashOutTran


class Command(BaseCommand):
    help = 'Settles card transactions based on unique reference (RRN)'

    def add_arguments(self, parser):
        # Expecting a list of transaction references as a comma-separated string
        parser.add_argument('transaction_references', type=str, help='Comma-separated list of transaction references')

    def handle(self, *args, **kwargs):
        transaction_references = str(kwargs['transaction_references']).replace(" ", "").split(',')
        self.stdout.write(self.style.SUCCESS(transaction_references))

        try:
            with transaction.atomic():
                transactions = CardTransaction.objects.filter(reference_number__in=transaction_references, host_resp_code="00")
                trans_count = transactions.count()

                if not transactions.exists():
                    self.stdout.write(self.style.ERROR('No transactions found with the given RRNs.'))
                    return

                settled_count = 0
                for trans in transactions:
                    cash_out = StartCashOutTran.objects.get(rrn=trans.reference_number)
                    user = cash_out.user
                    check_wallet = WalletSystem.objects.filter(Q(user=user) & Q(wallet_type="COLLECTION")).last()
                    amount = trans.amount
                    liberty_commission = trans.liberty_commission

                    debit_credit = DebitCreditRecordOnAccount.objects.filter(unique_reference=trans.reference_number)

                    if debit_credit:
                        self.stdout.write(self.style.ERROR(f'Transaction with RRN {trans.reference_number}; amount = {amount} is already settled with DebitCreditID: {debit_credit.last().id}'))
                    else:
                        try:
                            WalletSystem.fund_balance(
                                user=user, wallet=check_wallet, amount=amount - liberty_commission, trans_type="CASH_OUT",
                                unique_reference=trans.reference_number
                            )
                            trans.status = "SUCCESSFUL"
                            trans.save()
                        except Exception as e:
                            print("error", e)

                        settled_count += 1
                        self.stdout.write(self.style.SUCCESS(f'Successfully settled {user.email} with RRN {trans.reference_number}; amount = {amount}'))

                self.stdout.write(self.style.SUCCESS(f'Found {trans_count} transactions, and settled {settled_count}'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error occurred while settling transactions: {str(e)}'))



