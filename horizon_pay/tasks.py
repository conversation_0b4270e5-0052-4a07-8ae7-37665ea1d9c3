from datetime import datetime
from celery import shared_task
from django.conf import settings
from horizon_pay.helpers.helper_function import decrypt_trans_pin, send_bank_transfer_redirect
from horizon_pay.models import CardIssuerTable, CardTransaction, HorizonPayTable, StartCashOutTran, TerminalSerialTable, NewCardSaveDataTable
from accounts.models import Transaction
from main.models import Whitelist, User
import json


@shared_task
def handle_bank_performance(pan):

    CardIssuerTable.create_issuer_logs(
        pan=pan[0:6]
    )
    return "Bank Performance handled"
    

@shared_task
def task_to_save_other_haptics_data(instance_id):
    instance = HorizonPayTable.objects.filter(id=instance_id).last()

    horizon_pay_ip = Whitelist.objects.filter(service_name="HORIZON_HAPTICKS").last()
    if horizon_pay_ip:
        if instance.ip_addr == horizon_pay_ip.host:
            instance.ip_correct = True

    data = json.loads(instance.payload)

    instance.mti = data.get("MTI")
    instance.amount = data.get("amount")
    instance.terminal_id = data.get("terminalId")
    instance.response_code = data.get("responseCode")
    instance.response_description = data.get("responseDescription")
    instance.pan = data.get("PAN")
    instance.stan = data.get("STAN")
    instance.auth_code = data.get("authCode")
    instance.transaction_time = data.get("transactionTime")
    instance.reversal = data.get("reversal")
    instance.merchant_id = data.get("merchantId")
    instance.merchant_name = data.get("merchantName")
    instance.merchant_address = data.get("merchantAddress")
    instance.rrn = data.get("rrn")

    instance.save()

    return "Horizon pay table saved"


# @shared_task
def send_money_by_card_transfer_out(data, user_access_token, card_trans_id):
    card_trans = CardTransaction.objects.filter(id=card_trans_id).last()

    decrypted_transaction_pin = decrypt_trans_pin(data["transaction_pin"])
    decrypted_access_token = decrypt_trans_pin(user_access_token)

    data["transaction_pin"] = decrypted_transaction_pin

    send_money = send_bank_transfer_redirect(
        send_money_bank_transfer_payload = data,
        user_access_token = decrypted_access_token
    )

    if card_trans:
        card_trans.send_money_by_card_resp = send_money
        card_trans.transaction.send_money_by_card_resp = send_money

        card_trans.transaction.save()
        card_trans.save()

    return "Done"



@shared_task
def accept_cashout_resolve_task(rrn, terminal_id, amount, response_json, force_resolve=False, resolved_by=None):
    # if Transaction.objects.filter(unique_reference=rrn).exists():
    #     return "ALREADY DONE"
    # else:

    settle_cash_out = HorizonPayTable.new_function_to_resolve_and_settle_cashout(rrn, terminal_id, amount, response_json, force_resolve=force_resolve, resolved_by=resolved_by)
    print(settle_cash_out)
        # initialized_trans = StartCashOutTran.objects.filter(rrn=rrn, trans_complete=False, tID=terminal_id, amount_started=amount).first()
        # tID_user = User.objects.filter(terminal_id=terminal_id).last()
        # if initialized_trans and tID_user:
        #     if initialized_trans.trans_complete:
        #         pass
        #     else:

        #         HorizonPayTable.resolve_and_settle_funds(
        #             user = tID_user,
        #             data = response_json,
        #             initialized_trans_data = initialized_trans
        #         )
        
    return "NOW DONE"



@shared_task
def batch_insert_terminal_ids_task(data_list):
    from horizon_pay.helpers.helper_function import register_terminal_cards_app


    # if settings.ENVIRONMENT == "development":
    #     successful_test  = [d['deviceSerialNumber'] for d in data_list]
    #     insert_to_cards_app = {"message": "", "terminalsInserted": successful_test, "terminalsNotInserted": []}	
    # else:
    insert_to_cards_app = register_terminal_cards_app(data=data_list)

    NewCardSaveDataTable.objects.create(
        register_tid_initial_payload = data_list,
        register_tid_response = json.dumps(insert_to_cards_app)
    )

    successful_insert = insert_to_cards_app.get("terminalsInserted", {})
    # unsuccessful_insert = insert_to_cards_app.get("data", {}).get("terminalsNotInserted", {})

    if successful_insert:
        for tid in TerminalSerialTable.objects.filter(terminal_serial__in=successful_insert):
            tid.is_registered = True
            tid.save()
        
    return "NOW DONE"





@shared_task
def auto_resolve_pending_card_trx():
    card_trx = StartCashOutTran.objects.filter(trans_complete=False, rrn__in=CardTransaction.objects.filter(host_resp_code="00").values_list("reference_number", flat=True)).order_by("-id")[:5]
    for trx in card_trx:
        rrn = trx.rrn
        terminal_id = trx.tID
        amount = trx.amount_started
        response_json = HorizonPayTable.objects.filter(rrn=rrn).first()

        settle_cash_out = HorizonPayTable.new_function_to_resolve_and_settle_cashout(rrn, terminal_id, amount, response_json, force_resolve=False, resolved_by=None)
        
        print(settle_cash_out)
    
        
    return "NOW DONE"


