from django.urls import path, include
from horizon_pay import views

# URLCOnf

horizon_pay = [
    path('api/call_back/', views.HorizonPayCallBackAPIView.as_view()),
    path('api/libpaypos/initialize/', views.Start2FAView.as_view()),
    path('api/libpaypos/call_back/', views.LibertyPayPOSCallBackAPIView.as_view()),
    path('api/can_withdraw/<str:method>', views.HorizonCheckAvailabilityAPIView.as_view()),
    path('api/card_issuer/performance/', views.CardIssuerCashOutView.as_view()),
    path("api/horizon_pay_table", views.HorizonPayTableListView.as_view()),
    path("api/resend_hapticks_data/", views.SecondLibertyPayPOSCallBackAPIView.as_view()),
    path("api/initialize/card/new/", views.BeginCashOutTrans.as_view()),
    path("api/format_data/", views.CardDataJSONFormatterAPIView.as_view()),
    path("api/check_cashout_charge/", views.CalculateCashOutFeeAPIView.as_view()),
    path("api/get_dynamic_cashout_data/", views.CashOutTransactionHistoryAPIView.as_view()),
    path("api/get_agent_deposit_fee/", views.CheckActualDepositFeeAPIView.as_view()),
    path("api/limit-check/", views.LimitCheckAPIView.as_view()),  # This will be decommissioned
    path("api/limit-check-new/", views.LimitCheckNewAPIView.as_view()),
    path("api/reversal-callback/", views.ReversalCallbackAPIView.as_view(), name="reversal-callback"),
    path("auto-register-terminal/", views.AutoRegisterTerminalAPIView.as_view(), name="register-terminal"),
    path('verify-terminal/', views.VerifyTerminalDetailsAPIView.as_view(), name='verify-terminal'),

    path("check-card-transaction-status/", views.CheckCardTransactionStatusAPIView.as_view(), name="check-transaction-status"),
    path('check-paybox-merchant/', views.CheckPayboxMerchantAPIView.as_view(), name='check-paybox-merchant'),
    path('check-terminal-purchase/', views.CheckTerminalPurchaseAPIView.as_view(), name='check-terminal-purchase'),
    path('update-terminal-user-type/', views.UpdateTerminalUserTypeAPIView.as_view(), name='update-terminal-user-type'),
    path('check-start-cashout/', views.ValidateCardTransaction.as_view(), name='check-start-cashout'),

]

urlpatterns = [
    *horizon_pay
]
