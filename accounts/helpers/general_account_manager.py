from django.conf import settings
from main.models import ConstantTable
from datetime import datetime
import requests
import json
import ast
import uuid
import random
import jwt
import time


def vfd_call_back_for_funding(data):

    url = f"{settings.BASE_URL}/accounts/callback/vfd_send/"

    payload = ast.literal_eval(data)

    # payload = json.dumps(json.loads(r'{data.replace("'", '"')}'))

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Hook {settings.VFD_WEBHOOK_AUTH}"
    }


    response = requests.request("POST", url=url, headers=headers, json=payload)

    res = response.json()
    return res


def dynamic_call_back_for_funding(data):

    url = f"{settings.BASE_URL}/accounts/callback/dynamic_callback/"

    payload = json.dumps(data)

    headers = {
        "Content-Type": "application/json"
    }


    response = requests.request("POST", url=url, headers=headers, data=payload)

    res = response.json()
    print("[[[[[[[[[[[[[[[[[[[[[")
    print(res)
    print("[[[[[[[[[[[[[[[[[[[[[")
    return res


def paystack_verify_funding_transaction(reference, amount):

    url = f"https://api.paystack.co/transaction/verify/{reference}"

    # pin = settings.WOVEN_PAYOUT_PIN

    headers = {'Authorization' : f"Bearer {settings.PAYSTACK_API_SECRET}"}


    response = requests.request("GET", url, headers=headers)
    res = response.json()

    if res["status"] == True \
        and res.get("data").get("status") == "success" \
            and res.get("data").get("amount") == amount*100:

        return True
    else:
        # {
        #     "status": false,
        #     "message": "Transaction reference not found"
        # }
        return False


def send_transaction_notification_to_horizon_pay(main_data):
    
    url = f"{settings.HORIZON_PAY_URL}/api/notification/liberty"

    headers = {
            "Accept": "application/json",
            "Authorization": f"Bearer {settings.HORIZON_BEARER}",
            "Content-Type": "application/json",
            "Cookie": f"XSRF-TOKEN={settings.HORIZON_COOKIE_TOKEN}",
    }

    payload = main_data

    response = requests.request("POST", url, headers=headers, json=payload)
    resp = response.json()
    return resp


def create_verifiable_transaction(new_data, instant=False):

    from accounts.tasks import create_verification_trans_task

    # session = requests.Session()


    if settings.ENVIRONMENT == "development":
        url = "http://localhost:9000/main/create_verifiable_transaction/"

    else:
        url = f"{settings.TRANSACTION_VERIFICATION_BACEKEND_URL}/main/create_verifiable_transaction/"


    payload = new_data
    headers = {"Authorization": f"Hook {settings.VFD_WEBHOOK_AUTH}"}


    if ConstantTable.get_constant_table_instance().use_task_for_transfers and instant is False:
        print("This will be true", payload.get("user_email"))
        create_verification_trans_task.apply_async(
                            queue="sendmoney",
                            kwargs={
                                "url": url,
                                "payload": payload,
                                "headers": headers
                            }
                        )
        
        response = "ran in celery"

    else:
        try:
            response = requests.request("POST", url, data=payload, headers=headers, timeout=10)
            print(json.loads(response.text))
        except requests.exceptions.RequestException as e:
            response = f"{e}"
            pass
        # try:
        #     response = session.post(url, data=payload, headers=headers, timeout=10)
        #     print(json.loads(response.text))
        # except requests.exceptions.RequestException:
        #     pass

        # session.close()

    return response


def print_airtime_pin_request(data):
    
    url = f"{settings.PRINT_AIRTIME_BASE_URL}/redbiller/purchase_pin/"

    # headers = {
    #         "Accept": "application/json",
    #         "Authorization": f"Bearer {settings.HORIZON_BEARER}",
    #         "Content-Type": "application/json",
    #         "Cookie": f"XSRF-TOKEN={settings.HORIZON_COOKIE_TOKEN}",
    # }

    payload = data

    try:
        response = requests.request("POST", url, json=payload, timeout=100)
        print("--------------------------------")
        print("--------------------------------")
        print(response.json())
        print("--------------------------------")
        print("--------------------------------")
        resp = response.json()
    except requests.exceptions.RequestException as e:
        resp = {
            "status": "PENDING",
            "error_from": "VAS_BACKEND",
            "message": f"{e}"
        }

    return resp



def initialize_paystack(email, amount):
    url = "https://api.paystack.co/transaction/initialize"

    headers = {
        "Authorization": f"Bearer {settings.PAYSTACK_API_SECRET}",
        "Content-Type": "application/json",
    }

    data = { 
        "email": email, 
        "amount": amount
    }

    response = requests.request("POST", url, json=data, headers=headers, timeout=30)
    resp = response.json()
    return resp


def send_paystack_data_to_marketing(data):
    
    url = f"{settings.MARKETING_PAYSTACK_URL}/salesrep/new_form_paystack_call_back/"

    # headers = {
    #         "Accept": "application/json",
    #         "Authorization": f"Bearer {settings.HORIZON_BEARER}",
    #         "Content-Type": "application/json",
    #         "Cookie": f"XSRF-TOKEN={settings.HORIZON_COOKIE_TOKEN}",
    # }

    payload = data

    response = requests.request("POST", url, data=payload, timeout=30)
    resp = response.json()
    return resp


def send_paystack_data_to_savings(data):
    
    url = "https://savingsback.libertypayng.com/payment/webhook/"

    headers = {
        "Content-Type": "application/json",
        "Webhook-Header": f"{settings.AJO_MULTI_AUTH}"
    }

    payload = data

    response = requests.request("POST", url, data=payload, timeout=30, headers=headers)
    resp = response.json()
    return resp


def pay_for_terminal_function(data, access_token):
    
    url = f"{settings.BASE_URL}/send/send_money_paybuddy/"

    headers = {
        "Authorization": f"Bearer {access_token}"
    }

    payload = data

    response = requests.request("POST", url, headers=headers, json=payload)
    resp = response.json()
    return resp


def check_created_references(liberty_reference):
    from accounts.models import Transaction

    if Transaction.objects.filter(liberty_reference=liberty_reference).exists():
        return True
    else:
        return False
    

def create_temp_trans_access_token(session_key, temp_user_id, added_time):
  

    expiry_time = time.time() + added_time  # expire in added_time seconds

    payload = {
        "token_type": "access",
        "exp": expiry_time,
        "jti": session_key,
        "user_id": temp_user_id
    }

    secret = settings.SECRET_KEY

    # encode the JWT
    encoded_jwt = jwt.encode(payload, secret, algorithm='HS256')

    return encoded_jwt



def charge_recurring_or_sweep_function(data, access_token, trans_key):
    from horizon_pay.helpers.helper_function import decrypt_trans_pin


    session_key = data.session_key

    # Split the key into parts using the '-' separator
    session_parts = session_key.split('-')

    expiration_time = int(session_parts[1])

    current_time = int(time.time())

    if data.amount < 50:
        return {
            "status": False,
            "message": "Amount is less than 50",
        }
    
    if not trans_key:
        return {
            "status": False,
            "message": "No Trans Key Provided",
        }
    
    new_trans_key = decrypt_trans_pin(trans_key)


    if current_time < expiration_time:

        url = f"{settings.BASE_URL}/send/send_money_bank_account/"

        headers = {
            "Authorization": f"Bearer {access_token}",
            
        }

        payload = {
            "from_wallet_type": "COLLECTION",
            "data": [
                {
                    "account_number": data.account_number,
                    "account_name": data.account_name,
                    "bank_code": data.bank_code,
                    "bank_name": data.bank_name,
                    "amount": data.amount,
                    "narration": data.narration if data.narration else "MERCHANT_SWEEP" if data.recur_instance.recur_type == "SWEEP" else "RECUR_TRANSFER",
                    "customer_reference": data.customer_reference,
                    "is_beneficiary": "False",
                    "save_beneficiary": "False",
                    "remove_beneficiary": "False",
                    "is_recurring": "False",
                    "ledger_commission": 0,
                    "commission_type": None
                }
            ],
            "send_by_card_rrn": None,
            "total_amount": 100.00,
            "total_amount_with_charge": 130.00,
            "transaction_pin": new_trans_key
        }

        try:
            response = requests.request("POST", url, headers=headers, json=payload)
            resp = response.json()
            first_resp = response.json()
            resp = {
                "status": True,
                "data": first_resp
            }

        except requests.exceptions.RequestException as err:
            resp = {
                "status": False,
                "message": f"{err}"
            }
            
    else:
        resp = {
            "status": False,
            "message": f"Session has expired. Current Time(Epoch) - {current_time}"
        }
    

    return resp
    




# def create_liberty_references(transaction_type, provider):
#     today_string = datetime.now().strftime("%d/%m/%Y")
#     gen_uuid = str(uuid.uuid4()).replace('-', '').upper()
#     generated_id = ''.join(random.choice(gen_uuid) for i in range(15))

#     if provider == "REDBILLER":

#         if transaction_type == "FUND_BY_USSD":
#             liberty_reference = f"LGLPRBFUSSD{generated_id}"

#         elif transaction_type == "USSD_WITHDRAW":
#             liberty_reference = f"LGLPRBWUSSD{generated_id}"
#         else:
#             liberty_reference = f"LGLPUSSD{generated_id}"
        
#     elif provider == "SUDO":
#         if transaction_type == "VIRTUAL_CARD_CREATE":
#             liberty_reference = f"LGLP_VRCD_{generated_id}"

#         elif transaction_type == "PHYSICAL_CARD_CREATE":
#             liberty_reference = f"LGLP_PHYCD_{generated_id}"

#         else:
#             liberty_reference = f"LGLP_TRNS_{generated_id}"
#     else:
#         liberty_reference = f"LGLP-{today_string}{generated_id}"

#     check_ref = check_created_references(liberty_reference=liberty_reference)
    
#     if check_ref == True:
#         return create_liberty_references(transaction_type, provider)
#     else:
#         return liberty_reference