from http import client
from django.conf import settings
from time import sleep
import requests
import json
import uuid
import random
import hashlib
import datetime

from accounts.model_choices import VfdAccountUpdateType


# from main.models import ConstantTable
# settings.configure()


class VFDBank:
    base_url = settings.VFD_BASE_URL
    account_base_url = settings.VFD_ACCOUNT_CREATION_BASE_URL_LIVE
    access_token = settings.VFD_ACCESS_TOKEN
    wallet_credentials = settings.VFD_WALLET_CREDENTIALS


    headers = {
        # "Authorization": f"Bearer {access_token}",
        "AccessToken": f"{access_token}",
        "accept": "application/json"
    }
    params = {
        # "wallet-credentials": f"{wallet_credentials}"
    }



    @classmethod
    def create_vfd_wallet(cls, user, user_phone_number, retry_count, overide=False, existing_account=None):
        from accounts.helpers.helper_func import get_most_recent_account

        filter_url = "client/create"

        existing_acct = existing_account or get_most_recent_account(user)

        # Determine if it's a new account or a duplicate
        if existing_acct:
            previousAccountNo = existing_acct
            # For duplicate account creation
            url = f"{cls.account_base_url}{filter_url}?previousAccountNo={previousAccountNo}"
            payload = {"Payload":url}
        else:
            # For new account creation
            # dob = user.check_kyc.bvn_rel.bvn_birthdate.strftime("%d-%b-%Y")
            dob = datetime.datetime.strptime(user.check_kyc.bvn_rel.bvn_birthdate, "%Y-%m-%d").strftime("%d-%b-%Y")
            bvn = user.check_kyc.bvn_rel.bvn_number
            url = f"{cls.account_base_url}{filter_url}?bvn={bvn}&dateOfBirth={dob}"
            payload = {"Payload":url}

        if settings.ENVIRONMENT == "development":

            get_account_num = ''.join(random.choice("**********") for _ in range(10))
            mock_response = {
                "status": "00",
                'message': 'Successful Creation',
                "data": {
                    'firstname': user.check_kyc.bvn_rel.bvn_first_name,
                    'middlename': user.check_kyc.bvn_rel.bvn_middle_name or "",
                    'lastname': user.check_kyc.bvn_rel.bvn_last_name,
                    'bvn': bvn,
                    'phone': user.phone_number,
                    "dob": dob,
                    "accountNo": get_account_num
                }
            }
            return {
                "main_data": mock_response,
                "initial_payload": payload
            }

        else:
            try:
                # API request
                response = requests.post(url=url, headers=cls.headers, json=payload, timeout=15)
                res = response.json()

                return {
                    "main_data": res,
                    "initial_payload": payload
                }

            except requests.exceptions.RequestException as err:
                # Handle request exceptions
                return {
                    "main_data": {
                        "status": "99",
                        "message": str(err)
                    },
                    "initial_payload": payload
                }



    @classmethod
    def create_vfd_wallet_ajo(cls, bvn_details: dict, num_to_add):
        filter_url = "client/create"
        dob = datetime.datetime.strptime(bvn_details["dob"], "%Y-%m-%d").strftime("%d-%b-%Y")
        bvn = bvn_details["bvn"]
        url = f"{cls.account_base_url}{filter_url}?bvn={bvn}&dateOfBirth={dob}"

        payload = {"Payload":url}

        if settings.ENVIRONMENT == "development":
            # Mock response for development
            get_account_num = ''.join(random.choice("**********") for _ in range(10))
            mock_response = {
                "status": "00",
                'message': 'Successful Creation',
                "data": {
                    'firstname': bvn_details["firstname"],
                    'middlename': bvn_details.get("middlename", ""),
                    'lastname': bvn_details["lastname"],
                    'bvn': bvn,
                    'phone': bvn_details['phone'],
                    "dob": dob,
                    "accountNo": get_account_num
                }
            }
            return {
                "main_data": mock_response,
                "initial_payload": payload
            }

        else:
            try:
                # API request for new account creation
                response = requests.post(url=url, headers=cls.headers, json=payload, timeout=15)
                res = response.json()

                return {
                    "main_data": res,
                    "initial_payload": payload
                }

            except requests.exceptions.RequestException as err:
                # Handle request exceptions
                return {
                    "main_data": {
                        "status": "99",
                        "message": str(err)
                    },
                    "initial_payload": payload
                }



    # @classmethod
    # def create_vfd_wallet(cls, user, user_phone_number, retry_count, overide=False):

    #     filter_url = "wallet2/clientdetails/create"
    #     url = cls.base_url + filter_url

    #     if user.check_kyc.bvn_rel.bvn_phone_number is None or user.check_kyc.bvn_rel.bvn_phone_number == "" or user.check_kyc.bvn_rel.bvn_phone_number == " ":
    #         phone_number = user.phone_number
    #     else:
    #         phone_number = user.check_kyc.bvn_rel.bvn_phone_number


    #     if user.vfd_bvn_acct_num_count < 1 or overide == True:
    #         firstname = user.check_kyc.bvn_rel.bvn_first_name
    #         lastname = user.check_kyc.bvn_rel.bvn_last_name
    #         bvn = user.check_kyc.bvn_rel.bvn_number
    #         phone_num = phone_number

    #     else:
    #         number_to_add = (user.vfd_bvn_acct_num_count - 1) + 1
    #         firstname = user.check_kyc.bvn_rel.bvn_first_name
    #         lastname = user.check_kyc.bvn_rel.bvn_last_name
    #         bvn = f"{user.check_kyc.bvn_rel.bvn_number}-{number_to_add}"
    #         phone_num = f"{phone_number}-{number_to_add}"


    #     payload = {
    #         "firstname": firstname,
    #         "lastname": lastname,
    #         "dob": user.check_kyc.bvn_rel.bvn_birthdate,
    #         "address": f"{user.street} {user.lga} {user.state}",
    #         "gender": user.check_kyc.bvn_rel.bvn_gender,
    #         "phone": phone_num,
    #         "bvn": bvn
    #     }


    #     middle_name = user.check_kyc.bvn_rel.bvn_middle_name
    #     if middle_name is not None:
    #         payload["middlename"] = middle_name
    #     else:
    #         pass

    #     gender = user.check_kyc.bvn_rel.bvn_gender
    #     if gender is not None:
    #         payload["gender"] = gender
    #     else:
    #         pass



    #     if settings.ENVIRONMENT == "development":
    #         all_numbers = ["0","1","2","3","4","5","6","7","8","9"]

    #         get_account_num = ''.join(random.choice(all_numbers) for i in range(10))
    #         payload["dob"] = "12-Aug-1954"
    #         vfd_create = {
    #             "main_data": {
    #                 "status": "00",
    #                 'message': 'Successful Creation',
    #                 "data": {
    #                     'firstname': firstname,
    #                     'middlename': "middlename",
    #                     'lastname': lastname,
    #                     'bvn': bvn,
    #                     'phone': phone_num,
    #                     "dob": "12-Aug-1954",
    #                     "accountNo": get_account_num
    #                 }
    #             },
    #             "initial_payload": payload
    #         }

    #         return vfd_create

    #     else:

    #         try:

    #             response = requests.request(
    #                 "POST", url=url, headers=cls.headers, params=cls.params, json=payload, timeout=15
    #             )
    #             res = response.json()
    #             # return res

    #             return {
    #                 "main_data": res,
    #                 "initial_payload": payload
    #             }



    #         except requests.exceptions.RequestException as err:


    #             return {
    #                 "main_data": {
    #                     "status": "99",
    #                     "message": str(err)
    #                 },
    #                 "initial_payload": payload
    #             }


            # response = {
            #     "status": "00",
            #     "message": "Successful Creation",
            #     "data": {
            #         "firstname": "Ogba",
            #         "middlename": "middlename",
            #         "lastname": "lastname",
            #         "bvn": "***********",
            #         "phone": "************",
            #         "dob": "12-Aug-1954",
            #         "accountNo": "**********"
            #     }
            # }


            #         {
            # "status": "929",
            # "message": "BVN Exist"
            # }
        # except ConnectionError as err:
        #     return str(err)
        # except requests.ConnectTimeout as err:
        #     return str(err)
        # finally as e:
        #     return None


    # @classmethod
    # def create_vfd_wallet_ajo(cls, bvn_details: dict, num_to_add):

    #     filter_url = "wallet2/clientdetails/create"
    #     url = cls.base_url + filter_url


    #     firstname = bvn_details["firstname"]
    #     lastname = bvn_details["lastname"]
    #     address = bvn_details["address"]
    #     gender = bvn_details["gender"]
    #     dob = datetime.date.strftime(bvn_details["dob"], "%d %B %Y")
    #     phone_num = f"{bvn_details['phone']}-{num_to_add}"
    #     bvn = f"{bvn_details['bvn']}-{num_to_add}"




    #     payload = {
    #         "firstname": firstname,
    #         "lastname": lastname,
    #         "dob": dob,
    #         "address": address,
    #         "gender": gender,
    #         "phone": phone_num,
    #         "bvn": bvn
    #     }



    #     if settings.ENVIRONMENT == "development":
    #         all_numbers = ["0","1","2","3","4","5","6","7","8","9"]

    #         get_account_num = ''.join(random.choice(all_numbers) for i in range(10))
    #         payload["dob"] = "12-Aug-1954"
    #         vfd_create = {
    #             "main_data": {
    #                 "status": "00",
    #                 'message': 'Successful Creation',
    #                 "data": {
    #                     'firstname': firstname,
    #                     'middlename': "middlename",
    #                     'lastname': lastname,
    #                     'bvn': bvn,
    #                     'phone': phone_num,
    #                     "dob": "12-Aug-1954",
    #                     "accountNo": get_account_num
    #                 }
    #             },
    #             "initial_payload": payload
    #         }

    #         return vfd_create

    #     else:

    #         try:

    #             response = requests.request(
    #                 "POST", url=url, headers=cls.headers, params=cls.params, json=payload, timeout=15
    #             )
    #             res = response.json()
    #             # return res

    #             return {
    #                 "main_data": res,
    #                 "initial_payload": payload
    #             }



    #         except requests.exceptions.RequestException as err:
    #             # return {
    #             #     "status": "99",
    #             #     "message": str(err)
    #             # }

    #             return {
    #                 "main_data": {
    #                     "status": "99",
    #                     "message": str(err)
    #                 },
    #                 "initial_payload": payload
    #             }


            # response = {
            #     "status": "00",
            #     "message": "Successful Creation",
            #     "data": {
            #         "firstname": "Ogba",
            #         "middlename": "middlename",
            #         "lastname": "lastname",
            #         "bvn": "***********",
            #         "phone": "************",
            #         "dob": "12-Aug-1954",
            #         "accountNo": "**********"
            #     }
            # }


            #         {
            # "status": "929",
            # "message": "BVN Exist"
            # }
        # except ConnectionError as err:
        #     return str(err)
        # except requests.ConnectTimeout as err:
        #     return str(err)
        # finally as e:
        #     return None



    @classmethod
    def create_corporate_account(cls, user, rc_number, company_name, incorp_date, bvn):

        filter_url = "wallet2/corporateclient/create"
        url = cls.base_url + filter_url

        # if user.vfd_bvn_acct_num_count < 1 or overide == True:
        #     firstname = user.check_kyc.bvn_rel.bvn_first_name
        #     lastname = user.check_kyc.bvn_rel.bvn_last_name
        #     bvn = user.check_kyc.bvn_rel.bvn_number
        #     phone_num = phone_number

        # else:
        #     number_to_add = (user.vfd_bvn_acct_num_count - 1) + 1
        #     firstname = user.check_kyc.bvn_rel.bvn_first_name
        #     lastname = user.check_kyc.bvn_rel.bvn_last_name
        #     bvn = f"{user.check_kyc.bvn_rel.bvn_number}-{number_to_add}"
        #     phone_num = f"{phone_number}-{number_to_add}"

        if user.vfd_bvn_acct_num_count < 1:
            final_bvn = bvn

        else:
            number_to_add = (user.vfd_bvn_acct_num_count - 1) + 1
            final_bvn = f"{bvn}-{number_to_add}"



        payload = {
            "rcNumber": f"{rc_number}",
            "companyName": f"{company_name}",
            "incorporationDate": f"{incorp_date}",
            "bvn": f"{final_bvn}"
        }


        try:
            response = requests.request(
                "POST", url=url, json=payload, headers=cls.headers, params=cls.params
            )

            res = response.json()
            return {
                "main_data": res,
                "initial_payload": payload
            }

        except requests.exceptions.RequestException as err:
            return {
                "main_data": {
                    "status": "99",
                    "message": str(err)
                },
                "initial_payload": payload
            }




    @classmethod
    def get_bvn_consent(cls, bvn, check_type="02"):
        """
        BVN Enquiry - 01
        Account Creation - 02
        Account Lookup - 03
        """

        filter_url = "wallet2/bvn-consent"
        url = cls.base_url + filter_url

        cls.params["bvn"] = bvn
        cls.params["type"] = check_type


        try:

            response = requests.request(
                "GET", url=url, headers=cls.headers, params=cls.params, timeout=15
            )
            res = response.json()
            return {
                "main_data": res,
            }



        except requests.exceptions.RequestException as err:
            return {
                "main_data": {
                    "status": "99",
                    "message": str(err)
                },
            }


    @classmethod
    def create_vfd_wallet_v2(cls, user, overide=False, previous_account_no=None):

        filter_url = "wallet2/client/individual"
        url = cls.base_url + filter_url

        firstname = user.check_kyc.bvn_rel.bvn_first_name
        lastname = user.check_kyc.bvn_rel.bvn_last_name
        bvn = user.check_kyc.bvn_rel.bvn_number

        if overide == True:
            previous_account_no = None

        payload = {
            "firstname": firstname,
            "lastname": lastname,
            "dob": user.check_kyc.bvn_rel.bvn_birthdate,
            "address": f"{user.street} {user.lga} {user.state}",
            "gender": user.check_kyc.bvn_rel.bvn_gender,
            "bvn": bvn,
            "previousAccountNo": previous_account_no
        }


        middle_name = user.check_kyc.bvn_rel.bvn_middle_name
        if middle_name is not None:
            payload["middlename"] = middle_name
        else:
            pass

        gender = user.check_kyc.bvn_rel.bvn_gender
        if gender is not None:
            payload["gender"] = gender
        else:
            pass


        try:

            response = requests.request(
                "POST", url=url, headers=cls.headers, params=cls.params, json=payload, timeout=15
            )
            res = response.json()
            # return res

            return {
                "main_data": res,
                "initial_payload": payload
            }



        except requests.exceptions.RequestException as err:
            # return {
            #     "status": "99",
            #     "message": str(err)
            # }

            return {
                "main_data": {
                    "status": "99",
                    "message": str(err)
                },
                "initial_payload": payload
            }


    @classmethod
    def create_corporate_account_v2(cls, user, rc_number, company_name, incorp_date, bvn, previous_account_no=None):

        filter_url = "wallet2/client/corporate"
        url = cls.base_url + filter_url


        payload = {
            "rcNumber": f"{rc_number}",
            "companyName": f"{company_name}",
            "incorporationDate": f"{incorp_date}",
            "bvn": f"{bvn}",
            "previousAccountNo": previous_account_no
        }


        try:
            response = requests.request(
                "POST", url=url, json=payload, headers=cls.headers, params=cls.params
            )

            res = response.json()
            return {
                "main_data": res,
                "initial_payload": payload
            }

        except requests.exceptions.RequestException as err:
            return {
                "main_data": {
                    "status": "99",
                    "message": str(err)
                },
                "initial_payload": payload
            }


    @classmethod
    def grequest_get_vfd_balances(cls, account_number_list):
        import grequests
        account_data = {}

        if settings.ENVIRONMENT == "development":
            for acct in account_number_list:
                account_number = acct
                account_data[account_number] = {
                    "status": "00",
                    "message": "Account Details",
                    "data": {
                        "accountNo": acct,
                        "accountBalance": random.randint(0, 500000),
                        "accountId": f"{random.randint(0, 100000)}",
                        "client": "Chukwuemeka Nwaoma",
                        "clientId": f"{random.randint(0, 100000)}",
                        "savingsProductName": "Corporate Current Account"
                    }
                }


        else:

            filter_url = "wallet2/account/enquiry"
            requests = [grequests.get(f'{filter_url}?accountNumber={account_number}') for account_number in account_number_list]

            responses = grequests.map(requests)
            for response in responses:
                try:
                    data = response.json()
                    if data.get("status") == "00":
                        account_number = data['data']['accountNo']
                        account_data[account_number] = data
                    else:
                        print(f"Error fetching data for account: {response.url}")
                        pass
                except:
                    pass


        return account_data








    @classmethod
    def vfd_account_enquiry(cls, account_number=None):

        filter_url = "wallet2/account/enquiry"
        url = cls.base_url + filter_url

        cls.params["accountNumber"] = account_number

        if settings.ENVIRONMENT == "development":

            return {
                "ip_addr": "",
                "socket": "**********",
                "status": "01",
                "message": "Account Not Found",
                "data": {
                    "accountId": "154779"
                },
            }

        else:

            try:
                response = requests.request(
                    "GET", url=url, headers=cls.headers, params=cls.params
                )

                return response.json()
            except requests.exceptions.RequestException as e:
                return {
                    "vfd_error": True,
                    "status": "91",
                    "message": f"VFD Request Broken {e}"
                }


    @classmethod
    def vfd_get_transfer_recipient(cls, transfer_type, account_number, bank_code):

        if settings.ENVIRONMENT == "production":

            filter_url = f"wallet2/transfer/recipient"
            url = cls.base_url + filter_url

            cls.params["accountNo"] = account_number
            cls.params["transfer_type"] = transfer_type
            cls.params["bank"] = bank_code

            try:

                response = requests.request(
                    "GET", url=url, headers=cls.headers, params=cls.params
                )

                return response.json()

            except requests.exceptions.RequestException as e:

                return {
                    "status": "91",
                    "message": f"VFD Request Broken {e}"
                }

        else:
            return {
                "status": "00",
                "message": "Account Found"
            }


    @classmethod
    def initiate_payout_v1(
        cls,
        beneficiary_account_name,
        beneficiary_nuban,
        beneficiary_bank_code,
        source_account,
        narration,
        amount,
        transfer_type,
        user_bvn,
        reference,
        transfer_leg=None
    ):

        if settings.ENVIRONMENT == "development":

            return {
                "status":"00",
                "message":"****************",
                "data":{
                    "txnId": reference,
                    "sessionId":"090110226514433719724158835748",
                    "reference":reference
                }
            }

        else:

            filter_url = "wallet2/transfer"
            url = cls.base_url + filter_url


            if transfer_leg is not None and transfer_leg in ["FIRST_LEG", "CASH_OUT", "VAS_COMMISSIONS", "TRNS_CB_COMM", "TRNS_CB_COMM_CASH", "NO_RO_EXT_COMM", "FND_BANK_COMM", "CARD_PUR_COMM", "OUT_OF_BOOKS"]:
                enquire_for_user_account = cls.vfd_account_enquiry()

            else:
                enquire_for_user_account = cls.vfd_account_enquiry(
                    account_number=source_account
                )

            print("source_account: ", source_account)
            print(source_account)
            print("source_account: ", source_account)

            if enquire_for_user_account.get("vfd_error") or enquire_for_user_account.get("data") is None:
                return {
                    "error": "VFD Request Broken",
                    "message": f"{enquire_for_user_account}"
                }


            fromSavingsId = enquire_for_user_account.get("data")["accountId"]
            fromClient = enquire_for_user_account.get("data")["client"]
            fromClientId = enquire_for_user_account.get("data")["clientId"]
            fromAccount = enquire_for_user_account.get("data")["accountNo"]


            get_recipient = cls.vfd_get_transfer_recipient(
                transfer_type=transfer_type,
                account_number=beneficiary_nuban,
                bank_code=beneficiary_bank_code,
            )


            if get_recipient.get("status") == "00":

                toClient = get_recipient.get("data")["name"]
                toClientBvn = get_recipient.get("data")["bvn"]
                toClientId = get_recipient.get("data")["clientId"]
                toSavings_or_sessions_id = get_recipient.get("data")["account"]["id"]


                get_signature = f"{fromAccount}{beneficiary_nuban}"
                signature_code = hashlib.sha512(str(get_signature).encode("utf-8") ).hexdigest()

                payload = {
                    "fromSavingsId": fromSavingsId,
                    "amount": float(amount),
                    "toAccount": beneficiary_nuban,
                    "fromBvn": user_bvn,
                    "signature": signature_code,
                    "fromAccount": fromAccount,
                    "toBvn": toClientBvn,
                    "remark": narration,
                    "fromClientId": fromClientId,
                    "fromClient": fromClient,
                    "toKyc": "99",
                    "reference": reference,
                    "toClientId": toClientId,
                    "toClient": toClient,
                    "toSession": toSavings_or_sessions_id,
                    "transferType": transfer_type,
                    "toBank": beneficiary_bank_code,
                    "toSavingsId": toSavings_or_sessions_id
                }




                try:
                    response = requests.request(
                        "POST", url=url, headers=cls.headers, params=cls.params, json=payload)

                    return response.json()

                except requests.exceptions.RequestException as e:
                    return {
                        "error": "VFD Request Broken",
                        "message": f"{e}"
                    }

            return {
                "error": "No Recipient",
                "message": f"{get_recipient}"
            }


    @classmethod
    def initiate_payout(
        cls,
        beneficiary_account_name,
        beneficiary_nuban,
        beneficiary_bank_code,
        source_account,
        narration,
        amount,
        transfer_type,
        user_bvn,
        reference,
        transfer_leg=None,
        sender_account_id=None
    ):

        if settings.ENVIRONMENT == "development":

            return {
                "status":"00",
                "message":"****************",
                "data":{
                    "txnId": reference,
                    "sessionId":"090110226514433719724158835748",
                    "reference":reference
                }
            }

        else:

            filter_url = "wallet2/transfer"
            url = cls.base_url + filter_url


            enquire_for_user_account = cls.vfd_account_enquiry()


            print("source_account: ", source_account)

            if enquire_for_user_account.get("vfd_error") or enquire_for_user_account.get("data") is None:
                return {
                    "error": "VFD Request Broken on Enquiry",
                    "message": f"{enquire_for_user_account}"
                }


            fromSavingsId = enquire_for_user_account.get("data")["accountId"]
            fromClient = enquire_for_user_account.get("data")["client"]
            fromClientId = enquire_for_user_account.get("data")["clientId"]
            fromAccount = enquire_for_user_account.get("data")["accountNo"]


            get_recipient = cls.vfd_get_transfer_recipient(
                transfer_type=transfer_type,
                account_number=beneficiary_nuban,
                bank_code=beneficiary_bank_code,
            )


            if get_recipient.get("status") == "00":

                toClient = get_recipient.get("data")["name"]
                toClientBvn = get_recipient.get("data")["bvn"]
                toClientId = get_recipient.get("data")["clientId"]
                toSavings_or_sessions_id = get_recipient.get("data")["account"]["id"]


                get_signature = f"{fromAccount}{beneficiary_nuban}"
                signature_code = hashlib.sha512(str(get_signature).encode("utf-8") ).hexdigest()

                payload = {
                    "fromSavingsId": fromSavingsId,
                    "amount": float(amount),
                    "toAccount": beneficiary_nuban,
                    "fromBvn": user_bvn,
                    "signature": signature_code,
                    "fromAccount": fromAccount,
                    "toBvn": toClientBvn,
                    "remark": narration,
                    "fromClientId": fromClientId,
                    "fromClient": fromClient,
                    "toKyc": "99",
                    "reference": reference,
                    "toClientId": toClientId,
                    "toClient": toClient,
                    "toSession": toSavings_or_sessions_id,
                    "transferType": transfer_type,
                    "toBank": beneficiary_bank_code,
                    "toSavingsId": toSavings_or_sessions_id
                }

                if transfer_leg == "TRANSFER":
                    payload.update({"uniqueSenderAccountId": sender_account_id})



                try:
                    response = requests.request(
                        "POST", url=url, headers=cls.headers, params=cls.params, json=payload)

                    return response.json()

                except requests.exceptions.RequestException as e:
                    return {
                        "error": "VFD Request Broken",
                        "message": f"{e}"
                    }

            return {
                "error": "No Recipient",
                "message": f"{get_recipient}"
            }


    @classmethod
    def vfd_transaction_verification_handler(cls, reference, fail=False):

        filter_url = f"wallet2/transactions"
        url = cls.base_url + filter_url

        cls.params["reference"] = reference

        if settings.ENVIRONMENT == "development":
            if fail == True:
                resp = {
                    "status": "108",
                    "message": "No Transaction!",
                }

            else:
                resp = {
                    "status": "00",
                    "message": "Successful Transaction Retrieval",
                    "data": {
                        "TxnId": reference,
                        "amount": "1020.0",
                        "accountNo": "**********",
                        "transactionStatus": "00",
                        "transactionDate": "2022-07-15 13:37:39.0",
                        "toBank": "999999",
                        "fromBank": "999999",
                        "sessionId": "",
                        "bankTransactionId": "*********"
                    }
                }

        else:

            try:
                response = requests.request(
                    "GET", url=url, headers=cls.headers, params=cls.params
                )

                resp = response.json()

            except requests.exceptions.RequestException as e:
                resp = {
                    "status": "658",
                    "error": "VFD Request Broken",
                    "message": f"{e}"
                }

        print(resp)
        return resp





    @classmethod
    def get_vfd_float_balance(cls, account_number=None):

        if settings.ENVIRONMENT == "development":

            return 200000

        else:


            filter_url = "wallet2/account/enquiry"
            url = cls.base_url + filter_url

            cls.params["accountNumber"] = account_number

            try:

                response = requests.request(
                    "GET", url=url, headers=cls.headers, params=cls.params, timeout=5
                )

                resp = response.json()

                if resp.get("status") == "00":
                    vfd_balance = float(resp.get("data").get("accountBalance"))
                else:
                    vfd_balance = None

                return vfd_balance

            except requests.exceptions.RequestException as err:
                vfd_balance = None

                return vfd_balance



    @classmethod
    def get_all_bank_list(cls):

        filter_url = "wallet2/bank"
        url = cls.base_url + filter_url

        try:
            response = requests.request(
                "GET", url=url, headers=cls.headers, params=cls.params
            )

            res = response.json()
            return {
                "main_data": res,
            }

        except requests.exceptions.RequestException as err:
            return {
                "main_data": {
                    "status": "99",
                    "message": str(err)
                },
            }


    @classmethod
    def update_bvn_nin(cls, account_number, bvn, utype, nin:str=None, dob:str=None):

        filter_url = "wallet2/client/upgrade"
        url = cls.base_url + filter_url


        if utype == VfdAccountUpdateType.RECOMPLY and dob:
            payload = {
                "accountNo": account_number,
                "dob": dob,
                "action":"Recomply-With-BVN",
            }

        else:
            payload = {
                "accountNo": account_number,
                "action": "Update-BVN"
            }


        if nin:
            payload["nin"] = nin
        else:
            payload["bvn"] = bvn


        try:
            response = requests.request(
                "POST", url=url, headers=cls.headers, params=cls.params, json=payload
            )
            res = response.json()

            return {
                "main_data": res,
            }

        except requests.exceptions.RequestException as err:
            return {
                "main_data": {
                    "status": "99",
                    "message": str(err)
                },
            }


    # @classmethod
    # def get_account_transactions(cls, page: int, from_date: str, to_date: str, transaction_type: str, account_num: str=None):


    #     filter_url = "wallet2/account/transactions"
    #     url = cls.base_url + filter_url


    #     cls.params["page"] = page
    #     cls.params["fromDate"] = from_date
    #     cls.params["toDate"] = to_date
    #     cls.params["transactionType"] = transaction_type

    #     if account_num:
    #         cls.params["accountNo"] = account_num

    #     print(url)
    #     try:
    #         response = requests.request(
    #             "GET", url=url, headers=cls.headers, params=cls.params
    #         )

    #         res = response.json()
    #         return res

    #     except requests.exceptions.RequestException as err:
    #         return {
    #             "status": "9999",
    #             "url": url,
    #             "message": str(err)
    #         }


    @classmethod
    def get_account_transactions(cls, params: dict):
        from urllib.parse import urlencode



        filter_url = "wallet2/account/transactions"
        url = cls.base_url + filter_url

        # cls.params["page"] = page
        # cls.params["size"] = page
        # cls.params["fromDate"] = from_date
        # cls.params["toDate"] = to_date
        # cls.params["transactionType"] = transaction_type

        params.update(cls.params)

        encoded_params = urlencode(params).replace('+', '%20')
        final_url = f'{url}?{encoded_params}'

        # print(final_url)

        try:
            response = requests.request(
                "GET", url=final_url, headers=cls.headers
            )

            # request_info = response.request

            # # Use httpie to print the equivalent curl command
            # headers_str = '\n'.join([f"{key}: {value}" for key, value in request_info.headers.items()])
            # curl_command = f"curl -X {request_info.method} '{request_info.url}' -H '{headers_str}'"

            # print("Equivalent curl command:")
            # print(curl_command)


            res = response.json()
            return res

        except requests.exceptions.RequestException as err:
            return {
                "status": "9999",
                "url": url,
                "message": str(err)
            }

    @classmethod
    def get_account_with_bvn(cls, bvn: str):

        filter_url = f"wallet2/client?bvn={bvn}"
        url = cls.base_url + filter_url

        if settings.ENVIRONMENT == "development":
            return {
                "main_data": {
                    "data": {
                        "dateOfBirth": "20000"
                    }
                }
            }

        try:
            response = requests.request(
                "GET", url=url, headers=cls.headers, params=cls.params
            )
            res = response.json()

            return {
                "main_data": res,
            }

        except requests.exceptions.RequestException as err:
            return {
                "main_data": {
                    "status": "99",
                    "message": str(err)
                },
            }


    @classmethod
    def trigger_inflow_repush(cls, session_id):
        filter_url = "wallet2/transactions/repush"
        url = cls.base_url + filter_url
        payload = {
            "transactionId": "",
            "sessionId" : session_id,
            "pushIdentifier":"sessionId"
        }

        try:
            response = requests.request(
                "POST", url=url, headers=cls.headers, params=cls.params, json=payload
            )
            res = response.json()
            return{
                "main_data": res
            }
        except requests.exceptions.RequestException as err:
            return {
                "main_data": {
                    "status": "99",
                    "message": str(err)
                },
            }




