from django.conf import settings
from time import sleep
import requests
import json
import uuid
import random
import hashlib
import datetime
from django.core.cache import cache


class WemaBank:
    base_url = settings.CORE_BANKING_BASE_URL
    email = settings.CORE_BANKING_EMAIL
    password = settings.CORE_BANKING_PASSWORD
    base_headers = {}

    @classmethod
    def login(cls):
        url = cls.base_url + "/api/v1/companies/auth/login/"
        payload = {
            "email": cls.email,
            "password": cls.password
        }

        try:
            response = requests.request(
                "POST", url=url, data=payload, timeout=15
            )
            res = response.json()
            return res.get("data", {}).get("access")

        except requests.exceptions.RequestException as err:
            return None

    @classmethod
    def verify_token(cls, access_token):
        url = cls.base_url + "/api/v1/companies/auth/verify_token/"
        headers = {
            **cls.base_headers,
            "Authorization": f"Bearer {access_token}"
        }

        try:
            response = requests.request(
                "GET", url=url, headers=headers, timeout=15
            )
            return True if response.status_code == 200 else False

        except requests.exceptions as err:
            return False

    @classmethod
    def get_access_token(cls):

        # cache_key = 'CORE_BANKING_ACCESS_TOKEN'
        # access_token = cache.get(cache_key)
        # verify = cls.verify_token(access_token=access_token)

        # if access_token is None or not verify:
        #     access_token = cls.get_access_token()
        #     cache.set(cache_key, access_token)

        access_token = cls.login()

        return access_token

    @classmethod
    def _create_wema_account(cls, user, acct_num_count, overide=False):

        url = cls.base_url + "api/v1/wema/app2/virtual_accounts/"
        headers = {
            **cls.base_headers,
            "Authorization": f"Bearer {cls.get_access_token()}"
        }

        first_name = user.check_kyc.bvn_rel.bvn_first_name
        last_name = user.check_kyc.bvn_rel.bvn_last_name
        phone = user.phone_number
        email = user.email

        if acct_num_count < 1 or overide == True:
            bvn = user.check_kyc.bvn_rel.bvn_number
        else:
            number_to_add = (acct_num_count - 1) + 1
            bvn = f"{user.check_kyc.bvn_rel.bvn_number}-{number_to_add}"

        payload = {
            "first_name": first_name,
            "last_name": last_name,
            "phone": phone,
            "email": email,
            "bvn": bvn
        }

        if settings.ENVIRONMENT == "development":
            all_numbers = ["0","1","2","3","4","5","6","7","8","9"]

            get_account_num = ''.join(random.choice(all_numbers) for i in range(10))
            wema_create = {
                "main_data": {
                    "status": "00",
                    'message': 'Successful Creation',
                    "data": {
                        'firstname': first_name,
                        'middlename': "middlename",
                        'lastname': last_name,
                        'bvn': bvn,
                        'phone': phone,
                        "dob": "12-Aug-1954",
                        "accountNo": get_account_num
                    }
                },
                "initial_payload": payload
            }

            return wema_create

        else:

            try:
                response = requests.request(
                    "POST", url=url, headers=headers, data=payload, timeout=15
                )
                res = response.json()

                return {
                    "main_data": res,
                    "initial_payload": payload
                }

            except requests.exceptions.RequestException as err:
                return {
                    "main_data": {
                        "status_code": "99",
                        "message": str(err)
                    },
                    "initial_payload": payload
                }

            # {
            #     "status": "success",
            #     "status_code": 201,
            #     "data": {
            #         "message": "account created successfully.",
            #         "account_details": {
            #         "id": "bd0c941c-9aba-4e0b-8548-443f65922f4a",
            #         "created_at": "2023-11-09T15:55:41.261088+01:00",
            #         "updated_at": "2023-11-09T15:55:41.261088+01:00",
            #         "first_name": "Ciroma",
            #         "middle_name": null,
            #         "last_name": "Chukwuma",
            #         "bvn": null,
            #         "email": null,
            #         "phone": null,
            #         "date_of_birth": null,
            #         "account_number": "**********",
            #         "bank_name": "Wema Bank Plc",
            #         "company": "46abcb6d-a63f-4c40-a092-6b2fe2278292",
            #         "company_name": "GetLinked AI"
            #         }
            #     },
            #     "errors": null
            # }


    @classmethod
    def create_wema_account(cls, user, acct_num_count, overide=False):

        url = cls.base_url + "/api/v1/wema/virtual_accounts/"
        print(url)
        headers = {
            **cls.base_headers,
            "Authorization": f"Bearer {cls.get_access_token()}",
            'Content-Type': 'application/json'
        }
        print(headers)
        first_name = user.check_kyc.bvn_rel.bvn_first_name if user.check_kyc.bvn_rel.bvn_first_name else user.first_name
        last_name = user.check_kyc.bvn_rel.bvn_last_name if user.check_kyc.bvn_rel.bvn_last_name else user.last_name
        middle_name = "Nil"
        phone = user.phone_number
        email = user.email
        date_of_birth = "2000-12-31"

        if acct_num_count < 1 or overide == True:
            bvn = user.check_kyc.bvn_rel.bvn_number
        else:
            number_to_add = (acct_num_count - 1) + 1
            bvn = f"{user.check_kyc.bvn_rel.bvn_number}-{number_to_add}"

        payload = json.dumps({
            "first_name": first_name,
            "last_name": last_name,
            "middle_name": "-",
            "phone": phone,
            "email": email,
            "bvn": bvn,
            "date_of_birth": date_of_birth
        })

        if settings.ENVIRONMENT == "development":
            all_numbers = ["0","1","2","3","4","5","6","7","8","9"]

            get_account_num = ''.join(random.choice(all_numbers) for i in range(10))
            wema_create = {
                "main_data": {
                    "status": "00",
                    'message': 'Successful Creation',
                    "data": {
                        'firstname': first_name,
                        'middlename': "middlename",
                        'lastname': last_name,
                        'bvn': bvn,
                        'phone': phone,
                        "dob": "12-Aug-1954",
                        "accountNo": get_account_num
                    }
                },
                "initial_payload": payload
            }

            return wema_create

        else:

            try:
                response = requests.request(
                    "POST", url=url, headers=headers, data=payload, timeout=15
                )
                print(response.text)
                res = response.json()

                return {
                    "main_data": {
                                    "status": "00",
                                    "status_code": response.status_code,
                                    'message': 'Successful Creation',
                                    "data": {
                                        'firstname': first_name,
                                        'middlename': "-",
                                        'lastname': last_name,
                                        'bvn': bvn,
                                        'phone': phone,
                                        "dob": date_of_birth,
                                        "bank_code": res["data"]["account_details"]["bank_code"],
                                        "account_number": res["data"]["account_details"]["account_number"]
                                    }
                                },
                    "initial_payload": payload
                }

            except requests.exceptions.RequestException as err:
                return {
                    "main_data": {
                        "status_code": "99",
                        "message": str(err)
                    },
                    "initial_payload": payload
                }

            # {
            #     "status": "success",
            #     "status_code": 201,
            #     "data": {
            #         "message": "account created successfully.",
            #         "account_details": {
            #         "id": "bd0c941c-9aba-4e0b-8548-443f65922f4a",
            #         "created_at": "2023-11-09T15:55:41.261088+01:00",
            #         "updated_at": "2023-11-09T15:55:41.261088+01:00",
            #         "first_name": "Ciroma",
            #         "middle_name": null,
            #         "last_name": "Chukwuma",
            #         "bvn": null,
            #         "email": null,
            #         "phone": null,
            #         "date_of_birth": null,
            #         "account_number": "**********",
            #         "bank_name": "Wema Bank Plc",
            #         "company": "46abcb6d-a63f-4c40-a092-6b2fe2278292",
            #         "company_name": "GetLinked AI"
            #         }
            #     },
            #     "errors": null
            # }

    @classmethod
    def wema_transaction_verification_handler(cls, reference, fail=False):
        url = cls.base_url + "/accounts/transaction/verify/"
        headers = {
            **cls.base_headers,
            "Authorization": f"Bearer {cls.get_access_token()}"
        }

        cls.params["reference"] = reference

        if settings.ENVIRONMENT == "development":
            if fail == True:
                resp = {
                    "status": "108",
                    "message": "No Transaction!",
                }

            else:
                resp = {
                    "status": "00",
                    "message": "Successful Transaction Retrieval",
                    "data": {
                        "TxnId": reference,
                        "amount": "1020.0",
                        "accountNo": "**********",
                        "transactionStatus": "00",
                        "transactionDate": "2022-07-15 13:37:39.0",
                        "toBank": "999999",
                        "fromBank": "999999",
                        "sessionId": "",
                        "bankTransactionId": "*********"
                    }
                }

        else:

            try:
                response = requests.request(
                    "GET", url=url, headers=cls.headers, params=cls.params
                )

                resp = response.json()

            except requests.exceptions.RequestException as e:
                resp = {
                    "status": "658",
                    "error": "VFD Request Broken",
                    "message": f"{e}"
                }

        print(resp)
        return resp

    @classmethod
    def get_vfd_float_balance(cls, account_number=None):

        if settings.ENVIRONMENT == "development":
            return 5000
        else:
            filter_url = "wallet2/account/enquiry"
            url = cls.base_url + filter_url
            cls.params["accountNumber"] = account_number
            try:

                response = requests.request(
                    "GET", url=url, headers=cls.headers, params=cls.params, timeout=5
                )

                resp = response.json()

                if resp.get("status") == "00":
                    vfd_balance = float(resp.get("data").get("accountBalance"))
                else:
                    vfd_balance = None

                return vfd_balance

            except requests.exceptions.RequestException as err:
                vfd_balance = None

                return vfd_balance




