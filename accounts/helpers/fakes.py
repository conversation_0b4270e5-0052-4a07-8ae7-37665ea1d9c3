from datetime import datetime
from datetime import date






# print(1 % 5)
# print(0 % 5)
# # # abc = date(2022, 4, 2)
# abc = datetime.now()
# month, year = (abc.month-1, abc.year) if abc.month != 1 else (12, abc.year-1)
# pre_month = abc.replace(day=1, month=month, year=year)
# print(pre_month)


# print(1/100)
# amount = "23000"
# s = amount.replace(',', '')

# print(s)


# def add_up_num(num):
#     start = 2
#     result = 1
#     while start != num+1:
#         result += start
#         start += 1
    
#     return result

# print(add_up_num(5))


# include <stdio.h>

# int main() {

#     int num;
#     printf("Enter an integer: ");
#     scanf("%d", &num);
    
#     int start = 2;
#     int result = 1;

#     while(start != num+1){
#         result += start;
#         start += 1;
#     }
    
#     printf("%d", result);
#     return 0;
# }





# def add_up_num(num):
#     result = 0

#     while num != 3:
#         num = int(input("Enter your value: "))
#         result += num

#         print(result)
    
#     return "Got to '3'"

# print(add_up_num(5))


# include <stdio.h>

# int main() {

#     int num;
#     int result = 0;

#     while(num != 3){
#         printf("Enter an integer: ");
#         scanf("%d", &num);
        
#         result += num;
#     }
    
#     printf("%d", result);
#     return 0;
# }





# def seperate_string(string):
#     delim = ','
#     new_string = ''

#     for str in list(string):
#         new_string += (str + delim)
    
#     return(new_string[:-1])

# my_string = "cool"
# print(seperate_string(my_string))


# include <stdio.h>
# include <string.h>

# int main() {
    
#     char myString[50];
#     printf("Enter a string: ");
#     scanf("%s", &myString);
    
#     for (int i = 0; i < strlen(myString); i++) {
#         printf("%c,", myString[i]);
        
#     }
    
#     return 0;
# }









# def count_numbers():

#     for num in range(95):
#         if num == 65:
#             break
#         else:
#             print(num)

#     return "Ended at 65"

# print(count_numbers())


#include <stdio.h>
#include <string.h>


# int main() {
    
#     // char myString[50];
#     // printf("Enter a string: ");
#     // scanf("%s", &myString);
    
#     for (int i = 0; i <= 95; i++) {
#         if (i == 66){
#             break;
#         } else {
#             printf("%d\n", i);
#         }
#     }
    
#     return 0;
# }






# import pandas as pd


# file = pd.read_excel("employee_upload.xlsx")

# employee_list = []

# for i, data in file.iterrows():
#     data = {
#         "firstname": data["firstname"],
#         "lastname": data["lastname"],
#         "email": data["email"],
#         "phone_number": data["phonenumber"],
#         "gross_monthly_salay": data["gross_monthly_salary"],
#         "account_number": data["account_number"],
#         "bank_code": data["bank_code"],
#         "bank_name": data["bank_name"],
#     }

#     employee_list.append(data)

# print(employee_list)

# from rest_framework.parsers import JSONParser, MultiPartParser




# @permission_classes((IsAuthenticated,))
# @parser_classes([MultiPartParser])
# def upload_csv(request):
    	
#     try:
#         csv_file = request.FILES["csv_file"]
#         group_id    = request.POST.get("group")

#         if not csv_file.name.endswith('.csv'):
#             response = {"message":'File is not CSV type'}

#             return Response(data=response, status=status.HTTP_400_BAD_REQUEST)

#         #if file is too large, return
#         if csv_file.multiple_chunks():
#             response = "Uploaded file is too big (%.2f MB)." % (csv_file.size/(1000*1000),)
#             return Response(status=status.HTTP_406_NOT_ACCEPTABLE)

#         result = Group.process_contacts_from_csv(csv_file)
        
#         if result:

#             Group.add_contacts(group_id, result['contacts'])

#         response = {
#                     "message":'Contacts added',
#                     "errors": result["errors"],
#                     "added": result["total_added"]
#                     }

#         return Response(data=response, status=status.HTTP_202_ACCEPTED)

#     except Exception as e:

#         response = {"message":'Processing Error : '+repr(e)}

#         return Response(data=response, status=status.HTTP_400_BAD_REQUEST)




# import pandas as pd


# file = pd.read_excel("employee_upload.xlsx")

# employee_list = []

# for i, data in file.iterrows():
#     data = {
#         "firstname": data["firstname"],
#         "lastname": data["lastname"],
#         "email": data["email"],
#         "phone_number": data["phonenumber"],
#         "gross_monthly_salay": data["gross_monthly_salary"],
#         "account_number": data["account_number"],
#         "bank_code": data["bank_code"],
#         "bank_name": data["bank_name"],
#     }

#     employee_list.append(data)

# print(employee_list)


# ['*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************']




# def get_sum(n):
#     elements = []
#     for j in range(n + 1):
#         for i in range(n+1):
#             if i >= j:
#                 element = (2*j) + i + 1
#                 elements.append(element)
#             else:
#                 continue
    
#     return sum(elements)

# print(get_sum(2))