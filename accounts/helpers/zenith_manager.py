# import json
# import redis
# from datetime import timedelta
# from requests import exceptions, request

# from django.conf import settings


# def zenith_make_request(request_type: str, params: dict) -> dict:
#     try:
#         response = request(request_type, **params)
#         return response.json()
#     except exceptions.RequestException as error:
#         return {
#             "error": "Zenith Bank Request Broken",
#             "message": f"{error}"
#         }


# # Zenith API Specification(s).
# class ZenithBank:
#     """
#     - Access is granted to only whitelisted IP(s)
#     - Authorization header uses JWT
#     """
#     base_url = False
#     source_account = settings.ZENITH_SOURCE_ACCOUNT

#     if settings.ENVIRONMENT == "development":
#         base_url = settings.ZENITH_BASE_URL
#         username = settings.ZENITH_USER
#         password = settings.ZENITH_PROTECTOR

#     elif settings.ENVIRONMENT == "production":
#         base_url = settings.ZENITH_BASE_URL_LIVE
#         username = settings.ZENITH_USER_LIVE
#         password = settings.ZENITH_PROTECTOR_LIVE

#     headers = {
#         "Content-type": "application/json",
#         "accept": "application/json"
#     }

#     @classmethod
#     def generate_token(cls):
#         """
#         Generates Token for subsequent API calls.
#         Token is valid for 24 hours/1 day.
#         """
#         endpoint = "/api/authentication/getToken"
#         url = f"{cls.base_url}{endpoint}"

#         payload = json.dumps(
#             {
#                 "userIdentifyer": cls.username,
#                 "userProtector": cls.password
#             }
#         )
#         response = zenith_make_request("POST", dict(
#             url=url, headers=cls.headers, data=payload))
#         if (
#             response.get("responseCode") == "00"
#             and response.get("responseDescription") == "SUCCESS"
#         ):
#             token = response.get("tokenDetail").get("token")
#             if token is not None:
#                 return dict(status=True, token=token)
#             return dict(status=False, response=response)
#         return dict(status=False, response=response)

#     @classmethod
#     def login(cls):
#         """
#         Redis storage medium for zenith user token.
#         """
#         redis_db = redis.StrictRedis(
#             host="localhost", port="6379", db=0, decode_responses=True, encoding="utf-8"
#         )

#         zenith_user_token = redis_db.get("zenith_user_token")
#         if zenith_user_token is None:
#             get_token = cls.generate_token()
#             if get_token.get("status") == True:
#                 token = get_token.get("token")
#                 redis_db.set("zenith_user_token", token, ex=timedelta(days=1))
#                 return get_token
#             else:
#                 return get_token
#         else:
#             response = {
#                 "status": True,
#                 "token": zenith_user_token
#             }
#             return response

#     @classmethod
#     def initiate_payout(
#         cls,
#         beneficiary_account_name,
#         beneficiary_nuban,
#         beneficiary_bank_code,
#         source_account,
#         narration,
#         amount,
#         transfer_type,
#         user_bvn,
#         reference,
#         transfer_leg=None,
#         bank_name=None
#     ):
#         """
#         Currency: NGN
#         Transfer from source account to Nigerian banks.
#         """
#         if settings.ENVIRONMENT == "development":
#             return {
#                 "status": "00",
#                 "message": "****************",
#                 "data": {
#                     "txnId": reference,
#                     "sessionId": "090110226514433719724158835748",
#                     "reference": reference
#                 }
#             }

#         authenticate = cls.login()

#         if authenticate.get("status") == True:
#             token = authenticate.get("token")
#             cls.headers["Authorization"] = f"Bearer {token}"

#             if transfer_type == "intra":
#                 zenith_endpoint = "/api/transaction/zenithTransfer"
#                 url = f"{cls.base_url}{zenith_endpoint}"

#                 payload = json.dumps(
#                     {
#                         "amount": amount,
#                         "bankName": bank_name,
#                         "crAccount": beneficiary_nuban,
#                         "description": narration,
#                         "drAccount": source_account,
#                         "transactionReference": reference
#                     }
#                 )
#                 response = zenith_make_request("POST", dict(
#                     url=url, headers=cls.headers, data=payload))
#                 return dict(status=True, response=response)

#             if transfer_type == "inter":
#                 others_endpoint = "/api/transaction/otherBankTransfer"
#                 url = f"{cls.base_url}{others_endpoint}"

#                 payload = json.dumps(
#                     {
#                         "amount": amount,
#                         "bankCode": beneficiary_bank_code,
#                         "bankName": bank_name,
#                         "crAccount": beneficiary_nuban,
#                         "description": narration,
#                         "drAccount": source_account,
#                         "transactionReference": reference
#                     }
#                 )
#                 response = zenith_make_request("POST", dict(
#                     url=url, headers=cls.headers, data=payload))
#                 return dict(status=True, response=response)
#         return dict(status=False, response="Invalid or Expired Token.")

#     @classmethod
#     def get_transaction_details(cls, reference: str):
#         """
#         Fetch transaction details of (NGN) transfer.
#         """
#         if settings.ENVIRONMENT == "development":
#             resp = {
#                 "status": "00",
#                 "message": "Successful Transaction Retrieval",
#                 "data": {
#                     "TxnId": reference,
#                     "amount": "10.0",
#                     "accountNo": "**********",
#                     "transactionStatus": "00",
#                     "transactionDate": "2022-07-15 13:37:39.0",
#                     "toBank": "999999",
#                     "fromBank": "999999",
#                     "sessionId": "",
#                     "bankTransactionId": "*********"
#                 }
#             }

#         endpoint = "/api/enquiry/transaction"
#         url = f"{cls.base_url}{endpoint}"
#         authenticate = cls.login()

#         if authenticate.get("status") == True:
#             token = authenticate.get("token")
#             cls.headers["Authorization"] = f"Bearer {token}"

#             payload = json.dumps(
#                 {
#                     "transactionReference": reference
#                 }
#             )
#             response = zenith_make_request(request_type="POST", params=dict(
#                 url=url, headers=cls.headers, data=payload))
#             return dict(status=True, response=response)
#         return dict(status=False, response="Invalid or Expired Token.")

#     @classmethod
#     def get_account_balance(cls, account_number=None):
#         """
#         Fetch source account balance.
#         """
#         if settings.ENVIRONMENT == "development":
#             return 5000

#         endpoint = "/api/enquiry/balance"
#         url = f"{cls.base_url}{endpoint}"
#         authenticate = cls.login()

#         if authenticate.get("status") == True:
#             token = authenticate.get("token")
#             cls.headers["Authorization"] = f"Bearer {token}"

#             payload = json.dumps(
#                 {
#                     "accountNumber": account_number
#                 }
#             )
#             response = zenith_make_request("POST", dict(
#                 url=url, headers=cls.headers, data=payload))
#             return dict(status=True, response=response)
#         return dict(status=False, response="Invalid or Expired Token.")
