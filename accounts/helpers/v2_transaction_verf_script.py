from django.db import transaction as django_transaction
from django.db.models import Q
from django.db.models.query import QuerySet
from django.core.cache import cache
from main.models import ConstantTable
from accounts.models import Escrow, OtherCommissionsRecord, OtherAppTransNotify, Transaction, TransferVerificationObject, WalletSystem, AccountSystem, InAppTransactionNotification, DeletedReference, PromoCodeData, DebitCreditRecordOnAccount
from accounts.helpers.vfdbank_manager import VFDBank
from accounts.tasks import send_money_external_bank_transfer_task
from liberty_pay.settings import cloud_messaging
import uuid
import json
import ast



def push_out_new_legs_from_verf(response_data):
    
    account_provider = response_data["account_provider"]
    liberty_reference = response_data["liberty_reference"]
    transaction_status_check = response_data["status"]
    transaction_leg = response_data["transaction_leg"]
    transaction_type = response_data["transaction_type"]
    trans_bank_id = response_data["trans_bank_id"]
    transaction_status_code = response_data["transaction_status_code"]
    escrow_id = response_data["escrow_id"]



    data = response_data["payload"]
    try:
        verf_timestamp = ast.literal_eval(data).get("data", {}).get("transactionDate")
    except TypeError:
        verf_timestamp = data.get("data", {}).get("transactionDate")
    except:
        verf_timestamp = None


    get_all_trans_verf_qs = TransferVerificationObject.objects \
        .select_related("transaction_instance") \
        .filter(escrow_id=escrow_id)

    transaction = get_all_trans_verf_qs.get(liberty_reference=liberty_reference)
    escrow_instance = (
        transaction.transaction_instance.escrow_instance 
        or Escrow.objects.filter(escrow_id=escrow_id).last()
    )


    # Try Caching This

    transaction.is_verified = True
    transaction.trans_status_code = transaction_status_code
    transaction.transaction_instance.is_verified = True
    transaction.transaction_sub_type = transaction.transaction_instance.transaction_sub_type


    liberty_reversal_reference = Transaction.create_liberty_reference(suffix="LGLP-RVS")


    if escrow_instance and transaction:
        user_instance = escrow_instance.user
        user_wallet_instance = WalletSystem.objects.get(user=user_instance, wallet_type="COLLECTION")


        if transaction.is_finished_verification == True:
            pass
        else:
        
            print("I am Success")
            if transaction_status_check == "SUCCESSFUL": 
                transaction.is_finished_verification = True


                if transaction_leg == "EXTERNAL" \
                    and transaction_type == "SEND_BANK_TRANSFER" \
                        and escrow_instance.external_escrow == True:

                    print("GOT HERRRE")
                    print("GOT HERRRE")
                    print("GOT HERRRE")
                    wallet_type = escrow_instance.from_wallet_type
                    total_amount = escrow_instance.amount + escrow_instance.liberty_commission


                    source_account = AccountSystem.get_float_account(
                        from_wallet_type="FLOAT",
                        from_provider_type=escrow_instance.account_provider
                    )

                    escrow_instance.internal_escrow = False
                    escrow_instance.external_escrow = False
                    escrow_instance.save()
        
                    # Send Debit Alert
                    send_alert = WalletSystem.transaction_alert_notfication_manager(
                        user=user_instance,
                        amount = total_amount,
                        liberty_commission = escrow_instance.liberty_commission,
                        cr_dr = "DR",
                        narration = escrow_instance.narration,
                        from_wallet_type = wallet_type,
                        transaction_instance_id=transaction.transaction_instance.transaction_id
                    )


                    # Generate Promo Code

                    promo_code = PromoCodeData.generate_promo_code(
                        user=user_instance,
                        transaction_id=transaction.transaction_instance.transaction_id,
                        trans_type=transaction.transaction_instance.transaction_type
                    )
                    
                    # Change transaction status depending on response
                    transaction.transaction_instance.status = "SUCCESSFUL"
                    transaction.transaction_instance.leg_two_verification_payload = json.dumps(data)  
                    transaction.transaction_instance.unique_reference = trans_bank_id
                    transaction.transaction_instance.promo_code = promo_code
                    
                    transaction.transaction_ver_status = "SUCCESSFUL"
                    transaction.verification_payload = json.dumps(data) 
                    transaction.unique_reference = trans_bank_id 
                    transaction.verf_timestamp = verf_timestamp 
                    transaction.second_leg_done = True

                    transaction.transaction_instance.save()
                    transaction.save()

                    
                    
                    not_token=escrow_instance.user.firebase_key
                    not_title="Transaction Successful"
                    not_body=f"You have successfully transfered N{escrow_instance.amount} to {escrow_instance.to_account_name}. COMMISSION - {escrow_instance.liberty_commission}."
                    not_data={"amount_sent": f"{escrow_instance.amount}", "available_balance": f"{user_wallet_instance.available_balance}"}
                    
            
                    send_out_notification = cloud_messaging.send_broadcast(
                        token=not_token,
                        title=not_title,
                        body=not_body,
                        data=not_data
                    )

                    InAppTransactionNotification.create_in_app_transaction_notification(
                        user=escrow_instance.user,
                        title=not_title,
                        message_body=not_body            
                    )

                    print("I AM HERE NOW FOR COMMISSIONS")
                    print("I AM HERE NOW FOR COMMISSIONS")
                    print("I AM HERE NOW FOR COMMISSIONS")
                    

                    send_out_commission = WalletSystem.pay_commission_to_liberty(
                        user_id = escrow_instance.user.id,
                        wallet_id = escrow_instance.from_wallet_id,
                        wallet_type = wallet_type,
                        liberty_commission = escrow_instance.liberty_commission,
                        from_provider_type = account_provider,
                        get_source_account = source_account,
                        transaction_commission_id = transaction.transaction_instance.transaction_id,
                        get_escrow_id = escrow_instance.escrow_id,
                        transaction_sub_type="MAIN_TRSF_COMM"
                    )

                    if Transaction.objects.filter(escrow_id=escrow_id, transaction_sub_type="NO_RO_EXT_COMM").exists():
                        pass
                    else:
                
                        handle_agent_liberty_ro_commission = OtherCommissionsRecord.handle_extra_commissions_on_transfers(
                            user = escrow_instance.user,
                            amount = escrow_instance.amount,
                            transaction_instance_id = transaction.transaction_instance.transaction_id, 
                            wallet_id = escrow_instance.from_wallet_id,
                            wallet_type = wallet_type,
                            from_provider_type = account_provider,
                            get_escrow_id = escrow_instance.escrow_id
                        )
                    

                    total_verf_amount = escrow_instance.amount + (escrow_instance.send_money_transfer_fee if escrow_instance.send_money_transfer_fee else 0.00)

                    if transaction.transaction_instance.bank_float_balance_before:
                        bank_float_balance_after = transaction.transaction_instance.bank_float_balance_before - total_verf_amount
                    else:
                        bank_float_balance_after = None

                    OtherAppTransNotify.arrange_send_data_for_other_trnas_app_notify(
                        user = escrow_instance.user,
                        amount = total_verf_amount,
                        charge = 0.00,
                        balance = bank_float_balance_after if bank_float_balance_after is not None else 0.00,
                        entry = "DEBIT",
                        to_account = source_account.account_number,
                        desc = f"Funds Withdrawal For - {transaction.transaction_instance.beneficiary_account_name}",
                        trans_id = transaction.transaction_instance.transaction_id,
                        trans_type = transaction.transaction_instance.transaction_type
                    )



                elif transaction_leg == "COMMISSIONS" \
                    and transaction_type == "SEND_LIBERTY_COMMISSION":

                    escrow_instance.commissions_escrow = False
                    escrow_instance.save()

                    if escrow_instance.transfer_type == 'SEND_BANK_TRANSFER' and transaction.transaction_instance.transaction_sub_type not in ["NO_RO_EXT_COMM", "FND_BANK_COMM", "CARD_PUR_COMM"]:
                        bank_float_balance_after = None
                    else:
                        if transaction.transaction_instance.bank_float_balance_before:
                            bank_float_balance_after = transaction.transaction_instance.bank_float_balance_before - escrow_instance.amount
                        else:
                            bank_float_balance_after = None

                
                    # Change transaction status depending on response
                    transaction.transaction_instance.status = "SUCCESSFUL"
                    transaction.transaction_instance.unique_reference = trans_bank_id
                    transaction.transaction_instance.commissions_verification_payload = json.dumps(data)  
                    transaction.transaction_instance.bank_float_balance_after = bank_float_balance_after  

                    transaction.transaction_ver_status = "SUCCESSFUL"
                    transaction.unique_reference = trans_bank_id
                    transaction.verf_timestamp = verf_timestamp
                    transaction.verification_payload = json.dumps(data)  
                    transaction.commissions_leg_done = True


                    transaction.transaction_instance.save()
                    transaction.save()
        

                    print("commissions paid")

                    float_account = AccountSystem.get_float_account(
                        from_wallet_type="FLOAT",
                        from_provider_type=escrow_instance.account_provider
                    )

                    if transaction.transaction_instance.transaction_sub_type in ["NO_RO_EXT_COMM", "FND_BANK_COMM", "CARD_PUR_COMM"]:

                        total_verf_amount = transaction.transaction_instance.amount
                        OtherAppTransNotify.arrange_send_data_for_other_trnas_app_notify(
                            user = escrow_instance.user,
                            amount = total_verf_amount,
                            charge = 0.00,
                            balance = bank_float_balance_after if bank_float_balance_after is not None else 0.00,
                            entry = "DEBIT",
                            to_account = float_account.account_number,
                            desc = f"Funds Withdrawal For - {transaction.transaction_instance.beneficiary_account_name}",
                            trans_id = transaction.transaction_instance.transaction_id,
                            trans_type = transaction.transaction_instance.transaction_type
                        )



            elif transaction_status_check == "REVERSAL": 
                print("HEREEE TOOO")

               
                transaction.is_finished_verification = True
                    
                if transaction_leg == "EXTERNAL" \
                    and transaction_type == "SEND_BANK_TRANSFER" \
                        and escrow_instance.reversed == False:
                    
                    wallet_type = escrow_instance.from_wallet_type
                    
                    get_all_trans = Transaction.objects.filter(escrow_id=escrow_id)
                    with django_transaction.atomic():

                        escrow_instance.internal_escrow = True
                        escrow_instance.external_escrow = True
                        escrow_instance.reversed = True
                        escrow_instance.save()

                        transaction.transaction_instance.status = "SUCCESSFUL"
                        transaction.transaction_instance.is_reversed = True
                        transaction.transaction_instance.unique_reference = trans_bank_id
                        transaction.transaction_instance.leg_one_verification_payload = json.dumps(data)  

                        transaction.transaction_ver_status = "REVERSAL"
                        transaction.unique_reference = trans_bank_id
                        transaction.verf_timestamp = verf_timestamp
                        transaction.verification_payload = json.dumps(data)  

                        transaction.transaction_instance.save()
                        transaction.save()
                        

                        total_amount_removed = escrow_instance.amount + escrow_instance.liberty_commission
                        transaction_type = "REVERSAL_BANK_TRANSFER"
                        reversal_type = "FIRST_LEG_REVERSAL"


                        reversal_transaction_id = get_all_trans.filter(Q(transaction_type=transaction_type) | Q(reversal_type=reversal_type)).first()
                        if not reversal_transaction_id:
                            reversal_transaction_id = Transaction.objects.create(
                                user=escrow_instance.user,
                                account_provider=account_provider,
                                wallet_id=escrow_instance.from_wallet_id,
                                wallet_type=wallet_type,
                                transaction_type=transaction_type,
                                amount=total_amount_removed,
                                provider_fee=0.00,
                                liberty_reference=liberty_reversal_reference,
                                unique_reference=trans_bank_id,
                                beneficiary_account_name=escrow_instance.user_account_name,
                                beneficiary_nuban=escrow_instance.user_account_number,
                                total_amount_sent_out=total_amount_removed,
                                escrow_id=escrow_instance.escrow_id,
                                narration=f"REVERSAL FOR {escrow_instance.to_account_name}-{escrow_instance.to_nuban}/{escrow_instance.amount}/{escrow_instance.to_bank_name}",
                                status="SUCCESSFUL",
                                transaction_leg="REVERSAL",
                                reversal_type=reversal_type,
                                is_reversed=True
                            )
                            
                        else:
                            reversal_transaction_id.status = "SUCCESSFUL"
                            reversal_transaction_id.save()

                        if DebitCreditRecordOnAccount.objects.filter(transaction_instance_id=reversal_transaction_id.transaction_id).exists():
                            pass
                        else:
                            # Fund Wallet
                            reverse_transaction = WalletSystem.fund_balance(
                                user=escrow_instance.user,
                                wallet=user_wallet_instance,
                                amount=escrow_instance.amount + escrow_instance.liberty_commission,
                                trans_type=reversal_type,
                                transaction_instance_id = reversal_transaction_id.transaction_id,
                                unique_reference=f"{escrow_id}_RVSL"

                            )
                        

                        balance_before = reverse_transaction["balance_before"]

                        balance_after = WalletSystem.get_balance_after(
                            user = user_instance,
                            balance_before=balance_before,
                            total_amount=reversal_transaction_id.total_amount_sent_out,
                            is_credit=True
                        )

                        reversal_transaction_id.balance_before = balance_before
                        reversal_transaction_id.balance_after = balance_after
                        reversal_transaction_id.save()

                    # SMS REVERSALS
                    manage_reversal_sms = WalletSystem.transaction_alert_notfication_manager(
                        user = escrow_instance.user,
                        amount = escrow_instance.amount + escrow_instance.liberty_commission,
                        cr_dr = "CR",
                        narration = "LP-REVERSAL",
                        from_wallet_type = wallet_type,
                        transaction_instance_id = reversal_transaction_id.transaction_id
                    )


                elif transaction_leg == "COMMISSIONS" \
                    and transaction_type == "SEND_LIBERTY_COMMISSION":
                    
                    if transaction.trans_status_code == "108":
                        transaction.transaction_instance.status = "NOT_INITIATED"
                    else:
                        transaction.transaction_instance.status = "PENDING"

                    transaction.is_verified = False
                    transaction.is_finished_verification = False
                    transaction.verification_payload = json.dumps(data)


                    transaction.transaction_instance.save()
                    transaction.save()

                    pass
                    
                   

            elif transaction_status_check == "PENDING": 

                if transaction_leg == "EXTERNAL" \
                    and transaction_type == "SEND_BANK_TRANSFER" \
                        and escrow_instance.external_escrow == True:

                    transaction.transaction_instance.status = "PENDING"
                    transaction.transaction_instance.unique_reference = trans_bank_id
                    transaction.transaction_instance.leg_two_verification_payload = json.dumps(data)  
                    
                    transaction.transaction_ver_status = "PENDING"
                    transaction.verification_payload = json.dumps(data)  
                    transaction.unique_reference = trans_bank_id
                    transaction.verf_timestamp = verf_timestamp

                    transaction.transaction_instance.save()
                    transaction.save()



                elif transaction_leg == "COMMISSIONS" \
                    and transaction_type == "SEND_LIBERTY_COMMISSION":
                    
                    transaction.transaction_instance.status = "PENDING"
                    transaction.transaction_instance.unique_reference = trans_bank_id
                    transaction.transaction_instance.commissions_verification_payload = json.dumps(data)  

                    transaction.transaction_ver_status = "PENDING"
                    transaction.verification_payload = json.dumps(data)  
                    transaction.unique_reference = trans_bank_id
                    transaction.verf_timestamp = verf_timestamp

                    transaction.transaction_instance.save()
                    transaction.save()


            elif transaction_status_check == "NOT_FOUND": 
                print("GOT TO NOT FOUND")
                print("GOT TO NOT FOUND")
                print("GOT TO NOT FOUND")


                if transaction_leg == "EXTERNAL" \
                    and transaction_type == "SEND_BANK_TRANSFER" \
                        and escrow_instance.external_escrow == True:

                    transaction.transaction_instance.unique_reference = trans_bank_id
                    transaction.transaction_instance.leg_two_verification_payload = json.dumps(data)  
                    
                    transaction.transaction_ver_status = "NOT_FOUND"
                    transaction.unique_reference = trans_bank_id
                    transaction.verf_timestamp = verf_timestamp
                    transaction.verification_payload = json.dumps(data)  

                    transaction.transaction_instance.save()
                    transaction.save()




                elif transaction_leg == "COMMISSIONS" \
                    and transaction_type == "SEND_LIBERTY_COMMISSION":                    
                    transaction.transaction_instance.unique_reference = trans_bank_id
                    transaction.transaction_instance.commissions_verification_payload = json.dumps(data)  

                    transaction.transaction_ver_status = "NOT_FOUND"
                    transaction.unique_reference = trans_bank_id
                    transaction.verf_timestamp = verf_timestamp
                    transaction.verification_payload = json.dumps(data)  
                
                    transaction.transaction_instance.save()
                    transaction.save()


            else:
                transaction.verification_payload = json.dumps(data)  
                transaction.unique_reference = trans_bank_id
                transaction.verf_timestamp = verf_timestamp
                transaction.save()


        from accounts.tasks import update_transaction_summary
        update_transaction_summary.apply_async(
            queue="sms_queue",
            kwargs={
                "escrow_id": escrow_id,
            }
        )
        
    else:
        pass

    