from accounts.models import CommissionsRecord, Transaction, OtherCommissionsRecord
from main.models import User
from django_filters.rest_framework import FilterSet
import django_filters


class UserDataFilter(FilterSet):
    id = django_filters.NumberFilter(field_name="id", lookup_expr='exact')
    email = django_filters.CharFilter(field_name="email", lookup_expr='exact')
    type_of_user = django_filters.CharFilter(field_name="type_of_user", lookup_expr='exact')
    # Type of user filter

    class Meta:
        model = User
        fields = ["id", "email"]

class TransactionDateFilter(FilterSet):
    timestamp_gte = django_filters.IsoDateTimeFilter(field_name="date_created", lookup_expr='gte')
    timestamp_lte = django_filters.IsoDateTimeFilter(field_name="date_created", lookup_expr='lte')
    timestamp_exact = django_filters.DateRangeFilter(field_name="date_created", lookup_expr='exact')
    user_id = django_filters.NumberFilter(field_name="user_id", lookup_expr='exact')
    transaction_id = django_filters.CharFilter(field_name="transaction_id", lookup_expr='exact')
    status = django_filters.CharFilter(field_name="status", lookup_expr='exact')
    type_of_user = django_filters.CharFilter(field_name="type_of_user", lookup_expr='exact')
    wallet_type = django_filters.CharFilter(field_name="wallet_type", lookup_expr='exact')
    transaction_type = django_filters.CharFilter(field_name="transaction_type", lookup_expr='exact')

    class Meta:
        model = Transaction
        fields = [
            "user_id", "transaction_id", "timestamp_gte", "timestamp_lte", "transaction_type", "status", "type_of_user", "wallet_type",
            "transaction_type"
        ]

class EscrowDateFilter(FilterSet):
    timestamp_gte = django_filters.IsoDateTimeFilter(field_name="date_created", lookup_expr='gte')
    timestamp_lte = django_filters.IsoDateTimeFilter(field_name="date_created", lookup_expr='lte')
    timestamp_exact = django_filters.DateRangeFilter(field_name="date_created", lookup_expr='exact')



class CommissionsDateFilter(FilterSet):
    timestamp_gte = django_filters.IsoDateTimeFilter(field_name="date_created", lookup_expr='gte')
    timestamp_lte = django_filters.IsoDateTimeFilter(field_name="date_created", lookup_expr='lte')
    timestamp_exact = django_filters.DateRangeFilter(field_name="date_created", lookup_expr='exact')
    # category = django_filters.(field_name="date_created", lookup_expr='exact')

    class Meta:
        model = CommissionsRecord
        fields = ["timestamp_gte", "timestamp_lte", "bills_type"]

class OtherCommissionsRecordDateFilter(FilterSet):
    timestamp_gte = django_filters.IsoDateTimeFilter(field_name="date_created", lookup_expr='gte')
    timestamp_lte = django_filters.IsoDateTimeFilter(field_name="date_created", lookup_expr='lte')
    timestamp_exact = django_filters.DateRangeFilter(field_name="date_created", lookup_expr='exact')
    # category = django_filters.(field_name="date_created", lookup_expr='exact')

    class Meta:
        model = OtherCommissionsRecord
        fields = ["timestamp_gte", "timestamp_lte"]


# class CommissionsDateFilter(FilterSet):
#     timestamp_gte = django_filters.IsoDateTimeFilter(field_name="date_created", lookup_expr='gte')
#     timestamp_lte = django_filters.IsoDateTimeFilter(field_name="date_created", lookup_expr='lte')
#     timestamp_exact = django_filters.DateRangeFilter(field_name="date_created", lookup_expr='exact')
#     # category = django_filters.(field_name="date_created", lookup_expr='exact')

#     class Meta:
#         model = CommissionsRecord
#         fields = ["timestamp_gte", "timestamp_lte", "bills_type"]



class ForAllDateFilter(FilterSet):
    timestamp_gte = django_filters.IsoDateTimeFilter(field_name="date_created", lookup_expr='gte')
    timestamp_lte = django_filters.IsoDateTimeFilter(field_name="date_created", lookup_expr='lte')
    timestamp_exact = django_filters.DateRangeFilter(field_name="date_created", lookup_expr='exact')

    class Meta:
        fields = ["timestamp_gte", "timestamp_lte"]
    
class TransVerfDateFilter(FilterSet):
    timestamp_gte = django_filters.IsoDateTimeFilter(field_name="date_added", lookup_expr='gte')
    timestamp_lte = django_filters.IsoDateTimeFilter(field_name="date_added", lookup_expr='lte')
    timestamp_exact = django_filters.DateRangeFilter(field_name="date_added", lookup_expr='exact')

    class Meta:
        fields = ["timestamp_gte", "timestamp_lte"]


