from typing import List

from django.contrib.auth import get_user_model, authenticate
from django.core.cache import cache
from django.db.models.query import QuerySet
from rest_framework import serializers
from accounts.models import (
    Beneficiaries, POSRequest, TransactionPerformance, WalletSystem, Transaction, AirtimeToPinObject, MerchantDisbursements, FundingRecord, AirtimeToPinParent, AllBankList, BillsPaymentConstant, CommissionsRecord, InAppTransactionNotification,
    OtherCommissionsRecord, DailyRewardHistory, MonthlyRewardHistory, WeekHistory, LeaderBoard, OtherAppTransNotify, OtherServiceAccountSystem, CreditCardDetail, DebitCreditRecordOnAccount, UserOwnAccount, AutoSweepRecurringChargeTable, Escrow, TransferVerificationObject, SWEEP_INTERVAL_TYPES, RECUR_TYPES
)

from main.serializers import UserSerializerCardTransactionReport
from main.models import User, NewLocationList
from horizon_pay.models import CardTransaction, TerminalSerialTable

import json

class CardTransactionSerializer(serializers.ModelSerializer):
    class Meta:
        model = CardTransaction
        fields = ["reference_number", "pan_number"]

class TransactionHistorySerializer(serializers.ModelSerializer):
    transaction_type = serializers.CharField(read_only=True)
    wallet_type = serializers.CharField(read_only=True)
    fee = serializers.FloatField(read_only=True, source="liberty_commission")
    card_transactions = CardTransactionSerializer(read_only=True, many=True)
    bills_pay_data = serializers.SerializerMethodField()
    entry = serializers.SerializerMethodField()

    def get_entry(self, inst: Transaction):
        if any(inst.transaction_type in tup for tup in Transaction.DEBIT_TRANS) or any(inst.transaction_type in tup for tup in Transaction.OTHER_DEBIT_TRANS):
            return "DEBIT"
        else:
            return "CREDIT"

    def get_bills_pay_data(self, inst: Transaction):
        if inst.transaction_type == "BILLS_AND_PAYMENT":
            try:
                payload = eval(inst.payload)
            except Exception:
                payload = json.loads(inst.payload)
            except:
                payload = {}

            bills_data = {
                "ben_phone": payload.get("phoneNumber"),
                "biller": payload.get("biller"),
                "bills_type": payload.get("bills_type")
            }

        else:

            bills_data = None

        return bills_data

    class Meta:
        model = Transaction
        fields = [
            "user_email",
            "transaction_type",
            "entry",
            "amount",
            "fee",
            "sms_charge",
            "transaction_id",
            "wallet_type",
            "narration",
            "status",
            "balance_before",
            "balance_after",
            "beneficiary_account_name",
            "beneficiary_nuban",
            "beneficiary_bank_name",
            "source_account_name",
            "date_created",
            "vas_customer_message",
            "card_transactions",
            "bills_pay_data",
        ]

class EscrowHistorySerializer(serializers.ModelSerializer):

    class Meta:
        model = Escrow
        fields = [
            "date_created", "last_updated", "amount", "to_bank_name", "to_bank_code", "reversed", "internal_transaction_id", "external_transaction_id"
        ]

class InAppTransactionNotificationsHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = InAppTransactionNotification
        fields = ["title", "message", "date_created"]


class BuddyBeneficiariesSerializer(serializers.ModelSerializer):
    class Meta:
        model = Beneficiaries
        fields = ["id", "phone_number", "wallet_id", "account_name", "date_added"]


class AirtimePinSerializer(serializers.Serializer):
    NETWORK_PROVIDER_CHOICE = (
        ("MTN_NIGERIA", "MTN_NIGERIA"),
        ("AIRTEL_NIGERIA", "AIRTEL_NIGERIA"),
        ("GLO_NIGERIA", "GLO_NIGERIA"),
        ("NINE_MOBILE", "NINE_MOBILE"),
    )

    from_wallet_type = serializers.CharField(required=True)
    network_provider = serializers.ChoiceField(required=True, choices=NETWORK_PROVIDER_CHOICE)
    quantity = serializers.IntegerField(required=True)
    pin_amount = serializers.FloatField(required=True, min_value=30)
    total_pin_amount = serializers.FloatField(required=True, min_value=30)
    transaction_pin = serializers.CharField()


class AirtimeToPinObjectSerializer(serializers.ModelSerializer):
    batch_id = serializers.SerializerMethodField()

    def get_batch_id(self, inst: AirtimeToPinObject):
        return inst.biller_parent.batch_id
    class Meta:
        model = AirtimeToPinObject
        fields = ("batch_id", "card_pin", "serial_number", "transaction_status", "liberty_reference", "date_created", "last_updated")


class AirtimeToPinParentSerializer(serializers.ModelSerializer):
    airtime_pin_children = AirtimeToPinObjectSerializer(many=True)

    class Meta:
        model = AirtimeToPinParent
        fields = ["network", "airtime_pin_children", "total_pin_amount", "pin_amount", "is_successful"]

class BankTransferBeneficiariesSerializer(serializers.ModelSerializer):
    class Meta:
        model = Beneficiaries
        fields = [
            "id",
            "account_number",
            "account_name",
            "bank_name",
            "bank_code",
            "date_added",
        ]


class RemoveBeneficiariesSerializer(serializers.Serializer):
    id = serializers.IntegerField()

class BankFundingSerializer(serializers.Serializer):
    amount = serializers.FloatField(required=True)
    unique_reference = serializers.CharField(required=True)
    user_nuban = serializers.CharField(required=True)
    source_nuban = serializers.CharField(required=True)
    source_account_name = serializers.CharField(required=True)
    session_id = serializers.CharField(required=False)
    narration = serializers.CharField(required=True, allow_null=True, allow_blank=True)
    source_bank_code = serializers.CharField(required=True, allow_null=True)
    account_provider = serializers.CharField(required=True)
    provider_fee = serializers.FloatField(required=True, allow_null=True)
    transfer_status = serializers.CharField(required=True)
    provider_status = serializers.CharField(required=True)
    timestamp = serializers.CharField(required=True, allow_null=True)
    funding_payload = serializers.JSONField()
    float_inflow = serializers.BooleanField(required=False)

class GetVASPayableSerializer(serializers.Serializer):
    amount = serializers.FloatField(required=True, min_value=30)
    biller = serializers.CharField(required=True)

class BillerListSerializer(serializers.ModelSerializer):
    class Meta:
        model = BillsPaymentConstant
        fields = ["bills_type", "biller"]

class VASSerializer(serializers.Serializer):
    from_wallet_type = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    customerId = serializers.CharField(required=True)
    packageSlug = serializers.CharField(required=True)
    channel = serializers.CharField(required=True)
    amount = serializers.FloatField(required=True, min_value=30)
    customerName = serializers.CharField(required=True, allow_blank=True, allow_null=True)
    phoneNumber = serializers.CharField(required=True)
    bills_type = serializers.CharField(required=True)
    biller = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    local_channel = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    transaction_pin = serializers.CharField(required=True)
    tID = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    customer_reference = serializers.CharField(required=False, allow_null=True, allow_blank=True)

class LedgerHistorySerializer(serializers.Serializer):
    COMMISSION_TYPE_CHOICES = [
        ("BANK", "BANK"),
        ("CASH", "CASH")
    ]
    type_of_commission = serializers.ChoiceField(choices=COMMISSION_TYPE_CHOICES, required=True)
    amount = serializers.CharField(required=True)

class WEMAAcountLookupSerializer(serializers.Serializer):
    accountnumber = serializers.CharField(required=True, allow_null=False, allow_blank=False)

class BankListSerializer(serializers.ModelSerializer):
    class Meta:
        model = AllBankList
        fields = ["bank_code", "cbn_code", "name", "bank_short_name", "ussd_short_code"]

class TransferReferenceSerializer(serializers.Serializer):
    liberty_reference = serializers.CharField(required=True)

class TransactionManualResolveSerializer(serializers.Serializer):
    ids = serializers.CharField(required=True)

class RetriggerInflowSerializer(serializers.Serializer):
    start_id = serializers.IntegerField(required=True)
    end_id = serializers.IntegerField(required=True)

class TransactionEnquirySerializer(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        fields = [

            "status", "transaction_type", "liberty_reference", "unique_reference", "account_provider", "amount", "liberty_commission",
            "sms_charge", "total_amount_charged", "transaction_leg", "balance_before", "balance_after", "beneficiary_account_name",
            "source_account_name", "date_created",  "lotto_agent_user_id", "lotto_agent_user_phone", "type_of_user",
        ]

class CommissionsHistorySerializer(serializers.ModelSerializer):
    liberty_profit = serializers.FloatField(source="agent_cash_profit")
    class Meta:

        model = CommissionsRecord
        fields = [
            "amount",
            "biller",
            "bills_type",
            "liberty_profit",
            "balance_before",
            "balance_after",
            "date_created",
        ]

class TransactionPerformanceSerializer(serializers.ModelSerializer):
    class Meta:
        model = TransactionPerformance
        fields = "__all__"

class TransactionSerializerCardTransactionReport(serializers.ModelSerializer):
    user = UserSerializerCardTransactionReport(read_only=True)

    Tid = serializers.CharField(source="terminal_id")
    Amount = serializers.CharField(source="total_amount_charged")
    LibertyShare = serializers.CharField(source="liberty_commission")
    Fee = serializers.CharField(source="liberty_commission")
    Transaction = serializers.CharField(source="transaction_type")

    class Meta:
        model = Transaction
        fields = ["Tid", "Amount", "LibertyShare", "Fee", "user",
        "Transaction"]

class MerchantDisbursementsSerializer(serializers.ModelSerializer):
    class Meta:
        model = MerchantDisbursements
        exclude = ['user', 'wallet']

class InternalTrnasferManualResolveSerializer(serializers.Serializer):
    liberty_reference = serializers.CharField(required=True)

class OtherCommissionsRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = OtherCommissionsRecord
        fields = ["amount", "liberty_reference", "transaction_reason", "transaction_type", "ro_cash_profit",
            "agent_cash_profit", "transaction_owner", "balance_before", "balance_after", "date_created"
        ]

class FundingRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = FundingRecord
        fields = '__all__'
        extra_kwargs = {
            "amount": {"required": True, "allow_null": False},
            "record_type": {"required": True, "allow_null": False},
            "day": {"required": False, "allow_null": True}
        }

class ServiceTransactionHistorySerializer(serializers.ModelSerializer):
    transaction_type = serializers.CharField(read_only=True)
    wallet_type = serializers.CharField(read_only=True)
    fee = serializers.FloatField(read_only=True, source="liberty_commission")


    class Meta:
        model = Transaction
        fields = [
            "transaction_type",
            "liberty_reference",
            "unique_reference",
            "amount",
            "fee",
            "sms_charge",
            "transaction_id",
            "wallet_type",
            "narration",
            "status",
            "balance_before",
            "balance_after",
            "beneficiary_account_name",
            "source_account_name",
            "date_created",
            "last_updated",
        ]

class WalletSystemSerializer(serializers.ModelSerializer):
    class Meta:
        model = WalletSystem
        fields = ["wallet_type", "available_balance"]

class UserBalanceSerializer(serializers.ModelSerializer):
    wallets = WalletSystemSerializer(read_only=True, many=True)

    class Meta:
        model = User
        depth = 1
        fields = ("id", "wallets")

class UserBalanceFormSerializer(serializers.Serializer):
    user_ids = serializers.ListField(child = serializers.IntegerField())

class POSRequestSerializer(serializers.ModelSerializer):
    requested_pos = serializers.DictField(child=serializers.JSONField())

    class Meta:
        model = POSRequest
        fields = "__all__"
        extra_kwargs = {
            # 'relationship_officer_code': {'required': True},
            'pickup_option': {'required': True},
            'type_of_pos': {'required': True},
            'user': {'required': False}
        }


class PayForTerminalSerializer(serializers.Serializer):
    request_id = serializers.CharField(required=True)
    transaction_pin = serializers.CharField(required=True)
    amount = serializers.FloatField(required=True)


class DailyRewardHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = DailyRewardHistory
        fields = "__all__"

class MonthlyRewardHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = MonthlyRewardHistory
        fields = "__all__"

class WeekHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = WeekHistory
        fields = "__all__"

class LeaderBoardMobileSerializer(serializers.ModelSerializer):
    class Meta:
        model = LeaderBoard
        fields = "__all__"

class RecreateTransactionVerfObjectSerializer(serializers.Serializer):
    trans_ids = serializers.ListField(required=True)
    run_instant = serializers.BooleanField(default=False)

class BeginFundByUSSDSerializer(serializers.Serializer):
    TRANS_TYPE_CHOICE = (
        ("FUND_BY_USSD", "FUND_BY_USSD"),
        ("USSD_WITHDRAW", "USSD_WITHDRAW"),
    )

    trans_type = serializers.ChoiceField(required=True, choices=TRANS_TYPE_CHOICE)
    bank_code = serializers.CharField(required=True)
    amount = serializers.FloatField(required=True)

class VerifyFundByUSSDSerializer(serializers.Serializer):
    reference = serializers.CharField(required=True)

class NotifyAppTransSerializer(serializers.ModelSerializer):
    class Meta:
        model = OtherAppTransNotify
        fields = "__all__"


class ReceivingTransListSerializer(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        fields = [
            "lotto_agent_user_id", "lotto_agent_user_phone", "status", "transaction_type", "amount",
            "liberty_commission", "sms_charge", "balance_before", "balance_after", "source_account_name",
            "source_nuban", "liberty_reference", "is_reversed", "type_of_user",
        ]


class CreateSubAccountPersonalSerializer(serializers.Serializer):
    master_email = serializers.EmailField(required=False, allow_null=True, allow_blank=True)
    for_user_email = serializers.EmailField(required=False, allow_null=True, allow_blank=True)

class CreateCorporateAccountSerializer(serializers.Serializer):
    corporate_id = serializers.CharField(required=True)
    company_name = serializers.CharField(required=True)
    # incorp_date = serializers.CharField(required=True)
    # bvn = serializers.CharField(required=True)
    for_user_email = serializers.EmailField(required=True)


class CreatePersonalAccountSerializer(serializers.Serializer):
    user_id = serializers.CharField(required=True)
    provider = serializers.CharField(required=True)
    overide = serializers.BooleanField(required=False, allow_null=True)
    extra = serializers.BooleanField(required=False)

class GetOtherAccountSerializer(serializers.Serializer):
    user_id = serializers.SerializerMethodField()
    account_provider = serializers.CharField(required=True)
    account_type = serializers.CharField(required=True)
    account_number = serializers.CharField(required=True)
    available_balance = serializers.FloatField(required=True)
    account_number = serializers.CharField(required=True)
    bank_name = serializers.CharField(required=True)
    bank_code = serializers.CharField(required=True)
    is_test = serializers.BooleanField(required=True)
    is_active = serializers.BooleanField(required=True)

    def get_user_id(self, inst: OtherServiceAccountSystem):
        return inst.user.id
    class Meta:
        model = OtherServiceAccountSystem
        fields = [
            "user_id", "account_provider", "account_number", "account_name", "bank_name",
            "bank_code", "is_test", "is_active", "available_balance"
        ]


class LibertyRefListSerializer(serializers.Serializer):
    lib_refs = serializers.ListField(required=True)
    owner = serializers.EmailField(required=True)


class POSRequestHistorySerializer(serializers.ModelSerializer):

    class Meta:
        model = POSRequest
        fields = [
            "request_id",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["request_status"] = instance.get_request_history_status
        return representation


class GetTransWithEscrowSerializer(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        fields = [
            "status", "transaction_type", "amount", "escrow_id", "transaction_leg", "narration",
            "liberty_commission", "sms_charge", "balance_before", "balance_after", "beneficiary_nuban",
            "beneficiary_account_name", "beneficiary_bank_code", "liberty_reference", "is_reversed",
        ]

class EscrowRetrieveTransactionSerializer(serializers.ModelSerializer):

    commission = serializers.SerializerMethodField()
    beneficiary_nuban = serializers.SerializerMethodField()
    beneficiary_account_name = serializers.SerializerMethodField()
    beneficiary_bank_code = serializers.SerializerMethodField()

    class Meta:
        model = Escrow
        fields = [
            "transfer_type", "amount", "escrow_id", "narration",
            "commission", "balance_before", "balance_after", "beneficiary_nuban",
            "beneficiary_account_name", "beneficiary_bank_code", "customer_reference", "reversed", "bulk_id"
        ]


    def get_commission(self, instance: Escrow):
        return instance.send_money_transfer_fee

    def get_beneficiary_nuban(self, instance: Escrow):
        return instance.to_nuban

    def get_beneficiary_account_name(self, instance: Escrow):
        return instance.to_account_name

    def get_beneficiary_bank_code(self, instance: Escrow):
        return instance.to_bank_code


class GetCreditCardSerializer(serializers.ModelSerializer):
    class Meta:
        model = CreditCardDetail
        fields = ["bank_name", "account_name", "authorization_code", "exp_month", "exp_year", "brand", "card_type", "date_created"]


    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["masked_pan"] = f"{instance.bin}******{instance.card_last_four_digit}"
        return representation

class ViewDebitCreditRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = DebitCreditRecordOnAccount
        fields = ["user", "entry", "wallet_type", "balance_before", "amount", "balance_after", "type_of_trans",  "transaction_instance_id", "date_created"]


    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["user_email"] = instance.user.email
        return representation

class UserOwnAccountSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserOwnAccount
        fields = ["account_number", "bank_code", "date_created"]


class PostAutoSweepSerializer(serializers.Serializer):
    bank_name = serializers.CharField()
    bank_code = serializers.CharField()
    account_number = serializers.CharField()
    account_name = serializers.CharField()
    min_balance = serializers.FloatField()
    max_amount = serializers.FloatField()
    sweep_interval = serializers.ChoiceField(choices=SWEEP_INTERVAL_TYPES)
    recur_type = serializers.ChoiceField(choices=RECUR_TYPES)
    start_date = serializers.DateTimeField()
    sweep_hour = serializers.IntegerField()
    narration = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    transaction_pin = serializers.CharField()


class GetAutoSweepSerializer(serializers.ModelSerializer):

    class Meta:
        model = AutoSweepRecurringChargeTable
        fields = ["id", "recur_type", "start_date", "sweep_interval", "sweep_hour", "min_balance", "max_amount", "bank_name", "bank_code", "account_number", "account_name", "narration", "date_created"]


class WithdrawalByUSSDStatusQuerySerializer(serializers.Serializer):
    amount = serializers.FloatField(
        min_value=30.00, required=True
    )
    transaction_id = serializers.CharField(required=True)




class LibertyCardOperationsTransactionHistorySerializer(serializers.ModelSerializer):
    user_mail = serializers.SerializerMethodField()
    user_phone = serializers.SerializerMethodField()

    def get_user_mail(self, trns: Transaction):
        return trns.user.email

    def get_user_phone(self, trns: Transaction):
        return trns.user.phone_number


    class Meta:
        model = Transaction
        fields = [
            "user_mail",
            "user_phone",
            "transaction_type",
            "transaction_sub_type",
            "amount",
            "liberty_commission",
            "sms_charge",
            "provider_fee",
            "transaction_id",
            "narration",
            "status",
            "date_created",
            "payload"
        ]

class CoralUSSDLoginSerializer(serializers.Serializer):
    Username = serializers.CharField()
    Password = serializers.CharField()


class CreateLibertyReferenceSerializer(serializers.Serializer):
    prefix = serializers.CharField()

class HandleOOBSerializer(serializers.Serializer):
    account_provider = serializers.CharField()
    from_acct_no = serializers.CharField()
    to_acct_no = serializers.CharField()
    amount = serializers.FloatField()
    user_email = serializers.EmailField()
    pin = serializers.CharField()
    escrow_id = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    liberty_reference = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    old_liberty_reference = serializers.CharField(required=False, allow_null=True, allow_blank=True)


class PromoCodeTransactionModelDataSerializer(serializers.ModelSerializer):

    class Meta():
        fields = ["user_email", "transaction_id",]



class AjoUserBVNDetailsSerializer(serializers.Serializer):
    firstname = serializers.CharField()
    lastname = serializers.CharField()
    dob = serializers.DateField()
    address = serializers.CharField()
    gender = serializers.CharField()
    phone = serializers.CharField()
    bvn = serializers.CharField()


class CreateAjoUserAccountNumberSerializer(serializers.Serializer):
    unique_reference = serializers.CharField()
    account_name = serializers.CharField()
    ajo_collector = serializers.PrimaryKeyRelatedField(queryset=User.objects.all())
    bvn_details = AjoUserBVNDetailsSerializer(allow_null=True, write_only=True)
    corporate_id = serializers.CharField(required=False, allow_null=True, allow_blank=True)

class AdminCreateCorporateSerializer(serializers.Serializer):
    super_user = serializers.PrimaryKeyRelatedField(queryset=User.objects.all())
    new_user = serializers.PrimaryKeyRelatedField(queryset=User.objects.all())
    corporate_id = serializers.CharField()
    company_name = serializers.CharField()
    suffix = serializers.CharField(allow_null=True)
    get_location = serializers.PrimaryKeyRelatedField(queryset=NewLocationList.objects.all(), allow_null=True)



class HandleNoTransFoundAPIViewerializer(serializers.Serializer):
    account_provider = serializers.CharField()
    from_acct_no = serializers.CharField()
    to_acct_no = serializers.CharField()
    amount = serializers.FloatField()
    user_email = serializers.EmailField()
    pin = serializers.CharField()
    escrow_id = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    liberty_reference = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    old_liberty_reference = serializers.CharField(required=False, allow_null=True, allow_blank=True)


class GetVFDAccountTransactionsSerializer(serializers.Serializer):
    from_date = serializers.DateField()
    to_date = serializers.DateField()
    page = serializers.IntegerField()
    account_number = serializers.CharField(allow_blank=True, allow_null=True)
    transaction_type = serializers.CharField()


class TransVerfSerializer(serializers.ModelSerializer):

    class Meta:
        model = TransferVerificationObject
        fields = ["id", "liberty_reference", "beneficiary_nuban", "source_nuban", "bank_code", "transaction_leg", "trans_status_code"]

class ManualSendMoneyAdminSerializer(serializers.Serializer):
    trans_data = serializers.PrimaryKeyRelatedField(queryset=TransferVerificationObject.objects.all(), many=True)
    pin = serializers.CharField()
    use_task = serializers.BooleanField(required=False)


class AdminTransferSerializer(serializers.Serializer):
    from_account = serializers.CharField()
    to_account = serializers.CharField()
    amount = serializers.FloatField()
    pin = serializers.CharField()




class AllTransactionSerializer(serializers.ModelSerializer):

    class Meta:
        model = Transaction
        fields = "__all__"


class AllTransVerfSerializer(serializers.ModelSerializer):

    class Meta:
        model = TransferVerificationObject
        fields = "__all__"


class CommissionsRecordSerializer(serializers.ModelSerializer):

    class Meta:
        model = CommissionsRecord
        exclude = ["balance_before", "balance_after", "float_balance_before", "float_balance_after", "checked"]

class OtherCommissionsRecordSerializer(serializers.ModelSerializer):

    class Meta:
        model = OtherCommissionsRecord
        exclude = ["balance_before", "balance_after"]

class OtherCommissionsRecordExternalUpdateSerializer(serializers.Serializer):

    transaction_ref = serializers.CharField(max_length=255)
    transaction_type = serializers.CharField(max_length=255, default=False)
    transaction_reason = serializers.CharField(max_length=255, default=False)
    recipient = serializers.CharField(max_length=255)
    amount    = serializers.FloatField()
    profit    = serializers.FloatField()

    def validate_amount(self, value):
        if value <= 0:
            raise serializers.ValidationError("Amount must be greater than 0.")
        return value



class ReturnEscrowTransactions():
    serializer_class = EscrowRetrieveTransactionSerializer

    @classmethod
    def get_trans_with_escrow(cls, escrow_instance: Escrow):

        trans_details_qs = Transaction.objects.filter(escrow_id=escrow_instance)

        print(trans_details_qs, "MUA")

        int_trans = trans_details_qs.filter(transaction_leg="INTERNAL").last()
        ext_trans = trans_details_qs.filter(transaction_leg="EXTERNAL").last()
        rev_trans = trans_details_qs.filter(transaction_leg="REVERSAL").last()

        print("I got all the data needed^^^^^^^^^^")

        if rev_trans:
            if rev_trans.status != "SUCCESSFUL":
                new_status = "PENDING"
                status_code = "29" # pending_reversal_code
            else:
                new_status = "REVERSAL"
                status_code = "25" # successful_reversal_code


        elif ext_trans:
            if ext_trans.status == "SUCCESSFUL":
                new_status = "SUCCESSFUL"
                status_code = "00" # successful_external_transfer_code
            else:
                new_status = "PENDING"
                status_code = "09" # pending_external_transfer_code

        else:
            new_status = "PENDING"
            status_code = "19" # pending_internal_transfer_code

        sms_charge = int_trans.sms_charge if int_trans else 0.0

        serializer = cls.serializer_class(escrow_instance)
        data = serializer.data
        data["sms_charge"] = sms_charge
        data["status_code"] = status_code
        data["status"] = new_status
        data["internal_ref"] = int_trans.liberty_reference if int_trans else None
        data["external_ref"] = ext_trans.liberty_reference if ext_trans else None
        data["reversal_ref"] = rev_trans.liberty_reference if rev_trans else None


        print("I finished with checking trans^^^^^^^^^^")

        cache_key = f"{escrow_instance.escrow_id}_fetch_all_trans"
        cache.set(cache_key, data)

        return data




class VFDSessionIDInflowRetrySerializer(serializers.Serializer):
    session_id = serializers.CharField()

class RetriggerCallbackForOtherSerializer(serializers.Serializer):
    TRX_ID = "trx_id"
    REFERENCE = "reference"
    ID = "id"

    IDENTIFIER_TYPE_CHOICE = (
        (TRX_ID, "trx_id"),
        (REFERENCE, "reference"),
        (ID, "id"),
    )

    identifier = serializers.CharField()
    type = serializers.ChoiceField(required=True, choices=IDENTIFIER_TYPE_CHOICE)