from django.db import models, transaction as django_transaction, IntegrityError
from django.db.models import Q, Sum, QuerySet, F
from django.db.models.functions import TruncDate
from django.conf import settings
from django.utils import timezone
from django.core.cache import cache
from django.core.exceptions import ValidationError
from django.core.validators import MinValueValidator
from django_celery_beat.models import CrontabSchedule, PeriodicTask
from django.contrib.auth.hashers import make_password

from rest_framework.response import Response
from rest_framework import status

from accounts.helpers.helper_func import CyberPayClass, notify_admin_group, generate_unique_ids, get_previous_account_num_func, \
    deduct_commission_from_paybox_merchant, create_electronic_levy_transaction
from accounts.helpers.coral_pay_manager import ServicesVASApp
from accounts.helpers.test_send_money import test_create_test_verifiable_transaction_test
from accounts.helpers.woven_manager import Woven
from accounts.helpers.vfdbank_manager import VFDBank
from accounts.helpers.wema_bank_manager import WemaBank
from accounts.helpers.general_account_manager import paystack_verify_funding_transaction, send_transaction_notification_to_horizon_pay, \
    create_verifiable_transaction

from accounts.model_choices import TransferProviderType
from main.models import UnsentSMS, User, ConstantTable, SMSRecord, OtherServiceDetail, CorporateAccount, SuperAgentProfile, \
    list_of_allowed_terminal_users, UserOtherAccount
from main.model_choices import *
from main.helper.helper_function import credit_debit_alert_sms_to_user, standard_str_to_dt, mask_account_number, convert_num_to_currency

from liberty_pay.settings import cloud_messaging, notify_messaging
from datetime import datetime, timedelta, date

import uuid
import json
import pytz
import pandas as pd
import io
import random
import time

from model_utils import FieldTracker

VFD_PROVIDER = "VFD"
WOVEN_PROVIDER = "WOVEN"
FIDELITY_PROVIDER = "FIDELITY"

ACCOUNT_PROVIDERS = [(VFD_PROVIDER, "VFD"), (WOVEN_PROVIDER, "WOVEN"), (FIDELITY_PROVIDER, "FIDELITY")]

sending_list = ["SEND_BUDDY", "SEND_LOTTO_WALLET", "SEND_AJO_WALLET", "SEND_AJO_LOANS", "SEND_BANK_TRANSFER", "BILLS_AND_PAYMENT", "AIRTIME_PIN",
                "CARD_PURCHASE"]

receiving_list = [
    "REVERSAL_BUDDY", "REVERSAL_BANK_TRANSFER", "FUND_BUDDY", "FUND_LOTTO_WALLET", "FUND_AJO_WALLET", "FUND_AJO_LOANS",
    "FUND_BANK_TRANSFER", "FUND_PAYSTACK", "FUND_BY_USSD", "CARD_TRANSACTION_FUND", "USSD_WITHDRAW"
]

# WALLET SYSTEM


TIMEZONE = pytz.timezone(settings.TIME_ZONE)


class Lien(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    amount = models.FloatField()
    description = models.TextField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Lien for {self.user.email} - Amount: {self.amount} - {'Inactive' if self.is_active else 'Active'}"

    @classmethod
    def get_lien_amount(cls, user):
        result = cls.objects.filter(user=user, is_active=True).aggregate(total_amount=Sum('amount'))

        return result['total_amount'] or 0


class WalletSystem(models.Model):
    SPEND = "SPEND"
    COLLECTION = "COLLECTION"
    SAVINGS_WALLET = "SAVINGS"
    COMMISSIONS_WALLET = "COMMISSIONS"
    FLOAT_WALLET = "FLOAT"
    WHISPER_WALLET = "WHISPER"
    GENERAL_FLOAT_BEFORE = "GENERAL_FLOAT_BEFORE"
    GENERAL_FLOAT_AFTER = "GENERAL_FLOAT_AFTER"
    GENERAL_FLOAT = "GENERAL_FLOAT"
    MIN_COMM_EXC = "MIN_COMM_EXC"
    ELECTRONIC_LEVY_FLOAT = "ELECTRONIC_LEVY_FLOAT"
    OTHERS = "OTHERS"

    WALLET_TYPES = [
        (SPEND, "SPEND"),
        (COLLECTION, "COLLECTION"),
        (SAVINGS_WALLET, "SAVINGS"),
        (COMMISSIONS_WALLET, "COMMISSIONS"),
        (FLOAT_WALLET, "FLOAT"),
        (WHISPER_WALLET, "WHISPER"),
        (GENERAL_FLOAT_BEFORE, "GENERAL_FLOAT_BEFORE"),
        (GENERAL_FLOAT_AFTER, "GENERAL_FLOAT_AFTER"),
        (GENERAL_FLOAT, "GENERAL_FLOAT"),
        (MIN_COMM_EXC, "MIN_COMM_EXC"),
        (ELECTRONIC_LEVY_FLOAT, "ELECTRONIC_LEVY_FLOAT"),
        (OTHERS, "OTHERS"),
    ]

    user = models.ForeignKey(User, related_name="wallets", on_delete=models.CASCADE)
    wallet_id = models.UUIDField(default=uuid.uuid4, editable=False)
    wallet_type = models.CharField(max_length=300, choices=WALLET_TYPES)
    available_balance = models.FloatField(default=0.00, validators=[MinValueValidator(0.00)])
    hold_balance = models.FloatField(default=0.00)
    merchant_id = models.CharField(max_length=250, null=True, blank=True)
    comment = models.CharField(max_length=400, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    tracker = FieldTracker()
    callback = models.BooleanField(default=False)
    callback_url = models.URLField(blank=True, null=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.wallet_id}-{self.user}-{self.wallet_type}"

    def save(self, *args, **kwargs):

        try:
            super(WalletSystem, self).save(*args, **kwargs)
            if self.wallet_type == "COLLECTION":
                self.user.wallet_balance = self.available_balance
                try:
                    self.user.save()
                except:
                    pass

        except SyntaxError as err:
            print(err)
            details = f"ALERTTTTTTTTTTT!!\n\nALERTTTTTTTTTTT!!\nUser with email: {self.user.email} tried to save balance in a negative figure and has been suspended. Main Error {err}"

            # self.user.is_suspended = True
            # self.user.suspension_reason = details
            # self.user.save()

            User.suspend_user(
                user=self.user,
                reason=details
            )

            notify_admin_group(user=self.user, details=details)

            # raise Exception("Negative Balance Error")

            raise Exception(f"{err}")

    @classmethod
    def get_float_user(cls):
        """
        # This function gets the float user instance
        """
        user = User.objects.get(email=f"{settings.FLOAT_USER_EMAIL}")
        return user

    @classmethod
    def get_uncreated_wallets(cls, user):
        user_wallets_cache_id = f"{user.id}_wallets"
        wallet_list = cache.get(user_wallets_cache_id)

        if wallet_list is None:
            # get_all_wallets = WalletSystem.get_uncreated_wallets(user=user_instance)
            get_wallets = cls.objects.select_related("user").filter(user=user)
            wallet_list = []
            if get_wallets:
                for wallet in get_wallets:
                    wallet_list.append(wallet.wallet_type)

            cache.set(user_wallets_cache_id, wallet_list)

        return wallet_list

    @classmethod
    def get_available_wallet_types(cls):
        first_list = [item for t in cls.WALLET_TYPES for item in t]
        return [item for i, item in enumerate(first_list) if i % 2 == 0]

    @classmethod
    def create_spend_wallet(cls, user):
        wallet, created = WalletSystem.objects.get_or_create(user=user, wallet_type="SPEND")

        return wallet

    @classmethod
    def create_collection_wallet(cls, user):
        wallet, created = WalletSystem.objects.get_or_create(user=user, wallet_type="COLLECTION")

        return wallet

    @classmethod
    def create_savings_wallet(cls, user):
        wallet, created = WalletSystem.objects.get_or_create(user=user, wallet_type="SAVINGS")

        return wallet

    @classmethod
    def create_commissions_wallet(cls, user):
        wallet, created = WalletSystem.objects.get_or_create(user=user, wallet_type="COMMISSIONS")

        return wallet

    @classmethod
    def create_wallets(cls, user_id):
        user = User.objects.filter(id=user_id).first()
        spend_wallet = cls.create_spend_wallet(user=user)
        collection_wallet = cls.create_collection_wallet(user=user)
        savings_wallet = cls.create_savings_wallet(user=user)
        commissions_wallet = cls.create_commissions_wallet(user=user)

        AccountSystem.create_accounts(
            user=user, wallet=collection_wallet
        )

    @classmethod
    def fetch_wallets(cls, user):
        """
        # This function takes in a user instance and selected wallets and returns all user wallets
        """
        wallets = cls.objects.select_related("user").filter(user=user, is_active=True)
        return wallets

    @classmethod
    def get_wallet(cls, user, from_wallet_type) -> 'WalletSystem':
        """
        # This function takes in a user instance and a wallet type and return the wallet object
        """
        wallets_queryset = cls.fetch_wallets(user=user)
        wallet = wallets_queryset.filter(wallet_type=from_wallet_type).first()
        return wallet

    @classmethod
    def get_float_wallet(cls, from_wallet_type):
        """
        # This function takes in the float user's wallet type and returns the wallet object
        """
        user = User.objects.filter(email=f"{settings.FLOAT_USER_EMAIL}").first()
        wallets_queryset = cls.fetch_wallets(user=user)
        wallet = wallets_queryset.filter(wallet_type=from_wallet_type).first()
        return wallet

    @staticmethod
    def pay_commission_to_liberty(user_id, wallet_id, wallet_type, liberty_commission, from_provider_type, transaction_commission_id,
                                  get_source_account=None, unique_reference=None, get_escrow_id=None, transfer_leg=None, raw_amount=None,
                                  type_of_trans_done=None, transaction_sub_type=None, resend_data=None):
        """
        # This function handles payment of commission to Liberty Wallet and transfer of commission to Liberty Account in a user instance and selected wallets and returns all user wallets
        """

        # Pay commission To Liberty

        user = User.objects.filter(id=user_id).first()
        float_user = User.objects.filter(email=settings.FLOAT_USER_EMAIL).first()
        liberty_commission_bank_account = AccountSystem.get_float_account(
            from_wallet_type="COMMISSIONS", from_provider_type=from_provider_type
        )
        liberty_commission_bank_code = ""
        liberty_reference = ""
        pay_commission = {}

        if from_provider_type == "VFD":
            liberty_commission_bank_code = "999999"
            liberty_reference = Transaction.create_liberty_reference(suffix="LGLP-VFDC")

            # liberty_commission_bank_code = "090110"
        if from_provider_type == "WOVEN":
            liberty_commission_bank_code = "000017"
            liberty_reference = Transaction.create_liberty_reference(suffix="LGLP-WOVC")

        if from_provider_type == "WEMA":
            liberty_commission_bank_code = "035"
            liberty_reference = Transaction.create_liberty_reference(suffix="LGLP-WEMA")

        if from_provider_type == "FIDELITY":
            liberty_commission_bank_code = "070"
            liberty_reference = Transaction.create_liberty_reference(suffix="LGLP-FIDE")

        # liberty_commission_bank_code = liberty_commission_bank_account.bank_code
        liberty_commission_bank_account_name = liberty_commission_bank_account.account_name
        liberty_commission_bank_account_nuban = liberty_commission_bank_account.account_number

        if transfer_leg == "VAS_COMMISSIONS":
            narration = f"LP-VAS-CMS-{user.first_name}"
        elif transfer_leg == "CASH_OUT":
            narration = f"LP_CASH_OUT_CMS-{user.first_name}"
        elif transfer_leg == "TRNS_CB_COMM":
            narration = f"LP_TRNS_CB_COMM-{user.first_name}"
        elif transfer_leg == "TRNS_CB_COMM_CASH":
            narration = f"LP_TRNS_CB_COMM_CASH-{user.first_name}"
        elif transfer_leg == "PAYBOX_SALES":
            narration = f"LP_PAYBOX_SALES_CMS-{user.first_name}"
        else:
            narration = f"LP-TRNSF-CMS-{user.first_name}"

        # Create transaction for Liberty Commission

        if get_escrow_id is not None and get_source_account is not None:
            escrow_id = get_escrow_id
            escrow_instance = Escrow.objects.filter(escrow_id=escrow_id).first()
            source_account = get_source_account

            provider_fee = ConstantTable.get_provider_fee(from_provider_type=from_provider_type)
            commission_amount = liberty_commission - escrow_instance.send_money_transfer_extra_fee - provider_fee

        elif get_escrow_id is not None and get_source_account is None:

            escrow_instance = Escrow.objects.filter(escrow_id=get_escrow_id).first()

            source_account = AccountSystem.get_float_account(
                from_wallet_type="FLOAT",
                from_provider_type=from_provider_type
            )

            commission_amount = liberty_commission

            escrow_id = get_escrow_id

            if not escrow_instance.liberty_reference or not escrow_instance.to_bank_code:
                escrow_instance.amount = commission_amount
                escrow_instance.liberty_commission = commission_amount
                escrow_instance.to_account_name = liberty_commission_bank_account_name
                escrow_instance.to_nuban = liberty_commission_bank_account_nuban
                escrow_instance.to_bank_code = liberty_commission_bank_code
                escrow_instance.narration = narration
                escrow_instance.liberty_reference = liberty_reference

                escrow_instance.save()

        else:
            source_account = AccountSystem.get_float_account(
                from_wallet_type="FLOAT",
                from_provider_type=from_provider_type
            )

            commission_amount = liberty_commission

            escrow_instance = Escrow.objects.create(
                user=user,
                transfer_type="SEND_COMMISSION",
                amount=commission_amount,
                liberty_commission=commission_amount,
                user_account_number=source_account.account_number,
                user_account_name=source_account.account_name,
                to_account_name=liberty_commission_bank_account_name,
                to_nuban=liberty_commission_bank_account_nuban,
                to_bank_code=liberty_commission_bank_code,
                narration=narration,
                liberty_reference=liberty_reference
            )

            escrow_id = escrow_instance.escrow_id

        new_narration = f"{escrow_id}|{narration}"

        if commission_amount > 0:

            bank_float_account = AccountSystem.get_dynamic_float_account(from_wallet_type="FLOAT", from_provider_type=from_provider_type)
            if bank_float_account is not None:
                bank_float_balance_before = bank_float_account.wallet.available_balance
            else:
                bank_float_balance_before = 0.00

            if resend_data is None:

                liberty_commission_transaction_id = Transaction.objects.create(
                    user=user,
                    wallet_id=wallet_id,
                    account_id=source_account.account_id,
                    account_provider=source_account.account_provider,
                    wallet_type=wallet_type,
                    transaction_type="SEND_LIBERTY_COMMISSION",
                    transaction_commission_id=transaction_commission_id,
                    amount=commission_amount,
                    bank_float_balance_before=bank_float_balance_before,
                    escrow_id=escrow_id,
                    liberty_reference=liberty_reference,
                    beneficiary_account_name=liberty_commission_bank_account_name,
                    beneficiary_nuban=liberty_commission_bank_account_nuban,
                    beneficiary_bank_code=liberty_commission_bank_code,
                    source_nuban=source_account.account_number,
                    source_account_name=source_account.account_name,
                    narration=new_narration,
                    transaction_leg="COMMISSIONS",
                    transaction_sub_type=transaction_sub_type,
                    escrow_instance=escrow_instance
                )

                resend = False

            else:
                liberty_commission_transaction_id = resend_data
                liberty_reference = resend_data.liberty_reference

                resend = True

            if commission_amount >= 1:
                if from_provider_type == "WOVEN":

                    pay_commission = Woven.initiate_payout(
                        beneficiary_account_name=liberty_commission_bank_account_name,
                        beneficiary_nuban=liberty_commission_bank_account_nuban,
                        beneficiary_bank_code=liberty_commission_bank_code,
                        source_account=source_account.account_number,
                        narration=new_narration,
                        amount=commission_amount,
                        reference=liberty_reference
                    )

                    if pay_commission.get("status") == "success":
                        unique_reference = pay_commission.get("data").get("unique_reference")
                    else:
                        unique_reference = None

                    # if pay_commission.get("status") == "success":
                    #     has_sent_commissions = True
                    #     unique_reference = pay_commission.get("data").get("unique_reference")
                    # else:
                    #     has_sent_commissions = False

                if from_provider_type == "VFD":
                    pay_commission = VFDBank.initiate_payout(
                        beneficiary_account_name=liberty_commission_bank_account_name,
                        beneficiary_nuban=liberty_commission_bank_account_nuban,
                        beneficiary_bank_code=liberty_commission_bank_code,
                        source_account=source_account.account_number,
                        narration=new_narration,
                        amount=commission_amount,
                        transfer_type="intra",
                        transfer_leg=transfer_leg,
                        user_bvn=user.check_kyc.bvn_rel.bvn_number,
                        reference=liberty_reference
                    )

                    # if pay_commission.get("status") == "00":
                    #     has_sent_commissions = True
                    #     unique_reference = pay_commission.get("data").get("txnId")
                    # else:
                    #     has_sent_commissions = False

                    if pay_commission.get("status") == "00":
                        unique_reference = pay_commission.get("data").get("txnId")
                    else:
                        unique_reference = None

                if from_provider_type == "WEMA":
                    from main.helper.core_banking import LibertyCoreBankingAPI

                    pay_commission = LibertyCoreBankingAPI.send_money(
                        first_name=user.first_name,
                        last_name=user.last_name,
                        amount=commission_amount,
                        source_account=source_account.account_number,
                        provider="WEMA",
                        email=user.email,
                        phone_number=user.phone_number,
                        destination_account_no=liberty_commission_bank_account_nuban,
                        destination_account_name=liberty_commission_bank_account_name,
                        destination_bank_code=liberty_commission_bank_code,
                        narration=new_narration
                    )

                    if pay_commission.get("status") == "success":
                        unique_reference = pay_commission.get("reference")
                    else:
                        unique_reference = None

                if from_provider_type == "FIDELITY":
                    from main.helper.core_banking import LibertyCoreBankingAPI

                    pay_commission = LibertyCoreBankingAPI.send_money(
                        first_name=user.first_name,
                        last_name=user.last_name,
                        amount=commission_amount,
                        source_account=source_account.account_number,
                        provider="FIDELITY",
                        email=user.email,
                        phone_number=user.phone_number,
                        destination_account_no=liberty_commission_bank_account_nuban,
                        destination_account_name=liberty_commission_bank_account_name,
                        destination_bank_code=liberty_commission_bank_code,
                        narration=new_narration
                    )

                    if pay_commission.get("status") == "success":
                        unique_reference = pay_commission.get("reference")
                    else:
                        unique_reference = None

                liberty_commission_transaction_id.initial_transfer_response_payload = pay_commission
                liberty_commission_transaction_id.save()

                # Get Account Balance
                AccountSystem.get_balance_on_account_per_time(account_number=source_account.account_number, account_provider=from_provider_type)
                escrow_instance.commissions_payload = pay_commission

            escrow_instance.commissions_transaction_id = liberty_commission_transaction_id.transaction_id
            escrow_instance.save()

            # else:

            # Create Transaction verification instance for verification
            is_test_trans = False
            if settings.ENVIRONMENT == "development":
                is_test_trans = True

            verification_instance = dict(
                transaction_instance=liberty_commission_transaction_id,
                user_id=user.id,
                user_email=user.email,
                account_provider=from_provider_type,
                transaction_leg=liberty_commission_transaction_id.transaction_leg,
                transaction_type=liberty_commission_transaction_id.transaction_type,
                timestamp=str(datetime.now()),
                escrow_id=escrow_id,
                amount=commission_amount,
                liberty_reference=liberty_reference,
                is_test=is_test_trans,
                beneficiary_nuban=liberty_commission_bank_account_nuban,
                source_nuban=source_account.account_number,
                bank_code=liberty_commission_bank_code
            )

            verf_pending_trans = TransferVerificationObject.create_verfication_check(verification_instance)
        else:
            pass
        return escrow_id

    @staticmethod
    def pay_electronic_levy_commission_to_liberty(user_id, wallet_id, wallet_type, amount, transaction_id):
        """
        # This function sends electronic transfer levy commission into liberty float account (for ETL)
        """
        user = User.objects.filter(id=user_id).first()
        levy_bank_account = AccountSystem.get_float_account(from_wallet_type="COMMISSIONS", from_provider_type="VFD")

        liberty_commission_bank_code = "999999"
        levy_bank_account_account_name = levy_bank_account.account_name
        levy_bank_account_account_nuban = levy_bank_account.account_number
        liberty_reference = Transaction.create_liberty_reference(suffix="LGLP-VFDC")

        narration = f"ELECTRONIC_LEVY_COMMISSION|{transaction_id}"

        # Create Escrow
        escrow_instance = Escrow.objects.create(
            user=user, transfer_type="SEND_COMMISSION", amount=amount, liberty_commission=amount,
            user_account_number=levy_bank_account.account_number, user_account_name=levy_bank_account.account_name,
            to_account_name=levy_bank_account_account_name, to_nuban=levy_bank_account_account_nuban, to_bank_code=liberty_commission_bank_code,
            narration=narration, liberty_reference=liberty_reference
        )

        balance_before = levy_bank_account.wallet.available_balance if levy_bank_account.wallet else 0

        description = str(narration) + "|" + str(escrow_instance.id)
        levy_commission_transaction = Transaction.objects.create(
            user=user, wallet_id=wallet_id, account_id=levy_bank_account.account_id, account_provider=levy_bank_account.account_provider,
            wallet_type=wallet_type, transaction_type="SEND_LIBERTY_COMMISSION", transaction_commission_id=transaction_id, amount=amount,
            bank_float_balance_before=balance_before, escrow_id=escrow_instance.id, liberty_reference=liberty_reference,
            beneficiary_account_name=levy_bank_account_account_name, beneficiary_nuban=levy_bank_account_account_nuban,
            beneficiary_bank_code=liberty_commission_bank_code, source_nuban=levy_bank_account.account_number,
            source_account_name=levy_bank_account.account_name, narration=description, transaction_leg="COMMISSIONS", transaction_sub_type=None,
            escrow_instance=escrow_instance
        )

        pay_commission = VFDBank.initiate_payout(
            beneficiary_account_name=levy_bank_account_account_name, beneficiary_nuban=levy_bank_account_account_nuban,
            beneficiary_bank_code=liberty_commission_bank_code, source_account=levy_bank_account.account_number, narration=description,
            amount=amount, transfer_type="intra", transfer_leg=None, user_bvn=user.check_kyc.bvn_rel.bvn_number, reference=liberty_reference
        )

        levy_commission_transaction.initial_transfer_response_payload = pay_commission
        levy_commission_transaction.save()

        # Get Account Balance
        AccountSystem.get_balance_on_account_per_time(account_number=levy_bank_account.account_number, account_provider="VFD")

        escrow_instance.commissions_payload = pay_commission
        escrow_instance.commissions_transaction_id = levy_commission_transaction.transaction_id
        escrow_instance.save()

        is_test_trans = True
        if settings.ENVIRONMENT == "production":
            is_test_trans = False

        verification_instance = dict(
            transaction_instance=levy_commission_transaction, user_id=user.id, user_email=user.email, account_provider="VFD",
            transaction_leg=levy_commission_transaction.transaction_leg, transaction_type=levy_commission_transaction.transaction_type,
            timestamp=str(datetime.now()), escrow_id=escrow_instance.id, amount=amount, liberty_reference=liberty_reference, is_test=is_test_trans,
            beneficiary_nuban=levy_bank_account_account_nuban, source_nuban=levy_bank_account.account_number, bank_code=liberty_commission_bank_code
        )

        TransferVerificationObject.create_verfication_check(verification_instance)

        return True

    @classmethod
    def check_wallet_balance(cls, amount, wallet_instance: 'WalletSystem', check_hold=False):
        lien_amount = Lien.get_lien_amount(user=wallet_instance.user)

        if check_hold is True:
            available_balance = wallet_instance.hold_balance
        else:
            available_balance = wallet_instance.available_balance - lien_amount

        # if wallet_instance.user.type_of_user == "LIBERTY_RETAIL":
        #     retail_system = getattr(wallet_instance.user, 'retail_system', None)
        #     retail_balance = retail_system.available_balance if retail_system else 0
        #     amount += abs(retail_balance)

        if available_balance < amount:
            response = {
                "status": False,
                "balance": available_balance,
            }
        else:
            response = {
                "status": True,
                "balance": available_balance,
            }

        return response

    @classmethod
    def fund_to_hold_balance(cls, user, from_wallet_type, amount, trans_type=None, transaction_instance_id=None):

        wallets_queryset = cls.fetch_wallets(user=user)
        wallet = wallets_queryset.filter(wallet_type=from_wallet_type).last()

        balance_before = wallet.available_balance
        balance_after = wallet.available_balance - amount
        wallet.available_balance = balance_after

        hold_balance_before = wallet.hold_balance
        hold_balance_after = wallet.hold_balance + amount
        wallet.hold_balance = hold_balance_after

        wallet.save()

        debit_credit_record = DebitCreditRecordOnAccount.objects.create(
            user=user,
            entry="DEBIT",
            wallet=wallet,
            wallet_type=from_wallet_type,
            balance_before=balance_before,
            amount=amount,
            balance_after=balance_after,
            type_of_trans=trans_type,
            is_hold=True,
            hold_balance_before=hold_balance_before,
            hold_balance_after=hold_balance_after,
            transaction_instance_id=transaction_instance_id
        )

        return {
            "debit_credit_record_id": debit_credit_record.id,
            "balance_before": balance_before + hold_balance_before,
            "balance_after": balance_after + hold_balance_after,
            "hold_balance_before": hold_balance_before,
            "hold_balance_after": hold_balance_after
        }

    @classmethod
    def debit_from_hold_balance(cls, user, from_wallet_type, amount, trans_type=None, transaction_instance_id=None):

        wallets_queryset = cls.fetch_wallets(user=user)
        wallet = wallets_queryset.filter(wallet_type=from_wallet_type).last()

        balance_before = wallet.available_balance
        balance_after = wallet.available_balance

        hold_balance_before = wallet.hold_balance
        hold_balance_after = wallet.hold_balance - amount
        wallet.hold_balance = hold_balance_after

        wallet.save()

        debit_credit_record = DebitCreditRecordOnAccount.objects.create(
            user=user,
            entry="DEBIT",
            wallet=wallet,
            wallet_type=from_wallet_type,
            balance_before=balance_before,
            amount=amount,
            balance_after=balance_after,
            type_of_trans=trans_type,
            is_hold=True,
            hold_balance_before=hold_balance_before,
            hold_balance_after=hold_balance_after,
            transaction_instance_id=transaction_instance_id
        )

        return {
            "debit_credit_record_id": debit_credit_record.id,
            "balance_before": balance_before + hold_balance_before,
            "balance_after": balance_after + hold_balance_after,
            "hold_balance_before": hold_balance_before,
            "hold_balance_after": hold_balance_after
        }

    @classmethod
    def actual_deduct_balance(cls, wallet: "WalletSystem", amount):

        wallet.available_balance = F("available_balance") - float(amount)
        wallet.save()

        wallet.refresh_from_db()

        return wallet

    @classmethod
    def deduct_balance(cls, user: User, wallet: "WalletSystem", amount, trans_type=None, transaction_instance_id=None, unique_reference=None):

        if amount <= 0:
            raise Exception("Amount is less than Zero")

        amount = abs(amount)
        balance_before = wallet.available_balance

        wallet = cls.actual_deduct_balance(wallet, amount)

        balance_after = wallet.available_balance

        hold_balance_before = wallet.hold_balance
        hold_balance_after = wallet.hold_balance

        debit_credit_record = DebitCreditRecordOnAccount.objects.create(
            user=user,
            entry="DEBIT",
            wallet=wallet,
            wallet_type=wallet.wallet_type,
            balance_before=balance_before,
            amount=amount,
            balance_after=balance_after,
            type_of_trans=trans_type,
            hold_balance_before=hold_balance_before,
            hold_balance_after=hold_balance_after,
            transaction_instance_id=transaction_instance_id,
            unique_reference=unique_reference
        )

        return {
            "debit_credit_record_id": debit_credit_record.id,
            "balance_before": balance_before,
            "balance_after": balance_after
        }

    @classmethod
    def get_balance_after(cls, user, balance_before, total_amount, is_credit, transaction_type=None):
        first_whisper_charge = float(settings.WHISPER_CHARGE)
        if user.sms_subscription and transaction_type not in ["FUND_TRANSFER_FROM_COMMISSION", "AIRTIME_PIN", "BILLS_AND_PAYMENT",
                                                              "SEND_LIBERTY_COMMISSION"] and total_amount >= first_whisper_charge:
            whisper_charge = first_whisper_charge
        else:
            whisper_charge = 0.00

        print("::::", whisper_charge)

        if is_credit is False:
            balance_after = balance_before - total_amount - whisper_charge
        else:
            balance_after = balance_before + total_amount - whisper_charge

        return balance_after

    @classmethod
    def actual_fund_balance(cls, wallet: "WalletSystem", amount):

        wallet.available_balance = F("available_balance") + float(amount)
        wallet.save()

        wallet.refresh_from_db()

        return wallet

    @classmethod
    def fund_balance(cls, user: User, wallet: "WalletSystem", amount, trans_type=None, transaction_instance_id=None, reversal_trans_id=None,
                     unique_reference=None):

        if amount <= 0:
            raise Exception("Amount is less than Zero")

        with django_transaction.atomic():
            balance_before = wallet.available_balance

            wallet = cls.actual_fund_balance(wallet, amount=amount)

            balance_after = wallet.available_balance

            hold_balance_before = wallet.hold_balance
            hold_balance_after = wallet.hold_balance

            record = DebitCreditRecordOnAccount.objects.create(
                user=user,
                entry="CREDIT",
                wallet=wallet,
                wallet_type=wallet.wallet_type,
                balance_before=balance_before,
                amount=amount,
                balance_after=balance_after,
                type_of_trans=trans_type,
                hold_balance_before=hold_balance_before,
                hold_balance_after=hold_balance_after,
                transaction_instance_id=transaction_instance_id,
                unique_reference=unique_reference,
                reversal_trans_id=reversal_trans_id
            )

        ############################################################################################################################
        if user.type_of_user in list_of_allowed_terminal_users:
            # HANDLE LEDGER
            from accounts.tasks import handle_ledger_input_task

            handle_ledger_input_task.apply_async(
                queue="ledgertask" if user.type_of_user == "LIBERTY_RETAIL" else "ledgertaskother",
                kwargs={
                    "instance_id": transaction_instance_id,
                    "charge_type": "BANK",
                }
            )

        ############################################################################################################################
        # Check for Debts
        all_debts = UserDebt.objects.filter(user=user, is_active=True)
        if all_debts:
            for trans in all_debts:
                UserDebt.debit_user_debt(trans=trans)

        wallet.refresh_from_db()

        ############################################################################################################################

        return {
            "balance_before": balance_before,
            "balance_after": balance_after,
            "record": record,
            "wallet_instance": wallet
        }

    @classmethod
    def transaction_alert_notfication_manager(cls, user: User, amount, cr_dr, narration, from_wallet_type, liberty_commission=None,
                                              transaction_instance_id=None):

        from accounts.tasks import handle_sms_notification_task

        handle_sms_notification_task.apply_async(
            queue="sms_queue",
            kwargs={
                "user_id": user.id,
                "amount": float(amount),
                "cr_dr": cr_dr,
                "narration": narration,
                "from_wallet_type": from_wallet_type,
                "liberty_commission": liberty_commission,
                "transaction_instance_id": transaction_instance_id
            }
        )

        return True

    @classmethod
    def fund_wallet_pay_buddy(
            cls,
            sender_user_instance: User,
            buddy_user_instance: User,
            sender_wallet: "WalletSystem",
            receiver_wallet: "WalletSystem",
            amount,
            escrow_id,
            customer_reference=None,
            other_acc_number=None,
            metadata=None,
            sender_transaction=None,
    ):

        wallet = receiver_wallet
        receiver_wallet_type = wallet.wallet_type

        escrow_instance = Escrow.objects.filter(escrow_id=escrow_id).first()
        escrow_instance.internal_escrow = False
        escrow_instance.external_escrow = False
        escrow_instance.save()

        if escrow_instance.transfer_type == "SEND_LOTTO_WALLET":
            sender_trans_type = "SEND_LOTTO_WALLET"
            receiver_trans_type = "FUND_LOTTO_WALLET"
            duo_nomenclature = "AGENT"

        elif escrow_instance.transfer_type == "SEND_AJO_WALLET":
            sender_trans_type = "SEND_AJO_WALLET"
            receiver_trans_type = "FUND_AJO_WALLET"
            duo_nomenclature = "AGENT"

        elif escrow_instance.transfer_type == "SEND_AJO_LOANS":
            sender_trans_type = "SEND_AJO_LOANS"
            receiver_trans_type = "FUND_AJO_LOANS"
            duo_nomenclature = "AGENT"

        elif escrow_instance.transfer_type == "SEND_COLLECTION_ACCOUNT":
            sender_trans_type = "SEND_COLLECTION_ACCOUNT"
            receiver_trans_type = "FUND_COLLECTION_ACCOUNT"
            duo_nomenclature = ""

        else:
            sender_trans_type = "SEND_BUDDY"
            receiver_trans_type = "FUND_BUDDY"
            duo_nomenclature = "BUDDY"

        # sender_transaction = sender_user_instance.transactions.filter(
        #     user_id=sender_user_instance.id,
        #     escrow_id=escrow_instance.escrow_id,
        #     liberty_reference=escrow_instance.liberty_reference,
        # ).first()

        if not sender_transaction:
            # Create transaction for Sender
            sender_transaction = Transaction.objects.create(
                user=sender_user_instance,
                wallet_id=sender_wallet.wallet_id,
                wallet_type=sender_wallet.wallet_type,
                transaction_type=sender_trans_type,
                transaction_sub_type=metadata.get("type") if metadata else None,
                amount=amount,
                balance_before=escrow_instance.balance_before,
                balance_after=escrow_instance.balance_after,
                liberty_reference=escrow_instance.liberty_reference,
                liberty_commission=escrow_instance.liberty_commission,
                total_amount_charged=escrow_instance.total_amount_charged,
                total_amount_sent_out=escrow_instance.total_amount_charged,
                beneficiary_account_name=buddy_user_instance.bvn_full_name,
                beneficiary_nuban=buddy_user_instance.phone_number,
                beneficiary_wallet_id=wallet.wallet_id,
                beneficiary_wallet_type=receiver_wallet_type,
                escrow_id=escrow_instance.escrow_id,
                narration=escrow_instance.narration,
                status="PENDING",
                transaction_leg="EXTERNAL",
                unique_reference=customer_reference,
                debit_credit_record_id=escrow_instance.debit_credit_record_id,
                escrow_instance=escrow_instance
            )

        # Get debit_credit_record_id
        if escrow_instance.debit_credit_record_id is not None:
            debit_credit_record_id = DebitCreditRecordOnAccount.objects.filter(id=escrow_instance.debit_credit_record_id).last()
            if debit_credit_record_id:
                debit_credit_record_id.transaction_instance_id = sender_transaction.transaction_id
                debit_credit_record_id.save()

        with django_transaction.atomic():

            if escrow_instance.narration in [
                "LOTTO_COMMISSION", "LOTTERY_WINNING_COMMISSION",
                "LOTTERY_PLAY_COMMISSION", "SUPER_AGENT_COMMISSION_REWARD"
            ] and isinstance(metadata, dict):

                get_sales_rep = None

                if metadata.get("type") == OtherTrxSubType.SUPER_AGENT_COMMISSION_REWARD:
                    agent_id = metadata.get("user_id")
                    if agent_id and isinstance(agent_id, int):
                        # get_supervisor = SuperAgentProfile.objects.filter(sales_rep_code="48404C999")
                        try:
                            super_agent_profile = SuperAgentProfile.objects.get(
                                super_agent=buddy_user_instance, agent__id=int(agent_id)
                            )
                            get_sales_rep = super_agent_profile.supervisor
                        except:
                            pass

                else:
                    sales_rep_prof = OtherCommissionsRecord.check_if_sales_rep_exists(
                        downline_user=buddy_user_instance
                    )
                    if sales_rep_prof:
                        get_sales_rep = sales_rep_prof.sales_rep

                if get_sales_rep is None:
                    new_buddy_amount = amount
                    new_sales_rep_comm = 0
                    sales_rep = False

                else:
                    calc_super_agent_profit = (
                                                      ConstantTable.get_constant_table_instance().lotto_super_agent_profit / 100
                                              ) * amount

                    new_buddy_amount = amount - calc_super_agent_profit
                    new_sales_rep_comm = calc_super_agent_profit
                    sales_rep = True

                # Handle Agent Profit On Cash Out
                if (amount >= new_buddy_amount + new_sales_rep_comm) \
                        and new_sales_rep_comm > 0 and sales_rep == True:
                    handle_ro_commission = OtherCommissionsRecord.create_and_top_up_sales_rep_commissions(
                        agent=buddy_user_instance,
                        sales_rep=get_sales_rep,
                        amount=new_sales_rep_comm,
                        transaction_id=sender_transaction.transaction_id,
                        transaction_type="LOTTO_PLAY",
                        transaction_reason=f"Commission on LOTTO PLAY by {buddy_user_instance.bvn_first_name}" if buddy_user_instance.bvn_first_name else f"Commission on LOTTO PLAY by {buddy_user_instance.email}",
                        total_profit=amount + escrow_instance.liberty_commission,
                        liberty_profit=escrow_instance.liberty_commission,
                        ro_cash_profit=new_sales_rep_comm,
                        agent_cash_profit=new_buddy_amount
                    )

            else:
                new_buddy_amount = amount
                new_sales_rep_comm = 0

            buddy_balance_before = wallet.available_balance

            buddy_balance_after = WalletSystem.get_balance_after(
                user=buddy_user_instance,
                balance_before=buddy_balance_before,
                total_amount=new_buddy_amount,
                is_credit=True
            )

            fund_lotto_callback_payload = {
                "user_id": sender_user_instance.id,
                "reference": escrow_instance.fund_liberty_reference,
                "amount": new_buddy_amount,
                "liberty_commission": new_sales_rep_comm,
                "agent_phone": sender_user_instance.phone_number,
                "account_number": other_acc_number,
            }
            # Create transaction for Receiver
            receiver_transaction = Transaction.objects.create(
                user=wallet.user,
                wallet_id=wallet.wallet_id,
                wallet_type=wallet.wallet_type,
                transaction_type=receiver_trans_type,
                transaction_sub_type=metadata.get("type") if metadata else None,
                amount=new_buddy_amount,
                liberty_reference=escrow_instance.fund_liberty_reference,
                liberty_commission=new_sales_rep_comm,
                total_amount_received=amount,
                escrow_id=escrow_instance.escrow_id,
                source_account_name=sender_user_instance.bvn_full_name,
                source_nuban=sender_user_instance.phone_number,
                source_wallet_id=sender_wallet.wallet_id,
                source_wallet_type=sender_wallet.wallet_type,
                narration=escrow_instance.narration,
                status="PENDING",
                transaction_leg="EXTERNAL",
                balance_before=buddy_balance_before,
                balance_after=buddy_balance_after,
                lotto_agent_user_id=escrow_instance.lotto_agent_user_id,
                lotto_agent_user_phone=escrow_instance.lotto_agent_user_phone,
                unique_reference=customer_reference,
                callback_payload=fund_lotto_callback_payload
            )

            Transaction.serializer_trans(receiver_transaction)

            try:
                fund_buddy_balance = cls.fund_balance(
                    user=buddy_user_instance,
                    wallet=wallet,
                    amount=new_buddy_amount,
                    trans_type=receiver_trans_type,
                    transaction_instance_id=receiver_transaction.transaction_id,
                    unique_reference=escrow_instance.escrow_id
                )
            except IntegrityError:
                pass

            sender_transaction.status = "SUCCESSFUL"
            receiver_transaction.status = "SUCCESSFUL"
            receiver_transaction.balance_before = buddy_balance_before
            receiver_transaction.balance_after = buddy_balance_after

            sender_transaction.save()
            receiver_transaction.save()

        # debit_credit_record = fund_buddy_balance["record"]
        # debit_credit_record.transaction_instance_id = receiver_transaction.transaction_id
        # debit_credit_record.save()

        ############################################################################################################################
        # DEBIT SENDER SMS

        sender_not_token = sender_user_instance.firebase_key
        sender_not_title = "Transaction Successful"
        sender_not_body = f"You have successfully transfered N{amount} to {buddy_user_instance.bvn_full_name}"
        sender_not_data = {"amount_sent": f"{amount}", "available_balance": f"{sender_wallet.available_balance}"}

        send_out_notification = cloud_messaging.send_broadcast(
            token=sender_not_token,
            title=sender_not_title,
            body=sender_not_body,
            data=sender_not_data
        )

        InAppTransactionNotification.create_in_app_transaction_notification(
            user=sender_user_instance,
            title=sender_not_title,
            message_body=sender_not_body
        )

        if sender_trans_type == "SEND_BUDDY":
            cls.transaction_alert_notfication_manager(
                user=sender_user_instance,
                amount=float(amount),
                cr_dr="DR",
                narration=f"{escrow_instance.narration}-TO {duo_nomenclature}-{wallet.user.bvn_full_name}",
                from_wallet_type=sender_wallet.wallet_type,
                transaction_instance_id=sender_transaction.transaction_id
            )

        ############################################################################################################################
        # CREDIT RECIEVER SMS

        receiver_not_token = buddy_user_instance.firebase_key
        receiver_not_title = "Payment Received"
        receiver_not_body = f"You have recieved a CREDIT of N{new_buddy_amount} from {sender_user_instance.bvn_full_name}"
        receiver_not_data = {"amount_sent": f"{new_buddy_amount}", "available_balance": f"{wallet.available_balance}"}

        send_out_notification = cloud_messaging.send_broadcast(
            token=receiver_not_token,
            title=receiver_not_title,
            body=receiver_not_body,
            data=receiver_not_data
        )

        InAppTransactionNotification.create_in_app_transaction_notification(
            user=buddy_user_instance,
            title=receiver_not_title,
            message_body=receiver_not_body
        )

        cls.transaction_alert_notfication_manager(
            user=buddy_user_instance,
            amount=float(new_buddy_amount),
            cr_dr="CR",
            narration=f"{escrow_instance.narration}-FRM {duo_nomenclature}-{sender_wallet.user.bvn_full_name}",
            from_wallet_type=receiver_wallet_type,
            transaction_instance_id=receiver_transaction.transaction_id
        )

        return {
            "sender_transaction": sender_transaction,
            "receiver_transaction": receiver_transaction
        }

    @classmethod
    def fund_wallet_pay_buddy_and_hold_for_receiver(
            cls,
            sender_user_instance,
            buddy_user_instance,
            sender_wallet,
            receiver_wallet_id,
            receiver_wallet_type,
            amount,
            escrow_instance,
            customer_reference=None
    ):

        escrow_instance.internal_escrow = False
        escrow_instance.external_escrow = False
        escrow_instance.save()

        sender_trans_type = escrow_instance.transfer_type
        receiver_trans_type = escrow_instance.transfer_type

        # Create transaction for Sender
        sender_transaction = Transaction.objects.create(
            user=sender_user_instance,
            wallet_id=sender_wallet.wallet_id,
            wallet_type=sender_wallet.wallet_type,
            transaction_type=sender_trans_type,
            amount=amount,
            balance_before=escrow_instance.balance_before,
            balance_after=escrow_instance.balance_after,
            liberty_reference=escrow_instance.liberty_reference,
            liberty_commission=escrow_instance.liberty_commission,
            total_amount_charged=escrow_instance.total_amount_charged,
            total_amount_sent_out=escrow_instance.total_amount_charged,
            beneficiary_account_name=buddy_user_instance.bvn_full_name,
            beneficiary_wallet_id=receiver_wallet_id,
            beneficiary_wallet_type=receiver_wallet_type,
            escrow_id=escrow_instance.escrow_id,
            narration=escrow_instance.narration,
            status="PENDING",
            transaction_leg="EXTERNAL",
            unique_reference=customer_reference
        )

        # Get debit_credit_record_id
        if escrow_instance.debit_credit_record_id is not None:
            debit_credit_record_id = DebitCreditRecordOnAccount.objects.filter(id=escrow_instance.debit_credit_record_id).last()
            if debit_credit_record_id:
                debit_credit_record_id.transaction_instance_id = sender_transaction.transaction_id
                debit_credit_record_id.save()

        return sender_transaction

    @classmethod
    def release_fund_wallet_pay_buddy_for_receiver(
            cls,
            sender_transaction,
            sender_user_instance,
            buddy_user_instance,
            sender_wallet: "WalletSystem",
            wallet: "WalletSystem",
            escrow_instance,
            customer_reference=None
    ):

        amount = sender_transaction.amount

        fund_buddy_balance = cls.fund_balance(
            user=buddy_user_instance,
            wallet=wallet,
            amount=amount,
            trans_type=escrow_instance.transfer_type,
            unique_reference=escrow_instance.escrow_id
        )

        buddy_balance_before = fund_buddy_balance["balance_before"]

        buddy_balance_after = WalletSystem.get_balance_after(
            user=buddy_user_instance,
            balance_before=buddy_balance_before,
            total_amount=amount,
            is_credit=True
        )

        sender_transaction.status = "SUCCESSFUL"
        sender_transaction.save()

        # Create transaction for Receiver
        receiver_transaction = Transaction.objects.create(
            user=wallet.user,
            wallet_id=wallet.wallet_id,
            wallet_type=wallet.wallet_type,
            transaction_type=escrow_instance.transfer_type,
            amount=amount,
            liberty_reference=escrow_instance.fund_liberty_reference,
            liberty_commission=0.00,
            total_amount_received=amount,
            escrow_id=escrow_instance.escrow_id,
            source_account_name=sender_user_instance.bvn_full_name,
            source_wallet_id=sender_wallet.wallet_id,
            source_wallet_type=sender_wallet.wallet_type,
            narration=escrow_instance.narration,
            status="SUCCESSFUL",
            transaction_leg="EXTERNAL",
            balance_before=buddy_balance_before,
            balance_after=buddy_balance_after,
            lotto_agent_user_id=escrow_instance.lotto_agent_user_id,
            lotto_agent_user_phone=escrow_instance.lotto_agent_user_phone,
            unique_reference=customer_reference
        )

        Transaction.serializer_trans(receiver_transaction)

        debit_credit_record = fund_buddy_balance["record"]
        debit_credit_record.transaction_instance_id = receiver_transaction.transaction_id
        debit_credit_record.save()

        ############################################################################################################################
        # DEBIT SENDER SMS

        sender_not_token = sender_user_instance.firebase_key
        sender_not_title = "Transaction Successful"
        sender_not_body = f"You have successfully transfered N{amount} to {buddy_user_instance.bvn_full_name}"
        sender_not_data = {"amount_sent": f"{amount}", "available_balance": f"{sender_wallet.available_balance}"}

        send_out_notification = cloud_messaging.send_broadcast(
            token=sender_not_token,
            title=sender_not_title,
            body=sender_not_body,
            data=sender_not_data
        )

        InAppTransactionNotification.create_in_app_transaction_notification(
            user=sender_user_instance,
            title=sender_not_title,
            message_body=sender_not_body
        )

        cls.transaction_alert_notfication_manager(
            user=sender_user_instance,
            amount=float(amount),
            cr_dr="DR",
            narration=f"{escrow_instance.narration}-FOR-{wallet.user.bvn_full_name}",
            from_wallet_type=sender_wallet.wallet_type,
            transaction_instance_id=sender_transaction.transaction_id
        )

        ############################################################################################################################
        # CREDIT RECIEVER SMS

        receiver_not_token = buddy_user_instance.firebase_key
        receiver_not_title = "Payment Received"
        receiver_not_body = f"You have recieved a CREDIT of N{amount} from {sender_user_instance.bvn_full_name}"
        receiver_not_data = {"amount_sent": f"{amount}", "available_balance": f"{wallet.available_balance}"}

        send_out_notification = cloud_messaging.send_broadcast(
            token=receiver_not_token,
            title=receiver_not_title,
            body=receiver_not_body,
            data=receiver_not_data
        )

        InAppTransactionNotification.create_in_app_transaction_notification(
            user=buddy_user_instance,
            title=receiver_not_title,
            message_body=receiver_not_body
        )

        cls.transaction_alert_notfication_manager(
            user=buddy_user_instance,
            amount=float(amount),
            cr_dr="CR",
            narration=f"{escrow_instance.narration}-FOR-{sender_wallet.user.bvn_full_name}",
            from_wallet_type=wallet.wallet_type,
            transaction_instance_id=receiver_transaction.transaction_id
        )

    #########################################################################################################################
    @classmethod
    def release_hold_balance(
            cls,
            user_instance,
            wallet_instance: "WalletSystem",
            amount,
            escrow_id,
            customer_reference=None
    ):

        escrow_instance = Escrow.objects.filter(escrow_id=escrow_id).first()
        escrow_instance.internal_escrow = False
        escrow_instance.external_escrow = False
        escrow_instance.save()

        fund_user_balance = cls.fund_balance(
            user=user_instance,
            wallet=wallet_instance,
            amount=amount,
            trans_type=escrow_instance.transfer_type,
            unique_reference=escrow_id
        )

        user_balance_before = fund_user_balance["balance_before"]

        user_balance_after = WalletSystem.get_balance_after(
            user=user_instance,
            balance_before=user_balance_before,
            total_amount=amount,
            is_credit=True
        )

        # Create transaction instance
        transaction_instance = Transaction.objects.create(
            user=user_instance,
            wallet_id=wallet_instance.wallet_id,
            wallet_type=wallet_instance.wallet_type,
            transaction_type=escrow_instance.transfer_type,
            amount=amount,
            balance_before=user_balance_before,
            balance_after=user_balance_after,
            liberty_reference=escrow_instance.liberty_reference,
            liberty_commission=escrow_instance.liberty_commission,
            total_amount_charged=escrow_instance.total_amount_charged,
            total_amount_sent_out=escrow_instance.total_amount_charged,
            beneficiary_account_name=user_instance.bvn_full_name,
            beneficiary_wallet_id=wallet_instance.wallet_id,
            beneficiary_wallet_type=wallet_instance.wallet_type,
            escrow_id=escrow_instance.escrow_id,
            narration=escrow_instance.narration,
            status="SUCCESSFUL",
            transaction_leg="EXTERNAL",
            unique_reference=customer_reference
        )

        # Get debit_credit_record_id
        if escrow_instance.debit_credit_record_id is not None:
            debit_credit_record_id = DebitCreditRecordOnAccount.objects.filter(id=escrow_instance.debit_credit_record_id).last()
            if debit_credit_record_id:
                debit_credit_record_id.transaction_instance_id = transaction_instance.transaction_id
                debit_credit_record_id.save()

        debit_credit_record = fund_user_balance["record"]
        debit_credit_record.transaction_instance_id = transaction_instance.transaction_id
        debit_credit_record.save()

        ############################################################################################################################
        # CREDIT USER SMS

        receiver_not_token = user_instance.firebase_key
        receiver_not_title = "Payment Received"
        receiver_not_body = f"You have recieved a CREDIT of N{amount} from {user_instance.bvn_full_name}"
        receiver_not_data = {"amount_sent": f"{amount}", "available_balance": f"{wallet_instance.available_balance}"}

        send_out_notification = cloud_messaging.send_broadcast(
            token=receiver_not_token,
            title=receiver_not_title,
            body=receiver_not_body,
            data=receiver_not_data
        )

        InAppTransactionNotification.create_in_app_transaction_notification(
            user=user_instance,
            title=receiver_not_title,
            message_body=receiver_not_body
        )

        cls.transaction_alert_notfication_manager(
            user=user_instance,
            amount=float(amount),
            cr_dr="CR",
            narration=f"{escrow_instance.narration}-FRM {escrow_instance.transfer_type}-{user_instance.bvn_full_name}",
            from_wallet_type=wallet_instance.wallet_type,
            transaction_instance_id=transaction_instance.transaction_id
        )

    @classmethod
    def get_wallet_id_with_phone_number(cls, to_wallet_type, phone_number):
        user = User.objects.filter(phone_number=phone_number).last()
        wallets_queryset = cls.fetch_wallets(user=user)
        wallet = wallets_queryset.filter(wallet_type=to_wallet_type).first()
        return wallet.wallet_id

    @classmethod
    def move_to_escrow_send_pay_buddy(
            cls,
            user,
            from_wallet_id,
            to_wallet_id,
            balance_before,
            balance_after,
            from_wallet_type,
            to_wallet_type,
            transfer_type,
            amount,
            to_nuban,
            to_account_name,
            liberty_commission,
            total_amount_charged,
            narration,
            is_beneficiary,
            is_recurring,
            debit_credit_record_id=None,
            ip_addr=None,
            lotto_agent_user_id=None,
            lotto_agent_user_phone=None,
            customer_reference=None,
            bulk_id=None,
            metadata=None
    ):
        if transfer_type == "SEND_LOTTO_WALLET":
            liberty_reference = Transaction.create_liberty_reference(suffix="LGLP_SND_LOTTO_WALL")
            fund_liberty_reference = Transaction.create_liberty_reference(suffix="LGLP_FND_LOTTO_WALL")

        elif transfer_type == "SEND_AJO_WALLET":
            liberty_reference = Transaction.create_liberty_reference(suffix="LGLP_SND_AJO_WALL")
            fund_liberty_reference = Transaction.create_liberty_reference(suffix="LGLP_FND_AJO_WALL")

        elif transfer_type == "SEND_AJO_LOANS":
            liberty_reference = Transaction.create_liberty_reference(suffix="LGLP_SND_AJO_LOAN")
            fund_liberty_reference = Transaction.create_liberty_reference(suffix="LGLP_FND_AJO_LOAN")

        elif transfer_type == "SEND_COLLECTION_ACCOUNT":
            liberty_reference = Transaction.create_liberty_reference(suffix="LGLP_SND_COLL_ACCT")
            fund_liberty_reference = Transaction.create_liberty_reference(suffix="LGLP_FND_COLL_ACCT")

        elif transfer_type == "RELEASE_HOLD_BALANCE":
            liberty_reference = Transaction.create_liberty_reference(suffix="LGLP_RLS_HOLD")
            fund_liberty_reference = Transaction.create_liberty_reference(suffix="LGLP_FND_RLS_HOLD")

        elif transfer_type == "VIRTUAL_CARD_CREATE":
            liberty_reference = Transaction.create_liberty_reference(suffix="LGLP_VRCD")
            fund_liberty_reference = Transaction.create_liberty_reference(suffix="LGLP_OVRCD")

        elif transfer_type == "PHYSICAL_CARD_CREATE":
            liberty_reference = Transaction.create_liberty_reference(suffix="LGLP_PHYCD")
            fund_liberty_reference = Transaction.create_liberty_reference(suffix="LGLP_OPHYCD")

        elif transfer_type == "RETAIL_AUTO_DEBIT":
            liberty_reference = Transaction.create_liberty_reference(suffix="LGLP_ADLR")
            fund_liberty_reference = Transaction.create_liberty_reference(suffix="LGLP_OADLR")

        elif transfer_type == "TERMINAL_PURCHASE":
            liberty_reference = Transaction.create_liberty_reference(suffix="LGLP_TERM_PAY")
            fund_liberty_reference = Transaction.create_liberty_reference(suffix="LGLP_FND_TERM_PAY")

        else:
            liberty_reference = Transaction.create_liberty_reference(suffix="LGLP_SND_BUDDY")
            fund_liberty_reference = Transaction.create_liberty_reference(suffix="LGLP_FND_BUDDY")

        escrow_instance = Escrow.objects.create(
            user=user,
            from_wallet_id=from_wallet_id,
            to_wallet_id=to_wallet_id,
            from_wallet_type=from_wallet_type,
            to_wallet_type=to_wallet_type,
            transfer_type=transfer_type,
            amount=amount,
            to_nuban=to_nuban,
            to_account_name=to_account_name,
            ip_addr=ip_addr,
            balance_before=balance_before,
            balance_after=balance_after,
            liberty_commission=liberty_commission,
            liberty_reference=liberty_reference,
            fund_liberty_reference=fund_liberty_reference,
            total_amount_charged=total_amount_charged,
            narration=narration,
            is_beneficiary=is_beneficiary,
            is_recurring=is_recurring,
            debit_credit_record_id=debit_credit_record_id,
            lotto_agent_user_id=lotto_agent_user_id,
            lotto_agent_user_phone=lotto_agent_user_phone,
            customer_reference=customer_reference,
            bulk_id=bulk_id,
            pos_charge=0,
            pos_charge_type="BANK",
            metadata=json.dumps(metadata) if metadata else None
        )

        return escrow_instance

    @classmethod
    def debit_credit_for_other_services(
            cls,
            service_user_instance: User,
            service_wallet_instance,
            agent_user_instance: User,
            agent_wallet_instance,
            total_amount,
            service_comm,
            agent_comm,
            unique_reference,
            escrow_id
    ):

        new_response = {
            "status": False,
            "message": "Transaction could not complete",
            "escrow_id": escrow_id,
        }

        if total_amount > 0:
            escrow_instance = Escrow.objects.filter(escrow_id=escrow_id).first()
            escrow_instance.internal_escrow = False
            escrow_instance.external_escrow = False
            escrow_instance.save()

            if escrow_instance.transfer_type == "LOTTO_PLAY":
                sender_trans_type = "LOTTO_PLAY"
                receiver_trans_type = "LOTTO_PLAY_CR"

            elif escrow_instance.transfer_type == "CREDI_LOAN":
                sender_trans_type = "CREDI_LOAN"
                receiver_trans_type = "CREDI_LOAN_CR"

            elif escrow_instance.transfer_type == "LIBERTY_LIFE_PAYMENT":
                sender_trans_type = "LIBERTY_LIFE_PAYMENT"
                receiver_trans_type = "LIBERTY_LIFE_PAYMENT_CR"

            elif escrow_instance.transfer_type == "AUTO_REFUND":
                sender_trans_type = "AUTO_REFUND"
                receiver_trans_type = "AUTO_REFUND_CR"

            elif escrow_instance.transfer_type == "FUNDS_RETRIEVAL":
                sender_trans_type = "FUNDS_RETRIEVAL"
                receiver_trans_type = "FUNDS_RETRIEVAL_CR"

            elif escrow_instance.transfer_type == "SAVINGS":
                sender_trans_type = "SAVINGS"
                receiver_trans_type = "SAVINGS_CR"

            elif escrow_instance.transfer_type == "RETAIL_AUTO_DEBIT":
                sender_trans_type = "RETAIL_AUTO_DEBIT"
                receiver_trans_type = "RETAIL_AUTO_DEBIT_CR"

            else:
                sender_trans_type = escrow_instance.transfer_type
                receiver_trans_type = escrow_instance.transfer_type

            # Create transaction for Agent
            sender_transaction = Transaction.objects.create(
                user=agent_user_instance,
                wallet_id=agent_wallet_instance.wallet_id,
                wallet_type=agent_wallet_instance.wallet_type,
                transaction_type=sender_trans_type,
                amount=total_amount,
                balance_before=escrow_instance.balance_before,
                balance_after=escrow_instance.balance_after,
                liberty_reference=escrow_instance.liberty_reference,
                unique_reference=unique_reference,
                liberty_commission=escrow_instance.liberty_commission,
                total_amount_charged=escrow_instance.total_amount_charged,
                total_amount_sent_out=escrow_instance.total_amount_charged,
                beneficiary_account_name=service_user_instance.bvn_full_name if service_user_instance.bvn_first_name else service_user_instance.full_name,
                beneficiary_wallet_id=service_wallet_instance.wallet_id,
                beneficiary_wallet_type=service_wallet_instance.wallet_type,
                escrow_id=escrow_instance.escrow_id,
                narration=escrow_instance.narration,
                status="PENDING",
                transaction_leg="EXTERNAL",
            )

            # Get debit_credit_record_id
            if escrow_instance.debit_credit_record_id is not None:
                debit_credit_record_id = DebitCreditRecordOnAccount.objects.filter(id=escrow_instance.debit_credit_record_id).last()
                if debit_credit_record_id:
                    debit_credit_record_id.transaction_instance_id = sender_transaction.transaction_id
                    debit_credit_record_id.save()

            # Create transaction for Other Service User

            if escrow_instance.liberty_commission > 0:
                service_comm = service_comm - escrow_instance.liberty_commission

            receiver_reference = Transaction.create_liberty_reference(suffix="LGLP_FND_BUDDY")

            receiver_transaction = Transaction.objects.create(
                user=service_user_instance,
                wallet_id=service_wallet_instance.wallet_id,
                wallet_type=service_wallet_instance.wallet_type,
                transaction_type=receiver_trans_type,
                amount=service_comm,
                liberty_reference=receiver_reference,
                unique_reference=unique_reference,
                liberty_commission=escrow_instance.liberty_commission,
                total_amount_received=service_comm,
                escrow_id=escrow_instance.escrow_id,
                source_account_name=agent_user_instance.bvn_full_name,
                source_wallet_id=agent_wallet_instance.wallet_id,
                source_wallet_type=agent_wallet_instance.wallet_type,
                narration=escrow_instance.narration,
                status="PENDING",
                transaction_leg="EXTERNAL",
            )

            fund_service_user_balance = cls.fund_balance(
                user=service_user_instance,
                wallet=service_wallet_instance,
                amount=service_comm,
                trans_type=receiver_trans_type,
                transaction_instance_id=receiver_transaction.transaction_id,
                unique_reference=escrow_instance.escrow_id

            )

            service_user_balance_before = fund_service_user_balance["balance_before"]

            service_user_balance_after = WalletSystem.get_balance_after(
                user=service_user_instance,
                balance_before=service_user_balance_before,
                total_amount=service_comm,
                is_credit=True
            )

            sender_transaction.status = "SUCCESSFUL"
            receiver_transaction.status = "SUCCESSFUL"
            receiver_transaction.balance_before = service_user_balance_before
            receiver_transaction.balance_after = service_user_balance_after

            sender_transaction.save()
            receiver_transaction.save()

            new_response = {
                "status": True,
                "message": "Agent has been charged successfully",
                "escrow_id": escrow_id,
                "sender_trans_id": sender_transaction.transaction_id,
                "user_trans_id": receiver_transaction.transaction_id,
            }

            # Give Agent Commission If there is
            if agent_comm > 0:
                if escrow_instance.transfer_type == "LOTTO_PLAY":

                    get_sales_rep = OtherCommissionsRecord.check_if_sales_rep_exists(downline_user=agent_user_instance)

                    if get_sales_rep is None:
                        new_agent_comm = agent_comm
                        new_sales_rep_comm = 0
                        sales_rep = False

                    else:
                        calc_super_agent_profit = (ConstantTable.get_constant_table_instance().lotto_super_agent_profit / 100) * total_amount
                        new_agent_comm = agent_comm - calc_super_agent_profit
                        new_sales_rep_comm = calc_super_agent_profit
                        sales_rep = True

                    # Handle Agent Profit On Cash Out
                    if total_amount >= new_agent_comm + new_sales_rep_comm:

                        OtherCommissionsRecord.create_and_top_up_other_commissions_for_agent(
                            agent=agent_user_instance,
                            sales_rep=None,
                            amount=total_amount,
                            transaction_id=sender_transaction.transaction_id,
                            transaction_type=escrow_instance.transfer_type,
                            transaction_reason="Commission on LOTTO Play",
                            total_profit=agent_comm + escrow_instance.liberty_commission,
                            liberty_profit=escrow_instance.liberty_commission,
                            agent_cash_profit=new_agent_comm
                        )

                        if new_sales_rep_comm > 0 and sales_rep == True:
                            handle_ro_commission = OtherCommissionsRecord.create_and_top_up_sales_rep_commissions(
                                agent=agent_user_instance,
                                sales_rep=get_sales_rep.sales_rep,
                                amount=total_amount,
                                transaction_id=sender_transaction.transaction_id,
                                transaction_type="LOTTO_PLAY",
                                transaction_reason=f"Commission on LOTTO PLAY by {agent_user_instance.bvn_first_name}" if agent_user_instance.bvn_first_name else f"Commission on LOTTO PLAY by {agent_user_instance.email}",
                                total_profit=agent_comm + escrow_instance.liberty_commission,
                                liberty_profit=escrow_instance.liberty_commission,
                                ro_cash_profit=new_sales_rep_comm,
                                agent_cash_profit=new_agent_comm
                            )

            # DEBIT SENDER SMS

            sender_not_token = agent_user_instance.firebase_key
            sender_not_title = "Transaction Successful"
            sender_not_body = f"You have successfully paid N{total_amount} for {escrow_instance.transfer_type}"
            sender_not_data = {"amount_sent": f"{total_amount}", "available_balance": f"{agent_wallet_instance.available_balance}"}

            send_out_notification = cloud_messaging.send_broadcast(
                token=sender_not_token,
                title=sender_not_title,
                body=sender_not_body,
                data=sender_not_data
            )

            InAppTransactionNotification.create_in_app_transaction_notification(
                user=agent_user_instance,
                title=sender_not_title,
                message_body=sender_not_body
            )

            cls.transaction_alert_notfication_manager(
                user=agent_user_instance,
                amount=float(total_amount),
                cr_dr="DR",
                narration=f"{escrow_instance.narration}-TO BUDDY-{agent_user_instance.bvn_full_name}",
                from_wallet_type=agent_wallet_instance.wallet_type,
                transaction_instance_id=sender_transaction.transaction_id
            )

            ############################################################################################################################
            # CREDIT RECIEVER SMS

            receiver_not_token = service_user_instance.firebase_key
            receiver_not_title = "Payment Received"
            receiver_not_body = f"You have recieved a CREDIT of N{service_comm} from {agent_user_instance.bvn_full_name}"
            receiver_not_data = {"amount_sent": f"{service_comm}", "available_balance": f"{service_wallet_instance.available_balance}"}

            send_out_notification = cloud_messaging.send_broadcast(
                token=receiver_not_token,
                title=receiver_not_title,
                body=receiver_not_body,
                data=receiver_not_data
            )

            InAppTransactionNotification.create_in_app_transaction_notification(
                user=service_user_instance,
                title=receiver_not_title,
                message_body=receiver_not_body
            )

            cls.transaction_alert_notfication_manager(
                user=service_user_instance,
                amount=float(service_comm),
                cr_dr="CR",
                narration=f"{escrow_instance.narration}-FRM AGENT-{agent_user_instance.bvn_full_name}",
                from_wallet_type=service_wallet_instance.wallet_type,
                transaction_instance_id=receiver_transaction.transaction_id
            )

        return new_response

    @classmethod
    def fund_user_on_pay_by_ussd(
            cls,
            user_instance,
            ussd_trans_instance,
            liberty_commission,
            wallet,
            amount,
            narration,
    ):

        escrow_instance = Escrow.objects.create(
            user=user_instance,
            amount=amount,
            liberty_commission=liberty_commission,
            internal_escrow=False,
            external_escrow=False,
            narration=narration
        )

        if ussd_trans_instance.trans_type == "FUND_BY_USSD":
            trans_charge = (amount / 100) * 1
        else:
            trans_charge = (amount / 100) * 1.3

        fund_buddy_balance = cls.fund_balance(
            user=user_instance,
            wallet=wallet,
            amount=amount - trans_charge,
            trans_type=ussd_trans_instance.trans_type,
            unique_reference=escrow_instance.escrow_id
        )

        buddy_balance_before = fund_buddy_balance["balance_before"]

        buddy_balance_after = WalletSystem.get_balance_after(
            user=user_instance,
            balance_before=buddy_balance_before,
            total_amount=amount,
            is_credit=True
        )

        # Create transaction for Receiver
        receiver_transaction = Transaction.objects.create(
            user=user_instance,
            wallet_id=wallet.wallet_id,
            wallet_type=wallet.wallet_type,
            transaction_type=ussd_trans_instance.trans_type,
            amount=amount,
            liberty_reference=ussd_trans_instance.liberty_reference,
            liberty_commission=liberty_commission,
            total_amount_received=amount,
            escrow_id=escrow_instance.escrow_id,
            source_account_name=f"{ussd_trans_instance.first_name} {ussd_trans_instance.last_name}",
            narration=narration,
            status="SUCCESSFUL",
            transaction_leg="EXTERNAL",
            balance_before=buddy_balance_before,
            balance_after=buddy_balance_after
        )

        debit_credit_record = fund_buddy_balance["record"]
        debit_credit_record.transaction_instance_id = receiver_transaction.transaction_id
        debit_credit_record.save()

        ############################################################################################################################
        # CREDIT RECIEVER SMS

        receiver_not_token = user_instance.firebase_key
        receiver_not_title = "Payment Received"
        receiver_not_body = f"You have recieved a CREDIT of N{amount} from {user_instance.bvn_full_name}"
        receiver_not_data = {"amount_sent": f"{amount}", "available_balance": f"{wallet.available_balance}"}

        send_out_notification = cloud_messaging.send_broadcast(
            token=receiver_not_token,
            title=receiver_not_title,
            body=receiver_not_body,
            data=receiver_not_data
        )

        InAppTransactionNotification.create_in_app_transaction_notification(
            user=user_instance,
            title=receiver_not_title,
            message_body=receiver_not_body
        )

        cls.transaction_alert_notfication_manager(
            user=user_instance,
            amount=float(amount),
            cr_dr="CR",
            narration=f"{escrow_instance.narration}-FRM-{ussd_trans_instance.first_name} {ussd_trans_instance.last_name}",
            from_wallet_type=wallet.wallet_type,
            transaction_instance_id=receiver_transaction.transaction_id
        )


# ACCOUNT SYSTEM

class DebitCreditRecordOnAccount(models.Model):
    DEBIT = "DEBIT"
    CREDIT = "CREDIT"

    TRANS_TYPE_DEBIT_OR_CREDIT_CHOICES = [
        (CREDIT, "CREDIT"),
        (DEBIT, "DEBIT"),
    ]

    date_created = models.DateTimeField(auto_now_add=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    entry = models.CharField(max_length=200, choices=TRANS_TYPE_DEBIT_OR_CREDIT_CHOICES)
    wallet = models.ForeignKey(WalletSystem, on_delete=models.SET_NULL, null=True)
    balance_before = models.FloatField(default=0)
    amount = models.FloatField(validators=[MinValueValidator(0.0)])
    balance_after = models.FloatField(default=0)
    wallet_type = models.CharField(max_length=200, null=True, blank=True)
    type_of_trans = models.CharField(max_length=200, null=True, blank=True)
    transaction_instance_id = models.CharField(max_length=400, null=True, blank=True)
    unique_reference = models.CharField(max_length=100, unique=True, null=True, blank=True)
    reversal_trans_id = models.CharField(max_length=400, null=True, blank=True)
    last_updated = models.DateTimeField(auto_now=True)
    float_bal_before = models.FloatField(default=0)
    float_bal_after = models.FloatField(default=0)
    float_bal_done = models.BooleanField(default=False)
    is_hold = models.BooleanField(default=False)
    hold_balance_before = models.FloatField(default=0)
    hold_balance_after = models.FloatField(default=0)
    transaction_updated = models.BooleanField(default=False)
    is_paybox_merchant = models.BooleanField(default=False)

    def __str__(self):
        return str(self.user.email)

    def save(self, *args, **kwargs):
        self.balance_before = round(self.balance_before, 2)
        self.balance_after = round(self.balance_after, 2)
        self.is_paybox_merchant = self.user.is_paybox_merchant

        if self.float_bal_done:
            pass
        else:

            float_user = WalletSystem.get_float_user()
            float_gen_wallet_before = WalletSystem.get_wallet(user=float_user, from_wallet_type="GENERAL_FLOAT_BEFORE")
            float_gen_wallet_after = WalletSystem.get_wallet(user=float_user, from_wallet_type="GENERAL_FLOAT_AFTER")
            float_gen_wallet = WalletSystem.get_wallet(user=float_user, from_wallet_type="GENERAL_FLOAT")

            if float_gen_wallet:
                self.float_bal_before = float_gen_wallet.available_balance

                # if float_gen_wallet_before:
                #     float_gen_wallet_before.available_balance = float_gen_wallet.available_balance
                #     float_gen_wallet_before.save()

                if self.user != float_user:
                    self.float_bal_before = float_gen_wallet.available_balance

                    if self.user.sms_subscription:
                        whisper_charge = settings.WHISPER_CHARGE
                    else:
                        whisper_charge = 0.00

                    if self.entry == "CREDIT":
                        calc_bal_after = float_gen_wallet.available_balance + self.amount - whisper_charge
                    else:
                        calc_bal_after = float_gen_wallet.available_balance - self.amount - whisper_charge

                    self.float_bal_after = calc_bal_after

                    # if float_gen_wallet:
                    #     float_gen_wallet.available_balance = calc_bal_after
                    #     float_gen_wallet.save()

                    # float_gen_wallet_before.available_balance = calc_bal_after
                    # float_gen_wallet_before.save()

                    # if float_gen_wallet_after:
                    #     float_gen_wallet_after.available_balance = calc_bal_after
                    #     float_gen_wallet_after.save()

                    self.float_bal_done = True

            # if not self.transaction_instance_id or self.transaction_updated:
            #     pass
            # else:
            #     transaction_instance = Transaction.objects.filter(transaction_id=self.transaction_instance_id).last()
            #     transaction_instance.float_bal_before = self.float_bal_before
            #     transaction_instance.float_bal_after = self.float_bal_after
            #     print(transaction_instance.float_bal_after)
            #     print(transaction_instance.float_bal_after)
            #     transaction_instance.save()

            #     self.transaction_updated = True

        super(DebitCreditRecordOnAccount, self).save(*args, **kwargs)


class DebitCreditRecordOnAccountAudit(models.Model):
    DEBIT = "DEBIT"
    CREDIT = "CREDIT"

    TRANS_TYPE_DEBIT_OR_CREDIT_CHOICES = [
        (CREDIT, "CREDIT"),
        (DEBIT, "DEBIT"),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    entry = models.CharField(max_length=200, choices=TRANS_TYPE_DEBIT_OR_CREDIT_CHOICES)
    wallet = models.ForeignKey(WalletSystem, on_delete=models.CASCADE)
    balance_before = models.FloatField(default=0)
    amount = models.FloatField()
    balance_after = models.FloatField(default=0)
    wallet_type = models.CharField(max_length=200, null=True, blank=True)
    type_of_trans = models.CharField(max_length=200, null=True, blank=True)
    transaction_instance_id = models.CharField(max_length=300, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    float_bal_before = models.FloatField(default=0)
    float_bal_after = models.FloatField(default=0)
    float_bal_done = models.BooleanField(default=False)
    transaction_updated = models.BooleanField(default=False)

    def __str__(self):
        return str(self.user.email)


class AccountSystem(models.Model):
    # VFD_PROVIDER = "VFD"
    # WOVEN_PROVIDER = "WOVEN"

    # ACCOUNT_PROVIDERS = [(VFD_PROVIDER, "VFD"), (WOVEN_PROVIDER, "WOVEN")]

    TRUE_ACCOUNT_TYPES = [
        ("PERSONAL", "PERSONAL"),
        ("CORPORATE", "CORPORATE"),
        ("AJO_USER_CORPORATE", "AJO_USER_CORPORATE"),
        ("AJO_USER_PERSONAL", "AJO_USER_PERSONAL"),
        ("CORPORATE_LIBERTY_RETAIL", "CORPORATE_LIBERTY_RETAIL"),
    ]

    user = models.ForeignKey(User, related_name="accounts", on_delete=models.CASCADE)
    wallet = models.ForeignKey(
        WalletSystem, related_name="accounts", on_delete=models.SET_NULL, null=True, blank=True
    )
    account_id = models.UUIDField(default=uuid.uuid4, editable=False)
    account_provider = models.CharField(max_length=300, choices=AccountProviders.choices)
    account_type = models.CharField(max_length=300, choices=AccountTypes.choices)
    true_account_type = models.CharField(max_length=300, choices=TRUE_ACCOUNT_TYPES, default="PERSONAL")
    bank_name = models.CharField(max_length=300)
    bank_code = models.CharField(max_length=300)
    account_number = models.CharField(max_length=250, null=True, blank=True)
    account_name = models.CharField(max_length=250, null=True, blank=True)
    available_balance = models.FloatField(default=0.00)
    other_balance = models.FloatField(default=0.00)
    is_active = models.BooleanField(default=True)
    is_test = models.BooleanField(default=False)
    extra = models.BooleanField(default=False)
    initial_payload = models.TextField(null=True)
    payload = models.TextField(null=True)
    vfd_account_id = models.CharField(max_length=100, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now_add=True)

    @classmethod
    def get_uncreated_accounts(cls, user):
        return cls.objects.filter(user=user)
        # account_list = []
        # if get_accounts:
        #     for acct in get_accounts:
        #         account_list.append(acct)
        # return account_list

    @classmethod
    def get_created_account_type(cls, user):

        user_accounts_cache_id = f"{user.id}_accounts"

        accounts_list = cache.get(user_accounts_cache_id)
        if accounts_list is None:
            get_accounts = cls.objects.filter(user=user)
            accounts_list = []
            if get_accounts:
                for account in get_accounts:
                    # accounts_list.append(account.account_type)
                    accounts_list.append(
                        {
                            "acc_type": account.account_type,
                            "acc_prov": account.account_provider,
                        }
                    )

            cache.set(user_accounts_cache_id, accounts_list)
        return accounts_list

    @classmethod
    def create_account_by_provider(cls, user, wallet, provider):
        if provider == AccountProviders.VFD_PROVIDER:
            collection_account = AccountSystem.create_vfd_collection_account(
                user=user, wallet=wallet
            )
        elif provider == AccountProviders.WEMA_PROVIDER:
            collection_account = AccountSystem.create_wema_account(
                user=user, wallet=wallet
            )
        elif provider == AccountProviders.FIDELITY_PROVIDER:
            collection_account = AccountSystem.create_fidelity_account(
                user=user, wallet=wallet
            )
        else:
            collection_account = None

        return collection_account

    @classmethod
    def create_woven_collection_account(cls, user, wallet):
        user_collection_name = f"{user.full_name} (COLLECTION-{user.id}{str(uuid.uuid4())[0:1]})"
        retry_count = 3
        woven_intance = Woven.create_reserved_account(
            account_name=user_collection_name, retry_count=retry_count
        )
        if woven_intance["status"] == "success":
            response = woven_intance["data"]
            AccountSystem.objects.create(
                user=user,
                wallet=wallet,
                account_provider="WOVEN",
                account_type="COLLECTION",
                bank_name=response["bank_name"],
                bank_code="035",
                account_number=response["vnuban"],
                account_name=response["account_name"],
                is_active=True,
                available_balance=0.00,
                payload=woven_intance,
            )
        else:
            AccountCreationFailure.objects.create(
                user=user,
                account_provider="WOVEN",
                account_type="COLLECTION",
                payload=woven_intance,
            )

    @classmethod
    def create_woven_spend_account(cls, user, wallet):
        user_spend_name = f"{user.full_name} (SPEND-{user.id}{str(uuid.uuid4())[0:1]})"
        retry_count = 3
        woven_intance = Woven.create_reserved_account(
            account_name=user_spend_name, retry_count=retry_count
        )

        if woven_intance["status"] == "success":
            response = woven_intance["data"]
            AccountSystem.objects.create(
                user=user,
                wallet=wallet,
                account_provider="WOVEN",
                account_type="SPEND",
                bank_name=response["bank_name"],
                bank_code="035",
                account_number=response["vnuban"],
                account_name=response["account_name"],
                is_active=True,
                available_balance=0.00,
                payload=woven_intance,
            )
        else:
            AccountCreationFailure.objects.create(
                user=user,
                account_provider="WOVEN",
                account_type="SPEND",
                payload=woven_intance,
            )

    @classmethod
    def create_vfd_collection_account(cls, user, wallet):
        if settings.ENVIRONMENT == "production":
            is_test_account = False
        else:
            is_test_account = True

        user_phone_number = User.format_number_from_back_add_234(
            user.check_kyc.bvn_rel.bvn_phone_number) if user.check_kyc.bvn_rel.bvn_phone_number else User.format_number_from_back_add_234(
            user.phone_number)

        retry_count = 3

        #  if user.vfd_bvn_acct_num_count < 1 or overide == True:
        # get_any_previous_account_num = get_previous_account_num_func(user=user, account_type=``)
        vfd_create = None

        try:
            vfd_create = VFDBank.create_vfd_wallet(user=user, user_phone_number=user_phone_number, retry_count=retry_count)
            if vfd_create.get("main_data").get("status") == "929":
                # If an account already exists, create new one. This is for OtherAccountCreation
                account_no = vfd_create.get("main_data").get("data").get("accountNo")
                vfd_create = VFDBank.create_vfd_wallet(
                    user=user, user_phone_number=user_phone_number, retry_count=retry_count, existing_account=account_no
                )

        except Exception as err:
            print(f"VFD Wallet create error: {err}")
            pass

        # previous_account_no = get_previous_account_num_func(user, "PERSONAL")
        # print(previous_account_no)
        # vfd_create = VFDBank.create_vfd_wallet_v2(user, previous_account_no=previous_account_no)

        if vfd_create is not None:

            if vfd_create.get("main_data").get("status") == "00":
                response = vfd_create.get("main_data").get("data")
                AccountSystem.objects.create(
                    user=user,
                    wallet=wallet,
                    account_provider="VFD",
                    account_type="COLLECTION",
                    bank_name="VFD Microfinance Bank",
                    bank_code="999999",
                    account_number=response["accountNo"],
                    account_name=f'{response.get("firstname")} {response.get("lastname")}',
                    is_active=True,
                    is_test=is_test_account,
                    available_balance=0.00,
                    initial_payload=vfd_create.get("initial_payload"),
                    payload=vfd_create,
                )
                ############################################################################
                # UPDATE NUMBER OF USER ACCOUNTS

                user.vfd_bvn_acct_num_count += 1
                user.save(update_fields=["vfd_bvn_acct_num_count"])

                for user_data in User.objects.exclude(id=user.id).filter(bvn_number=user.check_kyc.bvn_rel.bvn_number):
                    user_data.vfd_bvn_acct_num_count = user.vfd_bvn_acct_num_count
                    user_data.save(update_fields=["vfd_bvn_acct_num_count"])
            ############################################################################
            else:
                AccountCreationFailure.objects.create(
                    user=user,
                    account_provider="VFD",
                    account_type="COLLECTION",
                    is_test=is_test_account,
                    initial_payload=vfd_create.get("initial_payload"),
                    payload=vfd_create,
                )
        else:
            AccountCreationFailure.objects.create(
                user=user,
                account_provider="VFD",
                account_type="COLLECTION",
                is_test=is_test_account,
                initial_payload=vfd_create,
                payload=vfd_create,
            )

    @classmethod
    def create_vfd_spend_account(cls, user, wallet):
        if settings.ENVIRONMENT == "production":
            is_test_account = False
        else:
            is_test_account = True

        user_spend_name = f"{user.full_name} (SPEND-{user.id})"
        user_phone_number = User.format_number_from_back_add_234(
            user.check_kyc.bvn_rel.bvn_phone_number) if user.check_kyc.bvn_rel.bvn_phone_number else User.format_number_from_back_add_234(
            user.phone_number)
        retry_count = 3

        try:
            vfd_create = VFDBank.create_vfd_wallet(user=user, user_phone_number=user_phone_number, retry_count=retry_count)
        except Exception as err:
            print(f"VFD wallet creation error: {err}")
            pass

        if vfd_create is not None:
            if vfd_create.get("main_data").get("status") == "00":
                response = vfd_create.get("main_data").get("data")

                AccountSystem.objects.create(
                    user=user,
                    wallet=wallet,
                    account_provider="VFD",
                    account_type="SPEND",
                    bank_name="VFD Microfinance Bank",
                    bank_code="999999",
                    account_number=response["accountNo"],
                    account_name=f'{response.get("firstname")} {response.get("lastname")}',
                    is_active=True,
                    is_test=is_test_account,
                    available_balance=0.00,
                    initial_payload=vfd_create.get("initial_payload"),
                    payload=vfd_create,
                )

                ############################################################################
                # UPDATE NUMBER OF USER ACCOUNTS

                user.vfd_bvn_acct_num_count += 1
                user.save(update_fields=["vfd_bvn_acct_num_count"])

                for user_data in User.objects.exclude(id=user.id).filter(bvn_number=user.check_kyc.bvn_rel.bvn_number):
                    user_data.vfd_bvn_acct_num_count = user.vfd_bvn_acct_num_count
                    user_data.save(update_fields=["vfd_bvn_acct_num_count"])
                ############################################################################


            else:
                AccountCreationFailure.objects.create(
                    user=user,
                    account_provider="VFD",
                    account_type="SPEND",
                    is_test=is_test_account,
                    initial_payload=vfd_create.get("initial_payload"),
                    payload=vfd_create,
                )
        else:
            AccountCreationFailure.objects.create(
                user=user,
                account_provider="VFD",
                account_type="SPEND",
                is_test=is_test_account,
                initial_payload=vfd_create.get("initial_payload"),
                payload=vfd_create,
            )

    @classmethod
    def create_accounts(cls, user, wallet: WalletSystem, account_type=AccountTypes.COLLECTION, provider: str = None):
        if provider and provider not in AccountProviders.values:
            raise Exception(f"{provider} is not a valid account provider.")

        if not provider:
            provider = ConstantTable.get_constant_table_instance().account_creation_provider

        if not AccountSystem.get_uncreated_accounts(user=user).filter(account_provider=provider, account_type=account_type).exists():
            cls.create_account_by_provider(user, wallet, provider)

        ## ATTEMPT TO CREATE A DEFAULT WEMA AND FIDELITY ACCOUNTS AS THIS IS REQUIRED FOR ALL USERS
        wema_account = AccountSystem.objects.filter(user=user, bank_name="Wema Bank PLC")
        fidelity_account = AccountSystem.objects.filter(user=user, bank_name="Fidelity Bank Plc")

        if not wema_account.exists() and account_type == AccountTypes.COLLECTION:
            cls.create_wema_account(user, wallet)
            cls.create_vfd_collection_account(user, wallet)

        if not fidelity_account.exists() and account_type == AccountTypes.COLLECTION:
            cls.create_fidelity_account(user, wallet)

        return True

    @classmethod
    def create_wema_account(cls, user, wallet, account_type=AccountTypes.COLLECTION):
        if settings.ENVIRONMENT == "production":
            is_test_account = False
        else:
            is_test_account = True

        user_account_num_track = UserAccountNumCount.get_counts(user=user)
        acct_num_count = user_account_num_track.wema_count

        if UserOtherAccount.objects.filter(other_account=user).exists():
            acct_num_count += 1

        response = None

        while True:
            wema_create = WemaBank.create_wema_account(user=user, acct_num_count=acct_num_count)
            print(wema_create)
            if wema_create is not None and wema_create.get("main_data").get("status_code") == 201:
                response = wema_create.get("main_data", {}).get("data", {})  # .get("account_details")
                acct_num_count += 10
            else:
                break
            if not AccountSystem.objects.filter(bank_name="Wema Bank PLC", account_provider=AccountProviders.WEMA_PROVIDER,
                                                account_number=response["account_number"]).exists():
                break

        if response:
            AccountSystem.objects.create(
                user=user,
                wallet=wallet,
                account_provider=AccountProviders.WEMA_PROVIDER,
                account_type=AccountTypes.COLLECTION,
                bank_name="Wema Bank PLC",
                bank_code="000017",
                account_number=response["account_number"],
                account_name=f'{response.get("firstname")} {response.get("lastname")}',
                is_active=True,
                is_test=is_test_account,
                available_balance=0.00,
                initial_payload=wema_create.get("initial_payload"),
                payload=wema_create,
            )
            ############################################################################
            # UPDATE NUMBER OF USER ACCOUNTS

            user_account_num_track.wema_count += 1
            user_account_num_track.save()

            for other_users in User.objects.exclude(id=user.id).filter(bvn_number=user.check_kyc.bvn_rel.bvn_number):
                other_user_count = UserAccountNumCount.get_counts(user=other_users)
                other_user_count.wema_count += 1
                other_user_count.save()

        ############################################################################

        else:
            AccountCreationFailure.objects.create(
                user=user,
                account_provider=AccountProviders.WEMA_PROVIDER,
                account_type=AccountTypes.COLLECTION,
                is_test=is_test_account,
                initial_payload=wema_create.get("initial_payload"),
                payload=wema_create,
            )

    @classmethod
    def create_fidelity_account(cls, user, wallet, account_type=AccountTypes.COLLECTION):
        """
        Create a Fidelity Bank account for a user.

        Args:
            user: The user object for whom to create the account
            wallet: The wallet to associate with the account
            account_type: The type of account to create (default: COLLECTION)

        Returns:
            AccountSystem: The created account object or None if creation failed
        """
        if settings.ENVIRONMENT == "production":
            is_test_account = False
        else:
            is_test_account = True

        # Get user details for account creation
        first_name = user.check_kyc.bvn_rel.bvn_first_name if hasattr(user, 'check_kyc') and hasattr(user.check_kyc, 'bvn_rel') else user.first_name
        last_name = user.check_kyc.bvn_rel.bvn_last_name if hasattr(user, 'check_kyc') and hasattr(user.check_kyc, 'bvn_rel') else user.last_name
        middle_name = user.check_kyc.bvn_rel.bvn_middle_name if hasattr(user, 'check_kyc') and hasattr(user.check_kyc, 'bvn_rel') and user.check_kyc.bvn_rel.bvn_middle_name else ""
        email = user.email
        phone_no = user.phone_number
        bvn = user.check_kyc.bvn_rel.bvn_number if hasattr(user, 'check_kyc') and hasattr(user.check_kyc, 'bvn_rel') else ""
        dob = user.check_kyc.bvn_rel.bvn_birthdate if hasattr(user, 'check_kyc') and hasattr(user.check_kyc, 'bvn_rel') else ""
        gender = user.check_kyc.bvn_rel.bvn_gender if hasattr(user, 'check_kyc') and hasattr(user.check_kyc, 'bvn_rel') else ""
        title = "Mr"
        state = str(user.state) if user.state else "Lagos"
        marital_status = str(user.marital_status).lower() if user.marital_status is not None else "single"
        if str(gender).lower() == "female":
            if marital_status == "single":
                title = "Miss"
            else:
                title = "Mrs"

        date_object = datetime.strptime(dob, "%Y-%m-%d")
        fidelity_formatted_date = date_object.strftime("%d-%m-%Y")

        address = str(user.street) if user.street else ""
        address2 = str(user.nearest_landmark) if user.nearest_landmark else ""
        lga = str(user.lga) if user.lga else ""

        # Call the core banking API to create a Fidelity account
        from main.helper.core_banking import LibertyCoreBankingAPI

        try:
            fidelity_account = LibertyCoreBankingAPI.create_account(
                first_name=first_name,
                last_name=last_name,
                middle_name=middle_name,
                email=email,
                phone_no=phone_no,
                bvn=bvn,
                provider="FIDELITY",
                dob=fidelity_formatted_date,
                gender=gender,
                title=title,
                address_1=address,
                address_2=address2,
                city=state,
                state=state,
                country="Nigeria",
                local_govt=lga,
                marital_status=str(user.marital_status) if user.marital_status is not None else "single"
            )

            if fidelity_account.get("status") == "success":
                response_data = fidelity_account.get("data", {})

                # Create the account in the system
                account = AccountSystem.objects.create(
                    user=user,
                    wallet=wallet,
                    account_provider="FIDELITY",
                    account_type=account_type,
                    bank_name="Fidelity Bank Plc",
                    bank_code="000007",  # Fidelity Bank code
                    account_number=response_data.get("account_number"),
                    account_name=response_data.get("account_name") or f"{first_name} {last_name}",
                    is_active=True,
                    is_test=is_test_account,
                    available_balance=0.00,
                    initial_payload=fidelity_account.get("request_payload"),
                    payload=fidelity_account
                )

                # Update the user's account count
                user_account_num_track = UserAccountNumCount.get_counts(user=user)
                user_account_num_track.fidelity_count += 1
                user_account_num_track.save()

                # Update BVN-linked users' account counts
                if hasattr(user, 'check_kyc') and hasattr(user.check_kyc, 'bvn_rel') and user.check_kyc.bvn_rel.bvn_number:
                    for other_users in User.objects.exclude(id=user.id).filter(bvn_number=user.check_kyc.bvn_rel.bvn_number):
                        other_user_count = UserAccountNumCount.get_counts(user=other_users)
                        other_user_count.fidelity_count += 1
                        other_user_count.save()

                return account
            else:
                # Log the error
                AccountCreationFailure.objects.create(
                    user=user,
                    account_provider="FIDELITY",
                    account_type=account_type,
                    is_test=is_test_account,
                    initial_payload=fidelity_account.get("request_payload"),
                    payload=fidelity_account,
                )
                return None

        except Exception as e:
            print(f"Error creating Fidelity account: {str(e)}")
            return None

    @classmethod
    def handle_bvn_consent(cls, bvn_number):
        get_consent = VFDBank.get_bvn_consent(bvn=bvn_number)

        status = get_consent.get("main_data").get("status")
        status_code = get_consent.get("main_data").get("data").get("statusCode")
        url = get_consent.get("main_data").get("data", {}).get("url")

        if status == "00":
            if status_code == "true":
                return True, None
            else:
                return False, url
        else:
            return None, None

    @classmethod
    def get_default_provider_accounts(cls, user):
        """
        # This function takes in a user instance and selected active account provider details
        """
        get_provider = cls.get_provider_type(user=user)
        # default_provider = ConstantTable.default_account_provider()
        # accounts = cls.objects.filter(user=user, account_provider=get_provider)
        accounts = cls.objects.filter(user=user, account_provider__in=["VFD", "WEMA", "FIDELITY"])
        return accounts

    @classmethod
    def fetch_accounts(cls, user):
        """
        # This function takes in a user instance and selected active account provider details
        """
        default_provider = cls.get_provider_type(user=user)

        accounts = cls.objects.select_related('user', 'wallet').filter(user=user, account_provider=default_provider)
        return accounts

    @classmethod
    def get_account_type(cls, user, from_wallet_type, from_provider_type):
        accounts_queryset = cls.fetch_accounts(user=user)
        account = accounts_queryset.filter(
            account_type=from_wallet_type, account_provider=from_provider_type
        ).first()

        return account

    @classmethod
    def get_provider_type(cls, user: User):
        # provider_name = user.accounts.all().first().account_provider
        if user.terminal_id and user.terminal_serial and user.terminal_provider is not None and user.custom_account_provider:
            provider_name = user.custom_account_provider
        else:
            provider_name = ConstantTable.default_account_provider()
        return provider_name

    @classmethod
    def get_float_account(cls, from_wallet_type, from_provider_type):
        """
        # This function returns the active float account provider details
        """
        # cache_key = "float_account_inst"
        # account = cache.get(cache_key)
        # if account is None:
        float_user = User.objects.get(email=f"{settings.FLOAT_USER_EMAIL}")
        accounts_queryset = cls.fetch_accounts(user=float_user)

        account = accounts_queryset.filter(
            account_type=from_wallet_type, account_provider=from_provider_type
        ).first()

        # cache.set(cache_key, account)

        return account

    # @classmethod
    # def get_electronic_levy_account(cls, ):

    @classmethod
    def get_dynamic_float_account(cls, from_wallet_type, from_provider_type):
        """
        # This function returns the active float account provider details
        """
        float_user = User.objects.get(email=f"{settings.FLOAT_USER_EMAIL}")
        accounts_queryset = AccountSystem.objects.filter(user=float_user)

        account = accounts_queryset.filter(
            account_type=from_wallet_type, account_provider=from_provider_type
        ).first()

        return account

    @classmethod
    def get_all_vfd_accounts(cls):
        float_user = User.get_float_user()
        return cls.objects.exclude(user=float_user).filter(account_provider=AccountProviders.VFD_PROVIDER,
                                                           account_type__in=[AccountTypes.COLLECTION, AccountTypes.OTHERS, AccountTypes.SPEND])

    @classmethod
    def move_to_escrow_bank_transfer(
            cls,
            user,
            from_wallet_id,
            from_wallet_type,
            amount,
            balance_before,
            balance_after,
            from_provider_type,
            liberty_commission,
            total_amount_charged,
            user_account_number,
            user_account_name,
            user_bank_name,
            user_bank_code,
            user_account_provider,
            to_account_name,
            to_nuban,
            to_bank_name,
            to_bank_code,
            narration,
            is_beneficiary,
            is_recurring,
            transaction_mode,
            transfer_type,
            debit_credit_record_id=None,
            ip_addr=None,
            send_by_card_rrn=None,
            customer_reference=None,
            bulk_id=None,
            pos_charge=0,
            pos_charge_type=None,
            liberty_reference=None,
            fund_liberty_reference=None,
            vfd_account_id=None
    ):
        # send_money_transfer_fee = ConstantTable.get_constant_table_instance().send_money_transfer_fee
        send_money_transfer_fee = ConstantTable.calculate_send_money_transaction_charge(user=user, amount=amount)
        send_money_transfer_extra_fee = ConstantTable.get_constant_table_instance().send_money_transfer_extra_fee

        escrow_instance = Escrow.objects.create(
            user=user,
            from_wallet_id=from_wallet_id,
            from_wallet_type=from_wallet_type,
            transfer_type=transfer_type,
            amount=amount,
            ip_addr=ip_addr,
            balance_before=balance_before,
            balance_after=balance_after,
            account_provider=from_provider_type,
            liberty_commission=liberty_commission,
            send_money_transfer_fee=send_money_transfer_fee,
            extra_fee=send_money_transfer_extra_fee,
            send_money_transfer_extra_fee=send_money_transfer_extra_fee,
            user_trans_band=user.trans_band,
            total_amount_charged=total_amount_charged,
            user_account_number=user_account_number,
            user_account_name=user_account_name,
            user_bank_name=user_bank_name,
            user_bank_code=user_bank_code,
            vfd_account_id=vfd_account_id if from_provider_type == "VFD" else user_account_number,
            user_account_provider=user_account_provider,
            to_account_name=to_account_name,
            to_nuban=to_nuban,
            to_bank_name=to_bank_name,
            to_bank_code=to_bank_code,
            narration=narration,
            is_beneficiary=is_beneficiary,
            is_recurring=is_recurring,
            transaction_mode=transaction_mode,
            debit_credit_record_id=debit_credit_record_id,
            send_money_by_card=True if send_by_card_rrn else False,
            send_by_card_rrn=send_by_card_rrn,
            customer_reference=customer_reference,
            bulk_id=bulk_id,
            pos_charge=pos_charge,
            pos_charge_type=pos_charge_type,
            liberty_reference=liberty_reference,
            fund_liberty_reference=fund_liberty_reference,
        )

        return escrow_instance

    @classmethod
    def general_send_money(
            cls,
            user: User,
            escrow_instance: 'Escrow',
            from_provider_type,
            trans_started
    ):

        transfer_charge = ConstantTable.get_provider_fee(from_provider_type=from_provider_type)

        escrow_id = escrow_instance.escrow_id

        if from_provider_type == TransferProviderType.VFD:
            liberty_reference = Transaction.create_liberty_reference(suffix="LGLP-VFDE")
        elif from_provider_type == TransferProviderType.WEMA:
            liberty_reference = Transaction.create_liberty_reference(suffix="LGLP-WEMA")
        elif from_provider_type == TransferProviderType.FIDELITY:
            liberty_reference = Transaction.create_liberty_reference(suffix="LGLP-FIDE")
        else:
            raise RuntimeError(f"Invalid Provider Class: {from_provider_type}")

        # Cache This
        bank_float_account = AccountSystem.get_dynamic_float_account(from_wallet_type="FLOAT", from_provider_type=from_provider_type)

        if bank_float_account is not None:
            bank_float_balance_before = bank_float_account.wallet.available_balance
        else:
            bank_float_balance_before = 0.00

        trans_stopped = datetime.now()
        delta_100 = timedelta(milliseconds=100)

        trans_time = trans_stopped - trans_started + delta_100

        external_transaction = Transaction.objects.create(
            user=user,
            escrow_instance=escrow_instance,
            account_provider=from_provider_type,
            bank_float_balance_before=bank_float_balance_before,
            wallet_id=escrow_instance.from_wallet_id,
            wallet_type=escrow_instance.from_wallet_type,
            transaction_type="SEND_BANK_TRANSFER",
            amount=escrow_instance.amount,
            balance_before=escrow_instance.balance_before,
            balance_after=escrow_instance.balance_after,
            extra_fee=escrow_instance.send_money_transfer_extra_fee,
            liberty_commission=escrow_instance.liberty_commission,
            liberty_reference=liberty_reference,
            provider_fee=transfer_charge,
            total_amount_sent_out=escrow_instance.amount,
            escrow_id=escrow_id,
            beneficiary_account_name=escrow_instance.to_account_name,
            beneficiary_nuban=escrow_instance.to_nuban,
            beneficiary_bank_code=escrow_instance.to_bank_code,
            beneficiary_bank_name=escrow_instance.to_bank_name,
            source_nuban=escrow_instance.user_account_number,
            source_account_name=escrow_instance.to_account_name,
            narration=escrow_instance.narration,
            status="IN_PROGRESS",
            transaction_leg="EXTERNAL",
            trans_time=trans_time
        )

        cache.set(external_transaction.transaction_id, external_transaction)

        return external_transaction.transaction_id

    @classmethod
    def account_send_money_external(
            cls, external_trans: 'Transaction', amount
    ):

        escrow_instance = external_trans.escrow_instance
        user = escrow_instance.user
        escrow_id = escrow_instance.escrow_id

        from_provider_type = escrow_instance.account_provider
        beneficiary_account_name = escrow_instance.to_account_name
        beneficiary_nuban = escrow_instance.to_nuban
        beneficiary_bank_code = escrow_instance.to_bank_code

        # Cache This
        source_account_number = AccountSystem.get_float_account(
            from_wallet_type="FLOAT", from_provider_type=from_provider_type
        ).account_number

        liberty_reference = external_trans.liberty_reference

        amount = escrow_instance.amount

        print(f"Before Initiate {from_provider_type} transfer", user.email)
        print(":::::::::::::::::::::::::::::::::::::::::::::::::::::")

        if from_provider_type == TransferProviderType.VFD:
            vfd_account_id = escrow_instance.vfd_account_id
            assert vfd_account_id, RuntimeError(f"No VFD Account ID - {liberty_reference}")

            # beneficiary_bank_code = "035"
            if settings.ENVIRONMENT == "development":
                formatted_ben_bank_code = f"999{beneficiary_bank_code}"
                if formatted_ben_bank_code == "999999":
                    transfer_type = "intra"
                else:
                    transfer_type = "inter"

            else:
                formatted_ben_bank_code = beneficiary_bank_code
                if formatted_ben_bank_code in ["090110", "999999"]:
                    transfer_type = "intra"
                else:
                    transfer_type = "inter"

            resp = VFDBank.initiate_payout(
                beneficiary_account_name=beneficiary_account_name,
                beneficiary_nuban=beneficiary_nuban,
                beneficiary_bank_code=formatted_ben_bank_code,
                source_account=source_account_number,
                narration=escrow_instance.narration,
                amount=amount,
                transfer_type=transfer_type,
                user_bvn=user.check_kyc.bvn_rel.bvn_number,
                reference=liberty_reference,
                transfer_leg="TRANSFER",
                sender_account_id=vfd_account_id
            )

            if resp.get("status") == "00":
                unique_reference = resp.get("data").get("txnId")
            else:
                unique_reference = None

        elif from_provider_type in [TransferProviderType.WEMA, TransferProviderType.FIDELITY]:
            # Use LibertyCoreBankingAPI for WEMA and FIDELITY transfers
            from main.helper.core_banking import LibertyCoreBankingAPI

            resp = LibertyCoreBankingAPI.send_money(
                first_name=user.first_name,
                last_name=user.last_name,
                amount=amount,
                source_account=source_account_number,
                provider=from_provider_type,
                email=user.email,
                phone_number=user.phone_number,
                destination_account_no=beneficiary_nuban,
                destination_account_name=beneficiary_account_name,
                destination_bank_code=beneficiary_bank_code,
                narration=escrow_instance.narration
            )

            if resp.get("status") == "success":
                unique_reference = resp.get("reference")
            else:
                unique_reference = None

        else:
            raise RuntimeError(f"Invalid provider type: {from_provider_type}")

        external_trans.initial_transfer_response_payload = resp
        external_trans.save()

        # Get Account Balance
        # AccountSystem.get_balance_on_account_per_time(
        #     account_number = source_account_number,
        #     account_provider = from_provider_type
        # )

        escrow_instance.external_payload = resp
        escrow_instance.liberty_reference = liberty_reference
        escrow_instance.external_transaction_id = external_trans.transaction_id
        escrow_instance.save()

        # Create Transaction verification instance for verification

        if settings.ENVIRONMENT == "development":
            is_test_trans = True
        elif settings.ENVIRONMENT == "production":
            is_test_trans = False

        verification_instance = dict(
            transaction_instance=external_trans,
            user_id=user.id,
            user_email=user.email,
            account_provider=from_provider_type,
            transaction_leg=external_trans.transaction_leg,
            transaction_type=external_trans.transaction_type,
            timestamp=str(datetime.now()),
            escrow_id=escrow_id,
            amount=amount,
            liberty_reference=liberty_reference,
            is_test=is_test_trans,
            beneficiary_nuban=beneficiary_nuban,
            source_nuban=source_account_number,
            bank_code=beneficiary_bank_code
        )

        # For VFD, we need to wait a bit before verification
        if from_provider_type == TransferProviderType.VFD:
            time.sleep(10)  # This is to give the transfer a little time to settle of VFD before running TSQ

        verf_pending_trans = TransferVerificationObject.create_verfication_check(verification_instance)

        return resp

    @classmethod
    def reversals_or_move_money_to_float(
            cls,
            user,
            account_number_to_be_debited,
            float_account_name,
            float_account_number,
            float_bank_code,
            transfer_charge,
            narration,
            amount,
            escrow_id,
            liberty_reference,
            liberty_commission,
            total_amount_sent_out,
            account_provider,
            transaction_type,
            transaction_leg,
            reversal_type=None,
            resend_transaction_data=None,
            source_account=None
    ):

        if transaction_leg == "REVERSAL" and not resend_transaction_data:
            if Transaction.objects.exclude(status="IGNORE_HISTORY").filter(escrow_id=escrow_id, transaction_type="REVERSAL_BANK_TRANSFER").exists():
                return False

        # Get Escrow Instance
        escrow_instance = Escrow.objects.filter(escrow_id=escrow_id).last()

        if source_account is None:
            source_account = AccountSystem.objects.filter(account_number=account_number_to_be_debited).first()

        if resend_transaction_data:
            transaction_instance = resend_transaction_data
            resend = True
        else:
            transaction_instance = Transaction.objects.create(
                user=user,
                wallet_id=escrow_instance.from_wallet_id,
                account_id=source_account.account_id,
                wallet_type=escrow_instance.from_wallet_type,
                account_provider=account_provider,
                transaction_type=transaction_type,
                amount=amount,
                liberty_reference=liberty_reference,
                liberty_commission=liberty_commission,
                total_amount_sent_out=total_amount_sent_out,
                escrow_id=escrow_id,
                beneficiary_account_name=float_account_name,
                beneficiary_nuban=float_account_number,
                beneficiary_bank_code=float_bank_code,
                source_nuban=source_account.account_number,
                source_account_name=source_account.account_name,
                narration=narration,
                status="IN_PROGRESS",
                transaction_leg=transaction_leg,
                reversal_type=reversal_type
            )

            resend = False

        if transaction_leg == "REVERSAL":
            wallet_instance = WalletSystem.get_wallet(user=user, from_wallet_type=escrow_instance.from_wallet_type)
            # wallet_instance = WalletSystem.get_wallet(user=user, wallet_type=escrow_instance.wallet_type)

            user_balance_before = wallet_instance.available_balance

            user_balance_after = WalletSystem.get_balance_after(
                user=user,
                balance_before=user_balance_before,
                total_amount=total_amount_sent_out,
                is_credit=True
            )

            transaction_instance.balance_before = user_balance_before
            transaction_instance.balance_after = user_balance_after

            transaction_instance.save()

            extra_fee = escrow_instance.extra_fee
            amount = amount - extra_fee

            narration = "LP_REVERSAL2"

        new_amount = amount

        if narration == "RVSL_QLP_FUND_HOUSE":
            extra_fee = escrow_instance.extra_fee
            new_amount = amount - extra_fee

        resp = None

        ###########################################################
        # WOVEN
        if account_provider == "WOVEN":
            beneficiary_bank_code = "035"

            resp = Woven.initiate_payout(
                beneficiary_account_name=float_account_number,
                beneficiary_nuban=float_account_number,
                beneficiary_bank_code=beneficiary_bank_code,
                source_account=source_account.account_number,
                narration=narration,
                amount=new_amount,
                reference=liberty_reference
            )

            if resp.get("status") == "success":
                unique_reference = resp.get("data").get("unique_reference")
            else:
                unique_reference = None


        ###########################################################
        # VFD
        elif account_provider == "VFD":
            beneficiary_bank_code = "999999"

            resp = VFDBank.initiate_payout(
                beneficiary_account_name=float_account_name,
                beneficiary_nuban=float_account_number,
                beneficiary_bank_code=beneficiary_bank_code,
                source_account=source_account.account_number,
                narration=narration,
                amount=new_amount,
                transfer_type="intra",
                user_bvn=user.check_kyc.bvn_rel.bvn_number,
                reference=liberty_reference
            )

            if resp.get("status") == "00":
                unique_reference = resp.get("data").get("txnId")
            else:
                unique_reference = None

        ###########################################################
        # WEMA
        elif account_provider == "WEMA":
            from main.helper.core_banking import LibertyCoreBankingAPI

            resp = LibertyCoreBankingAPI.send_money(
                first_name=user.first_name,
                last_name=user.last_name,
                amount=new_amount,
                source_account=source_account.account_number,
                provider="WEMA",
                email=user.email,
                phone_number=user.phone_number,
                destination_account_no=float_account_number,
                destination_account_name=float_account_name,
                destination_bank_code=float_bank_code,
                narration=narration
            )

            if resp.get("status") == "success":
                unique_reference = resp.get("reference")
            else:
                unique_reference = None

        ###########################################################
        # FIDELITY
        elif account_provider == "FIDELITY":
            from main.helper.core_banking import LibertyCoreBankingAPI

            resp = LibertyCoreBankingAPI.send_money(
                first_name=user.first_name,
                last_name=user.last_name,
                amount=new_amount,
                source_account=source_account.account_number,
                provider="FIDELITY",
                email=user.email,
                phone_number=user.phone_number,
                destination_account_no=float_account_number,
                destination_account_name=float_account_name,
                destination_bank_code=float_bank_code,
                narration=narration
            )

            if resp.get("status") == "success":
                unique_reference = resp.get("reference")
            else:
                unique_reference = None

        transaction_instance.initial_transfer_response_payload = resp
        transaction_instance.save()

        # Get Account Balance
        AccountSystem.get_balance_on_account_per_time(
            account_number=source_account.account_number,
            account_provider=account_provider
        )

        escrow_instance.internal_payload = resp
        escrow_instance.internal_transaction_id = transaction_instance.transaction_id
        escrow_instance.save()

        if resend_transaction_data:
            pass
        else:
            # Create Pending Transaction for verification

            if settings.ENVIRONMENT == "development":
                is_test_trans = True
            elif settings.ENVIRONMENT == "production":
                is_test_trans = False

            verification_instance = dict(
                transaction_instance=transaction_instance,
                user_id=user.id,
                user_email=user.email,
                account_provider=account_provider,
                transaction_leg=transaction_instance.transaction_leg,
                transaction_type=transaction_instance.transaction_type,
                timestamp=str(datetime.now()),
                escrow_id=escrow_id,
                amount=amount,
                liberty_reference=liberty_reference,
                is_test=is_test_trans,
                beneficiary_nuban=float_account_number,
                source_nuban=source_account.account_number,
                bank_code=beneficiary_bank_code
            )

            verf_pending_trans = TransferVerificationObject.create_verfication_check(verification_instance)

        return True

    @classmethod
    def get_balance_on_account_per_time(cls, account_number, account_provider):

        from accounts.tasks import get_balance_on_every_transfer_task

        get_balance_on_every_transfer_task.delay(account_number=account_number, account_provider=account_provider)

        return True

    @classmethod
    def handle_out_of_books(cls, from_account, to_account, amount, escrow_id, user_bvn_number, transaction_instance: 'Transaction', liberty_reference,
                            narration=None):
        beneficiary_bank_code = "999999"

        amount = float(amount)
        float_data = AccountSystem.get_dynamic_float_account(from_wallet_type="FLOAT", from_provider_type="VFD")

        record_sub_com = OutOfBookTransfer.objects.create(
            type_of_transfer="OTHERS",
            provider="VFD",
            amount=amount,
            from_account=from_account,
            to_account=to_account,
            created_liberty_reference=liberty_reference,
            escrow_id=escrow_id,
        )

        if from_account == float_data.account_number:
            transfer_leg = "OUT_OF_BOOKS"
        else:
            transfer_leg = None

        reverse_sub_comm = VFDBank.initiate_payout_v1(
            beneficiary_account_name="to_account_name",
            beneficiary_nuban=to_account,
            beneficiary_bank_code=beneficiary_bank_code,
            source_account=from_account,
            narration=transaction_instance.narration if transaction_instance else narration,
            amount=amount,
            transfer_type="intra",
            user_bvn=user_bvn_number,
            reference=liberty_reference,
            transfer_leg=transfer_leg
        )

        record_sub_com.send_payload = reverse_sub_comm
        record_sub_com.save()

        if transaction_instance:
            if settings.ENVIRONMENT == "development":
                is_test_trans = True
            elif settings.ENVIRONMENT == "production":
                is_test_trans = False

            verification_instance = dict(
                transaction_instance=transaction_instance,
                user_id=transaction_instance.user.id,
                user_email=transaction_instance.user.email,
                account_provider=transaction_instance.account_provider,
                transaction_leg=transaction_instance.transaction_leg,
                transaction_type=transaction_instance.transaction_type,
                timestamp=str(datetime.now()),
                escrow_id=escrow_id,
                amount=amount,
                liberty_reference=liberty_reference,
                is_test=is_test_trans,
                beneficiary_nuban=to_account,
                source_nuban=from_account,
                bank_code=beneficiary_bank_code
            )

            verf_pending_trans = TransferVerificationObject.create_verfication_check(verification_instance)

        return reverse_sub_comm

    @classmethod
    def handle_other_account_fund_and_debit(cls, entry, amount, account_inst: 'AccountSystem'):
        if entry == "CREDIT":
            account_inst.other_balance += amount
        else:
            account_inst.other_balance -= amount

        account_inst.save()

        return True

    @staticmethod
    def inflow_handler(user_id, data, provider="WEMA"):
        amount = float(data["amount"])
        unique_reference = data["unique_reference"]
        payment_reference = data["transaction_reference"]
        user_nuban = data["user_nuban"]
        source_nuban = data["source_nuban"]
        source_account_name = data["source_account_name"]
        narration = data["narration"]
        source_bank_code = data["source_bank_code"]
        account_provider = data["account_provider"]
        provider_fee = data["provider_fee"]
        transfer_status = data["transfer_status"]
        provider_status = data["provider_status"]
        timestamp = data["timestamp"]
        funding_payload = data["funding_payload"]
        float_inflow = False
        session_id = data["session_id"]

        user = User.objects.filter(id=user_id).last()

        # from accounts.models import TransactionLimitLog

        # Check for large inflows for new users (within first 30 days)
        inflow_check = TransactionLimitLog.check_inflow_blocking(user=user, amount=amount)

        liberty_reference = Transaction.create_liberty_reference("LGLP-INW_WEMA")
        if provider == "FIDELITY":
            liberty_reference = Transaction.create_liberty_reference("LGLP-INW_FIDE")
        wallet_instance = WalletSystem.get_wallet(user=user, from_wallet_type="COLLECTION")

        if wallet_instance:
            wallet_id = wallet_instance.wallet_id
            wallet_type = wallet_instance.wallet_type
            wallet_balance = wallet_instance.available_balance
        else:
            wallet_id = None
            wallet_type = None
            wallet_balance = None

        commission = ConstantTable.calculate_fund_bank_transfer_fees(user, amount)

        charges_on_inflow = 0
        if user.is_paybox_merchant:
            charges_on_inflow = deduct_commission_from_paybox_merchant(amount)

        commission += charges_on_inflow
        new_amount = amount - commission

        if not Transaction.objects.filter(unique_reference=unique_reference).exists():
            user_balance_before = wallet_instance.available_balance if wallet_instance else 0.0

            user_balance_after = WalletSystem.get_balance_after(
                user=user,
                balance_before=user_balance_before,
                total_amount=new_amount,
                is_credit=True
            )

            get_user_wallet = AccountSystem.objects.filter(account_number=user_nuban, account_provider=provider).filter(
                account_type__in=["COLLECTION", "OTHERS"]).first()

            user_balance_before = wallet_instance.available_balance

            transaction_instance = Transaction.objects.create(
                user=user,
                amount=amount,
                total_amount_received=new_amount,
                liberty_commission=commission,
                bank_float_balance_before=0,
                wallet_id=wallet_instance.wallet_id,
                account_id=get_user_wallet.account_id,
                account_provider=account_provider,
                wallet_type=wallet_instance.wallet_type,
                transaction_type="FUND_BANK_TRANSFER" if float_inflow == False else "FLOAT_INFLOW",
                narration=narration,
                status="PENDING" if float_inflow == False else "SUCCESSFUL",
                balance_before=user_balance_before if float_inflow == False else 0,
                balance_after=user_balance_after if float_inflow == False else 0,
                source_account_name=source_account_name,
                source_nuban=source_nuban,
                source_bank_code=source_bank_code,
                escrow_id=None,
                liberty_reference=liberty_reference,
                unique_reference=unique_reference,
                provider_fee=provider_fee,
                provider_status=provider_status,
                payload=funding_payload,
                callback_payload=json.dumps(funding_payload),
                beneficiary_account_name=get_user_wallet.account_name,
                beneficiary_nuban=user_nuban,
                session_id=session_id
            )

            if transaction_instance.status != "SUCCESSFUL":
                transaction_instance.provider_status = provider_status
                transaction_instance.status = "SUCCESSFUL"

                if not DebitCreditRecordOnAccount.objects.filter(user=user, entry="CREDIT", unique_reference=unique_reference).exists():
                    # fund wallet
                    fund_wallet = WalletSystem.fund_balance(
                        user=user,
                        wallet=wallet_instance,
                        amount=new_amount,
                        trans_type="FUND_BANK_TRANSFER",
                        transaction_instance_id=transaction_instance.transaction_id,
                        unique_reference=unique_reference
                    )

                    balance_after = fund_wallet['balance_after']
                    transaction_instance.balance_after = balance_after
                    transaction_instance.save()

                    create_electronic_levy_transaction(transaction_instance)

                    # Send Commission to Liberty
                    if commission > 0:
                        # Create Commission Scheduler Object
                        SendCommissionScheduler.objects.create(
                            user=user, wallet_id=wallet_instance.wallet_id, wallet_type=wallet_instance.wallet_type, amount=float(commission), provider=account_provider,
                            transaction_commission_id=str(transaction_instance.transaction_id), transfer_leg="FUND_BANK_TRANSFER"
                        )

                    ##########################################################################################
                    # SEND OUT APP NOTIFICATION
                    receiver_not_token = user.firebase_key
                    receiver_not_title = "Payment Received"
                    receiver_not_body = f"You have recieved a CREDIT of N{amount} from {source_account_name}"
                    receiver_not_data = {"amount_sent": f"{amount}", "available_balance": f"{balance_after}"}

                    send_out_notification = cloud_messaging.send_broadcast(
                        token=receiver_not_token,
                        title=receiver_not_title,
                        body=receiver_not_body,
                        data=receiver_not_data
                    )

                    # If the user was blocked due to large inflow, send a notification
                    if inflow_check["status"] == "blocked":
                        blocked_title = "Account Restricted"
                        blocked_body = "Your account has been restricted from making transfers due to receiving a large amount. Please contact support."
                        blocked_data = {"status": "blocked", "reason": "large_inflow"}

                        send_out_notification = cloud_messaging.send_broadcast(
                            token=receiver_not_token,
                            title=blocked_title,
                            body=blocked_body,
                            data=blocked_data
                        )

                        InAppTransactionNotification.create_in_app_transaction_notification(
                            user=user,
                            title=blocked_title,
                            message_body=blocked_body
                        )

        return True


class AccountCreationFailure(models.Model):
    # VFD_PROVIDER = "VFD"
    # WOVEN_PROVIDER = "WOVEN"

    # ACCOUNT_PROVIDERS = [(VFD_PROVIDER, "VFD"), (WOVEN_PROVIDER, "WOVEN")]

    SPEND = "SPEND"
    COLLECTION = "COLLECTION"
    CORPORATE = "CORPORATE"

    ACCOUNT_TYPES = [
        (SPEND, "SPEND"),
        (COLLECTION, "COLLECTION"),
        (CORPORATE, "CORPORATE"),
    ]

    user = models.ForeignKey(
        User, related_name="creation_fails", on_delete=models.CASCADE
    )
    account_provider = models.CharField(max_length=70, choices=ACCOUNT_PROVIDERS)
    account_type = models.CharField(max_length=300, choices=ACCOUNT_TYPES)
    is_test = models.BooleanField(default=False)
    initial_payload = models.TextField(null=True, blank=True)
    payload = models.TextField(null=True, blank=True)
    date_failed = models.DateTimeField(auto_now_add=True)


############################################################################################
# Beneficiaries


class Beneficiaries(models.Model):
    BUDDY = "BUDDY"
    BANK_TRANSFER = "BANK_TRANSFER"

    BENEFICIARY_TYPE_CHOICES = [(BUDDY, "BUDDY"), (BANK_TRANSFER, "BANK_TRANSFER")]

    user = models.ForeignKey(
        User, related_name="beneficiaries", on_delete=models.CASCADE
    )
    phone_number = models.CharField(max_length=20, null=True, blank=True)
    beneficiary_type = models.CharField(
        max_length=100, choices=BENEFICIARY_TYPE_CHOICES, null=True, blank=True
    )
    wallet_id = models.UUIDField(null=True, blank=True)
    account_number = models.CharField(max_length=20, null=True, blank=True)
    account_name = models.CharField(max_length=200, null=True, blank=True)
    bank_name = models.CharField(max_length=200, null=True, blank=True)
    bank_code = models.CharField(max_length=100, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    date_added = models.DateTimeField(auto_now_add=True)
    date_removed = models.DateTimeField(null=True, blank=True)

    @staticmethod
    def manage_other_beneficiaries(user, save_beneficiary, remove_beneficiary, data, trans_type):
        if remove_beneficiary == True:
            # Check if benefieciary exists
            check_beneficiary = Beneficiaries.objects.filter(
                Q(user=user)
                & Q(phone_number=data["buddy_phone_number"])
                & Q(beneficiary_type=trans_type)
                & Q(is_active=True)
            ).first()
            if check_beneficiary:
                check_beneficiary.is_active = False
                check_beneficiary.date_removed = datetime.now()
                check_beneficiary.save()
            else:
                pass
        elif save_beneficiary == True:
            # Check if benefieciary exists
            check_beneficiary = Beneficiaries.objects.filter(
                Q(user=user)
                & Q(phone_number=data["buddy_phone_number"])
                & Q(beneficiary_type=trans_type)
                & Q(is_active=True)
            ).first()
            if check_beneficiary:
                pass
            else:
                beneficiary_instance = Beneficiaries.objects.create(
                    user=user,
                    phone_number=data["buddy_phone_number"],
                    beneficiary_type=trans_type,
                    wallet_id=data["wallet_id"],
                    account_name=data["account_name"],
                )
        else:
            pass

    @staticmethod
    def manage_buddy_beneficiaries(user, save_beneficiary, remove_beneficiary, data):
        if remove_beneficiary == True:
            # Check if benefieciary exists
            check_beneficiary = Beneficiaries.objects.filter(
                Q(user=user)
                & Q(phone_number=data["buddy_phone_number"])
                & Q(is_active=True)
            ).first()
            if check_beneficiary:
                check_beneficiary.is_active = False
                check_beneficiary.date_removed = datetime.now()
                check_beneficiary.save()
            else:
                pass
        elif save_beneficiary == True:
            # Check if benefieciary exists
            check_beneficiary = Beneficiaries.objects.filter(
                Q(user=user)
                & Q(phone_number=data["buddy_phone_number"])
                & Q(is_active=True)
            ).first()
            if check_beneficiary:
                pass
            else:
                beneficiary_instance = Beneficiaries.objects.create(
                    user=user,
                    phone_number=data["buddy_phone_number"],
                    beneficiary_type="BUDDY",
                    wallet_id=data["wallet_id"],
                    account_name=data["account_name"],
                )
        else:
            pass

    @staticmethod
    def manage_bank_transfer_beneficiaries(
            user, save_beneficiary, remove_beneficiary, data
    ):
        if remove_beneficiary == True:
            # Check if benefieciary exists
            check_beneficiary = Beneficiaries.objects.filter(
                Q(user=user)
                & Q(account_number=data["account_number"])
                & Q(is_active=True)
            ).first()
            if check_beneficiary:
                check_beneficiary.is_active = False
                check_beneficiary.date_removed = datetime.now()
                check_beneficiary.save()
            else:
                pass
        elif save_beneficiary == True:
            # Check if benefieciary exists
            check_beneficiary = Beneficiaries.objects.filter(
                Q(user=user)
                & Q(account_number=data["account_number"])
                & Q(is_active=True)
            ).first()
            if check_beneficiary:
                pass
            else:
                beneficiary_instance = Beneficiaries.objects.create(
                    user=user,
                    beneficiary_type="BANK_TRANSFER",
                    account_number=data["account_number"],
                    account_name=data["account_name"],
                    bank_name=data["bank_name"],
                    bank_code=data["bank_code"],
                )
        else:
            pass


def create_any_default_unique_id():
    """
    Create Unique ID
    """

    epoch = int(time.time())
    reference = f"{uuid.uuid4()}_{str(epoch)}"
    return reference


class Escrow(models.Model):
    # VFD_PROVIDER = "VFD"
    # WOVEN_PROVIDER = "WOVEN"

    # ACCOUNT_PROVIDERS = [(VFD_PROVIDER, "VFD"), (WOVEN_PROVIDER, "WOVEN")]

    SPEND = "SPEND"
    COLLECTION = "COLLECTION"
    SAVINGS_WALLET = "SAVINGS"
    COMMISSIONS_WALLET = "COMMISSIONS"

    WALLET_TYPES = [
        (SPEND, "SPEND"),
        (COLLECTION, "COLLECTION"),
        (SAVINGS_WALLET, "SAVINGS"),
        (COMMISSIONS_WALLET, "COMMISSIONS"),
    ]

    SEND_BUDDY = "SEND_BUDDY"
    SEND_LOTTO_WALLET = "SEND_LOTTO_WALLET"
    SEND_AJO_WALLET = "SEND_AJO_WALLET"
    SEND_AJO_LOANS = "SEND_AJO_LOANS"
    SEND_COLLECTION_ACCOUNT = "SEND_COLLECTION_ACCOUNT"
    SEND_BANK_TRANSFER = "SEND_BANK_TRANSFER"
    SEND_COMMISSION = "SEND_COMMISSION"
    SEND_BACK_TO_FLOAT_TRANSFER = "SEND_BACK_TO_FLOAT_TRANSFER"
    FLOAT_INFLOW = "FLOAT_INFLOW"
    LOTTO_PLAY = "LOTTO_PLAY"
    LIBERTY_LIFE_PAYMENT = "LIBERTY_LIFE_PAYMENT"
    CREDI_LOAN = "CREDI_LOAN"
    AUTO_REFUND = "AUTO_REFUND"
    FUNDS_RETRIEVAL = "FUNDS_RETRIEVAL"
    SAVINGS = "SAVINGS"
    RELEASE_HOLD_BALANCE = "RELEASE_HOLD_BALANCE"
    CARD_PURCHASE = "CARD_PURCHASE"
    RETAIL_AUTO_DEBIT = "RETAIL_AUTO_DEBIT"
    USSD_WITHDRAW = "USSD_WITHDRAW"
    TERMINAL_PURCHASE = "TERMINAL_PURCHASE"
    WALLET_DEBIT_REVERSAL = "WALLET_DEBIT_REVERSAL"

    TRANSFER_TYPE_CHOICES = [
        (SEND_BUDDY, "SEND_BUDDY"),
        (SEND_LOTTO_WALLET, "SEND_LOTTO_WALLET"),
        (SEND_AJO_WALLET, "SEND_AJO_WALLET"),
        (SEND_AJO_LOANS, "SEND_AJO_LOANS"),
        (SEND_BANK_TRANSFER, "SEND_BANK_TRANSFER"),
        (SEND_COMMISSION, "SEND_COMMISSION"),
        (SEND_BACK_TO_FLOAT_TRANSFER, "SEND_BACK_TO_FLOAT_TRANSFER"),
        (FLOAT_INFLOW, "FLOAT_INFLOW"),
        (LOTTO_PLAY, "LOTTO_PLAY"),
        (LIBERTY_LIFE_PAYMENT, "LIBERTY_LIFE_PAYMENT"),
        (CREDI_LOAN, "CREDI_LOAN"),
        (AUTO_REFUND, "AUTO_REFUND"),
        (FUNDS_RETRIEVAL, "FUNDS_RETRIEVAL"),
        (SAVINGS, "SAVINGS"),
        (RELEASE_HOLD_BALANCE, "RELEASE_HOLD_BALANCE"),
        (CARD_PURCHASE, "CARD_PURCHASE"),
        (RETAIL_AUTO_DEBIT, "RETAIL_AUTO_DEBIT"),
        (USSD_WITHDRAW, "USSD_WITHDRAW"),
        (TERMINAL_PURCHASE, "TERMINAL_PURCHASE"),
        (WALLET_DEBIT_REVERSAL, "WALLET_DEBIT_REVERSAL"),
    ]

    SEND_MONEY_ONLINE = "SEND_MONEY_ONLINE"
    SEND_MONEY_OFFLINE = "SEND_MONEY_OFFLINE"
    CARD_WITHDRAW_ONLINE = "CARD_WITHDRAW_ONLINE"
    CARD_WITHDRAW_OFFLINE = "CARD_WITHDRAW_OFFLINE"
    CARD_WITHDRAW_SEND_MONEY_ONLINE = "CARD_WITHDRAW_SEND_MONEY_ONLINE"
    CARD_WITHDRAW_SEND_MONEY_OFFLINE = "CARD_WITHDRAW_SEND_MONEY_OFFLINE"

    TRANSACTION_MODE_CHOICES = [
        (SEND_MONEY_ONLINE, "SEND_MONEY_ONLINE"),
        (SEND_MONEY_OFFLINE, "SEND_MONEY_OFFLINE"),
        (CARD_WITHDRAW_ONLINE, "CARD_WITHDRAW_ONLINE"),
        (CARD_WITHDRAW_OFFLINE, "CARD_WITHDRAW_OFFLINE"),
        (CARD_WITHDRAW_SEND_MONEY_ONLINE, "CARD_WITHDRAW_SEND_MONEY_ONLINE"),
        (CARD_WITHDRAW_SEND_MONEY_OFFLINE, "CARD_WITHDRAW_SEND_MONEY_OFFLINE")
    ]

    SUCCESSFUL = "SUCCESSFUL"
    FAILED = "FAILED"
    REVERSED = "REVERSED"

    ESCROW_STATUS = [(SUCCESSFUL, "SUCCESSFUL"), (FAILED, "FAILED"), (REVERSED, "REVERSED")]

    user = models.ForeignKey(User, related_name="escrows", on_delete=models.CASCADE)
    from_wallet_id = models.CharField(max_length=200, null=True, blank=True)
    account_provider = models.CharField(
        max_length=100, choices=ACCOUNT_PROVIDERS, null=True, blank=True
    )
    from_wallet_type = models.CharField(
        max_length=100, choices=WALLET_TYPES, null=True, blank=True
    )
    to_wallet_type = models.CharField(
        max_length=100, choices=WALLET_TYPES, null=True, blank=True
    )
    transfer_type = models.CharField(
        max_length=100, choices=TRANSFER_TYPE_CHOICES, null=True, blank=True
    )
    transaction_mode = models.CharField(
        max_length=150, choices=TRANSACTION_MODE_CHOICES, null=True, blank=True
    )
    debit_credit_record_id = models.PositiveIntegerField(null=True, blank=True)
    escrow_id = models.CharField(max_length=150, default=create_any_default_unique_id, null=True, blank=True, db_index=True)
    internal_transaction_id = models.CharField(max_length=200, null=True, blank=True)
    external_transaction_id = models.CharField(max_length=200, null=True, blank=True)
    commissions_transaction_id = models.CharField(max_length=200, null=True, blank=True)
    liberty_reference = models.CharField(max_length=200, null=True, blank=True)
    fund_liberty_reference = models.CharField(max_length=200, null=True, blank=True)
    send_money_by_card = models.BooleanField(default=False)
    send_by_card_rrn = models.CharField(max_length=150, blank=True, null=True)
    amount = models.FloatField(null=True, blank=True, validators=[MinValueValidator(0.0)])
    user_trans_band = models.PositiveSmallIntegerField(null=True, blank=True)
    ip_addr = models.CharField(max_length=200, null=True, blank=True)
    liberty_commission = models.FloatField(null=True, blank=True)
    extra_fee = models.FloatField(default=0.00)
    send_money_transfer_fee = models.FloatField(null=True, blank=True)
    send_money_transfer_extra_fee = models.FloatField(null=True, blank=True)
    total_amount_charged = models.FloatField(null=True, blank=True)
    balance_before = models.FloatField(null=True, blank=True)
    balance_after = models.FloatField(null=True, blank=True)
    internal_escrow = models.BooleanField(default=True)
    external_escrow = models.BooleanField(default=True)
    commissions_escrow = models.BooleanField(default=True)
    escrow_transaction_status = models.CharField(max_length=200, null=True, blank=True, choices=ESCROW_STATUS)
    # sent_leg_one = models.BooleanField(default=False)
    # sent_leg_two = models.BooleanField(default=False)
    # sent_commissions = models.BooleanField(default=False)
    user_account_number = models.CharField(max_length=200, null=True, blank=True)
    user_account_name = models.CharField(max_length=200, null=True, blank=True)
    user_bank_name = models.CharField(max_length=200, null=True, blank=True)
    user_bank_code = models.CharField(max_length=100, null=True, blank=True)
    vfd_account_id = models.CharField(max_length=100, null=True, blank=True)
    user_account_provider = models.CharField(max_length=200, null=True, blank=True)
    to_account_id = models.CharField(max_length=200, null=True, blank=True)
    to_wallet_id = models.CharField(max_length=200, null=True, blank=True)
    to_account_name = models.CharField(max_length=250, null=True, blank=True)
    to_nuban = models.CharField(max_length=250, null=True, blank=True)
    to_bank_name = models.CharField(max_length=100, null=True, blank=True)
    to_bank_code = models.CharField(max_length=100, null=True, blank=True)
    narration = models.CharField(max_length=250, null=True, blank=True)
    lotto_agent_user_id = models.CharField(max_length=100, null=True, blank=True)
    customer_reference = models.CharField(max_length=500, null=True, blank=True)
    bulk_id = models.CharField(max_length=500, null=True, blank=True)
    lotto_agent_user_phone = models.CharField(max_length=100, null=True, blank=True)
    is_beneficiary = models.BooleanField(default=False)
    is_recurring = models.BooleanField(default=False)
    funding_back_to_float = models.BooleanField(default=False)
    is_for_reversal = models.BooleanField(default=False)
    reversed = models.BooleanField(default=False)
    pos_charge = models.FloatField(default=0)
    pos_charge_type = models.CharField(max_length=100, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    internal_payload = models.TextField(null=True, blank=True)
    external_payload = models.TextField(null=True, blank=True)
    commissions_payload = models.TextField(null=True, blank=True)
    metadata = models.TextField(null=True, blank=True)
    is_paybox_merchant = models.BooleanField(default=False)

    def save(self, *args, **kwargs):
        # Update is_paybox_merchant
        self.is_paybox_merchant = self.user.is_paybox_merchant
        super(Escrow, self).save(*args, **kwargs)

    def __str__(self):
        if self.escrow_id:
            return str(self.escrow_id)
        return str(self.id)

    def in_escrow_date(self):
        return "{date}".format(self.date_created)

    class Meta:
        indexes = [
            models.Index(fields=['escrow_id']),
            models.Index(fields=['customer_reference']),
        ]


class Transaction(models.Model):
    # VFD_PROVIDER = "VFD"
    # WOVEN_PROVIDER = "WOVEN"

    # ACCOUNT_PROVIDERS = [(VFD_PROVIDER, "VFD"), (WOVEN_PROVIDER, "WOVEN")]

    SPEND = "SPEND"
    COLLECTION = "COLLECTION"
    SAVINGS_WALLET = "SAVINGS"
    COMMISSIONS_WALLET = "COMMISSIONS"

    WALLET_TYPES = [
        (SPEND, "SPEND"),
        (COLLECTION, "COLLECTION"),
        (SAVINGS_WALLET, "SAVINGS"),
        (COMMISSIONS_WALLET, "COMMISSIONS"),
    ]

    ELECTRONIC_TRANSFER_LEVY = "ELECTRONIC_TRANSFER_LEVY"
    TERMINAL_PURCHASE = "TERMINAL_PURCHASE"
    WALLET_DEBIT_REVERSAL = "WALLET_DEBIT_REVERSAL"
    TERMINAL_PURCHASE_COMMISSION = "TERMINAL_PURCHASE_COMMISSION"
    SEND_BUDDY = "SEND_BUDDY"
    SEND_LOTTO_WALLET = "SEND_LOTTO_WALLET"
    SEND_AJO_WALLET = "SEND_AJO_WALLET"
    SEND_AJO_LOANS = "SEND_AJO_LOANS"
    SEND_COLLECTION_ACCOUNT = "SEND_COLLECTION_ACCOUNT"
    SEND_BANK_TRANSFER = "SEND_BANK_TRANSFER"
    SEND_LIBERTY_COMMISSION = "SEND_LIBERTY_COMMISSION"
    REVERSAL_BUDDY = "REVERSAL_BUDDY"
    REVERSAL_BANK_TRANSFER_IN = "REVERSAL_BANK_TRANSFER_IN"
    REVERSAL_BANK_TRANSFER = "REVERSAL_BANK_TRANSFER"
    FUND_BUDDY = "FUND_BUDDY"
    FUND_LOTTO_WALLET = "FUND_LOTTO_WALLET"
    FUND_AJO_WALLET = "FUND_AJO_WALLET"
    FUND_AJO_LOANS = "FUND_AJO_LOANS"
    FUND_COLLECTION_ACCOUNT = "FUND_COLLECTION_ACCOUNT"
    FUND_BANK_TRANSFER = "FUND_BANK_TRANSFER"
    FLOAT_INFLOW = "FLOAT_INFLOW"
    FUND_PAYSTACK = "FUND_PAYSTACK"
    FUND_QRCODE = "FUND_QRCODE"
    FUND_BY_USSD = "FUND_BY_USSD"
    FUND_TRANSFER_FROM_COMMISSION = "FUND_TRANSFER_FROM_COMMISSION"
    AJO_LOAN_COMMISSIONS = "AJO_LOAN_COMMISSIONS"
    ASSURED_LOAN_COMMISSIONS = "ASSURED_LOAN_COMMISSIONS"
    FUND_TRANSFER_FROM_OTHER_COMMISSION = "FUND_TRANSFER_FROM_OTHER_COMMISSION"
    CARD_TRANSACTION_FUND = "CARD_TRANSACTION_FUND"
    CARD_TRANSACTION_FUND_TRANSFER = "CARD_TRANSACTION_FUND_TRANSFER"
    USSD_WITHDRAW = "USSD_WITHDRAW"
    BILLS_AND_PAYMENT = "BILLS_AND_PAYMENT"
    BILLS_AND_PAYMENT_REVERSAL = "BILLS_AND_PAYMENT_REVERSAL"
    AIRTIME_PIN = "AIRTIME_PIN"
    AIRTIME_PIN_REVERSAL = "AIRTIME_PIN_REVERSAL"
    SEND_BACK_TO_FLOAT_TRANSFER = "SEND_BACK_TO_FLOAT_TRANSFER"
    LOTTO_PLAY = "LOTTO_PLAY"
    LIBERTY_LIFE_PAYMENT = "LIBERTY_LIFE_PAYMENT"
    LOTTO_PLAY_CR = "LOTTO_PLAY_CR"
    LIBERTY_LIFE_PAYMENT_CR = "LIBERTY_LIFE_PAYMENT_CR"
    CREDI_LOAN = "CREDI_LOAN"
    CREDI_LOAN_CR = "CREDI_LOAN_CR"
    AUTO_REFUND = "AUTO_REFUND"
    AUTO_REFUND_CR = "AUTO_REFUND_CR"
    FUNDS_RETRIEVAL = "FUNDS_RETRIEVAL"
    FUNDS_RETRIEVAL_CR = "FUNDS_RETRIEVAL_CR"
    SAVINGS = "SAVINGS"
    SAVINGS_CR = "SAVINGS_CR"
    RETAIL_AUTO_DEBIT = "RETAIL_AUTO_DEBIT"
    RETAIL_AUTO_DEBIT_CR = "RETAIL_AUTO_DEBIT_CR"
    LOTTO_WINNING_CREDIT = "LOTTO_WINNING_CREDIT"
    LOTTO_WINNING_DEBIT = "LOTTO_WINNING_DEBIT"
    RELEASE_HOLD_BALANCE = "RELEASE_HOLD_BALANCE"
    CARD_PURCHASE = "CARD_PURCHASE"
    SETTLE_CARDS_PURCHASE = "SETTLE_CARDS_PURCHASE"
    REFUND_CARD_PURCHASE = "REFUND_CARD_PURCHASE"
    SEND_REFUND_CARD_PURCHASE = "SEND_REFUND_CARD_PURCHASE"
    VIRTUAL_CARD_CREATE = "VIRTUAL_CARD_CREATE"
    PHYSICAL_CARD_CREATE = "PHYSICAL_CARD_CREATE"
    BANK_OOB_IN = "BANK_OOB_IN"
    BANK_OOB_OUT = "BANK_OOB_OUT"
    BANK_OOB_COMM = "BANK_OOB_COMM"
    RE_FAILED_TRANSFER_IN = "RE_FAILED_TRANSFER_IN"

    DEBIT_TRANS = [
        (ELECTRONIC_TRANSFER_LEVY, "ELECTRONIC_TRANSFER_LEVY"),
        (TERMINAL_PURCHASE, "TERMINAL_PURCHASE"),
        (SEND_BUDDY, "SEND_BUDDY"),
        (SEND_LOTTO_WALLET, "SEND_LOTTO_WALLET"),
        (SEND_AJO_WALLET, "SEND_AJO_WALLET"),
        (SEND_AJO_LOANS, "SEND_AJO_LOANS"),
        (SEND_COLLECTION_ACCOUNT, "SEND_COLLECTION_ACCOUNT"),
        (SEND_BANK_TRANSFER, "SEND_BANK_TRANSFER"),
        (SEND_LIBERTY_COMMISSION, "SEND_LIBERTY_COMMISSION"),
        (AJO_LOAN_COMMISSIONS, "AJO_LOAN_COMMISSIONS"),
        (ASSURED_LOAN_COMMISSIONS, "ASSURED_LOAN_COMMISSIONS"),
        (USSD_WITHDRAW, "USSD_WITHDRAW"),
        (BILLS_AND_PAYMENT, "BILLS_AND_PAYMENT"),
        (AIRTIME_PIN, "AIRTIME_PIN"),
        (LOTTO_PLAY, "LOTTO_PLAY"),
        (LIBERTY_LIFE_PAYMENT, "LIBERTY_LIFE_PAYMENT"),
        (AUTO_REFUND, "AUTO_REFUND"),
        (FUNDS_RETRIEVAL, "FUNDS_RETRIEVAL"),
        (SAVINGS, "SAVINGS"),
        (RETAIL_AUTO_DEBIT, "RETAIL_AUTO_DEBIT"),
        (VIRTUAL_CARD_CREATE, "VIRTUAL_CARD_CREATE"),
        (PHYSICAL_CARD_CREATE, "PHYSICAL_CARD_CREATE"),
        (CARD_PURCHASE, "CARD_PURCHASE"),
    ]

    CREDIT_TRANS = [
        (REVERSAL_BUDDY, "REVERSAL_BUDDY"),
        (REVERSAL_BANK_TRANSFER_IN, "REVERSAL_BANK_TRANSFER_IN"),
        (REVERSAL_BANK_TRANSFER, "REVERSAL_BANK_TRANSFER"),
        (FUND_BUDDY, "FUND_BUDDY"),
        (FUND_LOTTO_WALLET, "FUND_LOTTO_WALLET"),
        (FUND_AJO_WALLET, "FUND_AJO_WALLET"),
        (FUND_AJO_LOANS, "FUND_AJO_LOANS"),
        (FUND_COLLECTION_ACCOUNT, "FUND_COLLECTION_ACCOUNT"),
        (FUND_BANK_TRANSFER, "FUND_BANK_TRANSFER"),
        (FUND_PAYSTACK, "FUND_PAYSTACK"),
        (FUND_QRCODE, "FUND_QRCODE"),
        (FUND_BY_USSD, "FUND_BY_USSD"),
        (FUND_TRANSFER_FROM_COMMISSION, "FUND_TRANSFER_FROM_COMMISSION"),
        (TERMINAL_PURCHASE_COMMISSION, "TERMINAL_PURCHASE_COMMISSION"),
        (FUND_TRANSFER_FROM_OTHER_COMMISSION, "FUND_TRANSFER_FROM_OTHER_COMMISSION"),
        (CARD_TRANSACTION_FUND, "CARD_TRANSACTION_FUND"),
        (CARD_TRANSACTION_FUND_TRANSFER, "CARD_TRANSACTION_FUND_TRANSFER"),
        (BILLS_AND_PAYMENT_REVERSAL, "BILLS_AND_PAYMENT_REVERSAL"),
        (AIRTIME_PIN_REVERSAL, "AIRTIME_PIN_REVERSAL"),
        (CREDI_LOAN, "CREDI_LOAN"),
    ]

    OTHER_DEBIT_TRANS = [
        (SEND_BACK_TO_FLOAT_TRANSFER, "SEND_BACK_TO_FLOAT_TRANSFER"),
        (LOTTO_WINNING_DEBIT, "LOTTO_WINNING_DEBIT"),
        (REFUND_CARD_PURCHASE, "REFUND_CARD_PURCHASE"),
        (SEND_REFUND_CARD_PURCHASE, "SEND_REFUND_CARD_PURCHASE"),

    ]

    OTHER_CREDIT_TRANS = [
        (LOTTO_PLAY_CR, "LOTTO_PLAY_CR"),
        (LIBERTY_LIFE_PAYMENT_CR, "LIBERTY_LIFE_PAYMENT_CR"),
        (CREDI_LOAN_CR, "CREDI_LOAN_CR"),
        (AUTO_REFUND_CR, "AUTO_REFUND_CR"),
        (FUNDS_RETRIEVAL_CR, "FUNDS_RETRIEVAL_CR"),
        (RETAIL_AUTO_DEBIT_CR, "RETAIL_AUTO_DEBIT_CR"),
        (SAVINGS_CR, "SAVINGS_CR"),
        (FLOAT_INFLOW, "FLOAT_INFLOW"),
        (LOTTO_WINNING_CREDIT, "LOTTO_WINNING_CREDIT"),
        (RELEASE_HOLD_BALANCE, "RELEASE_HOLD_BALANCE"),
        (SETTLE_CARDS_PURCHASE, "SETTLE_CARDS_PURCHASE"),
        (WALLET_DEBIT_REVERSAL, "WALLET_DEBIT_REVERSAL"),
    ]

    HISTORY_TRANS_TYPES = DEBIT_TRANS + CREDIT_TRANS

    TRANSACTION_TYPE_CHOICES = HISTORY_TRANS_TYPES + OTHER_DEBIT_TRANS + OTHER_CREDIT_TRANS + [
        (BANK_OOB_IN, "BANK_OOB_IN"),
        (BANK_OOB_OUT, "BANK_OOB_OUT"),
        (BANK_OOB_COMM, "BANK_OOB_COMM"),
        (RE_FAILED_TRANSFER_IN, "RE_FAILED_TRANSFER_IN"),
    ]

    SUCCESSFUL = "SUCCESSFUL"
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    FAILED = "FAILED"
    REVERSED = "REVERSED"
    IGNORE_HISTORY = "IGNORE_HISTORY"

    TRANSACTION_STATUS_CHOICES = [
        (SUCCESSFUL, "SUCCESSFUL"),
        (PENDING, "PENDING"),
        (IN_PROGRESS, "IN_PROGRESS"),
        (FAILED, "FAILED"),
        (REVERSED, "REVERSED"),
        (IGNORE_HISTORY, "IGNORE_HISTORY"),
    ]

    INTERNAL = "INTERNAL"
    EXTERNAL = "EXTERNAL"
    TEMP_EXTERNAL = "TEMP_EXTERNAL"
    COMMISSIONS = "COMMISSIONS"
    REVERSAL = "REVERSAL"
    INFLOW_TO_FLOAT = "INFLOW_TO_FLOAT"
    RE_INTERNAL = "RE_INTERNAL"

    TRANSACTION_LEG_CHOICES = [
        (INTERNAL, "INTERNAL"),
        (EXTERNAL, "EXTERNAL"),
        (TEMP_EXTERNAL, "TEMP_EXTERNAL"),
        (COMMISSIONS, "COMMISSIONS"),
        (REVERSAL, "REVERSAL"),
        (INFLOW_TO_FLOAT, "INFLOW_TO_FLOAT"),
        (RE_INTERNAL, "RE_INTERNAL")
    ]

    USSD = "USSD"
    SEND_MONEY_ONLINE = "SEND_MONEY_ONLINE"
    SEND_MONEY_OFFLINE = "SEND_MONEY_OFFLINE"
    CARD_WITHDRAW_ONLINE = "CARD_WITHDRAW_ONLINE"
    CARD_WITHDRAW_OFFLINE = "CARD_WITHDRAW_OFFLINE"
    FUND_PAYSTACK_ONLINE = "FUND_PAYSTACK_ONLINE"
    # CARD_WITHDRAW_SEND_MONEY_ONLINE = "CARD_WITHDRAW_SEND_MONEY_ONLINE"
    # CARD_WITHDRAW_SEND_MONEY_OFFLINE = "CARD_WITHDRAW_SEND_MONEY_OFFLINE"

    TRANSACTION_MODE_CHOICES = [
        (USSD, "USSD"),
        (SEND_MONEY_ONLINE, "SEND_MONEY_ONLINE"),
        (SEND_MONEY_OFFLINE, "SEND_MONEY_OFFLINE"),
        (CARD_WITHDRAW_ONLINE, "CARD_WITHDRAW_ONLINE"),
        (CARD_WITHDRAW_OFFLINE, "CARD_WITHDRAW_OFFLINE"),
        (FUND_PAYSTACK_ONLINE, "FUND_PAYSTACK_ONLINE"),
        # (CARD_WITHDRAW_SEND_MONEY_ONLINE, "CARD_WITHDRAW_SEND_MONEY_ONLINE"),
        # (CARD_WITHDRAW_SEND_MONEY_OFFLINE, "CARD_WITHDRAW_SEND_MONEY_OFFLINE"),
    ]

    USER_WALLET_TYPE_CHOICES = [
        ("POS", "POS"),
        ("MOBILE", "MOBILE")
    ]

    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    transaction_type = models.CharField(
        max_length=100, choices=TRANSACTION_TYPE_CHOICES, null=True, blank=True
    )
    transaction_sub_type = models.CharField(
        max_length=250, null=True, blank=True
    )
    amount = models.FloatField(null=True, blank=True, validators=[MinValueValidator(0.0)])
    liberty_commission = models.FloatField(null=True, blank=True)
    sms_charge = models.FloatField(default=0.00)
    status = models.CharField(
        max_length=150, choices=TRANSACTION_STATUS_CHOICES, default="PENDING"
    )
    balance_before = models.FloatField(null=True, blank=True)
    balance_after = models.FloatField(null=True, blank=True)
    float_bal_before = models.FloatField(null=True, blank=True)
    float_bal_after = models.FloatField(null=True, blank=True)
    bank_float_balance_before = models.FloatField(null=True, blank=True)
    bank_float_balance_after = models.FloatField(null=True, blank=True)
    user = models.ForeignKey(
        User,
        related_name="transactions",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    beneficiary_account_name = models.CharField(max_length=250, null=True, blank=True)
    beneficiary_nuban = models.CharField(max_length=250, null=True, blank=True)
    source_account_name = models.CharField(max_length=250, null=True, blank=True)
    source_nuban = models.CharField(max_length=250, null=True, blank=True)
    transaction_leg = models.CharField(
        max_length=200, choices=TRANSACTION_LEG_CHOICES, null=True, blank=True
    )
    user_full_name = models.CharField(max_length=150, null=True, blank=True)
    user_email = models.EmailField(null=True, blank=True, db_index=True)
    user_trans_band = models.PositiveSmallIntegerField(null=True, blank=True)
    account_provider = models.CharField(
        max_length=100, choices=ACCOUNT_PROVIDERS, null=True, blank=True
    )
    escrow_id = models.CharField(max_length=150, null=True, blank=True, db_index=True)
    liberty_reference = models.CharField(max_length=200, db_index=True)
    ip_addr = models.CharField(max_length=200, null=True, blank=True)
    is_reversed = models.BooleanField(default=False)
    transaction_id = models.UUIDField(default=uuid.uuid4, editable=False, db_index=True)
    sales_rep = models.BooleanField(default=False)
    extra_fee = models.FloatField(default=0.00)
    final_liberty_rev = models.FloatField(null=True, blank=True)
    liberty_profit = models.FloatField(null=True, blank=True)
    ro_profit = models.FloatField(null=True, blank=True)
    agent_profit = models.FloatField(null=True, blank=True)
    transaction_mode = models.CharField(
        max_length=150, choices=TRANSACTION_MODE_CHOICES, null=True, blank=True
    )
    send_money_by_card = models.BooleanField(default=False)
    send_by_card_rrn = models.CharField(max_length=150, blank=True, null=True)
    wallet_id = models.CharField(max_length=100, null=True, blank=True)
    account_id = models.CharField(max_length=100, null=True, blank=True)
    wallet_type = models.CharField(
        max_length=100, choices=WALLET_TYPES, null=True, blank=True
    )
    is_verified = models.BooleanField(default=False)
    transaction_commission_id = models.CharField(max_length=250, null=True, blank=True)
    provider_fee = models.FloatField(null=True, blank=True)
    spread = models.FloatField(null=True, blank=True)
    total_amount_charged = models.FloatField(null=True, blank=True)
    total_amount_sent_out = models.FloatField(null=True, blank=True)
    total_amount_received = models.FloatField(null=True, blank=True)
    total_amount_resolved = models.FloatField(null=True, blank=True)
    provider_status = models.CharField(max_length=150, null=True, blank=True)
    source_account_id = models.CharField(max_length=200, null=True, blank=True)
    source_wallet_id = models.CharField(max_length=200, null=True, blank=True)
    source_wallet_type = models.CharField(
        max_length=100, choices=WALLET_TYPES, null=True, blank=True
    )
    source_bank_name = models.CharField(max_length=100, null=True, blank=True)
    source_bank_code = models.CharField(max_length=100, null=True, blank=True)
    unique_reference = models.CharField(max_length=200, null=True, blank=True, db_index=True)
    beneficiary_bank_name = models.CharField(max_length=200, null=True, blank=True)
    beneficiary_bank_code = models.CharField(max_length=100, null=True, blank=True)
    beneficiary_wallet_id = models.CharField(max_length=200, null=True, blank=True)
    beneficiary_wallet_type = models.CharField(
        max_length=100, choices=WALLET_TYPES, null=True, blank=True
    )
    narration = models.CharField(max_length=250, null=True, blank=True)
    session_id = models.CharField(max_length=200, null=True, blank=True)
    promo_code = models.CharField(max_length=100, null=True, blank=True)
    user_wallet_type = models.CharField(max_length=100, choices=USER_WALLET_TYPE_CHOICES, default="MOBILE")
    terminal_id = models.CharField(max_length=100, null=True, blank=True)
    type_of_user = models.CharField(max_length=500, null=True, blank=True)
    to_beneficiary = models.BooleanField(default=False)
    is_recurring = models.BooleanField(default=False)
    reversal_type = models.CharField(max_length=200, null=True, blank=True)
    save_beneficiary = models.BooleanField(default=False)
    vas_customer_message = models.TextField(null=True, blank=True)
    is_verified = models.BooleanField(default=False)
    initial_transfer_response_payload = models.TextField(null=True, blank=True)
    transfer_verf_payload = models.TextField(null=True, blank=True)
    leg_one_verification_payload = models.TextField(null=True, blank=True)
    leg_two_verification_payload = models.TextField(null=True, blank=True)
    commissions_verification_payload = models.TextField(null=True, blank=True)
    funding_back_to_float_verification_payload = models.TextField(null=True, blank=True)
    reversal_verification_payload = models.TextField(null=True, blank=True)
    is_internal_reversal = models.BooleanField(default=False)
    email_sent = models.BooleanField(default=False)
    email_sent_payload = models.TextField(null=True, blank=True)
    push_notify = models.BooleanField(default=False)
    vas_deps_checked = models.BooleanField(default=False)
    float_bal_before_updated = models.BooleanField(default=False)
    float_bal_after_updated = models.BooleanField(default=False)
    push_notify_payload = models.TextField(null=True, blank=True)
    payload = models.TextField(null=True, blank=True)
    callback_sent = models.BooleanField(default=False)
    callback_payload = models.TextField(null=True, blank=True)
    callback_response = models.TextField(null=True, blank=True)
    lotto_agent_user_id = models.CharField(max_length=100, null=True, blank=True)
    lotto_agent_user_phone = models.CharField(max_length=100, null=True, blank=True)
    is_other_account = models.BooleanField(default=False)
    is_other_account_number = models.CharField(max_length=100, null=True, blank=True)
    is_other_account_owner = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    other_account_owner_settled = models.BooleanField(default=False)
    oth_acct_num_settlement_ref = models.CharField(max_length=150, null=True, blank=True)
    debit_credit_record_id = models.CharField(max_length=300, null=True, blank=True)
    send_money_by_card_resp = models.TextField(null=True, blank=True)
    trans_counted = models.BooleanField(default=False)
    trans_time = models.CharField(max_length=100, null=True, blank=True)
    is_paybox_merchant = models.BooleanField(default=False)
    escrow_instance = models.ForeignKey(Escrow, on_delete=models.PROTECT, null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['escrow_id']),
            models.Index(fields=['liberty_reference', 'escrow_id', 'transaction_id', 'unique_reference']),
            models.Index(fields=['transaction_id']),
            models.Index(fields=['transaction_type', 'unique_reference']),
            models.Index(fields=['transaction_type']),
            models.Index(TruncDate("date_created"), "date_created", name="date_created_date_idx")

        ]

    def __str__(self):
        return str(self.transaction_id)

    def transaction_date(self):
        return "{date}".format(self.date_created)

    @classmethod
    def create_liberty_reference(cls, suffix):
        epoch = int(time.time())
        liberty_reference = f"{suffix}-{str(epoch)[-10:]}-{uuid.uuid4()}"
        return liberty_reference

    @classmethod
    def create_liberty_reference_no_suffix(cls):
        epoch = int(time.time())
        liberty_reference = f"{str(epoch)}-{uuid.uuid4()}"
        return liberty_reference

    @classmethod
    def create_liberty_reference_with_old_reference(cls, liberty_reference, suffix: str):
        return f"{liberty_reference}-{suffix}{str(random.randint(0, 20))}"

    @staticmethod
    def serializer_trans(instance):
        from accounts.serializers import ReceivingTransListSerializer

        if instance.transaction_type in receiving_list:
            serializer = ReceivingTransListSerializer(instance)

            callback_payload = json.dumps(serializer.data)
            instance.callback_payload = callback_payload

            instance.save()
        else:
            pass

    def save(self, *args, **kwargs):

        # wallet_instance = WalletSystem.objects.get(wallet_id=self.wallet_id)
        # if self.escrow_id:
        #     get_escrow_instance = Escrow.objects.filter(escrow_id=self.escrow_id).first()
        #     if get_escrow_instance:
        #         self.transaction_mode = get_escrow_instance.transaction_mode

        #         if self.transaction_type in ["SEND_BANK_TRANSFER", "SEND_LIBERTY_COMMISSION", "CARD_TRANSACTION_FUND", "CARD_TRANSACTION_FUND_TRANSFER"]:
        #             self.send_money_by_card = get_escrow_instance.send_money_by_card
        #             self.send_by_card_rrn = get_escrow_instance.send_by_card_rrn

        #         if not self.ip_addr and self.transaction_type not in ["FUND_BUDDY", "FUND_BANK_TRANSFER"]:
        #             self.ip_addr = get_escrow_instance.ip_addr

        if self.user.sms_subscription is True and self.transaction_type not in ["FUND_TRANSFER_FROM_COMMISSION", "AIRTIME_PIN", "BILLS_AND_PAYMENT",
                                                                                "SEND_LIBERTY_COMMISSION"]:
            self.sms_charge = float(settings.WHISPER_CHARGE)
        else:
            self.sms_charge = 0.00

        if self.liberty_commission is None:
            self.liberty_commission = 0.00

        if self.user:
            self.user_email = self.user.email
            self.user_full_name = self.user.bvn_full_name
            self.type_of_user = self.user.type_of_user
            self.is_paybox_merchant = self.user.is_paybox_merchant

            if self.user.terminal_id is not None:
                self.user_wallet_type = "POS"
                self.terminal_id = self.user.terminal_id
            else:
                self.user_wallet_type = "MOBILE"

            if self.user.has_sales_rep:
                self.sales_rep = True

        # if self.float_bal_before_updated or self.float_bal_after_updated:
        #     pass
        # else:
        #     debit_credit_record = DebitCreditRecordOnAccount.objects.filter(transaction_instance_id=self.transaction_id).last()
        #     if debit_credit_record:
        #         self.float_bal_before = debit_credit_record.float_bal_before
        #         self.float_bal_after = debit_credit_record.float_bal_after

        #         self.float_bal_before_updated = True
        #         self.float_bal_after_updated = True

        # # Transaction Performance and own accounts
        # if self.status == "SUCCESSFUL" and self.is_reversed == False and self.transaction_type not in ["SEND_LIBERTY_COMMISSION", "SEND_BACK_TO_FLOAT_TRANSFER"] and self.transaction_leg not in ["INTERNAL", "TEMP_EXTERNAL"]:
        #     if self.trans_counted == False:

        #         TransactionPerformance.create_trans_perform(
        #             user=self.user,
        #             trans_type=self.transaction_type
        #         )

        #         # if self.transaction_type == "SEND_BANK_TRANSFER" and self.transaction_leg == "EXTERNAL":
        #         #     beneficiary_name = self.beneficiary_account_name
        #         #     user_full_name = self.user_full_name

        #         #     if get_names_match(user_full_name, beneficiary_name) == True:
        #         #         UserOwnAccount.add_own_account_to_table(
        #         #             user=self.user,
        #         #             account_number=self.beneficiary_nuban,
        #         #             bank_code=self.beneficiary_bank_code,
        #         #         )
        #         #     else:
        #         #         pass

        #         self.trans_counted = True
        #     else:
        #         pass
        # else:
        #     pass

        super(Transaction, self).save(*args, **kwargs)

    @classmethod
    def reverse_bills_airpin_transactions(cls, transaction: 'Transaction', provider_status=None, payload_reversal_reason=None):
        debit_credit_record_list = list(
            DebitCreditRecordOnAccount.objects.filter(user=transaction.user).values_list('transaction_instance_id', flat=True))

        if str(transaction.transaction_id) in debit_credit_record_list:

            if not DebitCreditRecordOnAccount.objects.filter(transaction_instance_id=transaction.transaction_id, entry="DEBIT").exists():
                response = {
                    "status": "NO_CHARGE",
                    "reversal_trans": None
                }

            elif DebitCreditRecordOnAccount.objects.filter(
                    Q(reversal_trans_id=transaction.transaction_id, entry="CREDIT") |
                    Q(unique_reference=transaction.escrow_id)
            ).exists():

                response = {
                    "status": "CREDIT_EXIST",
                    "reversal_trans": None
                }
            else:

                reversal_transaction = cls.objects.create(
                    user=transaction.user,
                    wallet_id=transaction.wallet_id,
                    wallet_type=transaction.wallet_type,
                    transaction_type=f"{transaction.transaction_type}_REVERSAL",
                    transaction_sub_type=transaction.transaction_sub_type,
                    amount=transaction.amount,
                    ip_addr=transaction.ip_addr,
                    escrow_id=transaction.escrow_id,
                    liberty_reference=transaction.liberty_reference,
                    liberty_commission=transaction.liberty_commission,
                    total_amount_charged=transaction.total_amount_charged,
                    narration=transaction.narration,
                    status="IN_PROGRESS",
                    payload=payload_reversal_reason,
                    transaction_leg=transaction.transaction_leg,
                    terminal_id=transaction.terminal_id,
                    transaction_mode=transaction.transaction_mode
                )

                try:
                    wallet = WalletSystem.objects.get(wallet_id=transaction.wallet_id, user=transaction.user)
                except:
                    return False

                # Refund Money
                reversal_deb_cred = WalletSystem.fund_balance(
                    user=transaction.user,
                    wallet=wallet,
                    amount=transaction.amount,
                    trans_type=f"{transaction.transaction_type}-REVERSAL",
                    transaction_instance_id=reversal_transaction.transaction_id,
                    reversal_trans_id=transaction.transaction_id,
                    unique_reference=transaction.escrow_id
                )

                reversal_transaction.balance_before = reversal_deb_cred.get("balance_before")
                reversal_transaction.balance_after = reversal_deb_cred.get("balance_after")
                reversal_transaction.debit_credit_record_id = reversal_deb_cred.get("record").id
                reversal_transaction.status = "SUCCESSFUL"
                reversal_transaction.save()

                response = {
                    "status": "REVERSED",
                    "reversal_trans": reversal_transaction
                }

        else:
            response = {
                "status": "NO_DEBIT",
                "reversal_trans": None
            }

        transaction.provider_status = provider_status
        transaction.status = "FAILED"
        transaction.save()

        return response


#######################################################################


class TransferVerificationObject(models.Model):
    SUCCESSFUL = "SUCCESSFUL"
    REVERSAL = "REVERSAL"
    PENDING = "PENDING"
    NOT_FOUND = "NOT_FOUND"
    BULK_RESLV = "BULK_RESLV"

    TRANSACTION_STATUS_CHOICES = [
        (SUCCESSFUL, "SUCCESSFUL"),
        (REVERSAL, "REVERSAL"),
        (PENDING, "PENDING"),
        (NOT_FOUND, "NOT_FOUND"),
        (BULK_RESLV, "BULK_RESLV"),
    ]

    transaction_instance = models.ForeignKey(Transaction, related_name="verifiable_trans", on_delete=models.CASCADE)
    user_id = models.PositiveIntegerField(null=True, blank=True, db_index=True)
    user_email = models.EmailField(null=True, blank=True, db_index=True)
    # transaction_ver_status = models.CharField(
    #     max_length=200, choices=TRANSACTION_STATUS_CHOICES, null=True, blank=True
    # )
    account_provider = models.CharField(max_length=100, null=True, blank=True)
    transaction_leg = models.CharField(max_length=200, null=True, blank=True)
    transaction_ver_status = models.CharField(
        max_length=200, default="NOT_INITIATED"
    )
    transaction_type = models.CharField(max_length=500)
    transaction_sub_type = models.CharField(max_length=500, null=True, blank=True)
    escrow_id = models.CharField(max_length=500, null=True, blank=True, db_index=True)
    amount = models.FloatField()
    liberty_reference = models.CharField(max_length=300, db_index=True)
    unique_reference = models.CharField(max_length=300, null=True, blank=True, db_index=True)
    trans_status_code = models.CharField(max_length=100, null=True, blank=True)
    is_verified = models.BooleanField(default=False)
    is_finished_verification = models.BooleanField(default=False)
    source_nuban = models.CharField(max_length=100, null=True, blank=True)
    beneficiary_nuban = models.CharField(max_length=100, null=True, blank=True)
    bank_code = models.CharField(max_length=100, null=True, blank=True)
    first_leg_done = models.BooleanField(default=False)
    second_leg_done = models.BooleanField(default=False)
    commissions_leg_done = models.BooleanField(default=False)
    reversal_leg_done = models.BooleanField(default=False)
    send_back_to_float_done = models.BooleanField(default=False)
    verification_payload = models.TextField(null=True, blank=True)
    raw_payload_response = models.TextField(null=True, blank=True)
    verf_timestamp = models.CharField(max_length=150, null=True, blank=True)
    timestamp = models.CharField(max_length=100, null=True, blank=True)
    num_of_checks = models.IntegerField(default=0)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    is_test = models.BooleanField(default=False)
    bank_performance_checked = models.BooleanField(default=False)
    has_duplicate = models.BooleanField(default=False)
    duplicate_reference = models.CharField(max_length=100, null=True, blank=True)
    verify_type = models.CharField(max_length=100, null=True, blank=True)
    checked_for_duplicate = models.BooleanField(default=False)
    duplicate_counted = models.BooleanField(default=False)
    auto_verify = models.BooleanField(default=True)

    class Meta:
        indexes = [
            models.Index(fields=['liberty_reference', 'escrow_id', 'unique_reference']),
        ]

    def verification_handler(self, instant=False):
        print("I GOT TO THE VERIFICATION HANDLER")
        print("I GOT TO THE VERIFICATION HANDLER")
        print("I GOT TO THE VERIFICATION HANDLER")
        print("I GOT TO THE VERIFICATION HANDLER")
        print("I GOT TO THE VERIFICATION HANDLER")

        if self.transaction_type == "SEND_LIBERTY_COMMISSION" and self.amount < 1:
            LessMinimumAmountComm.handle_low_comm_amount(transf=self)
            verify_transaction = None

        else:
            new_data = {
                "user_id": self.user_id,
                "user_email": self.user_email,
                "escrow_id": str(self.escrow_id),
                "amount": self.amount,
                "account_provider": self.account_provider,
                "liberty_reference": self.liberty_reference,
                "timestamp": self.timestamp,
                "transaction_leg": self.transaction_leg,
                "transaction_type": self.transaction_type,
                "is_test": self.is_test,
            }

            # if settings.VERIFY_TRANSFER_LOCALLY == "yes":
            if self.is_test == True:
                is_local = True
                new_data["is_local"] = is_local

                verify_transaction = test_create_test_verifiable_transaction_test(new_data)

            else:
                is_local = False
                new_data["is_local"] = is_local

                verify_transaction = create_verifiable_transaction(new_data, instant=instant)

        return verify_transaction

    @classmethod
    def create_verfication_check(cls, data, instant=False):

        try:
            verf_pending_trans = cls.objects.get(liberty_reference=data["liberty_reference"])
            if not verf_pending_trans.timestamp:
                verf_pending_trans.timestamp = f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")}'
                verf_pending_trans.save()
        except cls.DoesNotExist:
            verf_pending_trans = cls.objects.create(**data)

        return verf_pending_trans.verification_handler(instant=instant)

    @classmethod
    def not_initiated_func(cls, not_data_list):
        for transaction_verf in not_data_list:
            transaction_verf: TransferVerificationObject

            if settings.ENVIRONMENT == "development":
                is_test_trans = True
            elif settings.ENVIRONMENT == "production":
                is_test_trans = False

            liberty_reference = transaction_verf.liberty_reference
            amount = transaction_verf.amount

            print(transaction_verf.date_added)
            print(transaction_verf.id)
            print(liberty_reference)

            check_status = VFDBank.vfd_transaction_verification_handler(reference=liberty_reference, fail=True)
            check_status_code = check_status["status"] if check_status["status"] == "108" else check_status["data"]["transactionStatus"]

            if check_status_code == "108":

                response = AccountSystem.handle_out_of_books(
                    from_account=transaction_verf.source_nuban,
                    to_account=transaction_verf.beneficiary_nuban,
                    amount=amount,
                    escrow_id=transaction_verf.escrow_id,
                    user_bvn_number=transaction_verf.transaction_instance.user.bvn_number,
                    transaction_instance=transaction_verf.transaction_instance,
                    liberty_reference=liberty_reference
                )

            else:

                verification_instance = dict(
                    transaction_instance=transaction_verf.transaction_instance,
                    user_id=transaction_verf.user_id,
                    user_email=transaction_verf.user_email,
                    account_provider=transaction_verf.account_provider,
                    transaction_leg=transaction_verf.transaction_leg,
                    transaction_type=transaction_verf.transaction_type,
                    timestamp=str(datetime.now()),
                    escrow_id=transaction_verf.escrow_id,
                    amount=transaction_verf.amount,
                    liberty_reference=liberty_reference,
                    is_test=is_test_trans
                )

                verf_pending_trans = cls.create_verfication_check(verification_instance)

            transaction_verf.num_of_checks += 1
            transaction_verf.save()

        return

    @classmethod
    def pending_comms_func(cls, pending_data_list):
        for transaction_verf in pending_data_list:
            transaction_verf: TransferVerificationObject

            if settings.ENVIRONMENT == "development":
                is_test_trans = True
            elif settings.ENVIRONMENT == "production":
                is_test_trans = False

            print(transaction_verf.date_added)
            print(transaction_verf.id)
            print(transaction_verf.liberty_reference)

            liberty_reference = transaction_verf.liberty_reference

            verification_instance = dict(
                transaction_instance=transaction_verf.transaction_instance,
                user_id=transaction_verf.user_id,
                user_email=transaction_verf.user_email,
                account_provider=transaction_verf.account_provider,
                transaction_leg=transaction_verf.transaction_leg,
                transaction_type=transaction_verf.transaction_type,
                timestamp=str(datetime.now()),
                escrow_id=transaction_verf.escrow_id,
                amount=transaction_verf.amount,
                liberty_reference=liberty_reference,
                is_test=is_test_trans
            )

            verf_pending_trans = cls.create_verfication_check(verification_instance)

        return

    # @classmethod
    # def resolve_failed_verfication_check(cls, data, instant=None):

    #     new_data = {
    #         "user_id": data["user_id"],
    #         "user_email": data["user_email"],
    #         "escrow_id": data["escrow_id"],
    #         "amount": data["amount"],
    #         "account_provider": data["account_provider"],
    #         "liberty_reference": data["liberty_reference"],
    #         "timestamp": data["timestamp"],
    #         "transaction_leg": data["transaction_leg"],
    #         "transaction_type": data["transaction_type"],
    #         "is_test": data["is_test"],
    #     }

    #     if data["is_test"] == True or settings.VERIFY_TRANSFER_LOCALLY == "yes":
    #         is_local = True
    #         new_data["is_local"] = is_local

    #         verify_transaction = test_create_test_verifiable_transaction_test(new_data)

    #     else:
    #         is_local = False
    #         new_data["is_local"] = is_local

    #         verify_transaction = create_verifiable_transaction(
    #             new_data = new_data,
    #             instant=instant
    #         )

    # @classmethod
    # def push_out_new_legs_from_verf(cls, data):
    #     print(data)

    def save(self, *args, **kwargs):
        if self.is_verified is False:
            if not self.transaction_ver_status == "PENDING" \
                    or self.transaction_ver_status == "NOT_FOUND":
                pass
            else:
                if settings.ENVIRONMENT == "development":
                    is_test_trans = True
                elif settings.ENVIRONMENT == "production":
                    is_test_trans = False

                timestamp = str(datetime.now())

                # verify_transaction = create_verifiable_transaction(
                #         new_data = {
                #             "user_id": self.user_id,
                #             "user_email": self.user_email,
                #             "escrow_id": self.escrow_id,
                #             "amount": self.amount,
                #             "account_provider": self.account_provider,
                #             "liberty_reference": self.liberty_reference,
                #             "timestamp": timestamp,
                #             "transaction_leg": self.transaction_leg,
                #             "transaction_type": self.transaction_type,
                #             "is_test": self.is_test
                #         }
                #     )
        super(TransferVerificationObject, self).save(*args, **kwargs)


class DeletedReference(models.Model):
    transaction_instance = models.ForeignKey(Transaction, on_delete=models.CASCADE)
    liberty_reference = models.CharField(max_length=200)
    new_liberty_reference = models.CharField(max_length=200, null=True, blank=True)
    trans_type = models.CharField(max_length=200, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.transaction_instance.liberty_reference


class RawPayStack(models.Model):
    reference = models.CharField(max_length=500, null=True, blank=True)
    data_for = models.CharField(max_length=70, null=True, blank=True)
    payload = models.TextField()
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class CreditCardDetail(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="credit_card")
    card_signature = models.CharField(max_length=4000)
    bank_name = models.CharField(max_length=2100)
    account_name = models.CharField(
        max_length=2100, default="Invalid", blank=True)
    bin = models.CharField(max_length=26)
    card_last_four_digit = models.CharField(max_length=24)
    authorization_code = models.CharField(max_length=2100)
    exp_month = models.CharField(max_length=22)
    exp_year = models.CharField(max_length=24)
    card_type = models.CharField(max_length=2200)
    channel = models.CharField(max_length=2200)
    country_code = models.CharField(max_length=2200)
    brand = models.CharField(max_length=2200)
    date_created = models.DateTimeField(auto_now_add=True)
    updated_date_created = models.DateTimeField(auto_now=True)
    device_provider = models.CharField(max_length=2300, null=True, blank=True)

    def __str__(self):
        return str(self.user.email)

    @staticmethod
    def create_credit_card(user, card_signature, account_name, bank_name, bin, card_last_four_digit, authorization_code,
                           exp_month, exp_year, card_type, channel, country_code, brand, device_provider):
        similar_old_cards = user.credit_card.filter(card_signature=card_signature, card_last_four_digit=card_last_four_digit,
                                                    card_type=card_type, account_name=account_name, bank_name=bank_name)

        if similar_old_cards.exists():
            return

        new_credit_card = CreditCardDetail(
            user=user,
            card_signature=card_signature,
            bank_name=bank_name,
            account_name="invalid" if account_name is None else account_name,
            bin=bin,
            card_last_four_digit=card_last_four_digit,
            authorization_code=authorization_code,
            exp_month=exp_month,
            exp_year=exp_year,
            card_type=card_type,
            channel=channel,
            country_code=country_code,
            brand=brand,
            device_provider=device_provider
        )

        new_credit_card.save()

        return new_credit_card


class InitializedPayStackTrans(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    amount = models.FloatField()
    authorization_url = models.CharField(max_length=700)
    reference = models.CharField(max_length=500)
    access_code = models.CharField(max_length=500)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class PayStackTransaction(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="paystack_transactions")
    amount = models.FloatField(default=0.0)
    reference = models.CharField(max_length=2115, )
    event = models.CharField(max_length=2115, )
    paid_at = models.DateTimeField()
    created_at = models.DateTimeField()
    received_at = models.DateTimeField(auto_now_add=True)
    channel = models.CharField(max_length=2110)
    raw_data = models.CharField(max_length=22000)
    channel = models.CharField(max_length=2120)
    currency = models.CharField(max_length=2120)
    reason = models.CharField(max_length=2120, null=True)
    mobile = models.BooleanField(null=True)
    bank = models.CharField(max_length=2120, null=True)
    card_type = models.CharField(max_length=2125, null=True)
    gateway_response = models.CharField(max_length=2125)
    device_provider = models.CharField(max_length=2300, null=True, blank=True)
    cash_balance = models.FloatField(null=True, blank=True)

    @staticmethod
    def create_transaction(user, data):

        card_details = data.get("data").get("authorization")
        logs = data.get("data").get("log")
        amount = (data.get("data").get("amount")) / 100

        payment_reference = data.get("data").get("reference")
        device_provider = data.get("device_provider")
        # similar_payments = PayStackTransaction.objects.filter(reference = payment_reference)

        if isinstance(logs, dict):
            mobile = logs.get("mobile")
        else:
            mobile = True

        liberty_reference = Transaction.create_liberty_reference("LGLP-INW_PYSTCK")
        wallet_instance = WalletSystem.get_wallet(user=user, from_wallet_type="COLLECTION")
        if wallet_instance:
            wallet_id = wallet_instance.wallet_id
            wallet_type = wallet_instance.wallet_type
            wallet_balance = wallet_instance.available_balance
        else:
            wallet_id = None
            wallet_type = None
            wallet_balance = None

        charge_on_paystack = (amount / 100) * 3

        if not Transaction.objects.filter(unique_reference=payment_reference).exists():

            paystack_transaction = PayStackTransaction.objects.create(
                user=user,
                amount=amount,
                reference=payment_reference,
                event=data.get(
                    "event"),
                paid_at=standard_str_to_dt(
                    data.get("data", {}).get("paid_at")),
                created_at=standard_str_to_dt(
                    data.get("data", {}).get("created_at")),
                channel=data.get(
                    "data", {}).get("channel"),
                currency=data.get(
                    "data", {}).get("currency"),
                raw_data=data.get(
                    "raw_data"),
                reason=data.get(
                    "data", {}).get("reason"),
                mobile=mobile,
                card_type=data.get("data", {}).get(
                    "authorization", {}).get("card_type"),
                bank=data.get("data", {}).get(
                    "authorization", {}).get("bank"),
                gateway_response=data.get(
                    "data", {}).get("gateway_response"),
                device_provider=device_provider,
                cash_balance=wallet_balance
            )

            user_balance_before = wallet_instance.available_balance if wallet_instance else 0.0

            user_balance_after = WalletSystem.get_balance_after(
                user=user,
                balance_before=user_balance_before,
                total_amount=amount - charge_on_paystack,
                is_credit=True
            )

            transaction_instance = Transaction.objects.create(
                user=user,
                wallet_id=wallet_id,
                wallet_type=wallet_type,
                transaction_type="FUND_PAYSTACK",
                amount=amount,
                liberty_commission=charge_on_paystack,
                balance_before=user_balance_before,
                balance_after=user_balance_after,
                total_amount_received=amount,
                provider_status=paystack_transaction.event,
                liberty_reference=liberty_reference,
                unique_reference=payment_reference,
                transaction_mode="FUND_PAYSTACK_ONLINE",
                payload=str(data)
            )

            CreditCardDetail.create_credit_card(
                user,
                card_details.get(
                    "card_signature", ""),
                card_details.get(
                    "account_name", ""),
                card_details.get(
                    "bank", ""),
                card_details.get("bin", ""),
                card_details.get(
                    "last4", ""),
                card_details.get(
                    "authorization_code", ""),
                card_details.get(
                    "exp_month", ""),
                card_details.get(
                    "exp_year", ""),
                card_details.get(
                    "card_type", ""),
                card_details.get(
                    "channel", ""),
                card_details.get(
                    "country_code", ""),
                card_details.get(
                    "brand", ""),
                device_provider=device_provider
            )

            if paystack_transaction.event == "charge.success" and "Approved" in paystack_transaction.gateway_response or paystack_transaction.gateway_response == "Payment successful" or "authorized" in paystack_transaction.gateway_response:

                verify_transaction = paystack_verify_funding_transaction(reference=payment_reference, amount=amount)

                if wallet_instance and verify_transaction:
                    # SETTLE MONEY

                    PayStackTransaction.settle_money_function(
                        user=user,
                        amount=amount,
                        wallet_instance=wallet_instance,
                        transaction_instance_id=transaction_instance.transaction_id,
                        charge_on_paystack=charge_on_paystack,
                        unique_reference=payment_reference
                    )

                    transaction_instance.status = "SUCCESSFUL"
                    transaction_instance.save()

                    ##########################################################################################
                    card_sender_name = card_details.get("account_name", "")

                    # SEND OUT APP NOTIFICATION
                    receiver_not_token = user.firebase_key
                    receiver_not_title = "Payment Received"
                    receiver_not_body = f"You have recieved a CREDIT of N{amount} from {card_sender_name}"
                    receiver_not_data = {"amount_sent": f"{amount}", "available_balance": f"{wallet_instance.available_balance}"}

                    send_out_notification = cloud_messaging.send_broadcast(
                        token=receiver_not_token,
                        title=receiver_not_title,
                        body=receiver_not_body,
                        data=receiver_not_data
                    )

                else:
                    pass

            else:
                pass

        else:
            transaction_instance = Transaction.objects.filter(unique_reference=payment_reference).last()
            paystack_transaction = PayStackTransaction.objects.filter(reference=payment_reference).last()
            debit_credit = DebitCreditRecordOnAccount.objects.filter(transaction_instance_id=transaction_instance.transaction_id).last()

            if transaction_instance.status in ["SUCCESSFUL", "FAILED"] or debit_credit is not None:
                pass
            else:
                if paystack_transaction.event == "charge.success" and "Approved" in paystack_transaction.gateway_response or paystack_transaction.gateway_response == "Payment successful" or "authorized" in paystack_transaction.gateway_response:

                    verify_transaction = paystack_verify_funding_transaction(reference=payment_reference, amount=amount)

                    if wallet_instance and verify_transaction:
                        # SETTLE MONEY

                        PayStackTransaction.settle_money_function(
                            user=user,
                            amount=amount,
                            wallet_instance=wallet_instance,
                            transaction_instance_id=transaction_instance.transaction_id,
                            charge_on_paystack=charge_on_paystack,
                            unique_reference=payment_reference
                        )

                        transaction_instance.status = "SUCCESSFUL"
                        transaction_instance.save()

                        ##########################################################################################
                        card_sender_name = card_details.get("account_name", "")

                        # SEND OUT APP NOTIFICATION
                        receiver_not_token = user.firebase_key
                        receiver_not_title = "Payment Received"
                        receiver_not_body = f"You have recieved a CREDIT of N{amount} from {card_sender_name}"
                        receiver_not_data = {"amount_sent": f"{amount}", "available_balance": f"{wallet_instance.available_balance}"}

                        send_out_notification = cloud_messaging.send_broadcast(
                            token=receiver_not_token,
                            title=receiver_not_title,
                            body=receiver_not_body,
                            data=receiver_not_data
                        )

                    else:
                        pass

                else:
                    pass

        return True

    @staticmethod
    def settle_money_function(user, amount, wallet_instance, transaction_instance_id=None, charge_on_paystack=None, unique_reference=None):
        # Fund User Wallet

        charge_on_paystack = charge_on_paystack if charge_on_paystack else 0.00

        fund_user_wallet = WalletSystem.fund_balance(
            user=user,
            wallet=wallet_instance,
            amount=amount - charge_on_paystack,
            trans_type="FUND_PAYSTACK",
            transaction_instance_id=transaction_instance_id,
            unique_reference=unique_reference
        )

        send_credit_alert = WalletSystem.transaction_alert_notfication_manager(
            user=user,
            amount=amount,
            cr_dr="CR",
            narration="LP-PYSTK-INW",
            from_wallet_type=wallet_instance.wallet_type,
            transaction_instance_id=transaction_instance_id
        )

        return


class LedgerBalance(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    available_balance = models.FloatField(default=0.00)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return f"{self.user}"

    @classmethod
    def fund_ledger_balance(cls, amount, user):
        ledger_bal = cls.get_legder_instance(user=user)
        ledger_bal.available_balance += amount
        ledger_bal.save()

    @classmethod
    def debit_ledger_balance(cls, amount, user):
        ledger_bal = cls.get_legder_instance(user=user)
        ledger_bal.available_balance -= amount
        ledger_bal.save()

    @classmethod
    def get_legder_instance(cls, user):
        ledger_instance, created = cls.objects.get_or_create(user=user)
        return ledger_instance


class LedgerHistoryModel(models.Model):
    BANK = "BANK"
    CASH = "CASH"

    COMMISSION_TYPE_CHOICES = [
        (BANK, "BANK"),
        (CASH, "CASH")
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    transaction = models.ForeignKey(Transaction, on_delete=models.CASCADE)
    ledger_instance = models.ForeignKey(LedgerBalance, on_delete=models.CASCADE)
    commission = models.FloatField()
    commission_type = models.CharField(max_length=150, choices=COMMISSION_TYPE_CHOICES, null=True, blank=True)
    previous_balance = models.FloatField(default=0.00)
    current_balance = models.FloatField(default=0.00)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        self.current_balance = self.previous_balance + self.commission
        super(LedgerHistoryModel, self).save(*args, **kwargs)

    @classmethod
    def create_ledger_object(
            cls, user, ledger_commission,
            commission_type, transaction_instance
    ):
        ledger_bal_instance = LedgerBalance.get_legder_instance(user=user)

        ledger_history = cls.objects.create(
            user=user,
            transaction=transaction_instance,
            ledger_instance=ledger_bal_instance,
            commission=ledger_commission,
            commission_type=commission_type,
            previous_balance=ledger_bal_instance.available_balance
        )

        # Fund Ledger Commission
        fund_ledger = LedgerBalance.fund_ledger_balance(user=user, amount=ledger_commission)


class DailyFundingRecord(models.Model):
    date = models.DateField()
    agent = models.ForeignKey(User, on_delete=models.CASCADE)


class FundingRecord(models.Model):
    RECORD_TYPE_CHOICES = [
        ('TRANSFER_IN', "TRANSFER_IN"),
        ('CASH_IN', "CASH_IN"),
        ('CASH_OUT', "CASH_OUT"),
        ('TRANSFER_OUT', "TRANSFER_OUT")
    ]
    time = models.TimeField(auto_now_add=True)
    record_type = models.CharField(max_length=266, choices=RECORD_TYPE_CHOICES, null=True)
    amount = models.FloatField(default=0.0)
    day = models.ForeignKey(DailyFundingRecord, related_name="records", on_delete=models.CASCADE)


class BillsPaymentConstant(models.Model):
    CORALPAY = "CORALPAY"
    REDBILLER = "REDBILLER"
    BILL_PROVIDER_TYPE_CHOICES = [
        (CORALPAY, "CORALPAY"),
        (REDBILLER, "REDBILLER"),
    ]

    PERCENTAGE = "PERCENTAGE"
    FIXED = "FIXED"
    BILL_COMMISSIONS_TYPE_CHOICES = [
        (PERCENTAGE, "PERCENTAGE"),
        (FIXED, "FIXED"),
    ]

    CHARGE_AGENT = "CHARGE_AGENT"
    GIVE_AGENT = "GIVE_AGENT"

    SHARE_COMMISSIONS_TYPE_CHOICES = [
        (CHARGE_AGENT, "CHARGE_AGENT"),
        (GIVE_AGENT, "GIVE_AGENT"),
    ]

    DATA_BUNDLE = "DATA_BUNDLE"
    ELECTRICITY = "ELECTRICITY"
    SUBSCRIPTION = "SUBSCRIPTION"
    BETTING = "BETTING"
    CABLE_TV = "CABLE_TV"
    VTU = "VTU"
    AIRTIME_PRINT = "AIRTIME_PRINT"
    OTHER = "OTHER"

    BILLS_TYPE_CHOICES = [
        (DATA_BUNDLE, "DATA_BUNDLE"),
        (ELECTRICITY, "ELECTRICITY"),
        (SUBSCRIPTION, "SUBSCRIPTION"),
        (BETTING, "BETTING"),
        (CABLE_TV, "CABLE_TV"),
        (VTU, "VTU"),
        (AIRTIME_PRINT, "AIRTIME_PRINT"),
        (OTHER, "OTHER"),
    ]

    bill_provider = models.CharField(max_length=200, choices=BILL_PROVIDER_TYPE_CHOICES)
    bills_type = models.CharField(max_length=200, choices=BILLS_TYPE_CHOICES)
    biller = models.CharField(max_length=200)
    commissions_type = models.CharField(max_length=200, choices=BILL_COMMISSIONS_TYPE_CHOICES)
    commissions_share_type = models.CharField(max_length=200, choices=SHARE_COMMISSIONS_TYPE_CHOICES)
    biller_charge = models.FloatField(default=0.00)
    provider_fee = models.FloatField(default=0.00)
    liberty_profit = models.FloatField(default=0.00)
    agent_profit = models.FloatField(default=0.00)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def clean(self):

        expected_total = round(self.liberty_profit, 2) + self.provider_fee + round(self.agent_profit, 2)
        actual_total = round(self.biller_charge, 2)

        if abs(expected_total - actual_total) > 0.01:
            raise ValidationError('The sum of liberty profit, provider fee, and agent profit must be within 0.01 of biller charge')

    @staticmethod
    def default_bill_provider():
        return "CORALPAY"

    @classmethod
    def get_amount_payable(cls, amount, biller, bill_provider):

        get_biller_charge = cls.objects.filter(bill_provider=bill_provider, biller=biller).last()
        if get_biller_charge.commissions_share_type == "CHARGE_AGENT":
            if get_biller_charge.commissions_type == "PERCENTAGE":
                total_charge = amount + get_biller_charge.biller_charge
                liberty_commission = get_biller_charge.liberty_profit
                agent_commission = get_biller_charge.agent_profit
                amount_payable = (amount / 100) * total_charge
            else:
                total_charge = get_biller_charge.biller_charge
                liberty_commission = get_biller_charge.biller_charge
                agent_commission = get_biller_charge.agent_profit
                amount_payable = total_charge + amount

            liberty_main_commission = total_charge

        else:
            amount_payable = amount
            liberty_commission = get_biller_charge.liberty_profit
            agent_commission = get_biller_charge.agent_profit
            liberty_main_commission = 0.00

        response = {
            "amount_payable": amount_payable,
            "liberty_commission": liberty_commission,
            "agent_commission": agent_commission,
            "liberty_main_commission": liberty_main_commission
        }

        return response

    @classmethod
    def share_commissions_profit(cls, biller, amount, bill_provider):
        get_biller_charge = cls.objects.filter(bill_provider=bill_provider, biller=biller).last()

        if get_biller_charge.commissions_share_type == "GIVE_AGENT":
            if get_biller_charge.commissions_type == "PERCENTAGE":
                total_profit = (amount / 100) * (get_biller_charge.biller_charge - get_biller_charge.provider_fee)
                liberty_profit = (amount / 100) * get_biller_charge.liberty_profit
                agent_cash_profit = (amount / 100) * get_biller_charge.agent_profit
                provider_fee = (amount / 100) * get_biller_charge.provider_fee

            else:
                total_profit = get_biller_charge.biller_charge - get_biller_charge.provider_fee
                liberty_profit = get_biller_charge.liberty_profit
                agent_cash_profit = get_biller_charge.agent_profit
                provider_fee = get_biller_charge.provider_fee

        else:
            if get_biller_charge.commissions_type == "PERCENTAGE":
                total_profit = (amount / 100) * (get_biller_charge.biller_charge - get_biller_charge.provider_fee)
                liberty_profit = (amount / 100) * get_biller_charge.liberty_profit
                agent_cash_profit = (amount / 100) * get_biller_charge.agent_profit
                provider_fee = (amount / 100) * get_biller_charge.provider_fee

            else:
                total_profit = get_biller_charge.biller_charge - get_biller_charge.provider_fee
                liberty_profit = get_biller_charge.liberty_profit
                agent_cash_profit = get_biller_charge.agent_profit
                provider_fee = get_biller_charge.provider_fee

        response = {
            "total_profit": total_profit,
            "liberty_profit": liberty_profit,
            "agent_cash_profit": agent_cash_profit,
            "provider_fee": provider_fee
        }

        return response


credit_list = [
    "REVERSAL_BUDDY", "REVERSAL_BANK_TRANSFER_IN", "REVERSAL_BANK_TRANSFER", "FUND_BUDDY", "FUND_LOTTO_WALLET", "FUND_AJO_WALLET", "FUND_AJO_LOANS",
    "FUND_COLLECTION_ACCOUNT", "FUND_BANK_TRANSFER", "FUND_PAYSTACK", "FUND_QRCODE", "FUND_BY_USSD", "FUND_TRANSFER_FROM_OTHER_COMMISSION",
    "FUND_TRANSFER_FROM_COMMISSION", "CARD_TRANSACTION_FUND", "CARD_TRANSACTION_FUND_TRANSFER", "LOTTO_PLAY_CR", "LIBERTY_LIFE_PAYMENT_CR", "CREDI_LOAN_CR", "AUTO_REFUND_CR",
    "FUNDS_RETRIEVAL_CR", "SAVINGS_CR", "REFUND_CARD_PURCHASE", "BILLS_AND_PAYMENT_REVERSAL", "AIRTIME_PIN_REVERSAL", "VIRTUAL_CARD_CREATE_REVERSAL",
    "PHYSICAL_CARD_CREATE_REVERSAL"
]

debit_list = [
    "SEND_BUDDY", "SEND_LOTTO_WALLET", "SEND_AJO_WALLET", "SEND_AJO_LOANS", "SEND_COLLECTION_ACCOUNT", "SEND_BANK_TRANSFER",
    "BILLS_AND_PAYMENT", "AIRTIME_PIN", "LOTTO_PLAY", "LIBERTY_LIFE_PAYMENT", "CREDI_LOAN", "AUTO_REFUND", "FUNDS_RETRIEVAL", "SAVINGS", "CARD_PURCHASE", "RETAIL_AUTO_DEBIT",
    "SETTLE_CARDS_PURCHASE", "VIRTUAL_CARD_CREATE", "PHYSICAL_CARD_CREATE", "SEND_REFUND_CARD_PURCHASE",
    "USSD_WITHDRAW", "RETAIL_AUTO_DEBIT"
]

accepted_status_list = ["SUCCESSFUL"]


# accepted_status_list = ["SUCCESSFUL", "PENDING", "IN_PROGRESS", "FAILED", "REVERSED"]

# # NEPA_TRANS_TYPES = ["APLE", "PRIVIDA", "LUMOS", "IBEDC", "JEDC", "KAEDCO", "IKEDC", "KEDCO", "AEDC", "PHEDC", "EEDC", "EKEDC"]
# BETTING_TRANS_TYPES = ["ELIESTLOTTO", "GREENLOTTO", "B9", "CLOUDBET", "BETLAND", "MERRYBET", "1XBET", "NAIJABET", "LIVESCOREBET", "BETWAY", "BETKING", "SUPABET", "NAIRABET", "BANGBET", "WINNERS_GOLDEN_BET", "WINNERS_GOLDEN_CHANCE", "WESTERN_LOTTO", "MLOTTO", "ZOOMLIFESTYLE", "BET9JA"]
# CABLE_TRANS_TYPES = ["SHOWMAX", "STARTIMES", "GOTV", "DSTV"]


class AirtimeToPinParent(models.Model):
    MTN_NIGERIA = "MTN_NIGERIA"
    AIRTEL_NIGERIA = "AIRTEL_NIGERIA"
    GLO_NIGERIA = "GLO_NIGERIA"
    NINE_MOBILE = "NINE_MOBILE"

    NETWORK_PROVIDER_CHOICE = [
        ("MTN_NIGERIA", "MTN_NIGERIA"),
        ("AIRTEL_NIGERIA", "AIRTEL_NIGERIA"),
        ("GLO_NIGERIA", "GLO_NIGERIA"),
        ("9_MOBILE", "9_MOBILE"),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    batch_id = models.CharField(max_length=250)
    liberty_reference = models.CharField(max_length=250, null=True, blank=True)
    unique_reference = models.CharField(max_length=250, null=True, blank=True)
    network = models.CharField(max_length=250, choices=NETWORK_PROVIDER_CHOICE)
    quantity = models.IntegerField(default=1)
    from_wallet_type = models.CharField(max_length=250, null=True, blank=True)
    pin_amount = models.FloatField()
    total_pin_amount = models.FloatField()
    is_successful = models.BooleanField(default=False)
    payload = models.TextField(null=True, blank=True)
    second_payload = models.TextField(null=True, blank=True)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class AirtimeToPinObject(models.Model):
    SUCCESSFUL = "SUCCESSFUL"
    FAILED = "FAILED"
    PENDING = "PENDING"
    NO_PIN_YET = "NO_PIN_YET"

    TRANSACTION_STATUS = [
        (SUCCESSFUL, "SUCCESSFUL"),
        (FAILED, "FAILED"),
        (PENDING, "PENDING"),
        (NO_PIN_YET, "NO_PIN_YET")
    ]

    biller_parent = models.ForeignKey(AirtimeToPinParent, related_name="airtime_pin_children", on_delete=models.CASCADE)
    pin_amount = models.FloatField()
    card_pin = models.CharField(max_length=150, null=True, blank=True)
    liberty_reference = models.CharField(max_length=150, null=True, blank=True)
    serial_number = models.CharField(max_length=150, null=True, blank=True)
    liberty_commission = models.FloatField(null=True, blank=True)
    agent_commission = models.FloatField(null=True, blank=True)
    unique_reference = models.CharField(max_length=150, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    transaction_status = models.CharField(max_length=150, choices=TRANSACTION_STATUS, default="NO_PIN_YET")
    payload = models.TextField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if self.card_pin is None:
            self.card_pin = ""
        super(AirtimeToPinObject, self).save(*args, **kwargs)


# class UnVerifiedTransaction(models.Model):
#     INTERNAL = "INTERNAL"
#     EXTERNAL = "EXTERNAL"
#     COMMISSIONS = "COMMISSIONS"

#     TRANSACTION_LEG_CHOICES = [(INTERNAL, "INTERNAL"), (EXTERNAL, "EXTERNAL"), (COMMISSIONS, "COMMISSIONS")]

#     user = models.ForeignKey(User, related_name="user_unverified_send", on_delete=models.CASCADE)
#     from_provider_type = models.CharField(max_length=300)
#     from_wallet_type = models.CharField(max_length=300)
#     account = models.ForeignKey(AccountSystem, related_name="unverified_transct", on_delete=models.CASCADE)
#     Transaction = models.ForeignKey(Transaction, on_delete=models.CASCADE)
#     amount = models.FloatField()
#     provider_fee = models.FloatField(null=True, blank=True)
#     liberty_reference = models.CharField(max_length=300)
#     escrow_id = models.CharField(max_length=300, null=True, blank=True)

#     # disbursed = models.BooleanField(default=False)
#     is_verified = models.BooleanField(default=False)
#     bank_code = models.CharField(max_length=100)
#     # disbursement_unique_id = models.CharField(max_length=70, unique=True)
#     source_unique_ref = models.CharField(max_length=300, null=True, blank=True)
#     name = models.CharField(max_length=300)
#     payout_verified = models.BooleanField(default=False)
#     payout_payload = models.TextField(null=True, blank=True)
#     source_response_payload = models.TextField(null=True, blank=True)
#     verification_response_payload = models.TextField(null=True, blank=True)

#     transaction_leg = models.CharField(
#         max_length=200, choices=TRANSACTION_LEG_CHOICES, null=True, blank=True
#     )
#     date_added = models.DateTimeField(auto_now_add=True)
#     last_updated = models.DateTimeField(auto_now=True)


#                 beneficiary_account_name=escrow_instance.to_account_name,
#                 beneficiary_nuban=escrow_instance.to_nuban,
#                 beneficiary_bank_code=escrow_instance.to_bank_code,
#                 narration=escrow_instance.narration,
#                 amount=escrow_instance.amount,
#                 escrow_id=escrow_instance.escrow_id,

class WEMACallBack(models.Model):
    payload = models.TextField()
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class FidelityCallBack(models.Model):
    payload = models.TextField()
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class AccountOutflowsTrail(models.Model):
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    user_email = models.CharField(max_length=200, null=True, blank=True)
    amount = models.FloatField(default=0)
    key = models.CharField(default="", max_length=150)


class AccountInflowPayload(models.Model):
    account_provider = models.CharField(max_length=200)
    account_num = models.CharField(max_length=200, null=True, blank=True)
    unique_reference = models.CharField(max_length=200, null=True, blank=True)
    transaction_reference = models.CharField(max_length=200, null=True, blank=True)
    amount = models.FloatField(null=True, blank=True)
    payload = models.TextField()
    session_id = models.CharField(max_length=200, null=True, blank=True)
    rejected_inflow = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    TARGET_ACCOUNTS = {}
    {"**********": "DANIEL UGAH",
     "**********": "LUKMAN YUSUF",
     "**********": "ABDULRAZAQ GAMANDI",
     "**********": "ROOSEVELT ABANDY",
     "**********": "TEMITOPE LEWIS",
     "**********": "TAIWO HUENU",
     "**********": "TAJUDEEN OWOLABI",
     "**********": "boma okuru",
     "**********": "MICHEAL OLORUNGBOHUNMI",
     "**********": "JEREMIAH AGU",
     "**********": "ABIOLA IDOWU",
     "**********": "joseph afasinu",
     "**********": "OLUSEYI OLUBAJO",
     "**********": "OMOLOLU TAIWO",
     "**********": "Lazarus Nwankwo",
     "**********": "ELIZABETH OGUNDELE",
     "**********": "ABISOLA OGUNFUNMILAYO",
     "**********": "FUNMILAYO ADEDEJI",
     "**********": "OLUWADAMILARE OGUNTUGA",
     "**********": "MOHAMMED AREGBESOLA",
     "**********": "TIMILEYIN TAIWO",
     "**********": "EDMUND GIWA",
     "**********": "MAYOPOSI FASOLA",
     "**********": "ABISOLA OLUYOLE",
     "**********": "OLUWASEGUN OLOYEDE",
     "**********": "CHISOM OKOLI",
     "1013733931": "ISAAC EGBEI",
     "1013643526": "TAIWO LAWAL",
     "1013631446": "MOSES OLUMORIN",
     "1013629218": "DAVID EFOSA AMAYO",
     "1013623120": "OLUSEYE AWOYEMI",
     "1013622295": "OLUWAFEMI KEHINDE",
     "1013593834": "IDRIS FATAI",
     "1013593315": "CHUKWUEMEKA NWAOMA",
     "1013591373": "ODUNLAMI BUKOYE",
     "1013581558": "DAVID OLANIYI",
     "1013581211": "ADEKOLADE JOHNSON",
     "**********": "JOHNSON ABE",
     "**********": "MATTHEW ADOIDE",
     "**********": "KPONGETTE INYANG",
     "**********": "MARIAM BUSARI",
     "**********": "Augustine Jibunoh",
     "**********": "TOBI TAIWO",
     "**********": "OLUKUNLE ABOLADE",
     "**********": "INIOLUWA AKINYOSOYE",
     "**********": "TOLULOPE KOMOLAFE",
     "**********": "DAVID OLUYOLE",
     "**********": "DAMILOLA AFOLAYAN"
     }

    # def save(self, *args, **kwargs):
    #     print("SAVING ACCOUNT INFLOW ")
    #     if (
    #         self.account_number in self.TARGET_ACCOUNTS.keys()
    #     ):
    #         self.account_number = f"target-{self.account_number}"
    #         print(self.account_number, "is in target accounts")
    #     print(self.account_number, "is not in target accounts")

    #     super(AccountSystem, self).save(*args, **kwargs)

    @classmethod
    def is_to_be_skipped(cls, account_number):

        if account_number in cls.TARGET_ACCOUNTS.keys():
            return True
        else:
            return False


class InAppTransactionNotification(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    title = models.CharField(max_length=200)
    message = models.CharField(max_length=500)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def create_in_app_transaction_notification(cls, user: User, title, message_body):

        if user.sms_subscription:
            notification_message = f"{message_body}. SMS CHARGE - {settings.WHISPER_CHARGE}"
        else:
            notification_message = message_body

        cls.objects.create(
            user=user,
            title=title,
            message=notification_message
        )

        return


# class DailyVASProfitRecord(models.Model):
#     total_amount_collected = models.FloatField()
#     total_cost_of_acq = models.FloatField()
#     liberty_profit = models.FloatField()
#     total_comm_paid_out = models.FloatField()
#     balance_before = models.FloatField()
#     balance_after = models.FloatField()
#     date_created = models.DateTimeField(auto_now_add=True)
#     last_updated = models.DateTimeField(auto_now=True)


# @classmethod
# def record_daily_vas_profit(cls, total_amount_collected, total_cost_of_acq, liberty_profit, total_comm_paid_out):
#     get_vas_user = WalletSystem.get_vas_user()

#     record = cls.objects.create(
#         total_amount_collected = total_amount_collected,
#         total_cost_of_acq = total_cost_of_acq,
#         liberty_profit = liberty_profit,
#         total_comm_paid_out = total_comm_paid_out,
#         balance_before = get_vas_user.bills_pay_comm_balance,
#         balance_after = get_vas_user.bills_pay_comm_balance + agent_cash_profit,
#         float_balance_before = get_float_user.bills_pay_comm_balance,
#         float_balance_after = get_float_user.bills_pay_comm_balance + liberty_profit
#     )

#     user.bills_pay_comm_balance_daily += agent_cash_profit
#     user.bills_pay_comm_balance += agent_cash_profit
#     user.save()


#     get_float_user.bills_pay_comm_balance_daily += liberty_profit
#     get_float_user.bills_pay_comm_balance += liberty_profit
#     get_float_user.save()


#     from_provider_type = ConstantTable.default_account_provider()

#     send_commission = WalletSystem.pay_commission_to_liberty(
#         user_id=user.id,
#         wallet_id=None,
#         wallet_type=None,
#         liberty_commission=liberty_profit,
#         from_provider_type=from_provider_type,
#         transaction_commission_id = transaction_id,
#         transfer_leg="VAS_COMMISSIONS"
#     )

class CommissionsRecord(models.Model):
    CREDIT = "CREDIT"
    DEBIT = "DEBIT"

    ENTRY_TYPES = [
        (CREDIT, "CREDIT"),
        (DEBIT, "DEBIT"),
    ]
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    entry = models.CharField(max_length=100, choices=ENTRY_TYPES, default="CREDIT")
    amount = models.FloatField()
    liberty_reference = models.CharField(max_length=200)
    transaction_id = models.CharField(max_length=200, null=True, blank=True, db_index=True)
    biller = models.CharField(max_length=200, null=True, blank=True)
    bills_type = models.CharField(max_length=200, null=True, blank=True)
    provider_fee = models.FloatField(default=0.00)
    total_cost_of_acq = models.FloatField(null=True, blank=True)
    total_profit = models.FloatField(null=True, blank=True)
    liberty_profit = models.FloatField(null=True, blank=True)
    agent_cash_profit = models.FloatField(null=True, blank=True)
    balance_before = models.FloatField()
    balance_after = models.FloatField()
    float_balance_before = models.FloatField()
    float_balance_after = models.FloatField()
    checked = models.BooleanField(default=False)
    reconciled = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def debit_bill_and_pay_commissions(cls, user: User, transaction_inst: Transaction, former_bills_inst: 'CommissionsRecord'):
        get_float_user = WalletSystem.get_float_user()
        # get_bills_type = BillsPaymentConstant.objects.filter(biller=biller).last()

        liberty_reference = former_bills_inst.liberty_reference

        total_cost_of_acq = former_bills_inst.total_cost_of_acq
        float_balance_before = get_float_user.bills_pay_comm_balance
        float_balance_after = get_float_user.bills_pay_comm_balance - former_bills_inst.liberty_profit

        amount = former_bills_inst.amount
        agent_cash_profit = former_bills_inst.agent_cash_profit
        liberty_profit = former_bills_inst.liberty_profit

        commissions_record = cls.objects.create(
            user=user,
            amount=amount,
            entry="DEBIT",
            liberty_reference=liberty_reference,
            biller=former_bills_inst.biller,
            transaction_id=transaction_inst.transaction_id,
            bills_type=former_bills_inst.bills_type,
            total_cost_of_acq=total_cost_of_acq,
            total_profit=former_bills_inst.total_profit,
            provider_fee=former_bills_inst.provider_fee,
            liberty_profit=former_bills_inst.liberty_profit,
            agent_cash_profit=former_bills_inst.agent_cash_profit,
            balance_before=user.bills_pay_comm_balance,
            balance_after=user.bills_pay_comm_balance - agent_cash_profit,
            float_balance_before=float_balance_before,
            float_balance_after=float_balance_after
        )

        user.bills_pay_comm_balance_daily -= agent_cash_profit
        user.bills_pay_comm_balance -= agent_cash_profit
        user.save()

        get_float_user.bills_pay_comm_balance_daily -= liberty_profit
        get_float_user.bills_pay_comm_balance -= liberty_profit
        get_float_user.save()

        ##################################################################################

        get_vas_user_det = OtherServiceDetail.objects.filter(service_name="VAS_USER_EMAIL", is_active=True).last()
        if get_vas_user_det:
            get_vas_user = get_vas_user_det.user

            vas_user_record = cls.objects.create(
                user=get_vas_user,
                amount=amount,
                entry="DEBIT",
                liberty_reference=liberty_reference,
                biller=former_bills_inst.biller,
                transaction_id=transaction_inst.transaction_id,
                bills_type=former_bills_inst.bills_type,
                total_cost_of_acq=total_cost_of_acq,
                total_profit=former_bills_inst.total_profit,
                provider_fee=former_bills_inst.provider_fee,
                liberty_profit=liberty_profit,
                agent_cash_profit=agent_cash_profit,
                balance_before=get_vas_user.bills_pay_comm_balance,
                balance_after=get_vas_user.bills_pay_comm_balance - total_cost_of_acq,
                float_balance_before=float_balance_before,
                float_balance_after=float_balance_after
            )

            get_vas_user.bills_pay_comm_balance_daily -= total_cost_of_acq
            get_vas_user.bills_pay_comm_balance -= total_cost_of_acq
            get_vas_user.save()

            ##################################################################################
            liberty_commission_bank_account = AccountSystem.get_float_account(
                from_wallet_type="COMMISSIONS", from_provider_type="VFD"
            )

            liberty_float_bank_account = AccountSystem.get_float_account(
                from_wallet_type="FLOAT", from_provider_type="VFD"
            )

            re_internal_transaction = Transaction.objects.create(
                user=user,
                account_provider=transaction_inst.account_provider,
                transaction_type="BANK_OOB_IN",
                amount=transaction_inst.amount,
                provider_fee=0,
                liberty_reference=liberty_reference,
                liberty_commission=0,
                extra_fee=0,
                user_trans_band=user.trans_band,
                escrow_id=transaction_inst.escrow_id,
                beneficiary_account_name=liberty_float_bank_account.account_name,
                beneficiary_nuban=liberty_float_bank_account.account_number,
                beneficiary_bank_code=liberty_float_bank_account.bank_code,
                source_nuban=liberty_commission_bank_account.account_number,
                source_account_name=liberty_commission_bank_account.account_name,
                narration="BANK_OOB_IN",
                status="IN_PROGRESS",
                transaction_leg="INTERNAL",
            )

            new_liberty_reference = Transaction.create_liberty_reference_with_old_reference(liberty_reference=liberty_reference, suffix="RVSL")

            handle_rev = AccountSystem.handle_out_of_books(
                from_account=liberty_commission_bank_account.account_number,
                to_account=liberty_float_bank_account.account_number,
                amount=liberty_profit,
                escrow_id=transaction_inst.escrow_id,
                user_bvn_number=user.bvn_number,
                transaction_instance=re_internal_transaction,
                liberty_reference=new_liberty_reference
            )

        return True

    @classmethod
    def create_and_top_up_bill_and_pay_commissions(cls, user, amount, biller, transaction_id, total_profit=None, liberty_profit=None,
                                                   agent_cash_profit=None, provider_fee=None, escrow_id=None, use_task=False):
        get_float_user = WalletSystem.get_float_user()
        get_bills_type = BillsPaymentConstant.objects.filter(biller=biller).last()

        if cls.objects.filter(transaction_id=transaction_id, entry=CommissionsRecord.CREDIT).exists():
            print("trans exists, nothing to credit")
            return

        liberty_reference = Transaction.create_liberty_reference(suffix="LP-BPAY")

        print("about to credit")

        total_cost_of_acq = amount + provider_fee
        float_balance_before = get_float_user.bills_pay_comm_balance
        float_balance_after = get_float_user.bills_pay_comm_balance + liberty_profit

        print(agent_cash_profit)
        print("""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""")
        print("""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""")

        with django_transaction.atomic():

            if agent_cash_profit > 0:
                user.refresh_from_db

                commissions_record = cls.objects.create(
                    user=user,
                    amount=amount,
                    liberty_reference=liberty_reference,
                    biller=biller,
                    transaction_id=transaction_id,
                    bills_type=get_bills_type.bills_type,
                    total_cost_of_acq=total_cost_of_acq,
                    total_profit=total_profit,
                    provider_fee=provider_fee,
                    liberty_profit=liberty_profit,
                    agent_cash_profit=agent_cash_profit,
                    balance_before=user.bills_pay_comm_balance,
                    balance_after=user.bills_pay_comm_balance + agent_cash_profit,
                    float_balance_before=float_balance_before,
                    float_balance_after=float_balance_after
                )

                user.bills_pay_comm_balance_daily += agent_cash_profit
                user.bills_pay_comm_balance += agent_cash_profit
                user.save()
                user.refresh_from_db

            get_float_user.refresh_from_db()
            get_float_user.bills_pay_comm_balance_daily += liberty_profit
            get_float_user.bills_pay_comm_balance += liberty_profit
            get_float_user.save()
            get_float_user.refresh_from_db()

            ##################################################################################

            get_vas_user_det = OtherServiceDetail.objects.filter(service_name="VAS_USER_EMAIL", is_active=True).last()
            if get_vas_user_det:
                get_vas_user = get_vas_user_det.user
                get_vas_user.refresh_from_db()

                vas_user_record = cls.objects.create(
                    user=get_vas_user,
                    # Adding Liberty Commission to this
                    amount=amount + liberty_profit,
                    liberty_reference=liberty_reference + "_VAS",
                    biller=biller,
                    transaction_id=transaction_id,
                    bills_type=get_bills_type.bills_type,
                    total_cost_of_acq=total_cost_of_acq,
                    total_profit=total_profit,
                    provider_fee=provider_fee,
                    liberty_profit=liberty_profit,
                    agent_cash_profit=agent_cash_profit,
                    balance_before=get_vas_user.bills_pay_comm_balance,
                    balance_after=get_vas_user.bills_pay_comm_balance + total_cost_of_acq,
                    float_balance_before=float_balance_before,
                    float_balance_after=float_balance_after
                )

                get_vas_user.bills_pay_comm_balance_daily += total_cost_of_acq
                get_vas_user.bills_pay_comm_balance += total_cost_of_acq
                get_vas_user.save()
                get_vas_user.refresh_from_db()

        ##################################################################################
        # from_provider_type = ConstantTable.default_account_provider()
        #
        # if liberty_profit > 0:
        #
        #     if use_task:
        #         from accounts.tasks import pay_extra_commission_to_liberty_task
        #
        #         pay_extra_commission_to_liberty_task.apply_async(
        #             queue="intervrecon",
        #             kwargs={
        #                 "user_id": user.id,
        #                 "wallet_id": None,
        #                 "wallet_type": None,
        #                 "from_provider_type": from_provider_type,
        #                 "liberty_commission": liberty_profit,
        #                 "transaction_commission_id": transaction_id,
        #                 "transfer_leg": "VAS_COMMISSIONS",
        #                 "transaction_sub_type": "BILLSPAYCOMM",
        #                 "get_escrow_id": "escrow_id",
        #             }
        #         )
        #
        #     else:
        #         Create Send Commission Scheduler
                # SendCommissionScheduler.objects.create(
                #     user=user, provider=from_provider_type, amount=float(liberty_profit), transaction_commission_id=str(transaction_id),
                #     transfer_leg="VAS_COMMISSIONS", escrow_id=escrow_id, transaction_sub_type="BILLSPAYCOMM"
                # )
                # send_commission = WalletSystem.pay_commission_to_liberty(
                #     user_id=user.id,
                #     wallet_id=None,
                #     wallet_type=None,
                #     from_provider_type=from_provider_type,
                #     liberty_commission=liberty_profit,
                #     transaction_commission_id=transaction_id,
                #     transfer_leg="VAS_COMMISSIONS",
                #     get_escrow_id=escrow_id,
                #     transaction_sub_type="BILLSPAYCOMM"
                # )


class BillsPaymentDumpData(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    transaction_instance = models.ForeignKey(Transaction, on_delete=models.CASCADE, null=True, blank=True)
    transaction_id = models.CharField(max_length=200, null=True, blank=True)
    liberty_reference = models.CharField(max_length=200, null=True, blank=True)
    transaction_type = models.CharField(max_length=200, null=True, blank=True)
    amount = models.FloatField(null=True, blank=True)
    internal_service_data = models.TextField(null=True, blank=True)
    external_service_data = models.TextField(null=True, blank=True)
    reverify_data = models.TextField(null=True, blank=True)
    dump = models.TextField()
    is_reversed = models.BooleanField(default=False)
    manaul_update_successful = models.BooleanField(default=False)
    status = models.CharField(max_length=150, default="PENDING")
    package_slug = models.CharField(max_length=200, null=True, blank=True)
    bills_type = models.CharField(max_length=200, null=True, blank=True)
    biller = models.CharField(max_length=200, null=True, blank=True)
    sent = models.BooleanField(default=False)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):

        if self.transaction_instance:
            self.transaction_id = self.transaction_instance.transaction_id

        # if self.is_reversed:
        #     debit_credit_record_list = list(DebitCreditRecordOnAccount.objects.filter(user=self.user).values_list('transaction_instance_id', flat=True))

        #     if self.transaction_instance:
        #         # print(debit_credit_record_list.count(str(self.transaction_instance.transaction_id)))
        #         # if debit_credit_record_list.count(str(self.transaction_instance.transaction_id)) > 2 :
        #         if str(self.transaction_instance.transaction_id) in debit_credit_record_list:

        #             if DebitCreditRecordOnAccount.objects.filter(transaction_instance_id=self.transaction_instance.transaction_id, entry="CREDIT").exists() or self.transaction_instance.status == "SUCCESSFUL":

        #                 self.internal_service_data = "Transaction is already successful"
        #                 pass

        #             else:
        #                 if self.is_reversed and self.amount:

        #                     self.transaction_instance.status = "FAILED"
        #                     self.transaction_instance.balance_after = self.transaction_instance.balance_before
        #                     self.transaction_instance.save()

        #                     fund_user_wallet = WalletSystem.fund_balance(
        #                         user = self.user,
        #                         wallet = ,
        #                         amount = self.amount,
        #                         trans_type = f"{self.transaction_type}-REVERSAL",
        #                         transaction_instance_id = self.transaction_instance.transaction_id,
        #                         unique_reference=self.transaction_instance.escrow_id

        #                     )

        #         else:
        #             if self.is_reversed and self.amount:

        #                 self.transaction_instance.status = "FAILED"
        #                 self.transaction_instance.balance_after = self.transaction_instance.balance_before
        #                 self.transaction_instance.save()

        #                 fund_user_wallet = WalletSystem.fund_balance(
        #                     user = self.user,
        #                     to_wallet_type = "COLLECTION",
        #                     amount = self.amount,
        #                     trans_type = f"{self.transaction_type}-REVERSAL",
        #                     transaction_instance_id = self.transaction_instance.transaction_id,
        #                     unique_reference=self.transaction_instance.escrow_id

        #                 )

        #                 if self.transaction_type == "AIRTIME_PIN":
        #                     airtime_pin_parent = AirtimeToPinParent.objects.filter(liberty_reference=self.transaction_instance.liberty_reference).last()
        #                     if airtime_pin_parent:
        #                         for pin in airtime_pin_parent.airtime_pin_children.all():
        #                             pin.transaction_status = "FAILED"
        #                             pin.is_active = False
        #                             pin.save()

        super(BillsPaymentDumpData, self).save(*args, **kwargs)

    @staticmethod
    def handle_airtime_pin_resolve(batch_id, request_data, airtime_pin_parent: AirtimeToPinParent = None, create_transaction: Transaction = None):
        get_provider_status = request_data.get("status")

        if not airtime_pin_parent:
            airtime_pin_parent = AirtimeToPinParent.objects.filter(batch_id=batch_id).last()

        if airtime_pin_parent:
            with django_transaction.atomic():

                airtime_pin_parent.second_payload = request_data
                airtime_pin_parent.save()

                if not create_transaction:
                    create_transaction = Transaction.objects.filter(liberty_reference=airtime_pin_parent.liberty_reference).last()

                vas_dump_data = BillsPaymentDumpData.objects.filter(transaction_instance=create_transaction).last()
                if create_transaction and vas_dump_data:
                    if create_transaction.status not in ["SUCCESSFUL", "FAILED"]:

                        request_user = airtime_pin_parent.user
                        pin_amount = airtime_pin_parent.pin_amount
                        quantity = airtime_pin_parent.quantity
                        get_total_amount = airtime_pin_parent.total_pin_amount
                        from_wallet_type = airtime_pin_parent.from_wallet_type
                        network_provider = airtime_pin_parent.network

                        wallet_instance = WalletSystem.get_wallet(
                            user=airtime_pin_parent.user, from_wallet_type=from_wallet_type
                        )

                        all_created_pin_instances = airtime_pin_parent.airtime_pin_children.all()

                        unique_reference = request_data.get("data", {"reference": None}).get("reference")

                        if get_provider_status == "SUCCESSFUL":

                            pins_list_length = len(request_data["data"]["pins"])
                            pins_list = request_data["data"]["pins"]

                            objects_to_update = all_created_pin_instances.order_by('id')[0:pins_list_length]

                            for (pin, object) in zip(pins_list, objects_to_update):
                                object.card_pin = pin
                                object.unique_reference = unique_reference
                                object.transaction_status = "SUCCESSFUL"
                                object.save()

                            create_transaction.status = "SUCCESSFUL"
                            create_transaction.provider_status = request_data["status"]
                            create_transaction.unique_reference = unique_reference
                            create_transaction.save()

                            vas_dump_data.status = "SUCCESSFUL"
                            vas_dump_data.reverify_data = request_data
                            vas_dump_data.save()

                            if not airtime_pin_parent.is_successful:
                                airtime_pin_parent.is_successful = True
                                airtime_pin_parent.save()

                                ##########################################################################################################################################

                                # SEND OUT APP NOTIFICATION
                                not_token = request_user.firebase_key
                                not_title = "Transaction Successful"
                                not_body = f"You have successfully performed an Airtime to Pin transaction of N{get_total_amount}"
                                not_data = {"amount_sent": f"{get_total_amount}", "available_balance": f"{wallet_instance.available_balance}"}

                                send_out_notification = cloud_messaging.send_broadcast(
                                    token=not_token,
                                    title=not_title,
                                    body=not_body,
                                    data=not_data
                                )

                                InAppTransactionNotification.create_in_app_transaction_notification(
                                    user=request_user,
                                    title=not_title,
                                    message_body=not_body
                                )

                                #####################################################################################################

                                # HANDLE COMMISSIONS AND PROFITS

                                get_profits = BillsPaymentConstant.share_commissions_profit(biller=network_provider, amount=pin_amount,
                                                                                            bill_provider="REDBILLER")

                                total_profit = get_profits["total_profit"]
                                liberty_profit = get_profits["liberty_profit"]
                                agent_cash_profit = get_profits["agent_cash_profit"]
                                provider_fee = get_profits["provider_fee"]

                                for _ in range(quantity):
                                    # Fund User commission wallet
                                    CommissionsRecord.create_and_top_up_bill_and_pay_commissions(
                                        user=request_user,
                                        amount=pin_amount,
                                        biller=network_provider,
                                        transaction_id=create_transaction.transaction_id,
                                        total_profit=total_profit,
                                        liberty_profit=liberty_profit,
                                        agent_cash_profit=agent_cash_profit,
                                        provider_fee=provider_fee,
                                        escrow_id=create_transaction.escrow_id
                                    )

                            #########################################################################################################################

                            response = {
                                "status": "success",
                                "message": "SUCCESSFUL RESPONSE RECEIVED",
                            }


                        elif get_provider_status in ["DUPLICATE", "CANCELLED", "FAILED"]:

                            # Refund Money

                            reversal_transaction = Transaction.reverse_bills_airpin_transactions(transaction=create_transaction,
                                                                                                 provider_status=get_provider_status)

                            for pin in all_created_pin_instances:
                                pin.transaction_status = "FAILED"
                                pin.is_active = False
                                pin.save()

                            vas_dump_data.status = "FAILED"
                            vas_dump_data.save()

                            response = {
                                "status": "reversed",
                                "message": "Transaction Failed. Thank you"
                            }

                        else:

                            create_transaction.status = "PENDING"
                            create_transaction.provider_status = get_provider_status
                            create_transaction.unique_reference = unique_reference
                            create_transaction.save()

                            response = {
                                "status": "pending",
                                "message": "error",
                                "data": []
                            }

                    else:

                        response = {
                            "status": "exists",
                            "message": "Transaction Already SUCCESSFUL OR FAILED"
                        }



                else:

                    response = {
                        "status": "failed",
                        "message": "NOT SUCCESSFUL PAYLOAD"
                    }


        else:
            response = {
                "status": "error",
                "message": "CANT RECOGNIZE DATA"
            }

        return response


class AllBankList(models.Model):
    name = models.CharField(max_length=500)
    bank_code = models.CharField(max_length=50, null=True, blank=True)
    cbn_code = models.CharField(max_length=50, null=True, blank=True)
    ussd_short_code = models.CharField(max_length=50, null=True, blank=True)
    bank_short_name = models.CharField(max_length=100, null=True, blank=True)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.name

    def clean(self):
        if not self.cbn_code and not self.bank_code:
            raise ValidationError('Must enter one of CBN code or Bank code')

    @classmethod
    def get_all_bank_list(cls, ):
        cache_key = "all_bank_list"
        cached_data = cache.get(cache_key)
        if cached_data is not None:
            queryset = cached_data
        else:
            queryset = AllBankList.objects.all()
            # Store the data in the cache
            cache.set(cache_key, queryset)

        return queryset

    @classmethod
    def update_bank_list(cls):
        """
        Update AllBankList from PayStack API data.
        Creates new entries if they don't exist, updates existing ones.

        Args:
            bank_list: List of dictionaries containing bank data from API
            example:
                    bank_list = [
                                    {
                                        "id": 796,
                                        "code": "090718",
                                        "name": "ZITRA MFB",
                                        "logo": "",
                                        "created": "2024-08-05 21: 24: 19"
                                    }
                        ]


                        {'main_data':
                        {
                            'status': '00',
                          'message': 'All Banks With Respective Codes',
                          'data': {
                          'bank': [{'id': 712,
                             'code': '110072',
                             'name': '78 FINANCE COMPANY LTD',
                             'logo': '',
                             'created':
        """
        bank_list = list()

        try:
            data = VFDBank.get_all_bank_list()
            if "main_data" in data:
                status_code = data.get("main_data").get("status")
                if status_code == "00":
                    bank_list = data.get("main_data").get("data").get("bank")

        except Exception as err:
            print(err)
            pass

        if bank_list:

            for bank_data in bank_list:
                bank_code = bank_data.get('code')
                if bank_code:
                    bank, created = cls.objects.get_or_create(cbn_code=bank_code)
                    bank.name = bank_data.get('name')
                    bank.bank_code = bank_code
                    bank.bank_short_name = bank_data.get('slug')
                    bank.save()

            # Invalidate cache after updates
            cache_key = "all_bank_list"
            cache.delete(cache_key)

        return True


class SalesRepCommissionTable(models.Model):
    sale_rep = models.ForeignKey(User, related_name="sales_rep_comm", on_delete=models.CASCADE)
    downline_user = models.ForeignKey(User, on_delete=models.CASCADE)
    downline_transaction_type = models.CharField(max_length=200, null=True, blank=True)
    amount = models.FloatField()
    balance_before = models.FloatField()
    balance_after = models.FloatField()
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    # if get_sales_rep_user:
    #     return True
    # else:
    #     return False

    @classmethod
    def create_and_top_up_sales_rep_commissions(cls, downline_user, amount, downline_transaction_type):
        from admin_dashboard.models import SalesRep

        get_sales_rep = None
        if get_sales_rep:
            # get_sales_rep_comm = ConstantTable.get_sales_rep_commission(get_sales_rep_user)

            # if downline_transaction_type == "TRANSFERS":
            #     sales_rep_comm = get_sales_rep_comm["transfer"]
            # else:
            #     sales_rep_comm = (amount/100) * get_sales_rep_comm["cash_out"]
            #     # formatted_sales_rep_comm = (amount/100) * sales_rep_comm

            sales_rep_user = get_sales_rep.sales_rep

            commissions_record = cls.objects.create(
                sale_rep=sales_rep_user,
                downline_user=downline_user,
                downline_transaction_type=downline_transaction_type,
                # amount=sales_rep_comm,
                liberty_reference=f"LP-REP-COM-{str(uuid.uuid4())[0:12]}",
                balance_before=sales_rep_user.sales_rep_comm_balance,
                # balance_after=sales_rep_user.sales_rep_comm_balance + sales_rep_comm
            )

            # sales_rep_user.sales_rep_comm_balance += sales_rep_comm
            # sales_rep_user.sales_rep_comm_balance_daily += sales_rep_comm
            sales_rep_user.save()

        else:
            pass


class GeneralDataDump(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    transaction_type = models.CharField(max_length=150, null=True, blank=True)
    payload = models.TextField()
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class StartFundByUSSDTran(models.Model):
    TRANS_TYPE_CHOICES = [
        ("FUND_BY_USSD", "FUND_BY_USSD"),
        ("USSD_WITHDRAW", "USSD_WITHDRAW"),
    ]
    TRANSACTION_STATUS = [
        ("PENDING", "PENDING"),
        ("SUCCESSFUL", "SUCCESSFUL"),
        ("FAILED", "FAILED"),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    trans_type = models.CharField(max_length=150, choices=TRANS_TYPE_CHOICES)
    first_name = models.CharField(max_length=200)
    last_name = models.CharField(max_length=200)
    user_bvn = models.CharField(max_length=100)
    amount_started = models.FloatField(null=True, blank=True)
    bank_code = models.CharField(max_length=100)
    bank_name = models.CharField(max_length=150)
    liberty_reference = models.CharField(max_length=200)
    ussd_code = models.CharField(max_length=100, null=True, blank=True)
    narration = models.CharField(max_length=150, null=True, blank=True)
    trans_complete = models.BooleanField(default=False)
    amount_resolved = models.FloatField(null=True, blank=True)
    charge = models.FloatField(null=True, blank=True)
    settlement = models.FloatField(null=True, blank=True)
    ussd_biller = models.CharField(max_length=150, default="CORALPAY")
    initiate_data = models.TextField(null=True, blank=True)
    resolve_data = models.TextField(null=True, blank=True)
    start_ip_addr = models.CharField(max_length=150, null=True, blank=True)
    check_ip_addr = models.CharField(max_length=150, null=True, blank=True)
    returned_checked_from = models.CharField(max_length=150, null=True, blank=True)
    internal_response = models.TextField(null=True, blank=True)
    external_response = models.TextField(null=True, blank=True)
    status = models.CharField(max_length=100, choices=TRANSACTION_STATUS, default="PENDING")
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def calculate_charge(cls, amount, trans_type):
        liberty_charge = 0
        return liberty_charge

    @classmethod
    def create_fund_by_ussd_trans(cls, user: User, amount_started, bank_code, bank_name, trans_type, ip_addr, biller=None):

        if biller == "CORALPAY":
            create_reference = Transaction.create_liberty_reference(suffix="CU")

            started_trans = cls.objects.create(
                user=user,
                trans_type=trans_type,
                first_name=user.bvn_first_name,
                last_name=user.bvn_last_name,
                user_bvn=user.bvn_number,
                amount_started=amount_started,
                bank_code=bank_code,
                bank_name=bank_name,
                narration=trans_type,
                liberty_reference=create_reference,
                start_ip_addr=ip_addr
            )

        else:
            create_reference = Transaction.create_liberty_reference(suffix="REDBILLER")

            started_trans = cls.objects.create(
                user=user,
                trans_type=trans_type,
                first_name=user.bvn_first_name,
                last_name=user.bvn_last_name,
                user_bvn=user.bvn_number,
                amount_started=amount_started,
                bank_code=bank_code,
                bank_name=bank_name,
                narration=trans_type,
                liberty_reference=create_reference,
                start_ip_addr=ip_addr,
                ussd_biller=biller
            )

        return started_trans

    @classmethod
    def handle_reference_from_funding(cls, reference, request, request_from):
        if reference is None:
            response = {
                "error": "549",
                "message": "No Reference Attached"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)

        get_trans = StartFundByUSSDTran.objects.filter(liberty_reference=reference).last()
        if not get_trans:
            response = {
                "error": "527",
                "message": "Reference does not exist"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        # Get IP ADDRESS
        address = request.META.get('HTTP_X_FORWARDED_FOR')
        if address:
            ip_addr = address.split(',')[-1].strip()
        else:
            ip_addr = request.META.get('REMOTE_ADDR')

        if get_trans.trans_complete:
            response = {
                "message": "Payment Receeived",
                "amount": get_trans.amount_resolved,
                "bank_name": get_trans.bank_name,
            }

        else:
            verify_trans = ServicesVASApp.verify_redbiller_payment(liberty_reference=reference)

            if verify_trans.get("status") == "true" and "Approved" in verify_trans.get("details").get("status"):
                get_trans.trans_complete = True
                get_trans.redbiller_resolve_data = verify_trans
                get_trans.amount_resolved = verify_trans.get("details").get("amount")
                get_trans.charge = verify_trans.get("details").get("charge")
                get_trans.settlement = verify_trans.get("details").get("settlement")
                # get_trans.check_ip_addr = ip_addr
                get_trans.returned_checked_from = request_from
                get_trans.save()

                if Transaction.objects.filter(liberty_reference=reference).exists():
                    pass
                else:
                    StartFundByUSSDTran.resolve_fund_by_ussd_trans(
                        user=request.user,
                        trans_instance=get_trans,
                    )

                response = {
                    "message": "Payment Receeived",
                    "amount": get_trans.amount_resolved,
                    "bank_name": get_trans.bank_name,
                }

            else:
                response = {
                    "error": "546",
                    "message": "No Payment",
                    "amount_expected": get_trans.amount_started,
                    "ussd_code": get_trans.ussd_code,
                    "bank_name": get_trans.bank_name,
                }

        return Response(response, status=status.HTTP_200_OK)

    @classmethod
    def resolve_fund_by_ussd_trans(cls, user, trans_instance):

        liberty_commission = StartFundByUSSDTran.calculate_charge(amount=trans_instance.amount_resolved, trans_type=trans_instance.trans_type)
        check_wallet = WalletSystem.get_wallet(user=user, from_wallet_type="COLLECTION")

        if check_wallet:

            create_trans = WalletSystem.fund_user_on_pay_by_ussd(
                user_instance=user,
                ussd_trans_instance=trans_instance,
                liberty_commission=liberty_commission,
                wallet=check_wallet,
                amount=trans_instance.amount_resolved,
                narration=trans_instance.narration,
            )

        else:
            pass

        return create_trans


class DailyUptimeDowntimeRecord(models.Model):
    entry_type = models.CharField(max_length=250)
    uptime = models.FloatField(default=0.00)
    downtime = models.FloatField(default=0.00)
    downtime_stat = models.TextField(null=True)
    day_complete = models.BooleanField(default=False)
    for_date = models.DateField()
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        self.uptime = round(self.uptime, 2)
        self.downtime = round(self.downtime, 2)
        super(DailyUptimeDowntimeRecord, self).save(*args, **kwargs)

    @classmethod
    def get_yesterday(cls, entry, date):

        try:
            yesterday, created = cls.objects.get_or_create(entry_type=entry, for_date=date)
        except cls.MultipleObjectsReturned:
            all_obj = cls.objects.filter(entry_type=entry, for_date=date)
            last_obj = all_obj.last()
            all_obj.exclude(id=last_obj.id).delete()

            yesterday = last_obj

        print(yesterday, "##")
        return yesterday

    @classmethod
    def get_today(cls, entry):
        today = datetime.now().date()

        today_calc = cls.objects.filter(entry_type=entry, date_created__date=today).last()
        if not today_calc:
            today = cls.objects.create(date_created=datetime.now(), entry_type=entry)
        else:
            today = today_calc

        return today

    @classmethod
    def calculate_up_down_time(cls, data):
        uptime_count = data["uptime_count"]
        downtime_count = data["downtime_count"]
        downtime_stat = data["downtime_stat"]

        overall_count = uptime_count + downtime_count

        if uptime_count > 0 or downtime_count > 0:
            uptime = (uptime_count / overall_count) * 100
            downtime = 100 - uptime
        else:
            uptime = 0
            downtime = 0

        return {
            "uptime": uptime,
            "downtime": downtime,
            "downtime_stat": downtime_stat,
        }

    @classmethod
    def update_record_table(cls, obj, result):
        obj.uptime = result["uptime"]
        obj.downtime = result["downtime"]
        obj.downtime_stat = result["downtime_stat"]
        obj.day_complete = True
        obj.save()

        return "DONE"


class SendMoneyDumpData(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    transfer_type = models.CharField(max_length=150, null=True, blank=True)
    dump = models.TextField()
    money_removed = models.BooleanField(default=False)
    escrow_created = models.BooleanField(default=False)
    result = models.TextField(null=True, blank=True)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


##############################################
# Merchant Sweep / Disbursement
##############################################

class MerchantDisbursements(models.Model):
    FREQUENCY_CHOICES = [
        ('DAILY', "DAILY"),
        ('WEEKLY', "WEEKLY"),
        ('MONTHLY', "MONTHLY"),
        ('HOURLY', "HOURLY"),
        ('INSTANT', "INSTANT"),
    ]
    DAY_CHOICES = [
        ('MON', "MON"),
        ('TUE', "TUE"),
        ('WED', "WED"),
        ('THU', "THU"),
        ('FRI', "FRI"),
        ('SAT', "SAT"),
        ('SUN', "SUN")
    ]
    INTERVAL_CHOICES = [
        (1, 1),
        (2, 2),
        (4, 4),
        (6, 6),
        (12, 12)
    ]
    STATUS_CHOICES = [
        ('PROCESSING', "PROCESSING"),
        ('DORMANT', "DORMANT")
    ]
    user = models.ForeignKey(User, related_name="disbursement", on_delete=models.CASCADE)
    wallet = models.ForeignKey(WalletSystem, related_name="disbursement", on_delete=models.CASCADE)
    bank_code = models.CharField(max_length=200)
    account_number = models.CharField(max_length=200)
    sweep_limit = models.FloatField(null=True, blank=True)
    min_balance = models.FloatField(null=True, blank=True)
    frequency = models.CharField(max_length=200, choices=FREQUENCY_CHOICES)
    task = models.OneToOneField(PeriodicTask, null=True, blank=True, on_delete=models.CASCADE)
    time = models.TimeField(null=True, blank=True)
    date = models.DateField(null=True, blank=True)

    # required for weekly
    day = models.CharField(max_length=30, null=True, blank=True, choices=DAY_CHOICES)

    # required for hourly
    interval = models.IntegerField(choices=INTERVAL_CHOICES, blank=True, null=True)

    # for second implementation
    status = models.CharField(max_length=30, blank=True, null=True, default="DORMANT", choices=STATUS_CHOICES)
    previous_runtime = models.DateTimeField(null=True, blank=True)

    def setup_task(self):
        schedule = self.crontab_schedule()
        tmp_time = datetime.combine(date.today(), self.time)
        self.task, created = PeriodicTask.objects.get_or_create(
            name=f'Disbursing funds to {self.user} {self.frequency} by {self.time}',
            task='this_send_money',
            args=json.dumps([self.user.email]),
            crontab=schedule,
            start_time=TIMEZONE.localize(tmp_time)
        )

    def crontab_schedule(self):
        if self.frequency == 'HOURLY':
            a, created = CrontabSchedule.objects.get_or_create(
                minute="0",
                hour=f"*/{self.interval}",
                day_of_week='*',
                day_of_month='*',
                month_of_year='*',
            )
            return a
        elif self.frequency == 'DAILY':
            tmp_time = TIMEZONE.localize(datetime.combine(date.today(), self.time))
            a, created = CrontabSchedule.objects.get_or_create(
                minute=tmp_time.minute,
                hour=tmp_time.hour,
                day_of_week='*',
                day_of_month='*',
                month_of_year='*',
            )
            return a
        elif self.frequency == 'WEEKLY':
            day = self.day.lower()
            tmp_time = TIMEZONE.localize(datetime.combine(date.today(), self.time))
            a, created = CrontabSchedule.objects.get_or_create(
                minute=tmp_time.minute,
                hour=tmp_time.hour,
                day_of_week=day,
                day_of_month='*',
                month_of_year='*',
            )
            return a
        elif self.frequency == 'MONTHLY':
            tmp_datetime = TIMEZONE.localize(datetime.combine(self.date, self.time))
            a, created = CrontabSchedule.objects.get_or_create(
                minute=tmp_datetime.minute,
                hour=tmp_datetime.hour,
                day_of_week='*',
                day_of_month=tmp_datetime.day,
                month_of_year='*',
            )
            return a

    def delete(self, *args, **kwargs):
        if self.task is not None:
            self.task.delete()
        return super(self.__class__, self).delete(*args, **kwargs)


class MerchantDisbursementManager(models.Model):
    total_count = models.IntegerField(default=0)
    current_count = models.IntegerField(default=0)

    def save(self, *args, **kwargs):
        if self.current_count >= self.total_count:
            self.current_count = 0
            self.total_count = 0
        return super(self.__class__, self).save(*args, **kwargs)


###############
# Merchant Sweep / Disbursement End
##############################################


class CashOutChargeBand(models.Model):
    CARD_WITHDRAW_AGENT = "CARD_WITHDRAW_AGENT"
    CARD_WITHDRAW_MERCHANT = "CARD_WITHDRAW_MERCHANT"

    TRANSACTION_TYPE = [
        (CARD_WITHDRAW_AGENT, "CARD_WITHDRAW_AGENT"),
        (CARD_WITHDRAW_MERCHANT, "CARD_WITHDRAW_MERCHANT")
    ]

    band = models.PositiveSmallIntegerField(default=1)
    constant_instance = models.ForeignKey(ConstantTable, on_delete=models.CASCADE, null=True, blank=True, related_name="cash_out_charges")
    transaction_type = models.CharField(max_length=200, choices=TRANSACTION_TYPE, default="CARD_WITHDRAW_AGENT")
    lower_limit = models.FloatField()
    upper_limit = models.FloatField()
    agent_charge_value = models.FloatField(null=True, blank=True)
    provider_charge_value = models.FloatField(null=True, blank=True)
    first_liberty_profit = models.FloatField(null=True, blank=True)
    ro_profit = models.FloatField(null=True, blank=True)
    second_liberty_profit = models.FloatField(null=True, blank=True)
    agent_profit = models.FloatField(null=True, blank=True)
    liberty_profit = models.FloatField(null=True, blank=True)
    cash_back_up_to_percent = models.CharField(max_length=40, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def calculate_cash_out_agent_fees(cls, amount, user: User):
        if ConstantTable.get_constant_table_instance().free_cash_out == True:
            response = {
                "total_charge": 0,
                "liberty_profit": 0,
                "ro_profit": 0,
                "agent_profit": 0,
            }

        else:
            get_fee = cls.objects.filter(transaction_type="CARD_WITHDRAW_AGENT", upper_limit__gte=amount, lower_limit__lte=amount,
                                         band=user.trans_band).last()
            if get_fee:
                response = {
                    "total_charge": get_fee.agent_charge_value,
                    "liberty_profit": get_fee.liberty_profit,
                    "ro_profit": get_fee.ro_profit,
                    "agent_profit": get_fee.agent_profit,
                }

            else:
                response = {
                    "total_charge": 100,
                    "liberty_profit": 100,
                    "ro_profit": 3,
                    "agent_profit": 0,
                }

        return response

    @classmethod
    def calculate_cash_out_merchant_fees(cls, amount):

        const = ConstantTable.get_constant_table_instance()

        # liberty_cash_out_fee = const.liberty_cash_out_fee
        nibbs_cash_out_fee = const.nibbs_cash_out_fee
        interswitch_cash_out_fee_value = const.interswitch_cash_out_fee_value
        ro_cash_out_fee = const.ro_cash_out_fee
        ro_cash_out_fee_value = const.ro_cash_out_fee_value
        cash_back_cash_out_fee = const.cash_back_cash_out_fee
        max_merchant_charge = const.merchant_cash_out_max_charge_amount

        transaction_charge_percent = ConstantTable.get_constant_table_instance().merchant_cash_out_percent
        amount_div = amount / 100

        if amount_div < 1:
            amount_div = 1

        charge = amount_div * transaction_charge_percent
        if charge > max_merchant_charge:
            merchant_charge_value = max_merchant_charge
        else:
            merchant_charge_value = charge

        # Calculate Provider Cash Value
        if amount > 8000:
            provider_charge_value = interswitch_cash_out_fee_value
        else:
            provider_charge_value = float("{:.2f}".format((amount / 100) * nibbs_cash_out_fee))

        # Calculate Other Values
        first_liberty_profit = float("{:.2f}".format(merchant_charge_value - provider_charge_value))

        # Calculate RO Profit
        if amount > 4000:
            ro_profit = ro_cash_out_fee_value
        else:
            ro_profit = float("{:.2f}".format((first_liberty_profit / 100) * ro_cash_out_fee))

        # Calculate Second Liberty Profit
        second_liberty_profit = float("{:.2f}".format(first_liberty_profit - ro_profit))

        # Calculate Agent Profit
        merchant_profit = float("{:.3f}".format((second_liberty_profit / 100) * cash_back_cash_out_fee))

        # Calculate Liberty Profit
        liberty_profit = float("{:.3f}".format(second_liberty_profit - merchant_profit))

        # Calculate Cash Back Up to Percent
        calculated_cash_back_percent = float("{:.2f}".format((merchant_profit / merchant_charge_value) * 100))
        cash_back_up_to_percent = f"{calculated_cash_back_percent}%"

        response = {
            "total_charge": merchant_charge_value,
            "liberty_profit": liberty_profit,
            "ro_profit": ro_profit,
            "agent_profit": merchant_profit
        }

        return response

    # def save(self, *args, **kwargs):
    #     liberty_cash_out_fee = ConstantTable.get_constant_table_instance().liberty_cash_out_fee

    #     # for charge in CashOutChargeBand.objects.all():
    #     #     charge.agent_charge_value = (charge.upper_limit/100) * liberty_cash_out_fee
    #     #     charge.save()
    #     # if self.transaction_charge_percent:
    #     #     pass

    #     super(CashOutChargeBand, self).save(*args, **kwargs)

    # @classmethod
    # def calculate_agent_cash_out_charge(cls, amount):
    #     constant_instance = ConstantTable.get_constant_table_instance().liberty_cash_out_fee
    #     charge_bands = ChargeBand.objects.filter(Q(constant=constant_instance) & Q(transaction_type="CARD_WITHDRAW_AGENT"))
    #     print(charge_bands)
    #     for inst in charge_bands:
    #         if inst.lower_limit <= amount < inst.upper_limit:
    #             transaction_charge = (amount/100) * inst.transaction_charge_percent + inst.transaction_charge_value
    #     else:
    #         transaction_charge = 50

    #     return transaction_charge


class OtherCommissionsRecord(models.Model):
    CASH_OUT = "CASH_OUT"
    TRANSFER = "TRANSFER"
    LOTTO_PLAY = "LOTTO_PLAY"
    LIBERTY_LIFE_PAYMENT = "LIBERTY_LIFE_PAYMENT"
    CREDI_LOAN = "CREDI_LOAN"
    AJO_LOAN = "AJO_LOAN"
    ASSURED_LOANS = "ASSURED_LOANS"
    ASSURED_LOAN_COMMISSIONS = "ASSURED_LOAN_COMMISSIONS"

    TRANSACTION_TYPE = [
        (CASH_OUT, "CASH_OUT"),
        (TRANSFER, "TRANSFER"),
        (LOTTO_PLAY, "LOTTO_PLAY"),
        (LIBERTY_LIFE_PAYMENT, "LIBERTY_LIFE_PAYMENT"),
        (CREDI_LOAN, "CREDI_LOAN"),
        (AJO_LOAN, "AJO_LOAN"),
        (ASSURED_LOANS, "ASSURED_LOANS"),
        (ASSURED_LOAN_COMMISSIONS, "ASSURED_LOAN_COMMISSIONS"),
    ]

    SALES_REP = "SALES_REP"
    AGENT = "AGENT"
    LIBERTY = "LIBERTY"

    TRANSACTION_OWNER = [
        (SALES_REP, SALES_REP),
        (AGENT, AGENT),
        (LIBERTY, LIBERTY)
    ]

    ENTRY_TYPE = [
        ("CREDIT", "CREDIT"),
        ("DEBIT", "DEBIT"),
    ]

    entry = models.CharField(max_length=100, default="CREDIT")
    agent = models.ForeignKey(User, on_delete=models.CASCADE)
    sales_rep = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name="sales_rept_other_comm")
    is_paybox_merchant = models.BooleanField(default=False)
    amount = models.FloatField()
    liberty_reference = models.CharField(max_length=200)
    transaction_id = models.CharField(max_length=200, null=True, blank=True)
    transaction_type = models.CharField(max_length=200, choices=TRANSACTION_TYPE)
    transaction_owner = models.CharField(max_length=200, choices=TRANSACTION_OWNER)
    transaction_reason = models.CharField(max_length=200)
    total_profit = models.FloatField(null=True, blank=True)
    liberty_profit = models.FloatField(null=True, blank=True)
    ro_cash_profit = models.FloatField(null=True, blank=True)
    agent_cash_profit = models.FloatField(null=True, blank=True)
    balance_before = models.FloatField()
    balance_after = models.FloatField()
    # float_balance_before = models.FloatField()
    # float_balance_after = models.FloatField()
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @staticmethod
    def check_if_sales_rep_exists(downline_user):
        from admin_dashboard.models import SalesRep

        if downline_user.sales_rep_upline_code is None:
            return None
        else:
            get_sales_rep_user = SalesRep.objects.filter(sales_rep_code=downline_user.sales_rep_upline_code).last()

            return get_sales_rep_user

    @staticmethod
    def get_sales_rep_with_code(sales_rep_code):
        from admin_dashboard.models import SalesRep

        get_sales_rep_user = SalesRep.objects.filter(sales_rep_code=sales_rep_code).last()

        return get_sales_rep_user

    @classmethod
    def handle_extra_commissions_on_transfers(
            cls, user, amount, transaction_instance_id,
            wallet_id, wallet_type, from_provider_type, get_escrow_id=None
    ):

        total_profit = ConstantTable.get_constant_table_instance().send_money_transfer_extra_fee
        ro_profit = ConstantTable.get_constant_table_instance().ro_transfer_fee_value
        agent_profit = ConstantTable.get_constant_table_instance().agent_transfer_fee_value

        get_sales_rep = OtherCommissionsRecord.check_if_sales_rep_exists(downline_user=user)
        provider_fee = ConstantTable.get_provider_fee(from_provider_type=from_provider_type)

        if get_sales_rep is None:
            liberty_profit = (((total_profit - ro_profit) + ro_profit) + provider_fee) if (agent_profit <= 0) else (ro_profit + provider_fee)
            # final_profit = ro_profit
            sales_rep = False


        else:
            liberty_profit = ((total_profit - ro_profit) + provider_fee) if (agent_profit <= 0) else provider_fee
            # final_profit = 00
            sales_rep = True

        # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        # print("total_profit: ", total_profit)
        # print("agent_profit: ", agent_profit)
        # print("ro_profit: ", ro_profit)
        # print("liberty_profit: ", liberty_profit)
        # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")

        # Handle Agent Profit On Transfers
        if total_profit >= agent_profit + ro_profit:
            if agent_profit > 0:
                handle_agent_commisson = OtherCommissionsRecord.create_and_top_up_other_commissions_for_agent(
                    agent=user,
                    sales_rep=get_sales_rep.sales_rep if get_sales_rep else None,
                    amount=amount,
                    transaction_id=transaction_instance_id,
                    transaction_type="TRANSFER",
                    transaction_reason="Cash Back On Transfer",
                    total_profit=total_profit,
                    liberty_profit=liberty_profit,
                    ro_cash_profit=ro_profit,
                    agent_cash_profit=agent_profit,
                )

                # Handle RO Profit On Cash Out

                get_transaction = Transaction.objects.filter(transaction_id=transaction_instance_id).last()

                get_transaction.sales_rep = sales_rep
                get_transaction.final_liberty_rev = liberty_profit
                get_transaction.liberty_profit = liberty_profit
                get_transaction.ro_profit = ro_profit
                get_transaction.agent_profit = agent_profit

                get_transaction.save()

            if ro_profit > 0:
                if get_sales_rep:
                    handle_ro_commission = OtherCommissionsRecord.create_and_top_up_sales_rep_commissions(
                        agent=user,
                        sales_rep=get_sales_rep.sales_rep,
                        amount=amount,
                        transaction_id=transaction_instance_id,
                        transaction_type="TRANSFER",
                        transaction_reason=f"Commission on Transfer Transaction by {user.bvn_first_name}" if user.bvn_first_name else f"Commission on Transfer Transaction by {user.email}",
                        total_profit=total_profit,
                        liberty_profit=liberty_profit,
                        ro_cash_profit=ro_profit,
                        agent_cash_profit=agent_profit
                    )
                else:
                    pass

            if agent_profit <= 0:
                handle_liberty_commisson = OtherCommissionsRecord.create_and_top_up_other_commissions_for_liberty(
                    agent=user,
                    sales_rep=get_sales_rep.sales_rep if get_sales_rep else None,
                    amount=amount,
                    transaction_id=transaction_instance_id,
                    transaction_type="TRANSFER",
                    transaction_reason="Liberty Transfer Record",
                    total_profit=total_profit,
                    liberty_profit=liberty_profit,
                    ro_cash_profit=ro_profit,
                    agent_cash_profit=agent_profit,
                )

            commission_amount = liberty_profit - provider_fee

            if commission_amount > 0:
                # Create Send Commission Scheduler
                SendCommissionScheduler.objects.create(
                    user=user, wallet_id=wallet_id, wallet_type=wallet_type, amount=float(commission_amount), provider=from_provider_type,
                    transaction_commission_id=str(transaction_instance_id), transfer_leg="TRNS_CB_COMM", escrow_id=get_escrow_id,
                    transaction_sub_type="NO_RO_EXT_COMM"
                )
                # send_commission = WalletSystem.pay_commission_to_liberty(
                #     user_id=user.id,
                #     wallet_id=wallet_id,
                #     wallet_type=wallet_type,
                #     liberty_commission=liberty_profit - provider_fee,
                #     from_provider_type=from_provider_type,
                #     transaction_commission_id=transaction_instance_id,
                #     transfer_leg="TRNS_CB_COMM",
                #     get_escrow_id=get_escrow_id,
                #     transaction_sub_type="NO_RO_EXT_COMM"
                # )

                # from accounts.tasks import pay_extra_commission_to_liberty_task

                # pay_extra_commission_to_liberty_task.delay(
                #     user_id = user.id,
                #     wallet_id=wallet_id,
                #     wallet_type = wallet_type,
                #     final_profit = liberty_profit - provider_fee,
                #     from_provider_type = from_provider_type,
                #     transaction_instance_id = transaction_instance_id,
                #     transfer_leg="TRNS_CB_COMM",
                #     get_escrow_id = get_escrow_id,
                #     transaction_sub_type="NO_RO_EXT_COMM"
                # )

                # send_commission = WalletSystem.pay_commission_to_liberty(
                #     user_id=user.id,
                #     wallet_id=wallet_id,
                #     wallet_type=wallet_type,
                #     liberty_commission = final_profit,
                #     from_provider_type=from_provider_type,
                #     transaction_commission_id = transaction_instance_id,
                #     transfer_leg="TRNS_CB_COMM",
                #     get_escrow_id = get_escrow_id
                # )

            else:
                pass

        else:
            handle_agent_commisson = OtherCommissionsRecord.create_and_top_up_other_commissions_for_liberty(
                agent=user,
                sales_rep=get_sales_rep.sales_rep if get_sales_rep else None,
                amount=amount,
                transaction_id=transaction_instance_id,
                transaction_type="TRANSFER",
                transaction_reason="Liberty Transfer Record",
                total_profit=total_profit,
                liberty_profit=liberty_profit,
                ro_cash_profit=ro_profit,
                agent_cash_profit=agent_profit,
            )

    @classmethod
    def create_and_top_up_other_commissions_for_liberty(
            cls, agent, sales_rep, amount, transaction_id, transaction_type,
            transaction_reason, total_profit=None, liberty_profit=None,
            ro_cash_profit=None, agent_cash_profit=None, transaction_owner=None
    ):

        commissions_record = cls.objects.create(
            agent=agent,
            sales_rep=sales_rep,
            amount=amount,
            liberty_reference=f"LP-OTH_COM-{str(uuid.uuid4())[0:12]}",
            transaction_id=transaction_id,
            transaction_type=transaction_type,
            transaction_reason=transaction_reason,
            transaction_owner="LIBERTY",
            total_profit=total_profit,
            liberty_profit=liberty_profit,
            ro_cash_profit=ro_cash_profit,
            agent_cash_profit=agent_cash_profit,
            balance_before=agent.other_comm_balance,
            balance_after=agent.other_comm_balance + agent_cash_profit
        )

    @classmethod
    def create_and_top_up_other_commissions_for_agent(
            cls, agent, sales_rep, amount, transaction_id, transaction_type,
            transaction_reason, total_profit=None, liberty_profit=None,
            ro_cash_profit=None, agent_cash_profit=None, transaction_owner=None
    ):

        commissions_record = cls.objects.create(
            agent=agent,
            sales_rep=sales_rep,
            amount=amount,
            liberty_reference=f"LP-OTH_COM-{str(uuid.uuid4())}",
            transaction_id=transaction_id,
            transaction_type=transaction_type,
            transaction_reason=transaction_reason,
            transaction_owner="AGENT",
            total_profit=total_profit,
            liberty_profit=liberty_profit,
            ro_cash_profit=ro_cash_profit,
            agent_cash_profit=agent_cash_profit,
            balance_before=agent.other_comm_balance,
            balance_after=agent.other_comm_balance + agent_cash_profit
        )

        agent.other_comm_balance_daily += agent_cash_profit
        agent.other_comm_balance += agent_cash_profit
        agent.save()

    @classmethod
    def create_and_top_up_sales_rep_commissions(
            cls, agent, sales_rep, amount, transaction_id, transaction_type,
            transaction_reason, total_profit=None, liberty_profit=None,
            ro_cash_profit=None, agent_cash_profit=None
    ):

        commissions_record = cls.objects.create(
            sales_rep=sales_rep,
            agent=agent,
            amount=amount,
            liberty_reference=f"LP-OTH_COM-{str(uuid.uuid4())}",
            transaction_id=transaction_id,
            transaction_type=transaction_type,
            transaction_reason=transaction_reason,
            transaction_owner="SALES_REP",
            total_profit=total_profit,
            liberty_profit=liberty_profit,
            ro_cash_profit=ro_cash_profit,
            agent_cash_profit=agent_cash_profit,
            balance_before=sales_rep.other_comm_balance,
            balance_after=sales_rep.other_comm_balance + ro_cash_profit
        )

        sales_rep.other_comm_balance_daily += ro_cash_profit
        sales_rep.other_comm_balance += ro_cash_profit
        sales_rep.save()

    @classmethod
    def reverse_other_commissions_for_agent(
            cls, agent, sales_rep, amount, transaction_id, transaction_type,
            transaction_reason, total_profit=None, liberty_profit=None,
            ro_cash_profit=None, agent_cash_profit=None, transaction_owner=None
    ):

        commissions_record = cls.objects.create(
            entry="DEBIT",
            agent=agent,
            sales_rep=sales_rep,
            amount=amount,
            liberty_reference=f"LP-DEBIT-OTH_COM-{str(uuid.uuid4())}",
            transaction_id=transaction_id,
            transaction_type=transaction_type,
            transaction_reason=transaction_reason,
            transaction_owner="AGENT",
            total_profit=total_profit,
            liberty_profit=liberty_profit,
            ro_cash_profit=ro_cash_profit,
            agent_cash_profit=agent_cash_profit,
            balance_before=agent.other_comm_balance,
            balance_after=agent.other_comm_balance - agent_cash_profit
        )

        agent.other_comm_balance_daily -= agent_cash_profit
        agent.other_comm_balance -= agent_cash_profit
        agent.save()

    @classmethod
    def reverse_sales_rep_commissions(
            cls, agent, sales_rep, amount, transaction_id, transaction_type,
            transaction_reason, total_profit=None, liberty_profit=None,
            ro_cash_profit=None, agent_cash_profit=None
    ):
        commissions_record = cls.objects.create(
            entry="DEBIT",
            sales_rep=sales_rep,
            agent=agent,
            amount=amount,
            liberty_reference=f"LP-DEBIT-OTH_COM-{str(uuid.uuid4())}",
            transaction_id=transaction_id,
            transaction_type=transaction_type,
            transaction_reason=transaction_reason,
            transaction_owner="SALES_REP",
            total_profit=total_profit,
            liberty_profit=liberty_profit,
            ro_cash_profit=ro_cash_profit,
            agent_cash_profit=agent_cash_profit,
            balance_before=sales_rep.other_comm_balance,
            balance_after=sales_rep.other_comm_balance - ro_cash_profit
        )

        sales_rep.other_comm_balance_daily -= ro_cash_profit
        sales_rep.other_comm_balance -= ro_cash_profit
        sales_rep.save()

    def save(self, *args, **kwargs):
        self.balance_before = float("{:.3f}".format(self.balance_before))
        self.balance_after = float("{:.3f}".format(self.balance_after))
        self.is_paybox_merchant = self.agent.is_paybox_merchant

        super(OtherCommissionsRecord, self).save(*args, **kwargs)


class RequisitionGroup(models.Model):
    name = models.CharField(max_length=255, default="My Team")
    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='paymentownergroups_owned')
    super_admins = models.ManyToManyField(User, related_name='paymentsuperadmingroups_joined', blank=True)
    admins = models.ManyToManyField(User, related_name='paymentadmingroups_joined', blank=True)
    members = models.ManyToManyField(User, related_name='paymentmembergroups_joined', blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def get_approver_or_reviewer(cls, user_email, user_type):
        if user_email is None:
            return None

        approver_or_reviewer = User.objects.filter(email=user_email).last()
        if approver_or_reviewer:
            if user_type == "UPLOADER":
                check_req_group = cls.objects.filter(
                    Q(owner=approver_or_reviewer) | Q(super_admins__email=approver_or_reviewer) | Q(admins__email=approver_or_reviewer) | Q(
                        members__email=approver_or_reviewer)).last()
                if check_req_group:
                    return check_req_group

            # elif user_type == "REVIEWER":
            #     check_req_group = cls.objects.filter(Q(owner=approver_or_reviewer) | Q(super_admins__email=approver_or_reviewer) | Q(admins__email=approver_or_reviewer)).last()
            #     if check_req_group:
            #         return approver_or_reviewer

            # elif user_type == "APPROVER":
            #     check_req_group = cls.objects.filter(Q(owner=approver_or_reviewer) | Q(super_admins__email=approver_or_reviewer)).last()
            #     if check_req_group:
            #         return approver_or_reviewer

        return None

    @classmethod
    def get_approver_or_reviewer_with_group(cls, user_email, user_type, req_group):
        if user_email is None:
            return None

        approver_or_reviewer = User.objects.filter(email=user_email).last()
        if approver_or_reviewer:
            # if user_type == "UPLOADER":
            #     check_req_group = req_group.filter(Q(owner=approver_or_reviewer) | Q(super_admins__email=approver_or_reviewer) | Q(admins__email=approver_or_reviewer) | Q(members__email=approver_or_reviewer)).last()
            #     if check_req_group:
            #         return approver_or_reviewer

            if user_type == "REVIEWER":
                check_req_group = req_group.super_admins.all()
                if approver_or_reviewer in check_req_group:
                    return approver_or_reviewer

            elif user_type == "APPROVER":
                check_req_group = req_group.admins.all()
                if approver_or_reviewer in check_req_group:
                    return approver_or_reviewer

        return None

    @classmethod
    def get_group_members(cls, user):

        get_user_group = cls.objects.filter(
            Q(owner=user) | Q(super_admins__email=user.email) | Q(admins__email=user.email) | Q(members__email=user.email)).last()
        if get_user_group:
            members_list = list(get_user_group.members.values_list('email', flat=True)) + [get_user_group.owner.email]
            reviewers_list = list(get_user_group.admins.values_list('email', flat=True)) + [get_user_group.owner.email]
            approvers_list = list(get_user_group.super_admins.values_list('email', flat=True)) + list(
                get_user_group.admins.values_list('email', flat=True)) + [get_user_group.owner.email]

            unique_members_list = list(set(members_list))
            unique_reviewers_list = list(set(reviewers_list))
            unique_approvers_list = list(set(approvers_list))

            new_response = {
                "approvers_list": unique_approvers_list,
                "reviewers_list": unique_reviewers_list,
                "members_list": unique_members_list,
            }

            return new_response

        return None


class UploadSendMoneyData(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    reviewer = models.ForeignKey(User, related_name="payment_reviewer_user", on_delete=models.SET_NULL, null=True, blank=True)
    approver = models.ForeignKey(User, related_name="payment_approver_user", on_delete=models.SET_NULL, null=True, blank=True)
    title = models.CharField(max_length=200)
    transfer_type = models.CharField(max_length=200)
    request_id = models.CharField(max_length=200)
    unique_reference = models.CharField(max_length=200, default=uuid.uuid4, unique=True)
    firstname = models.CharField(max_length=100, null=True, blank=True)
    lastname = models.CharField(max_length=100, null=True, blank=True)
    buddy_phone_number = models.CharField(max_length=100, null=True, blank=True)
    amount = models.FloatField(default=0)
    account_number = models.CharField(max_length=100, null=True, blank=True)
    account_name = models.CharField(max_length=100, null=True, blank=True)
    bank_code = models.CharField(max_length=100, null=True, blank=True)
    bank_name = models.CharField(max_length=150, null=True, blank=True)
    narration = models.CharField(max_length=150, null=True, blank=True)
    escrow_id = models.CharField(max_length=150, null=True, blank=True)
    payload = models.JSONField()

    resolved_data = models.JSONField(null=True, blank=True)
    send_money_data = models.JSONField(null=True, blank=True)
    good_data = models.JSONField(null=True, blank=True)
    bad_data = models.JSONField(null=True, blank=True)
    is_good = models.BooleanField(default=False)
    is_processed = models.BooleanField(default=False)
    is_sent = models.BooleanField(default=False)
    status = models.CharField(max_length=100, null=True, blank=True)
    is_reversed = models.BooleanField(default=False)
    approved = models.BooleanField(default=False)
    rejected = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def test_process_data_from_excel_to_instances(cls, user, file, title, transfer_type):
        file_data = file.read()
        df = pd.read_excel(io.BytesIO(file_data), dtype=str, engine='openpyxl')

        print("df", df)
        print(df.columns)

        for column in df.columns:
            if "payload" == column:
                print(column)

        payloads = []
        for i, data in df.iterrows():
            payloads.append(data.get("payload"))

        from pprint import pprint
        new_payloads = []
        for i in payloads:
            # print(type(i))
            new_data = eval(i)
            # new_payloads.append(new_data)
            # print(new_data)
            if new_data.get("account_number") == "":
                print(new_data)

    @classmethod
    def add_leading_zeros_to_bank_codes(cls, bank_code):
        if bank_code is None:
            return None

        num_str = str(bank_code)
        if len(num_str) < 6:
            num_str = '0' * (6 - len(num_str)) + num_str
        return num_str

    @classmethod
    def add_leading_zeros_to_account_numbers(cls, account_number):
        if account_number is None:
            return None

        num_str = str(account_number)
        if len(num_str) < 10:
            num_str = '0' * (10 - len(num_str)) + num_str
        return num_str

    @classmethod
    def process_fake_excel(cls, user, file, title, transfer_type):
        file_data = file.read()
        df = pd.read_excel(io.BytesIO(file_data), dtype=str, engine='openpyxl')

        # if transfer_type == "BUDDY":
        #     column_list = [
        #         "firstname", "lastname", "amount", "narration", "buddy_phone_number",
        #     ]

        # else:
        #     column_list = [
        #         "firstname", "lastname", "amount", "narration",
        #         "account_number", "bank_code", "bank_name"
        #     ]

        # missing_columns = []

        # for column in column_list:
        #     if not column in df.columns:
        #         missing_columns.append(column)

        # if len(missing_columns) > 0:
        #     return {
        #         "error": True,
        #         "missing_columns": missing_columns,
        #         "request_id": None
        #     }

        # # else:

        send_money_list = []

        for i, data in df.iterrows():
            phone = data.get("phone")

            send_money_list.append(phone)

        return send_money_list

    @classmethod
    def process_data_from_excel(cls, user, file, title, transfer_type):
        file_data = file.read()
        df = pd.read_excel(io.BytesIO(file_data), dtype=str, engine='openpyxl')

        if transfer_type == "BUDDY":
            column_list = [
                "firstname", "lastname", "amount", "narration", "buddy_phone_number",
            ]

        else:
            column_list = [
                "firstname", "lastname", "amount", "narration",
                "account_number", "bank_code", "bank_name"
            ]

        missing_columns = []

        for column in column_list:
            if not column in df.columns:
                missing_columns.append(column)

        if len(missing_columns) > 0:
            return {
                "error": True,
                "missing_columns": missing_columns,
                "request_id": None
            }

        # else:

        send_money_list = []

        if transfer_type == "BUDDY":

            for i, data in df.iterrows():
                data = {
                    "id": i,
                    "firstname": data.get("firstname"),
                    "lastname": data.get("lastname"),
                    "buddy_phone_number": User.format_number_from_back_add_234(data.get("buddy_phone_number")),
                    "amount": data.get("amount"),
                    "narration": data.get("narration")
                }

                send_money_list.append(data)

        else:

            for i, data in df.iterrows():
                data = {
                    "id": i,
                    "firstname": data.get("firstname"),
                    "lastname": data.get("lastname"),
                    "account_number": data.get("account_number"),
                    "amount": data.get("amount"),
                    "bank_code": data.get("bank_code"),
                    "bank_name": data.get("bank_name"),
                    "narration": data.get("narration")
                }

                send_money_list.append(data)

        instance = cls.objects.create(
            user=user,
            title=title,
            payload=send_money_list
        )

        return {
            "error": False,
            "missing_columns": None,
            "request_id": instance.request_id

        }

    @classmethod
    def process_data_from_excel_to_instances(cls, user, file, title, transfer_type):
        file_data = file.read()
        df = pd.read_excel(io.BytesIO(file_data), dtype=str, engine='openpyxl')

        if transfer_type == "BUDDY":
            column_list = [
                "firstname", "lastname", "amount", "narration", "buddy_phone_number",
            ]

        else:
            column_list = [
                "firstname", "lastname", "amount", "narration",
                "account_number", "bank_code", "bank_name"
            ]

        missing_columns = []

        for column in column_list:
            if not column in df.columns:
                missing_columns.append(column)

        if len(missing_columns) > 0:
            return {
                "error": True,
                "missing_columns": missing_columns,
                "request_id": None
            }

        send_money_list = []
        request_id = f"LGLP_BULK_{uuid.uuid4()}"

        if transfer_type == "BUDDY":

            for i, data in df.iterrows():
                json_data = {
                    "id": i,
                    "request_id": request_id,
                    "firstname": data.get("firstname"),
                    "lastname": data.get("lastname"),
                    "buddy_phone_number": User.format_number_from_back_add_234(data.get("buddy_phone_number")),
                    "amount": data.get("amount"),
                    "narration": data.get("narration")
                }

                data = cls(
                    user=user,
                    title=title,
                    transfer_type=transfer_type,
                    request_id=request_id,
                    firstname=data.get("firstname"),
                    lastname=data.get("lastname"),
                    buddy_phone_number=User.format_number_from_back_add_234(data.get("buddy_phone_number")),
                    amount=data.get("amount"),
                    narration=data.get("narration"),
                    payload=json_data
                )

                send_money_list.append(data)


        else:

            for i, data in df.iterrows():
                json_data = {
                    "id": i,
                    "request_id": request_id,
                    "firstname": data.get("firstname"),
                    "lastname": data.get("lastname"),
                    "account_number": data.get("account_number"),
                    "amount": data.get("amount"),
                    "bank_code": data.get("bank_code"),
                    "bank_name": data.get("bank_name"),
                    "narration": data.get("narration")
                }

                data = cls(
                    user=user,
                    title=title,
                    transfer_type=transfer_type,
                    request_id=request_id,
                    firstname=data.get("firstname"),
                    lastname=data.get("lastname"),
                    account_number=cls.add_leading_zeros_to_account_numbers(data.get("account_number")),
                    amount=data.get("amount"),
                    bank_code=cls.add_leading_zeros_to_bank_codes(data.get("bank_code")),
                    bank_name=data.get("bank_name"),
                    narration=data.get("narration"),
                    payload=json_data
                )

                send_money_list.append(data)

        cls.objects.bulk_create(send_money_list)
        # instance = cls.objects.create(
        #     user = user,
        #     title = title,
        #     payload = send_money_list
        # )

        return {
            "error": False,
            "missing_columns": None,
            "request_id": request_id
        }


class OutOfBookTransfer(models.Model):
    # TYPE_OF_TRANSFER = [
    #     ("COMMISSION_REVERSAL", "COMMISSION_REVERSAL"),
    #     ("NO_RO_EXT_COMM", "NO_RO_EXT_COMM"),
    #     ("CARDPURREF", "CARDPURREF"),
    #     ("OTHERS", "OTHERS"),
    #     ("BAD_FUNDING_MAIN", "BAD_FUNDING_MAIN"),
    #     ("BAD_FUNDING_COMM", "BAD_FUNDING_COMM"),
    #     ("BAD_FUNDING_COMM", "BAD_FUNDING_COMM"),
    # ]

    type_of_transfer = models.CharField(max_length=150, default="OTHERS")
    provider = models.CharField(max_length=100, blank=True, null=True)
    amount = models.FloatField(default=0)
    from_account = models.CharField(max_length=100, blank=True, null=True)
    to_account = models.CharField(max_length=100, blank=True, null=True)
    created_liberty_reference = models.CharField(max_length=100, blank=True, null=True)
    trans_id = models.CharField(max_length=100, blank=True, null=True)
    escrow_id = models.CharField(max_length=100, blank=True, null=True)
    send_payload = models.TextField(blank=True, null=True)
    initiate_transaction_payload = models.TextField(blank=True, null=True)
    verification_payload = models.TextField(blank=True, null=True)
    is_verified = models.BooleanField(default=False)
    is_done = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def complete_reversal_of_commission(cls, query):
        escrow_id = query.escrow_id
        escrow_qs = Transaction.objects.filter(escrow_id=escrow_id)
        if escrow_qs:
            get_commissions = escrow_qs.filter(transaction_type="SEND_LIBERTY_COMMISSION")
            if get_commissions:
                get_external_trans_id = escrow_qs.filter(transaction_type="SEND_BANK_TRANSFER", transaction_leg="EXTERNAL").last()
                if get_external_trans_id:
                    main_comms_record = OtherCommissionsRecord.objects \
                        .filter(transaction_id=get_external_trans_id.transaction_id)

                    get_other_comm_rec = main_comms_record.filter(entry="CREDIT")
                    if get_other_comm_rec:

                        if not main_comms_record.filter(entry="DEBIT", transaction_owner="AGENT").exists():
                            get_agent_com = get_other_comm_rec.filter(transaction_owner="AGENT").first()

                            if get_agent_com:
                                handle_agent_commission_reversal = OtherCommissionsRecord.reverse_other_commissions_for_agent(
                                    agent=get_agent_com.agent,
                                    sales_rep=get_agent_com.sales_rep if get_agent_com.sales_rep else None,
                                    amount=get_agent_com.amount,
                                    transaction_id=get_agent_com.transaction_id,
                                    transaction_type=get_agent_com.transaction_type,
                                    transaction_reason=f"Reversal of COMM on Reversed Transfer",
                                    total_profit=get_agent_com.total_profit,
                                    liberty_profit=get_agent_com.liberty_profit,
                                    ro_cash_profit=get_agent_com.ro_cash_profit,
                                    agent_cash_profit=get_agent_com.agent_cash_profit,
                                )

                            else:
                                print("NO AGENT COMMISSION CREDIT FOUND")

                        elif not main_comms_record.filter(entry="DEBIT", transaction_owner="SALES_REP").exists():
                            get_sales_rep_comm = get_other_comm_rec.filter(transaction_owner="SALES_REP").first()

                            if get_sales_rep_comm:
                                handle_ro_commission_reversal = OtherCommissionsRecord.reverse_sales_rep_commissions(
                                    agent=get_sales_rep_comm.agent,
                                    sales_rep=get_sales_rep_comm.sales_rep,
                                    amount=get_sales_rep_comm.amount,
                                    transaction_id=get_sales_rep_comm.transaction_id,
                                    transaction_type=get_sales_rep_comm.transaction_type,
                                    transaction_reason=f"Reversal of COMM on Reverse Transfer by {get_sales_rep_comm.agent.bvn_first_name}" if get_sales_rep_comm.agent.bvn_first_name else f"Commission on LOTTO PLAY by {get_sales_rep_comm.agent.email}",
                                    total_profit=get_sales_rep_comm.total_profit,
                                    liberty_profit=get_sales_rep_comm.liberty_profit,
                                    ro_cash_profit=get_sales_rep_comm.ro_cash_profit,
                                    agent_cash_profit=get_sales_rep_comm.agent_cash_profit
                                )
                            else:
                                print("NO SALES REP COMMISSION CREDIT FOUND")

                        else:
                            print("COMMISSION DEBIT EXISTS FOR AGENT AND SALES_REP")


                    else:
                        print("NO CREDIT ON COMMISSON FOUND GENERALLY")

                    get_escrow_instance = Escrow.objects.get(escrow_id=escrow_id)
                    get_escrow_instance.external_escrow = True
                    get_escrow_instance.commissions_escrow = True
                    get_escrow_instance.save()

                    get_external_trans_id.status = "PENDING"
                    get_external_trans_id.save()

                    get_transfer_verf = TransferVerificationObject.objects.get(transaction_instance=get_external_trans_id)
                    get_transfer_verf.transaction_ver_status = "NOT_INITIATED"
                    get_transfer_verf.is_verified = False
                    get_transfer_verf.second_leg_done = False
                    get_transfer_verf.is_finished_verification = False
                    get_transfer_verf.bank_performance_checked = False
                    get_transfer_verf.save()


                else:
                    print("INVALID EXTERNAL ID")

            else:
                print("NO SEND LIBERTY COMMISSION FOUND")

        else:
            print("INVALID ESCROW ID")

        query.is_done = True
        query.is_verified = True
        query.save()

        return True


class TransactionReward(models.Model):
    user = models.ForeignKey(User,
                             related_name="transaction_rewards",
                             on_delete=models.CASCADE,
                             null=True,
                             blank=True, )
    coin_amount = models.IntegerField(null=True, blank=True)
    amount = models.FloatField(null=True, blank=True)
    transaction_type = models.CharField(
        max_length=100, null=True, blank=True
    )
    transaction_date = models.DateTimeField()
    last_updated = models.DateTimeField(auto_now=True)
    transaction_percentage = models.FloatField(null=True, blank=True)
    transaction_percentage_amount = models.FloatField(null=True, blank=True)
    device_type = models.CharField(max_length=20, null=True, blank=True)
    transaction_id = models.CharField(max_length=2300, null=True, blank=True, unique=True)

    def save(self, *args, **kwargs):
        if self.amount is not None and self.amount > 0:
            self.transaction_percentage = float(settings.TRANSACTION_REWARD_PERCENTAGE)
            self.transaction_percentage_amount = self.transaction_percentage * self.amount
            self.transaction_percentage_amount = round(self.transaction_percentage_amount, 2)
        else:
            self.transaction_percentage_amount = 0.00
        super(TransactionReward, self).save(*args, **kwargs)


class WeekHistory(models.Model):
    week_start_date = models.DateField(null=True, blank=True)
    week_end_date = models.DateField(null=True, blank=True)
    week_date_added = models.DateTimeField(auto_now_add=True)


class DailyRewardHistory(models.Model):
    user = models.ForeignKey(User,
                             related_name="weekly_reward_history",
                             on_delete=models.CASCADE,
                             null=True,
                             blank=True, )
    send_money_count = models.IntegerField(default=0, blank=True, null=True)
    withdrawal_count = models.IntegerField(default=0, blank=True, null=True)
    airtime_data_count = models.IntegerField(default=0, blank=True, null=True)
    transaction_date = models.DateField()
    cash_back_amount = models.FloatField(null=True, blank=True)
    transaction_count = models.IntegerField(default=0, blank=True, null=True)
    eligible_transaction_count = models.IntegerField(default=0, blank=True, null=True)
    coin = models.IntegerField(default=0, blank=True, null=True)
    eligible_coin = models.IntegerField(default=0, blank=True, null=True)
    eligible_cash_back_amount = models.FloatField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)


class MonthlyRewardHistory(models.Model):
    user = models.ForeignKey(User,
                             related_name="monthly_reward_history",
                             on_delete=models.SET_NULL,
                             null=True,
                             blank=True, )
    send_money_count = models.IntegerField(default=0, blank=True, null=True)
    withdrawal_count = models.IntegerField(default=0, blank=True, null=True)
    airtime_data_count = models.IntegerField(default=0, blank=True, null=True)
    date_created = models.DateField(auto_now_add=True)
    cash_back_amount = models.FloatField(null=True, blank=True)
    total_coin = models.IntegerField(default=0, blank=True, null=True)
    eligible_coin = models.IntegerField(default=0, blank=True, null=True)


class LeaderBoardPOS(models.Model):
    month = models.CharField(max_length=20, null=True, blank=True)
    year = models.CharField(max_length=20, null=True, blank=True)
    user_rank = models.TextField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)


class LeaderBoardMobile(models.Model):
    month = models.CharField(max_length=20, null=True, blank=True)
    year = models.CharField(max_length=20, null=True, blank=True)
    user_rank = models.TextField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)


class LeaderBoard(models.Model):
    all_rank = models.JSONField(null=True, blank=True)
    leader_board_date = models.DateField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    device_type = models.CharField(max_length=20, null=True, blank=True)


# *******************************    MODEL   ******************************* #

def create_unique_request_id_for_agent():
    """
    Create Unique Request ID
    """
    gen_uuid = str(uuid.uuid4()).replace('-', '').upper()

    generated_id = ''.join(random.choice(gen_uuid) for i in range(6))

    today_date = datetime.now().strftime("%y%m%d")

    final_gen_uuid = f"{today_date}-{generated_id}"

    return final_gen_uuid


class POSRequest(models.Model):
    DISPATCH_TO_ADDRESS = "dispatch_to_address"
    VISIT_OFFICE_FOR_PICKUP = "visit_office"

    AGENCY_BANKING = "AGENCY_BANKING"
    MERCHANT_COLLECTION = "MERCHANT_COLLECTION"

    POS_PICKUP_CHOICES = [
        (DISPATCH_TO_ADDRESS, "dispatch_to_address"),
        (VISIT_OFFICE_FOR_PICKUP, "visit_office")
    ]
    TYPE_OF_POS_CHOICES = [
        (AGENCY_BANKING, "AGENCY_BANKING"),
        (MERCHANT_COLLECTION, "MERCHANT_COLLECTION")
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    request_id = models.CharField(
        max_length=32, default=create_unique_request_id_for_agent, unique=True
    )
    relationship_officer_code = models.CharField(max_length=25, null=True, blank=True)
    pickup_option = models.CharField(max_length=225, choices=POS_PICKUP_CHOICES, default=VISIT_OFFICE_FOR_PICKUP)
    type_of_pos = models.CharField(max_length=25, choices=TYPE_OF_POS_CHOICES, default=AGENCY_BANKING)
    requested_pos = models.JSONField()
    total_payable = models.FloatField(default=0)
    verf_fee = models.FloatField(default=0)
    payment_received = models.BooleanField(default=False)
    amount_received = models.FloatField(default=0)
    next_of_kin_submitted = models.BooleanField(default=False)
    guarantor_approved = models.BooleanField(default=False)
    address_verified = models.BooleanField(default=False)
    is_delivered = models.BooleanField(default=False)
    delivery_address = models.TextField(blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.user)

    class Meta:
        ordering = ["-date_created"]

    @property
    def get_total_quantity(self):
        return sum([v["quantity"] for k, v in self.requested_pos.items()])

    @property
    def get_payment_summary(self):
        verification_fee = self.verf_fee
        total_payable = self.total_payable

        data = {
            "subtotal": total_payable,
            "verification_fee": verification_fee,
            "total": total_payable + verification_fee
        }
        return data

    @property
    def get_request_details(self):
        data = {
            "payment_status": self.payment_received,
            "next_of_kin": self.next_of_kin_submitted,
            "guarantor": self.guarantor_approved,
            "address_verification": self.address_verified,
            "quantity": self.get_total_quantity,
            "amount": self.total_payable,
            "verification_fee": self.verf_fee,
            "total": self.total_payable + self.verf_fee
        }
        return data

    @property
    def get_request_history_status(self):
        status_benchmark = 100
        step_one = 20 if self.payment_received else 0
        step_two = 20 if self.next_of_kin_submitted else 0
        step_three = 20 if self.guarantor_approved else 0
        step_four = 20 if self.address_verified else 0
        step_five = 20 if self.is_delivered else 0

        status = sum([step_one, step_two, step_three, step_four, step_five])
        if status == status_benchmark:
            return {
                "status": "delivered",
                "percentage": status,
                "total_quantity": self.get_total_quantity,
                "date_requested": self.date_created
            }
        return {
            "status": "pending",
            "percentage": status,
            "total_quantity": self.get_total_quantity,
            "date_requested": self.date_created

        }


# *******************************    MODEL   ******************************* #


class ParallexToken(models.Model):
    token_field = models.CharField(max_length=3000)
    is_active = models.BooleanField(default=True)
    token_date = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return str(self.token_field)


class OtherAppTransNotify(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    trans_id = models.CharField(max_length=500)
    trans_type = models.CharField(max_length=250)
    amount = models.FloatField(default=0.00)
    charge = models.FloatField(default=0.00)
    balance = models.FloatField(default=0.00)
    entry = models.CharField(max_length=200)
    to_account = models.CharField(max_length=100)
    desc = models.CharField(max_length=250)
    message = models.CharField(max_length=800)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def arrange_send_data_for_other_trnas_app_notify(
            cls, user, amount, charge, balance,
            entry, to_account, desc, trans_id, trans_type
    ):

        formatted_amount = convert_num_to_currency(amount)

        message_body = f"Acct: {to_account}\nAmt: N{formatted_amount} {entry}\nChgs: N{convert_num_to_currency(charge)} (COMM VAT)\nDesc: {desc}\nBalance: N{convert_num_to_currency(balance)}"

        if cls.objects.filter(trans_id=trans_id).exists():
            pass
        else:
            trans_obj = cls.objects.create(
                user=user,
                amount=amount,
                charge=charge,
                balance=balance,
                entry=entry,
                to_account=to_account,
                desc=desc,
                trans_id=trans_id,
                trans_type=trans_type,
                message=message_body
            )

            notification_users = ["<EMAIL>", "<EMAIL>"]

            for email in notification_users:
                notify_user = User.objects.filter(email=email, notify_app_token__isnull=False).last()
                if not notify_user:
                    pass
                else:
                    fb_token = notify_user.notify_app_token
                    fb_title = "Transaction Successful"
                    fb_body = desc
                    fb_data = {"id": trans_obj.id, "amount_sent": f"{amount}", "available_balance": f"{balance}"}

                    send_out_notification = notify_messaging.send_broadcast(
                        token=fb_token,
                        title=fb_title,
                        body=fb_body,
                        data=fb_data
                    )

        return message_body


class OtherServiceAccountSystem(models.Model):
    ACCOUNT_TYPES = [
        ("COLLECTION", "COLLECTION"),
        ("AJO_USER_PERSONAL", "AJO_USER_PERSONAL"),
        ("AJO_USER_CORPORATE", "AJO_USER_CORPORATE"),
    ]

    TRUE_ACCOUNT_TYPES = [
        ("COLLECTION", "COLLECTION"),
        ("OTHERS", "OTHERS"),
    ]

    requested_by = models.ForeignKey(User, on_delete=models.CASCADE)
    user = models.ForeignKey(User, related_name="other_service_accounts", on_delete=models.CASCADE)
    account_id = models.UUIDField(default=uuid.uuid4, editable=False)
    account_provider = models.CharField(max_length=300, choices=ACCOUNT_PROVIDERS)
    account_type = models.CharField(max_length=300, choices=ACCOUNT_TYPES)
    true_account_type = models.CharField(max_length=300, choices=TRUE_ACCOUNT_TYPES)
    vfd_account_type = models.CharField(max_length=300)
    bank_name = models.CharField(max_length=300)
    bank_code = models.CharField(max_length=300)
    account_number = models.CharField(max_length=250, null=True, blank=True)
    account_name = models.CharField(max_length=250, null=True, blank=True)
    rc_number = models.CharField(max_length=100, null=True, blank=True)
    available_balance = models.FloatField(default=0.00)
    unique_reference = models.CharField(max_length=100, null=True, blank=True)
    ajo_collector = models.ForeignKey(User, related_name="ajo_collector", on_delete=models.SET_NULL, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    is_test = models.BooleanField(default=False)
    initial_payload = models.TextField(null=True)
    payload = models.TextField(null=True)
    extra = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):

        wallet = WalletSystem.get_wallet(user=self.user, from_wallet_type="OTHERS")
        if not wallet:
            wallet = WalletSystem.objects.create(user=self.user, wallet_type="OTHERS")
        else:
            pass

        # if self.is_active == True:
        #     account_sys = AccountSystem.objects.filter(user=self.user, wallet=wallet, account_number=self.account_number).last()
        #     if not account_sys:
        #         account_sys = AccountSystem.objects.create(
        #             user=self.user,
        #             wallet=wallet,
        #             account_provider=self.account_provider,
        #             account_type="OTHERS",
        #             bank_name=self.bank_name,
        #             bank_code=self.bank_code,
        #             account_number=self.account_number,
        #             account_name=self.account_name,
        #             is_test=self.is_test,
        #             initial_payload=self.initial_payload,
        #             payload=self.payload
        #         )
        #     else:
        #         pass
        # else:
        #     pass

        super(OtherServiceAccountSystem, self).save(*args, **kwargs)

    @classmethod
    def create_vfd_others_collection_acct(cls, user, requested_by, overide=False, ajo_user_details=None, extra=False):
        unique_reference = ajo_user_details.get("unique_reference") if ajo_user_details else None
        filter_query = Q(unique_reference=unique_reference, requested_by=requested_by, user=user) if unique_reference else Q(
            requested_by=requested_by, user=user)

        get_previous_account = cls.objects.filter(is_active=True).filter(filter_query).last()
        if get_previous_account and not extra:
            func_response = {
                "status": "success",
                "message": "account successfully created",
                "account_provider": get_previous_account.account_provider,
                "user_id": user.id,
                "data": {
                    "account_number": get_previous_account.account_number,
                    "account_name": get_previous_account.account_name,
                    "account_type": get_previous_account.account_type,
                    "bank_name": get_previous_account.bank_name,
                    "bank_code": get_previous_account.bank_code,
                    "is_test": get_previous_account.is_test,
                    "is_active": get_previous_account.is_active,
                    "timestamp": get_previous_account.date_created,
                    "unique_reference": get_previous_account.unique_reference,
                    "ajo_collector": get_previous_account.ajo_collector.id if get_previous_account.ajo_collector else None,
                },
            }

        else:
            vfd_create = None

            if settings.ENVIRONMENT == "development":
                is_test_account = True
            else:
                is_test_account = False

            retry_count = 3
            user_account_name = f"{user.full_name} {user.id}"

            user_phone_number = ajo_user_details.get("bvn_details", {}).get("phone") if ajo_user_details else User.format_number_from_back_add_234(
                user.check_kyc.bvn_rel.bvn_phone_number) if user.check_kyc.bvn_rel.bvn_phone_number else User.format_number_from_back_add_234(
                user.phone_number)

            try:
                if ajo_user_details:
                    bvn_details = ajo_user_details.get("bvn_details")

                    # get_bvn_det = OtherBVNDetail.create_other_bvn(
                    #     verified_for=ajo_user_details.get("ajo_collector"),
                    #     verification_type="AJO",
                    #     bvn_number=bvn_details['bvn']
                    # )

                    num_to_add = (user.vfd_bvn_acct_num_count - 1) + 1

                    vfd_create = VFDBank.create_vfd_wallet_ajo(bvn_details=bvn_details, num_to_add=num_to_add)

                else:

                    vfd_create = VFDBank.create_vfd_wallet(user=user, user_phone_number=user_phone_number, retry_count=retry_count, overide=overide)
            except Exception as err:
                print(f"Error occurred while creating VFD wallet: {err}")
                pass

            account_type = "OTHERS"

            if vfd_create is not None:
                if vfd_create.get("main_data").get("status") == "00":
                    response = vfd_create.get("main_data").get("data")

                    created_acc = cls.objects.create(
                        requested_by=requested_by,
                        user=user,
                        account_provider="VFD",
                        account_type="AJO_USER_PERSONAL" if ajo_user_details else "COLLECTION",
                        vfd_account_type="PERSONAL",
                        bank_name="VFD Microfinance Bank",
                        bank_code="999999",
                        account_number=response["accountNo"],
                        account_name=f'{response.get("firstname")} {response.get("lastname")}',
                        is_active=True,
                        is_test=is_test_account,
                        initial_payload=vfd_create.get("initial_payload"),
                        payload=vfd_create,
                        unique_reference=ajo_user_details.get("unique_reference") if ajo_user_details else None,
                        ajo_collector=ajo_user_details.get("ajo_collector") if ajo_user_details else None,
                        extra=extra,
                    )

                    ############################################################################
                    # UPDATE NUMBER OF USER ACCOUNTS
                    if overide == False:
                        user.vfd_bvn_acct_num_count += 1
                        user.save(update_fields=["vfd_bvn_acct_num_count"])

                        for user_data in User.objects.exclude(id=user.id).filter(bvn_number=user.check_kyc.bvn_rel.bvn_number):
                            user_data.vfd_bvn_acct_num_count = user.vfd_bvn_acct_num_count
                            user_data.save(update_fields=["vfd_bvn_acct_num_count"])
                    ############################################################################

                    func_response = {
                        "status": "success",
                        "message": "account successfully created",
                        "account_provider": created_acc.account_provider,
                        "user_id": user.id,
                        "data": {
                            "account_number": created_acc.account_number,
                            "account_name": created_acc.account_name,
                            "account_type": created_acc.account_type,
                            "bank_name": created_acc.bank_name,
                            "bank_code": created_acc.bank_code,
                            "is_test": created_acc.is_test,
                            "is_active": created_acc.is_active,
                            "timestamp": created_acc.date_created,
                            "unique_reference": created_acc.unique_reference,
                            "ajo_collector": created_acc.ajo_collector.id if created_acc.ajo_collector else None,
                        },
                    }

                    main_collection_wallet = WalletSystem.get_wallet(user=user, from_wallet_type="COLLECTION")

                    if not AccountSystem.objects.filter(account_number=created_acc.account_number).exists():
                        AccountSystem.objects.create(
                            user=user,
                            wallet=main_collection_wallet,
                            account_provider="VFD",
                            account_type=account_type,
                            true_account_type="AJO_USER_PERSONAL" if ajo_user_details else "PERSONAL",
                            bank_name=created_acc.bank_name,
                            bank_code=created_acc.bank_code,
                            account_number=created_acc.account_number,
                            account_name=created_acc.account_name,
                            is_active=True,
                            available_balance=0.00,
                            is_test=False,
                            extra=extra,
                            initial_payload=created_acc.initial_payload,
                            payload=created_acc.payload,
                        )

                else:
                    AccountCreationFailure.objects.create(
                        user=user,
                        account_provider="VFD",
                        account_type=account_type,
                        is_test=is_test_account,
                        initial_payload=vfd_create.get("initial_payload"),
                        payload=vfd_create,
                    )

                    func_response = {
                        "status": "error",
                        "error_code": "91",
                        "message": "account failed to create",
                        "account_provider": "VFD",
                        "user_id": user.id,
                        "data": {}
                    }
            else:
                AccountCreationFailure.objects.create(
                    user=user,
                    account_provider="VFD",
                    account_type=account_type,
                    is_test=is_test_account,
                    initial_payload=vfd_create.get("initial_payload"),
                    payload=vfd_create,
                )

                func_response = {
                    "status": "error",
                    "error_code": "92",
                    "message": "account failed to create",
                    "account_provider": "VFD",
                    "user_id": user.id,
                    "data": {}
                }

        return func_response

    @classmethod
    def create_account(cls, user_id, requested_by, account_provider):
        user = User.objects.filter(id=user_id).first()

        if account_provider == "VFD":
            response = cls.create_vfd_others_collection_acct(user=user, requested_by=requested_by)
        else:
            response = {
                "status": "error",
                "error_code": "93",
                "message": "account provider passed does not exist",
                "account_provider": account_provider,
                "user_id": user.id,
                "data": {}
            }

        return response

    @classmethod
    def create_corporate_account(cls, user: User, is_test_account, requested_by, rc_number, incorp_date, company_name, bvn, internal=False,
                                 get_corporate: CorporateAccount = None, ajo_user_details: dict = None, others=False):
        unique_reference = ajo_user_details.get("unique_reference") if ajo_user_details else None
        # filter_query = Q(unique_reference=unique_reference, requested_by=requested_by, user=user) if unique_reference else Q(requested_by=requested_by, user=user, is_active=True)

        if unique_reference:
            get_previous_account = cls.objects.filter(unique_reference=unique_reference, requested_by=requested_by, user=user).last()
            if get_previous_account:
                func_response = {
                    "status": "success",
                    "message": "account successfully created",
                    "account_provider": get_previous_account.account_provider,
                    "user_id": user.id,
                    "data": {
                        "account_number": get_previous_account.account_number,
                        "account_name": get_previous_account.account_name,
                        "account_type": get_previous_account.account_type,
                        "bank_name": get_previous_account.bank_name,
                        "bank_code": get_previous_account.bank_code,
                        "is_test": get_previous_account.is_test,
                        "is_active": get_previous_account.is_active,
                        "timestamp": get_previous_account.date_created,
                        "unique_reference": get_previous_account.unique_reference,
                        "ajo_collector": get_previous_account.ajo_collector.id if get_previous_account.ajo_collector else None,
                    },
                }

                return func_response

        # get_rc_accounts = cls.objects.filter(rc_number=rc_number)
        # if not get_rc_accounts:
        #     final_rc_number = rc_number
        # else:
        #     final_rc_number = rc_number

        # verify BVN KYC
        # BVN
        user.check_kyc.bvn_rel.is_verified = True
        user.check_kyc.bvn_rel.save()

        if settings.ENVIRONMENT == "development":
            all_numbers = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"]

            get_account_num = ''.join(random.choice(all_numbers) for i in range(10))

            vfd_create = {
                "main_data": {
                    "status": "00",
                    "data": {
                        "accountNo": get_account_num,
                        "accountName": company_name
                    }
                },
                "initial_payload": {
                    "rcNumber": f"{rc_number}",
                    "companyName": f"{company_name}",
                    "incorporationDate": f"{incorp_date}",
                    "bvn": f"{bvn}"
                }
            }


        else:
            vfd_create = VFDBank.create_corporate_account(
                user=user,
                rc_number=rc_number,
                company_name=company_name,
                incorp_date=incorp_date,
                bvn=bvn
            )

        account_requested_for = user

        # if for_user_email is not None:
        #     get_for_user = User.objects.filter(email=for_user_email).last()
        #     if get_for_user:
        #         account_requested_for = get_for_user
        #         specified_request_for = True
        #     else:
        #         account_requested_for = user
        #         specified_request_for = False
        # else:
        #     account_requested_for = user
        #     specified_request_for = False

        if vfd_create.get("main_data").get("status") == "00":
            if get_corporate:
                get_corporate.added_acc_num += 1
                get_corporate.save()

            response = vfd_create.get("main_data").get("data")
            created_acc = cls.objects.create(
                requested_by=requested_by,
                user=account_requested_for,
                account_provider="VFD",
                account_type="AJO_USER_CORPORATE" if ajo_user_details else "COLLECTION",
                true_account_type="OTHERS" if others else "COLLECTION",
                vfd_account_type="CORPORATE",
                bank_name="VFD Microfinance Bank",
                bank_code="999999",
                account_number=response["accountNo"],
                account_name=response["accountName"],
                is_active=True,
                is_test=is_test_account,
                rc_number=rc_number,
                initial_payload=vfd_create.get("initial_payload"),
                payload=vfd_create,
                unique_reference=ajo_user_details.get("unique_reference") if ajo_user_details else None,
                ajo_collector=ajo_user_details.get("ajo_collector") if ajo_user_details else None,
            )

            ############################################################################
            # UPDATE NUMBER OF USER ACCOUNTS

            account_requested_for.vfd_bvn_acct_num_count += 1
            account_requested_for.save(update_fields=["vfd_bvn_acct_num_count"])

            for user_data in User.objects.exclude(id=account_requested_for.id).filter(bvn_number=bvn):
                user_data.vfd_bvn_acct_num_count = account_requested_for.vfd_bvn_acct_num_count
                user_data.save(update_fields=["vfd_bvn_acct_num_count"])
            ############################################################################

            func_response = {
                "status": "success",
                "message": "account successfully created",
                "account_provider": created_acc.account_provider,
                "user_id": account_requested_for.id,
                "data": {
                    "account_number": created_acc.account_number,
                    "account_name": created_acc.account_name,
                    "account_type": created_acc.account_type,
                    "vfd_account_type": created_acc.vfd_account_type,
                    "bank_name": created_acc.bank_name,
                    "bank_code": created_acc.bank_code,
                    "is_test": created_acc.is_test,
                    "is_active": created_acc.is_active,
                    "timestamp": created_acc.date_created,
                    "unique_reference": created_acc.unique_reference,
                    "ajo_collector": created_acc.ajo_collector.id if created_acc.ajo_collector else None,
                },
            }

            # Create All Wallets For New User

            # Get all Wallet Instances
            get_all_wallets = WalletSystem.get_uncreated_wallets(user=account_requested_for)

            if "SPEND" not in get_all_wallets:
                spend_wallet = WalletSystem.create_spend_wallet(user=account_requested_for)

            if "COLLECTION" not in get_all_wallets:
                collection_wallet = WalletSystem.create_collection_wallet(user=account_requested_for)

            if "SAVINGS" not in get_all_wallets:
                savings_wallet = WalletSystem.create_savings_wallet(user=account_requested_for)

            if "COMMISSIONS" not in get_all_wallets:
                commissions_wallet = WalletSystem.create_commissions_wallet(user=account_requested_for)

            # Create Collection Wallet

            main_collection_wallet = WalletSystem.get_wallet(user=account_requested_for, from_wallet_type="COLLECTION")

            if AccountSystem.objects.filter(account_number=created_acc.account_number).exists():
                pass
            else:
                if ajo_user_details:
                    AccountSystem.objects.create(
                        user=account_requested_for,
                        wallet=main_collection_wallet,
                        account_provider="VFD",
                        account_type="OTHERS",
                        true_account_type="AJO_USER_CORPORATE",
                        bank_name=created_acc.bank_name,
                        bank_code=created_acc.bank_code,
                        account_number=created_acc.account_number,
                        account_name=created_acc.account_name,
                        is_active=True,
                        available_balance=0.00,
                        is_test=False,
                        initial_payload=created_acc.initial_payload,
                        payload=created_acc.payload,
                    )

                else:

                    # Get all account instances
                    get_all_account_type = AccountSystem.get_created_account_type(user=account_requested_for)

                    if not any("COLLECTION" in dct.values() for dct in get_all_account_type):

                        AccountSystem.objects.create(
                            user=account_requested_for,
                            wallet=main_collection_wallet,
                            account_provider="VFD",
                            account_type="COLLECTION",
                            true_account_type="CORPORATE",
                            bank_name=created_acc.bank_name,
                            bank_code=created_acc.bank_code,
                            account_number=created_acc.account_number,
                            account_name=created_acc.account_name,
                            is_active=True,
                            available_balance=0.00,
                            is_test=False,
                            initial_payload=created_acc.initial_payload,
                            payload=created_acc.payload
                        )

                    else:
                        AccountSystem.objects.create(
                            user=account_requested_for,
                            wallet=main_collection_wallet,
                            account_provider="VFD",
                            account_type="OTHERS",
                            true_account_type="CORPORATE_LIBERTY_RETAIL" if account_requested_for.type_of_user == "LIBERTY_RETAIL" else "CORPORATE",
                            bank_name=created_acc.bank_name,
                            bank_code=created_acc.bank_code,
                            account_number=created_acc.account_number,
                            account_name=created_acc.account_name,
                            is_active=True,
                            available_balance=0.00,
                            is_test=False,
                            initial_payload=created_acc.initial_payload,
                            payload=created_acc.payload
                        )

            if internal == False:

                # KYC 2
                account_requested_for.check_kyc.docsface_rel.is_verified = True
                account_requested_for.check_kyc.docsface_rel.save()

                # KYC 3
                today_date = datetime.now()
                account_requested_for.check_kyc.guarantor_rel.is_verified = True
                account_requested_for.check_kyc.guarantor_rel.date_added = today_date
                account_requested_for.check_kyc.guarantor_rel.date_started = today_date
                account_requested_for.check_kyc.guarantor_rel.date_verified = today_date
                account_requested_for.check_kyc.guarantor_rel.save()

            else:
                pass


        else:
            AccountCreationFailure.objects.create(
                user=user,
                account_provider="VFD",
                account_type="CORPORATE",
                is_test=is_test_account,
                initial_payload=vfd_create.get("initial_payload"),
                payload=vfd_create,
            )

            func_response = {
                "status": "error",
                "error_code": "91",
                "message": "account failed to create",
                "account_provider": "VFD",
                "user_id": user.id,
                "data": {}
            }

        return func_response

    @classmethod
    def get_company_name(cls, company_base, email):
        parts = email.split("@")

        if len(parts) == 2:
            try:
                username_and_number = parts[0].split(".")[1]
                username, number = username_and_number.split("+")

                return f"{company_base} {username.upper()} {number}"

            except:
                return None

    @classmethod
    def arrange_corporate_detail(cls, user: User, new_user: User, corporate_id: str, suffix=None, get_location=None, company_name=None,
                                 ajo_user_details=None, others=False, company_name_account_type=None):
        get_corporate = CorporateAccount.objects.filter(corporate_id=corporate_id).last()
        if get_corporate:
            # if suffix:
            #     num_to_add = suffix
            # else:

            rc_number = get_corporate.rc_number if get_corporate.added_acc_num < 1 else f"{get_corporate.rc_number}-{get_corporate.added_acc_num}"
            # num_to_add = user.vfd_bvn_acct_num_count if user.vfd_bvn_acct_num_count == 0 else (user.vfd_bvn_acct_num_count+1)

            # rc_number = f"{get_corporate.rc_number}-{num_to_add}"
            # rc_number = final_rc
            incorp_date = get_corporate.incorp_date
            bvn = user.check_kyc.bvn_rel.bvn_number
            retail_system = getattr(new_user, 'retail_system', None)

            if get_location:
                company_name = f"{get_corporate.company_name} {company_name_account_type if company_name_account_type else ''} {get_location.location} {get_location.sub_location_num + 1}" if suffix != "COLLECTION" else f"{get_corporate.company_name} {get_location.location} {retail_system.location_num} {suffix}"

                # company_name = f"{get_corporate.company_name} {get_location.location} {retail_system.location_num}" if suffix != "COLLECTION" else f"{get_corporate.company_name} {get_location.location} {retail_system.location_num} {suffix}"
            else:
                company_name = company_name if company_name else f"{get_corporate.company_name} {get_corporate.added_acc_num if get_corporate.added_acc_num > 0 else None}"

            if settings.ENVIRONMENT == "development":
                is_test_account = True
            else:
                is_test_account = False

            create_corp = OtherServiceAccountSystem.create_corporate_account(
                user=new_user,
                is_test_account=is_test_account,
                requested_by=user,
                rc_number=rc_number,
                incorp_date=incorp_date,
                company_name=company_name.upper(),
                bvn=bvn,
                get_corporate=get_corporate,
                ajo_user_details=ajo_user_details,
                others=others
            )

            if create_corp.get("status") == "success":

                if suffix == None and get_location:
                    get_sub_loc = get_location.sub_location_num + 1

                    get_location.sub_location_num = get_sub_loc
                    get_location.save()

                    retail_system.location_num = get_sub_loc
                    retail_system.save()

                response = {
                    "status": "success",
                    "message": "account successfully created",
                    "data": create_corp,
                }

            else:
                response = {
                    "status": "error",
                    "message": "Error Occured"
                }

        else:
            response = {
                "status": "error",
                "message": "No corporate account exists For corporate id entered"
            }

        return response


class QRCode(models.Model):
    STATUS_TYPES = [
        ("SUCCESSFUL", "SUCCESSFUL"),
        ("PENDING", "PENDING"),
        ("FAILED", "FAILED"),
    ]

    PROVIDER_TYPES = [
        ("CYBERPAY", "CYBERPAY"),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    requested_by = models.ForeignKey(User, on_delete=models.SET_NULL, related_name="requested_by", null=True, blank=True)
    transaction_instance = models.ForeignKey(Transaction, on_delete=models.SET_NULL, null=True, blank=True)
    provider = models.CharField(max_length=100, choices=PROVIDER_TYPES, null=True, blank=True)
    liberty_reference = models.CharField(max_length=150)
    provider_reference = models.CharField(max_length=150, null=True, blank=True)
    status = models.CharField(max_length=100, choices=STATUS_TYPES, default="PENDING")
    amount = models.FloatField(default=0.00)
    amount_in_kobo = models.FloatField(default=0.00)
    narration = models.CharField(max_length=150, null=True, blank=True)
    qr_reference = models.CharField(max_length=150, null=True, blank=True)
    order_serial = models.CharField(max_length=150, null=True, blank=True)
    generated = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    is_paid = models.BooleanField(default=False)
    qr_code_url = models.TextField(null=True, blank=True)
    initial_payload = models.TextField(null=True)
    returned_payload = models.TextField(null=True)
    verification_payload = models.TextField(null=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    # def save(self, *args, **kwargs):

    #     super(QRCode, self).save(*args, **kwargs)

    @classmethod
    def get_amount_in_kobo(cls, amount):
        kobo = amount * 100
        return kobo

    @classmethod
    def remove_amount_from_kobo(cls, amount):
        if amount is None or amount <= 0:
            return 0
        else:
            naira = amount / 100
            return naira

    @classmethod
    def create_qr_code_instance(cls, user, amount, narration):
        # liberty_reference = f"LP_QR_{uuid.uuid4()}"
        liberty_reference = Transaction.create_liberty_reference(suffix="LP_QR")

        # Create Payload
        initial_payload = {
            "Currency": "NGN",
            "MerchantRef": liberty_reference,
            "Amount": cls.get_amount_in_kobo(amount=amount),
            "Description": narration,
            "CustomerId": f"{user.customer_id}",
            "ReturnUrl": f"{settings.BASE_URL}/accounts/cyberpay_webhook/",
            "WebhookUrl": f"{settings.BASE_URL}/accounts/cyberpay_webhook/",
            "ProductCode": "",
            "Channel": "QRCode",
            # "CustomerName": ,
            # "CustomerEmail": ,
            # "CustomerMobile": ,
        }

        qr_instance = cls.objects.create(
            user=user,
            liberty_reference=liberty_reference,
            provider="CYBERPAY",
            amount=amount,
            narration=narration,
            initial_payload=json.dumps(initial_payload)
        )

        create_qr = CyberPayClass.send_out_qr_request(payload=initial_payload)

        qr_instance.returned_payload = create_qr
        qr_instance.save()

        if create_qr["status"] == False:
            qr_instance.status = "FAILED"
        else:
            qr_reference = create_qr["data"].get("data").get("transactionReference") if create_qr["data"].get("data") else None
            if qr_reference is not None:
                query_reference_data = CyberPayClass.query_qr_ref(reference=qr_reference)
                qr_instance.verification_payload = json.dumps(query_reference_data)

                if query_reference_data["status"] == False:
                    qr_instance.status = "FAILED"
                else:
                    query_reference = query_reference_data["data"]

                    if query_reference["succeeded"] in [False] and query_reference.get("data").get("status") != "Successful":
                        qr_instance.status = "FAILED"


                    elif query_reference["succeeded"] == True and query_reference.get("data").get("status") == "Successful":
                        provider_reference = query_reference.get("data").get("reference")
                        order_sn = query_reference.get("data").get("data").get("orderSn")
                        code_url = query_reference.get("data").get("data").get("codeUrl")

                        qr_instance.provider_reference = provider_reference
                        qr_instance.order_serial = order_sn
                        qr_instance.qr_code_url = code_url
                        qr_instance.status = "SUCCESSFUL"

                    else:
                        pass

                    print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::")
                    print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::")
                    print(query_reference["succeeded"], query_reference.get("data").get("status"))
                    print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::")
                    print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::")
            else:
                qr_instance.status = "FAILED"

        qr_instance.save()
        return qr_instance


class TransactionPerformance(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    send_bank_trans = models.PositiveIntegerField(default=0)
    send_buddy = models.PositiveIntegerField(default=0)
    send_lotto = models.PositiveIntegerField(default=0)
    send_ajo = models.PositiveIntegerField(default=0)
    send_ajo_loans = models.PositiveIntegerField(default=0)
    billspay = models.PositiveIntegerField(default=0)
    airpin = models.PositiveIntegerField(default=0)
    cashout = models.PositiveIntegerField(default=0)
    cards = models.PositiveIntegerField(default=0)
    fund_buddy = models.PositiveIntegerField(default=0)
    fund_bank_trans = models.PositiveIntegerField(default=0)
    others = models.JSONField(default=dict)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def create_trans_perform(cls, user, trans_type):

        today = datetime.now()

        try:

            perform_create, created = cls.objects.get_or_create(
                user=user,
                date_created__date=today
            )

        except cls.MultipleObjectsReturned:

            get_all = cls.objects.filter(
                user=user,
                date_created__date=today
            )

            perform_create = get_all.first()
            get_all.last().delete()

        if trans_type == "SEND_BUDDY":
            perform_create.send_buddy += 1
        elif trans_type == "SEND_LOTTO_WALLET":
            perform_create.send_lotto += 1
        elif trans_type == "SEND_AJO_WALLET":
            perform_create.send_ajo += 1
        elif trans_type == "SEND_AJO_LOANS":
            perform_create.send_ajo_loans += 1
        elif trans_type == "SEND_BANK_TRANSFER":
            perform_create.send_bank_trans += 1
        elif trans_type == "BILLS_AND_PAYMENT":
            perform_create.billspay += 1
        elif trans_type == "AIRTIME_PIN":
            perform_create.airpin += 1
        elif trans_type in ["CARD_TRANSACTION_FUND_TRANSFER", "CARD_TRANSACTION_FUND"]:
            perform_create.cashout += 1
        elif trans_type == "FUND_BUDDY":
            perform_create.fund_buddy += 1
        elif trans_type == "FUND_BANK_TRANSFER":
            perform_create.fund_bank_trans += 1

        else:
            dict_others = perform_create.others
            if trans_type in dict_others:
                dict_others[trans_type] += 1
            else:
                dict_others[trans_type] = 1

        perform_create.save()

        return True


class UserOwnAccount(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    account_number = models.CharField(max_length=100)
    bank_code = models.CharField(max_length=100)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def add_own_account_to_table(cls, user, account_number, bank_code):
        if cls.objects.filter(user=user, account_number=account_number, bank_code=bank_code).exists():
            pass
        else:
            cls.objects.create(
                user=user,
                account_number=account_number,
                bank_code=bank_code,
            )

        return True


class QRCyberWebhookData(models.Model):
    payload = models.TextField(null=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


SWEEP_INTERVAL_TYPES = [
    ("HOURLY", "HOURLY"),
    ("DAILY", "DAILY"),
    ("WEEKLY", "WEEKLY"),
    ("MONTHLY", "MONTHLY"),
]

RECUR_TYPES = [
    ("SWEEP", "SWEEP"),
    ("RECUR", "RECUR"),
]


class AutoSweepRecurringChargeTable(models.Model):
    SWEEP_INTERVAL_TYPES = [
        ("HOURLY", "HOURLY"),
        ("DAILY", "DAILY"),
        ("WEEKLY", "WEEKLY"),
        ("MONTHLY", "MONTHLY"),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    wallet = models.ForeignKey(WalletSystem, on_delete=models.CASCADE)
    recur_type = models.CharField(max_length=200, choices=RECUR_TYPES)
    min_balance = models.FloatField(validators=[MinValueValidator(0)], default=10000)
    max_amount = models.FloatField(validators=[MinValueValidator(0)])
    start_date = models.DateTimeField()
    sweep_interval = models.CharField(max_length=150, choices=SWEEP_INTERVAL_TYPES)
    sweep_hour = models.PositiveIntegerField(validators=[MinValueValidator(0)])
    last_charge_amount = models.FloatField(validators=[MinValueValidator(0)], null=True, blank=True)
    last_run_time = models.DateTimeField(null=True, blank=True)
    next_run_time = models.DateTimeField(null=True, blank=True)
    account_number = models.CharField(max_length=250)
    account_name = models.CharField(max_length=250)
    bank_name = models.CharField(max_length=300)
    bank_code = models.CharField(max_length=300)
    narration = models.CharField(max_length=250, null=True, blank=True)
    first_trans_key = models.CharField(max_length=100, null=True, blank=True)
    trans_key = models.CharField(max_length=100, null=True, blank=True, editable=False)
    is_active = models.BooleanField(default=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.user.email

    def clean(self):
        if self.first_trans_key:
            if not self.first_trans_key.isnumeric():
                raise ValidationError('First Trans key Must be Numeric')

            if len(self.first_trans_key) != 4:
                raise ValidationError('First Trans Key Must be Four Digits')

    def save(self, *args, **kwargs):
        if self.first_trans_key and self.first_trans_key.isnumeric() and len(self.first_trans_key) == 4:
            from horizon_pay.helpers.helper_function import encrypt_trans_pin
            get_encrypted_version = encrypt_trans_pin(self.first_trans_key)
            self.trans_key = get_encrypted_version
        else:
            pass

        self.first_trans_key = None

        super(AutoSweepRecurringChargeTable, self).save(*args, **kwargs)


class AutoSweepRecurringChargeHistory(models.Model):
    recur_instance = models.ForeignKey(AutoSweepRecurringChargeTable, on_delete=models.CASCADE)
    amount = models.FloatField(validators=[MinValueValidator(0)])
    account_number = models.CharField(max_length=250)
    account_name = models.CharField(max_length=250)
    bank_name = models.CharField(max_length=300)
    bank_code = models.CharField(max_length=300)
    customer_reference = models.CharField(max_length=300, default=Transaction.create_liberty_reference_no_suffix)
    escrow_id = models.CharField(max_length=250, null=True, blank=True)
    narration = models.CharField(max_length=250, null=True, blank=True)
    payload = models.TextField(null=True, blank=True)
    status = models.BooleanField(default=False)
    session_key = models.CharField(max_length=500)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class WithdrawalByUSSDNotificationData(models.Model):
    payload = models.TextField()
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class OpeningClosingBalance(models.Model):
    opening_bank = models.FloatField(default=0.0)
    opening_all_wallet = models.FloatField(default=0.0)
    opening_pos_wallet = models.FloatField(default=0.0)
    opening_mobile_wallet = models.FloatField(default=0.0)
    closing_bank = models.FloatField(default=0.0)
    closing_all_wallet = models.FloatField(default=0.0)
    closing_pos_wallet = models.FloatField(default=0.0)
    closing_mobile_wallet = models.FloatField(default=0.0)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class ParallexDumpData(models.Model):
    data_dump = models.TextField()
    trans_ref = models.CharField(max_length=250, null=True, blank=True)
    tsq_data = models.TextField(null=True, blank=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class PromoCodeDecider(models.Model):
    session_id = models.CharField(max_length=150, default=generate_unique_ids)
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    is_active = models.BooleanField(default=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class PromoCodeData(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    # promo_code_decider = models.ForeignKey(PromoCodeDecider, on_delete=models.CASCADE, null=True, blank=True)
    session_id = models.CharField(max_length=150)
    transaction_id = models.CharField(max_length=150)
    promo_code = models.CharField(max_length=100)
    winner_codes = models.JSONField(default=list)
    trans_type = models.CharField(max_length=100)
    # is_active = models.BooleanField(default=True)
    # end_date = models.DateTimeField()
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def generate_promo_code(cls, user: User, transaction_id, trans_type, num=9):
        import random

        current_time = timezone.now()
        decider_instance = PromoCodeDecider.objects.filter(is_active=True).last()
        if not decider_instance:
            return None

        # promo_code_start_time = ConstantTable.get_constant_table_instance().promo_code_start_time
        # promo_code_end_time = ConstantTable.get_constant_table_instance().promo_code_end_time

        promo_code_start_time = decider_instance.start_time
        promo_code_end_time = decider_instance.end_time

        if user.type_of_user in [
            "AGENT"] and promo_code_start_time and promo_code_end_time and promo_code_start_time <= current_time <= promo_code_end_time:

            characters = str(uuid.uuid4()).replace('-', '').upper()

            promo_code = ''.join(random.choice(characters) for i in range(num))

            try:
                cls.objects.get(promo_code=promo_code)
                return cls.generate_promo_code(user=user, transaction_id=transaction_id, num=num)

            except cls.DoesNotExist:
                cls.objects.create(user=user, session_id=decider_instance.session_id, promo_code=promo_code, transaction_id=transaction_id,
                                   trans_type=trans_type)
                return promo_code
        else:
            return None

    # def generate_unique_numbers(seed=None, num=9, start=1, end=100):
    #     if seed is None:
    #         seed = int(time.time())

    #     random.seed(seed)
    #     numbers = random.sample(range(start, end+1), num)
    #     return numbers

    # unique_numbers = generate_unique_numbers()
    # print(unique_numbers)


class ReconciledWalletData(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    user_email = models.CharField(max_length=100, blank=True, null=True)
    opening_balance = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    total_credit = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    total_debit = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    closing_balance_wallet = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    closing_balance_statement = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    balance_difference = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    is_ok = models.BooleanField(default=False)
    st_wt_bal_match = models.BooleanField(default=False)
    line_to_line_recon = models.JSONField(default=list, null=True)
    custom_date = models.DateField()
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def reconcile_line_to_line_debcred(cls, debcred_qs: QuerySet[DebitCreditRecordOnAccount]):
        opening_bal = 0.00
        bad_transactions = []

        for index, debcred in enumerate(debcred_qs.order_by('id')):
            if index == 0:
                opening_bal = debcred.balance_before

            amount = debcred.amount
            closing_bal = debcred.balance_after

            if debcred.entry == "CREDIT":
                bal_check = abs((opening_bal + amount) - closing_bal)
            else:
                bal_check = abs((opening_bal - amount) - closing_bal)

            if bal_check > 0.01:
                bad_transactions.append(
                    {
                        'id': debcred.id,
                        'transaction_type': debcred.type_of_trans,
                        'transaction_id': debcred.transaction_instance_id,
                        'opening_bal': opening_bal,
                        'amount': amount,
                        'closing_bal': closing_bal,
                    }
                )

            opening_bal = debcred.balance_after

        if bad_transactions:
            pass
            # User.suspend_user(
            #     user=user,
            #     reason=f"Wallet Could Not Be Reconciled as there is a mismatch with debit credit transaction: {bad_transactions}"
            # )

        return bad_transactions

    @classmethod
    def reconcile_Wallets(cls, user: User, custom_date: datetime):
        today = custom_date

        get_user_deb_cred = DebitCreditRecordOnAccount.objects.select_related("user", "wallet").filter(user=user)
        user_deb_cred_today = get_user_deb_cred.filter(date_created__date=today).order_by('id')

        if get_user_deb_cred and user_deb_cred_today:

            instance, created = cls.objects.get_or_create(
                user=user,
                custom_date=today
            )

            check_opening_balance = get_user_deb_cred.filter(date_created__date__lt=today).last()
            if check_opening_balance:
                opening_balance = check_opening_balance.balance_after
            else:
                opening_balance = user_deb_cred_today.first().balance_before

            total_credit = user_deb_cred_today.filter(entry="CREDIT").aggregate(Sum("amount"))["amount__sum"] or 0
            total_debit = user_deb_cred_today.filter(entry="DEBIT").aggregate(Sum("amount"))["amount__sum"] or 0

            last_trans = user_deb_cred_today.last()
            closing_balance_statement = last_trans.balance_after
            closing_balance_wallet = last_trans.wallet.available_balance

            balance_difference = (opening_balance + total_credit - total_debit) - closing_balance_statement

            if abs(balance_difference) < 0.01:
                is_ok = True
            else:
                is_ok = False

            if abs(closing_balance_statement - closing_balance_wallet) < 0.01:
                st_wt_bal_match = True
            else:
                st_wt_bal_match = False

            if not is_ok:
                line_to_line_recon = cls.reconcile_line_to_line_debcred(debcred_qs=user_deb_cred_today)
            else:
                line_to_line_recon = []

            instance.user_email = user.email
            instance.opening_balance = opening_balance
            instance.total_credit = total_credit
            instance.total_debit = total_debit
            instance.closing_balance_statement = closing_balance_statement
            instance.closing_balance_wallet = closing_balance_wallet
            instance.balance_difference = balance_difference
            instance.is_ok = is_ok
            instance.st_wt_bal_match = st_wt_bal_match
            instance.line_to_line_recon = line_to_line_recon

            instance.save()

            return instance

        return None


class UserDebt(models.Model):
    SUCCESSFUL = "SUCCESSFUL"
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    FAILED = "FAILED"
    REVERSED = "REVERSED"
    IGNORE_HISTORY = "IGNORE_HISTORY"

    TRANSACTION_STATUS_CHOICES = [
        (SUCCESSFUL, "SUCCESSFUL"),
        (PENDING, "PENDING"),
        (IN_PROGRESS, "IN_PROGRESS"),
        (FAILED, "FAILED"),
        (REVERSED, "REVERSED"),
        (IGNORE_HISTORY, "IGNORE_HISTORY"),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    user_email = models.CharField(max_length=100, null=True, blank=True)
    wallet = models.ForeignKey(WalletSystem, on_delete=models.CASCADE)
    amount = models.FloatField(validators=[MinValueValidator(0.01)])
    trans_type = models.CharField(max_length=100)
    transaction_instance_id = models.CharField(max_length=150, null=True, blank=True)
    transaction_instance_new_status = models.CharField(
        max_length=150, choices=TRANSACTION_STATUS_CHOICES, blank=True, null=True
    )
    is_active = models.BooleanField(default=True)
    ignore_history = models.BooleanField(default=False)
    unique_id = models.CharField(max_length=150, null=True, blank=True, unique=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        self.user_email = self.user.email

        if self.trans_type and not self.trans_type.endswith("-A"):
            self.trans_type = f"{self.trans_type}-A"

        super(UserDebt, self).save(*args, **kwargs)

    @classmethod
    def create_user_debt(cls, user, wallet: WalletSystem, amount, trans_type, transaction_instance_id, transaction_instance_new_status,
                         ignore_history, unique_id=None):
        created = cls.objects.create(
            user=user,
            wallet=wallet,
            amount=amount,
            trans_type=trans_type,
            transaction_instance_id=transaction_instance_id,
            transaction_instance_new_status=transaction_instance_new_status,
            ignore_history=ignore_history,
            unique_id=unique_id,
        )

        return created

    @classmethod
    def debit_user_debt(cls, trans: "UserDebt"):
        # for trans in cls.objects.filter(user=user):
        amount = trans.amount
        user = trans.user

        wallet_instance = trans.wallet
        # wallet_instance = WalletSystem.objects.filter(user=user, wallet_type="COLLECTION").first()

        if wallet_instance.available_balance >= amount:
            with django_transaction.atomic():
                main_trans_type = "RETAIL_AUTO_DEBIT-A"
                # if main_trans_type in trans.trans_type:
                if trans.trans_type == main_trans_type:
                    from retail.models import RetailSystem, LedgerTableModel
                    kwargs = {
                        "user": user,
                        "agent_wallet_instance": wallet_instance,
                        "amount": amount,
                        "trans_type": "RETAIL_AUTO_DEBIT",
                        "unique_id": trans.unique_id
                    }
                    debit_user = RetailSystem.debit_account_for_retail_comm(kwargs)
                    set_pre_debit = LedgerTableModel.set_pre_debit_to_true(trans.unique_id)

                else:
                    deduct_balance = WalletSystem.deduct_balance(
                        user=user,
                        wallet=wallet_instance,
                        amount=amount,
                        trans_type=trans.trans_type,
                        transaction_instance_id=trans.transaction_instance_id
                    )

                    debit_id = deduct_balance.get("debit_credit_record_id")

                    if trans.transaction_instance_id and trans.transaction_instance_new_status:
                        try:
                            main_transaction = Transaction.objects.get(transaction_id=trans.transaction_instance_id)
                            if trans.ignore_history:
                                main_transaction.status = "IGNORE_HISTORY"
                            else:
                                main_transaction.status = trans.transaction_instance_new_status

                            main_transaction.save()

                        except Transaction.DoesNotExist:
                            pass

                trans.is_active = False
                trans.save()

        else:
            pass

        return


class UserAccountNumCount(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    vfd_count = models.PositiveIntegerField(default=0, validators=[MinValueValidator(1)])
    wema_count = models.PositiveIntegerField(default=0, validators=[MinValueValidator(1)])
    fidelity_count = models.PositiveIntegerField(default=0, validators=[MinValueValidator(1)])
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def get_counts(cls, user):
        inst, created = cls.objects.get_or_create(user=user)

        return inst


class LessMinimumAmountComm(models.Model):
    transf = models.OneToOneField(TransferVerificationObject, on_delete=models.CASCADE)
    amount = models.FloatField()
    unique_reference = models.CharField(max_length=500)
    balance_before = models.FloatField()
    balance_after = models.FloatField()
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.transf.liberty_reference

    @classmethod
    def generate_unique_reference(cls):
        return Transaction.create_liberty_reference(suffix="MIN_COMM_EXC")

    @classmethod
    def get_or_generate_reference(cls):
        key = 'min_comm_exc_unique_reference'
        reference, expiration_date = cache.get(key, (None, None))

        current_datetime = datetime.now()
        end_of_day = datetime.combine(current_datetime.date(), datetime.max.time())

        if not reference or expiration_date < current_datetime:
            get_today_reference = cls.objects.filter(date_created__date=current_datetime.date()).first()

            if get_today_reference:
                reference = get_today_reference.unique_reference
            else:
                reference = cls.generate_unique_reference()

            expiration_date = end_of_day
            cache.set(key, (reference, expiration_date))

        return reference

    @classmethod
    def handle_low_comm_amount(cls, transf: TransferVerificationObject):
        amount = transf.amount
        float_wallet_data = WalletSystem.get_float_wallet(from_wallet_type="MIN_COMM_EXC")
        float_wallet_user = float_wallet_data.user
        escrow_id = transf.escrow_id
        escrow_instance = Escrow.objects.filter(escrow_id=escrow_id).last()
        verify_type = "MIN_COMM_EXC"

        if amount < 1:
            with django_transaction.atomic():
                unique_reference = cls.get_or_generate_reference()

                add_fund = WalletSystem.fund_balance(
                    user=float_wallet_user,
                    wallet=float_wallet_data,
                    amount=amount,
                    trans_type=verify_type,
                    transaction_instance_id=transf.transaction_instance.transaction_id,
                    unique_reference=transf.liberty_reference
                )

                balance_before = add_fund["balance_before"]
                balance_after = add_fund["balance_after"]

                cls.objects.create(
                    transf=transf,
                    amount=amount,
                    unique_reference=unique_reference,
                    balance_before=balance_before,
                    balance_after=balance_after
                )

                escrow_instance.commissions_escrow = False
                escrow_instance.save()

                # Change transaction status depending on response
                transf.transaction_instance.status = "SUCCESSFUL"
                transf.transaction_instance.unique_reference = unique_reference

                transf.transaction_ver_status = "SUCCESSFUL"
                transf.unique_reference = unique_reference
                transf.commissions_leg_done = True
                transf.verify_type = verify_type

                transf.transaction_instance.save()
                transf.save()

                print("commissions paid")

    @classmethod
    def send_out_lessmincomms(cls):
        pass


class PendingTransaction(models.Model):
    list_display = ["id", "date_created", "last_updated", "transaction_type", "amount", "status", "user_email", "beneficiary_account_name",
                    "beneficiary_nuban", "transaction_id", "transaction_leg", "escrow_id", "liberty_reference", "unique_reference", "narration"]

    trx_id = models.IntegerField()
    trx_inst = models.ForeignKey(Transaction, on_delete=models.CASCADE)
    trx_date_created = models.DateTimeField()
    trx_last_updated = models.DateTimeField()
    trx_type = models.CharField(max_length=100)
    trx_amount = models.FloatField()
    trx_status = models.CharField(max_length=100)
    user_email = models.EmailField(db_index=True)
    trx_verf_obj = models.ForeignKey(TransferVerificationObject, on_delete=models.CASCADE, null=True, blank=True)
    beneficiary_account_name = models.CharField(max_length=100, null=True, blank=True)
    beneficiary_nuban = models.CharField(max_length=100, null=True, blank=True)
    trx_transaction_id = models.CharField(max_length=100)
    trx_leg = models.CharField(max_length=100, null=True, blank=True)
    liberty_reference = models.CharField(max_length=100, null=True, blank=True)
    escrow = models.ForeignKey(Escrow, on_delete=models.CASCADE, null=True, blank=True)
    unique_reference = models.CharField(max_length=100, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class UserPasswordManualSet(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    password = models.CharField(max_length=700)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "USER PASSWORD MANUAL SET"
        verbose_name_plural = "USER PASSWORD MANUAL SET"

    def save(self, *args, **kwargs):
        if not self.pk:
            hashed_password = make_password(self.password)
            self.password = hashed_password
            User.objects.filter(id=self.user.id).update(
                registration_email_verified=True,
                password=hashed_password,
                has_login_pin=True
            )

        return super(UserPasswordManualSet, self).save(*args, **kwargs)


class CallbackSending(models.Model):
    transaction = models.ForeignKey(Transaction, on_delete=models.CASCADE, related_name='callbacks')
    received = models.BooleanField(default=False)
    not_received = models.BooleanField(default=False)
    retry_count = models.PositiveSmallIntegerField(default=0)
    callback_response = models.TextField(null=True, blank=True)
    last_retry = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['received', 'not_received']),
            models.Index(fields=['retry_count']),
        ]
        verbose_name = 'Callback Sending'
        verbose_name_plural = 'Callback Sendings'

    def __str__(self):
        return f"Callback for Transaction {self.transaction.transaction_id}"


class AgencyTeam(models.Model):
    name = models.CharField(max_length=300)
    supervisor = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True, related_name="agency_team_supervisor")
    agents = models.ManyToManyField(User)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.name)


class TerminalPurchaseCommission(models.Model):
    COMMISSION_TYPE_CHOICES = (
        ("agent", "Agent"),
        ("supervisor", "Supervisor"),
        ("manager", "Manager"),
    )
    user = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True)
    pos_request = models.ForeignKey(POSRequest, on_delete=models.SET_NULL, blank=True, null=True)
    commission_type = models.CharField(max_length=20, choices=COMMISSION_TYPE_CHOICES, default="agent")
    settled = models.BooleanField(default=False)
    payload = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class WalletDebitReversal(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    debit_credit = models.ForeignKey(DebitCreditRecordOnAccount, on_delete=models.SET_NULL, blank=True, null=True)
    transaction_id = models.CharField(max_length=400, null=True, blank=True)
    failed_transaction_type = models.CharField(max_length=100, blank=True, null=True)
    reversed = models.BooleanField(default=False)
    amount = models.FloatField(validators=[MinValueValidator(0.0)])
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.email}: {self.reversed}: {self.created_at}"


class SendCommissionScheduler(models.Model):
    user = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True)
    wallet_id = models.CharField(max_length=200, blank=True, null=True)
    wallet_type = models.CharField(max_length=200, blank=True, null=True)
    amount = models.FloatField(default=0.0)
    provider = models.CharField(max_length=100, blank=True, null=True)
    transaction_commission_id = models.CharField(max_length=100, blank=True, null=True)
    transfer_leg = models.CharField(max_length=100, blank=True, null=True)
    escrow_id = models.CharField(max_length=200, blank=True, null=True)
    transaction_sub_type = models.CharField(max_length=100, blank=True, null=True)
    response_payload = models.TextField(blank=True, null=True)
    settled = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        if self.user:
            return f"{self.user.email} ==>> {self.provider} - {self.amount}: date - {self.created_at}"
        return f"{self.provider} - {self.amount}: date - {self.created_at}"


class TransactionLimitLog(models.Model):
    ACTION_CHOICES = [
        ('LIMIT_CHECK', 'LIMIT_CHECK'),
        ('LIMIT_APPLIED', 'LIMIT_APPLIED'),
        ('INFLOW_CHECK', 'INFLOW_CHECK'),
        ('INFLOW_BLOCK', 'INFLOW_BLOCK'),
    ]

    REASON_CHOICES = [
        ('NEW_USER_7DAYS', 'NEW_USER_7DAYS'),
        ('NEW_USER_40DAYS', 'NEW_USER_40DAYS'),
        ('LARGE_INFLOW_30DAYS', 'LARGE_INFLOW_30DAYS'),
        ('NORMAL_LIMIT', 'NORMAL_LIMIT'),
    ]

    STATUS_CHOICES = [
        ('ALLOWED', 'ALLOWED'),
        ('BLOCKED', 'BLOCKED'),
        ('WARNING', 'WARNING'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='transaction_limit_logs')
    action_type = models.CharField(max_length=20, choices=ACTION_CHOICES)
    amount = models.FloatField()
    reason = models.CharField(max_length=20, choices=REASON_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    account_age_days = models.IntegerField(null=True, blank=True)
    transaction_id = models.CharField(max_length=100, null=True, blank=True)
    details = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.email} - {self.action_type} - {self.status} - {self.created_at}"

    @classmethod
    def log_transaction_limit_check(cls, user, amount, action_type, reason, status, account_age_days=None, transaction_id=None, details=None):
        """
        Create a log entry for transaction limit checks
        """
        log = cls.objects.create(
            user=user,
            action_type=action_type,
            amount=amount,
            reason=reason,
            status=status,
            account_age_days=account_age_days,
            transaction_id=transaction_id,
            details=details
        )
        return log

    @classmethod
    def check_user_account_age(cls, user):
        """
        Check user account age and return the number of days since registration
        """
        today = datetime.now().date()
        account_creation_date = user.date_created.date()
        account_age_days = (today - account_creation_date).days
        return account_age_days

    @classmethod
    def check_inflow_blocking(cls, user, amount):
        """
        Check if a user should be blocked from performing outflows due to large inflows
        """
        constant = ConstantTable.get_constant_table_instance()

        account_age_days = cls.check_user_account_age(user)
        if user.bypass_new_user_limit or user.type_of_user in ["MERCHANT", "AGENT", "LOTTO_AGENT", "STAFF_AGENT"]:
            account_age_days = 60

        if account_age_days <= 30 and amount >= constant.new_user_inflow_limit:
            today = datetime.now().date()

            # Log the check
            cls.log_transaction_limit_check(
                user=user,
                amount=amount,
                action_type='INFLOW_CHECK',
                reason='LARGE_INFLOW_30DAYS',
                status='WARNING',
                account_age_days=account_age_days,
                details=f"User received inflow of {amount} within first 30 days of account creation"
            )

            # Block the user from performing outflows
            user.send_money_status = False
            user.suspension_reason = f"Blocked from performing outflows due to large inflow ({amount}) within first 30 days of account creation"
            user.save()

            # Log the block
            cls.log_transaction_limit_check(
                user=user,
                amount=amount,
                action_type='INFLOW_BLOCK',
                reason='LARGE_INFLOW_30DAYS',
                status='BLOCKED',
                account_age_days=account_age_days,
                details=f"User blocked from performing outflows due to large inflow ({amount}) within first 30 days of account creation"
            )

            return {
                "status": "blocked",
                "message": "Your account has been blocked from performing transfers due to receiving a large amount. Please contact support."
            }

        return {
            "status": "allowed",
            "message": "Transaction allowed"
        }



