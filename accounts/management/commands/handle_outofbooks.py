from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from main.models import User
from accounts.models import AccountSystem, Transaction, OutOfBookTransfer
from accounts.helpers.vfdbank_manager import VFDBank




class Command(BaseCommand):
    help = 'A custom management command that accepts parameters for sending out of books.'

    # def add_arguments(self, parser):
        # parser.add_argument('--from', dest='arg1', type=str, help='sender_account_no.')
        # parser.add_argument('--to', dest='arg2', type=str, help='receiver_account_no.')
        # parser.add_argument('--amt', dest='arg3', type=float, help='amount.')
        # parser.add_argument('--user', dest='arg4', type=int, help='user_id.')
        # parser.add_argument('--escrow', dest='arg5', type=str, help='escrow_id.')


    def handle(self, *args, **kwargs):
        
        beneficiary_bank_code = "999999"
        liberty_reference = Transaction.create_liberty_reference(suffix="LGLP-VFD-OOB")

        liberty_float_bank_account = AccountSystem.get_float_account(
            from_wallet_type="COMMISSIONS", from_provider_type="VFD"
        ) 

        liberty_float_nuban = liberty_float_bank_account.account_number
        liberty_float_bank_name = liberty_float_bank_account.account_name

        # user = User.objects.filter(email="").last()

        # beneficiary_account = AccountSystem.get_account_type(
        #     user=user,
        #     from_wallet_type="COLLECTION",
        #     from_provider_type="VFD",
        # )

        # beneficiary_account_name = beneficiary_account.account_name
        # beneficiary_nuban = beneficiary_account.account_number

        user = liberty_float_bank_account.user
        beneficiary_account_name = "Liberty Assured Limited-Payroll"
        beneficiary_nuban = "**********"

        amount = 1350799
        escrow_id = ""



        record_sub_com = OutOfBookTransfer.objects.create(
            type_of_transfer = "OTHERS",
            provider = "VFD",
            amount = amount,
            from_account = liberty_float_nuban,
            to_account = beneficiary_nuban,
            created_liberty_reference = liberty_reference,
            escrow_id = escrow_id,
        )

        reverse_sub_comm = VFDBank.initiate_payout(
            beneficiary_account_name=beneficiary_account_name,
            beneficiary_nuban=beneficiary_nuban,
            beneficiary_bank_code=beneficiary_bank_code,
            source_account=liberty_float_nuban,
            narration=f"LPAY Commission Transfer Out",
            amount=amount,
            transfer_type="intra",
            user_bvn=user.bvn_number,
            reference=liberty_reference,
            transfer_leg="OUT_OF_BOOKS"
        )


        record_sub_com.send_payload = reverse_sub_comm
        record_sub_com.save()




        # from_acct_no = options.get('arg1')
        # to_acct_no = options.get('arg2')
        # amount = options.get('arg3')
        # user_id = options.get('arg4')
        # escrow_id = options.get('arg5')

    #     # Your custom logic goes here
    #     self.stdout.write(f'Argument 1: {from_acct_no}')
    #     self.stdout.write(f'Argument 2: {to_acct_no}')
    #     self.stdout.write(f'Argument 2: {amount}')
    #     self.stdout.write(f'Argument 2: {user_id}')
    #     self.stdout.write(f'Argument 2: {escrow_id}')



    #     if not from_acct_no or not to_acct_no or not amount or not user_id or not escrow_id:
    #         raise CommandError('Missing required arguments.')
        
    #     try:
    #         user = User.objects.get(id=int(user_id))
    #     except Exception as err:
    #         raise CommandError(f'Error: {err}')

        
#         get_ben_account_name = AccountSystem.objects.filter(user=user, account_number=to_acct_no).first()
#         if not get_ben_account_name:
#             raise CommandError(f'Error: Account Does Not Exist For User')
        

#         handle_rev = AccountSystem.handle_out_of_books(
#             from_account=from_acct_no,
#             to_account=to_acct_no,
#             amount=amount,
#             escrow_id=escrow_id,
#             account_inst=get_ben_account_name
#         )

#         print(handle_rev)
        

        


