from django.core.management.base import BaseCommand
from django.conf import settings
from django.db.models import Sum, Q
from main.models import ConstantTable
from accounts.models import WalletSystem, AccountSystem, Transaction, Escrow
from accounts.helpers.vfdbank_manager import VFDBank
from accounts.helpers.woven_manager import Woven
from accounts.tasks import send_money_external_bank_transfer_task

import uuid
import json



class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):

        float_user = WalletSystem.get_float_user()
        current_provider = AccountSystem.get_provider_type(user=float_user)

        if current_provider == "WOVEN":


            # FOR WOVEN
            woven_float_account = AccountSystem.get_dynamic_float_account(from_wallet_type="FLOAT", from_provider_type="WOVEN")
            woven_balance = Woven.get_woven_balance(source_account=woven_float_account.account_number)

            if woven_balance is not None:
                woven_float_account.wallet.available_balance = woven_balance
                woven_float_account.wallet.save()
                woven_float_account.save()
            else:
                pass

            woven_commission_account = AccountSystem.get_dynamic_float_account(from_wallet_type="COMMISSIONS", from_provider_type="WOVEN")
            commission_woven_balance = Woven.get_woven_balance(source_account=woven_commission_account.account_number)
            
            if commission_woven_balance is not None:
                woven_commission_account.wallet.available_balance = commission_woven_balance
                woven_commission_account.wallet.save()
                woven_commission_account.save()
            else:
                pass

################################################################################################################################################
        elif current_provider == "VFD":
            # FOR VFD        
            vfd_float_account = AccountSystem.get_dynamic_float_account(from_wallet_type="FLOAT", from_provider_type="VFD")
            vfd_balance = VFDBank.get_vfd_float_balance()

            if vfd_balance is not None:
                vfd_float_account.wallet.available_balance = vfd_balance if vfd_balance >= 0 else 0
                print(vfd_float_account.wallet.available_balance)

                vfd_float_account.wallet.save()
                vfd_float_account.save()
            else:
                pass

            vfd_commission_account = AccountSystem.get_dynamic_float_account(from_wallet_type="COMMISSIONS", from_provider_type="VFD")
            commission_vfd_balance = VFDBank.get_vfd_float_balance(account_number=vfd_commission_account.account_number)

            if commission_vfd_balance is not None:
                vfd_commission_account.wallet.available_balance = commission_vfd_balance if commission_vfd_balance >= 0 else 0
                vfd_commission_account.wallet.save()
                vfd_commission_account.save()
            else:
                pass