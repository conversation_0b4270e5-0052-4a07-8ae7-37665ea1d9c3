import ast
from datetime import datetime, timedelta
import json
from django.utils import timezone
from django.core.management.base import BaseCommand
from django.conf import settings
from django.db.models import Sum, Q
from accounts.helpers.helper_func import notify_admin_whatsapp_on_pending_trans
from main.models import ConstantTable, User
from accounts.models import CashOutChargeBand, Escrow, Transaction, WalletSystem
from accounts.helpers.vfdbank_manager import VFDBank
from horizon_pay.models import HorizonPayTable
from accounts.tasks import send_money_external_bank_transfer_task



class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):

        ten_minutes_ago = timezone.now() - timezone.timedelta(minutes=10)
        count_days_ago = timezone.now() - timezone.timedelta(days=1)

        unresolved_rrns = []

        pending_in_progress_trans = HorizonPayTable.objects \
            .filter(is_resolved = False, as_chargeback=False, wrong_tid=False, date_created__lte=ten_minutes_ago, date_created__gte=count_days_ago,) \
                .order_by("id")[:200]

        for i in pending_in_progress_trans:

            try:
                formatted_payload = json.loads(i.payload)
            except:
                formatted_payload = ast.literal_eval(i.payload)

            if Transaction.objects.filter(transaction_type__in=["CARD_TRANSACTION_FUND_TRANSFER", "CARD_TRANSACTION_FUND"], unique_reference=formatted_payload["rrn"]).values_list('unique_reference', flat=True).exists():
                i.is_resolved = True
            else:
                unresolved_rrns.append(f'{formatted_payload["rrn"]}---{i.date_created.strftime("%d/%m/%Y")}')
            


        HorizonPayTable.objects.bulk_update(pending_in_progress_trans, fields=["is_resolved"])

        if unresolved_rrns:
            pending_trans_id_list = [f"{trns} (CASH_OUT)" for trns in unresolved_rrns]
            
            trans_count = f"CASH_OUT - {len(unresolved_rrns)}"


            for num in ["2347039516293", "2348077469471", "2348167631246", "2348031346306", "2348090643057"]:
                notify_admin_whatsapp_on_pending_trans(num, pending_trans_id_list, trans_count)
