import logging
import time
from django.core.management.base import BaseCommand
from django.utils import timezone
from main.models import User
from accounts.models import AccountSystem, WalletSystem

# Configure logger
logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Creates Fidelity accounts for users who don't have one yet"

    def add_arguments(self, parser):
        parser.add_argument(
            '--batch-size',
            type=int,
            default=100,
            help='Number of users to process in each batch'
        )
        parser.add_argument(
            '--sleep-time',
            type=float,
            default=5.0,
            help='Sleep time between batches in seconds'
        )
        parser.add_argument(
            '--log-file',
            type=str,
            default='/var/log/fidelity_account_creation.log',
            help='Path to the log file'
        )

    def handle(self, *args, **options):
        batch_size = options['batch_size']
        sleep_time = options['sleep_time']
        log_file = options['log_file']
        
        # Configure file handler for logging
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # Log start of process
        start_time = timezone.now()
        logger.info(f"Starting Fidelity account creation process at {start_time}")
        
        # Get all users who don't have a Fidelity account
        total_users = User.objects.filter(last_login__isnull=False).count()
        logger.info(f"Total users with login history: {total_users}")
        self.stdout.write(f"Total users with login history: {total_users}")
        
        processed = 0
        created = 0
        failed = 0
        
        # Process users in batches
        for offset in range(0, total_users, batch_size):
            users_batch = User.objects.filter(last_login__isnull=False)[offset:offset+batch_size]
            
            for user in users_batch:
                processed += 1
                
                # Check if user already has a Fidelity account
                fidelity_account = AccountSystem.objects.filter(user=user, bank_name="Fidelity Bank Plc").exists()
                
                if not fidelity_account:
                    try:
                        # Get user's collection wallet
                        wallet = WalletSystem.objects.filter(wallet_type="COLLECTION", user=user).last()
                        
                        if wallet:
                            # Create Fidelity account
                            AccountSystem.create_fidelity_account(user, wallet)
                            created += 1
                            logger.info(f"Created Fidelity account for user {user.id}: {user.email}")
                            self.stdout.write(f"Created Fidelity account for user {user.id}: {user.email}")
                        else:
                            # Log users without collection wallet
                            logger.error(f"User {user.id}: {user.email} ({user.first_name} {user.last_name}) - No collection wallet found")
                            failed += 1
                    except Exception as e:
                        # Log other errors
                        logger.error(f"User {user.id}: {user.email} ({user.first_name} {user.last_name}) - Error: {str(e)}")
                        failed += 1
            
            progress_msg = f"Processed {processed}/{total_users} users. Created: {created}, Failed: {failed}"
            logger.info(progress_msg)
            self.stdout.write(progress_msg)
            
            # Sleep between batches
            if sleep_time > 0 and offset + batch_size < total_users:
                time.sleep(sleep_time)
        
        end_time = timezone.now()
        duration = end_time - start_time
        summary = (
            f"Completed! Processed {processed} users. "
            f"Created {created} Fidelity accounts. "
            f"Failed for {failed} users. "
            f"Duration: {duration}. "
            f"See {log_file} for details."
        )
        logger.info(summary)
        self.stdout.write(self.style.SUCCESS(summary))
        
        logger.removeHandler(file_handler)
