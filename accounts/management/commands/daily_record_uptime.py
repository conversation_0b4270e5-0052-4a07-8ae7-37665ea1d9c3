from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from django.db.models import Sum, Q
from horizon_pay.models import CardTransaction
from accounts.models import Escrow, DailyUptimeDowntimeRecord, TransferVerificationObject
from kyc_app.models import BVNDetail, KYCTable, DocumentFaceMatchKYC2Detail
from datetime import datetime, timedelta
from collections import Counter

import uuid
import json
import requests
import jwt




class Command(BaseCommand):
    help = ""

    def add_arguments(self, parser):
        parser.add_argument('--date', dest='date', type=str, help='date.')

    
    def handle(self, *args, **options):
        date = options.get('date')

        if date:
            try:
                yesterday_date = datetime.strptime(date, "%d-%m-%Y").date()
            except ValueError:
                raise CommandError('Invalid Date Format. Use DD-MM-YYYY')
        else:
            yesterday_date = datetime.now().date() - timedelta(days=1)

        entries = ["CASH_OUT", "TRANSFER"]

        for entry in entries:

            today_entry = DailyUptimeDowntimeRecord.objects.filter(for_date=datetime.now().date(), entry_type=entry).last()
            if today_entry:
                pass
            else:
                today_entry = DailyUptimeDowntimeRecord.objects.create(for_date=datetime.now().date(), entry_type=entry)

            print(yesterday_date)
            yesterday_record = DailyUptimeDowntimeRecord.get_yesterday(entry, yesterday_date)

            if not yesterday_record:
                pass
            else:

                if entry == "CASH_OUT":

                    yesterday_data = CardTransaction.objects.filter(date_created__date=yesterday_date)
                    yesterday_uptime = yesterday_data.filter(resultCode="00")
                    yesterday_downtime = yesterday_data.exclude(resultCode='00')

                    downtime_stat = Counter()

                    for downtime in yesterday_downtime:
                        code = downtime.resultCode
                        if code:
                            downtime_stat.update([code])
                            
                    downtime_stat = json.dumps(dict(downtime_stat))

                
                elif entry == "TRANSFER":
                    yesterday_data = Escrow.objects.filter(date_created__date=yesterday_date, transfer_type="SEND_BANK_TRANSFER")
                    yesterday_uptime = yesterday_data.filter(
                        internal_escrow=False,
                        external_escrow=False,
                    )
                    yesterday_downtime = yesterday_data.filter(
                        Q(internal_escrow=True) | Q(external_escrow=True)| Q(reversed=True)
                    )

                    downtime_stat = Counter()

                    for downtime in yesterday_downtime:
                        trans_verf = TransferVerificationObject.objects.filter(
                            escrow_id=downtime.escrow_id,
                            liberty_reference=downtime.liberty_reference
                        ).first()

                        if trans_verf:
                            code = trans_verf.trans_status_code
                            print(code, "))")
                            if code:
                                downtime_stat.update([code])
                            
                    downtime_stat = json.dumps(dict(downtime_stat))


                print(downtime_stat, "&&")
                data = {
                    "uptime_count": yesterday_uptime.count(),
                    "downtime_count": yesterday_downtime.count(),
                    "downtime_stat": downtime_stat,
                }

                get_result = DailyUptimeDowntimeRecord.calculate_up_down_time(data=data)

                update_record = DailyUptimeDowntimeRecord.update_record_table(obj = yesterday_record, result=get_result)





        # create_today_cashout = DailyUptimeDowntimeRecord.objects.create(date_created=datetime.now(), entry_type="CASH_OUT")
        # create_today_transfer = DailyUptimeDowntimeRecord.objects.create(date_created=datetime.now(), entry_type="TRANSFER")

        # yesterday_cashout_record = DailyUptimeDowntimeRecord.get_yesterday(entry="CASH_OUT")
        # yesterday_transfer_record = DailyUptimeDowntimeRecord.get_yesterday(entry="TRANSFER")

        # yesterday_cashout_data = CardTransaction.objects.filter(date_created__date=yesterday_date)
        # yesterday_cashout_uptime = yesterday_cashout_data.filter(resultCode="00")
        # yesterday_cashout_downtime = yesterday_cashout_data.filter(resultCode__in=['91','06'])
       
        # data = {
        #     "uptime_count": yesterday_cashout_uptime.count(),
        #     "downtime_count": yesterday_cashout_downtime.count()
        # }






