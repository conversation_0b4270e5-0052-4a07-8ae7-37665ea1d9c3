from django.core.management.base import BaseCommand
from datetime import datetime, timedelta
from accounts.models import Transaction
from accounts.serializers import TransactionHistorySerializer
from sendgrid import SendGrid<PERSON>IClient
from django.conf import settings
from main.models import User
import pandas as pd
import base64, pytz


class Command(BaseCommand):
    help = "To fetch all transactions in the last 24 hours and send by email to pre-defined stakeholders"
    file_path = 'records.xlsx'
    timezone = pytz.timezone(settings.TIME_ZONE)

    # def add_arguments(self, parser):
    #     parser.add_argument('email1', type=str, help='Main user email')
    #     parser.add_argument('-e2', '--email2', type=str, help='Define a username prefix', )

    def parse_transactions(self):
        d_now = datetime.now()
        d_yesterday = d_now - timedelta(days=1)
        now = self.timezone.localize(d_now)
        yesterday = self.timezone.localize(d_yesterday)
        transactions = Transaction.objects.filter(date_created__lte=now).filter(date_created__gte=yesterday)
        if len(transactions) > 0:
            serializer = TransactionHistorySerializer(transactions, many=True)
            df = pd.DataFrame(serializer.data)
            df.to_excel(self.file_path)
        else:
            with open(self.file_path, 'w') as f:
                f.write("")
        

    def send_email(self):
        to_emails = [{'email': '<EMAIL>'}, {'email': '<EMAIL>'},
        {'email': '<EMAIL>'}, {'email': '<EMAIL>'}]
        # to_emails = emails
        with open(self.file_path, 'rb') as f:
            data = f.read()
        encoded = base64.b64encode(data).decode()
        message = {
            'personalizations': [{
                'to': to_emails,
                'subject': 'Sending Transaction Records for the last 24 hours'
            }],
            'from': {
                'email': "<EMAIL>"
            },
            'content': [
                {
                    'type': 'text/plain',
                    'value': 'test'
                }
            ],
            'attachments': [{
                "filename": self.file_path,
                "content": encoded,
                "contentId": self.file_path,
                "disposition": "attachment"
            }]
        }
        try:
            sg = SendGridAPIClient(settings.SENDGRID_API_KEY)
            resp = sg.send(message)
        except Exception as e:
            pass
        
    def handle(self, *args, **kwargs):
        # email1 = kwargs['email1']
        # email2 = kwargs['email2']
        # emails = [{'email': email1}]
        # if email2:
        #     emails.append({'email': email2})
        self.parse_transactions()
        self.send_email()