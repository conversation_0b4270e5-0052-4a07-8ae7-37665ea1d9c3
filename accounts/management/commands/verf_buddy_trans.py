import datetime
from django.core.management.base import BaseCommand
from main.models import ConstantTable
from accounts.models import Escrow, OtherCommissionsRecord, Transaction, WalletSystem, DebitCreditRecordOnAccount
from accounts.tasks import send_callback_out_for_other_account_fund, send_callback_out_for_send_to_lotto, send_callback_out_for_send_to_ajo

from django.utils import timezone

from liberty_pay.settings import cloud_messaging
from accounts.models import InAppTransactionNotification



class Command(BaseCommand):
    help = ""

    def add_arguments(self, parser):
        parser.add_argument('--debcred', dest='arg1', type=int, help='Liberty References.')

    def handle(self, *args, **options):        
        deb_cred_id = options.get('arg1')
        print(deb_cred_id)
        if deb_cred_id:
            deb_cred_record = DebitCreditRecordOnAccount.objects.filter(id=deb_cred_id).last()
            if deb_cred_record:
                escrow_instance = Escrow.objects.filter(debit_credit_record_id=deb_cred_record.id).last()
                if escrow_instance:
                    all_trans = Transaction.objects.filter(escrow_id=escrow_instance.escrow_id)
                else:
                    raise Exception("No Escrow")
            else:
                raise Exception("Deb Cred")
                    

        else:


            start_date = datetime.date(2023, 5, 27)
            end_date = datetime.date(2023, 6, 2)

            # for debcred in DebitCreditRecordOnAccount.objects.filter(id__in=[1226]):
                # escrow_instance = Escrow.objects.filter(debit_credit_record_id=debcred.id).last()

            five_minutes_ago = timezone.now() - timezone.timedelta(minutes=15)
            five_days_ago = timezone.now() - timezone.timedelta(days=5)

            trans_type_needed = ["SEND_LOTTO_WALLET", "SEND_AJO_WALLET", "SEND_AJO_LOANS", "SEND_COLLECTION_ACCOUNT", "SEND_BUDDY", "LOTTO_PLAY", "LIBERTY_LIFE_PAYMENT", "CREDI_LOAN", "AUTO_REFUND", "FUNDS_RETRIEVAL", "SAVINGS", "RETAIL_AUTO_DEBIT"]

            all_trans = Transaction.objects.filter(transaction_type__in=trans_type_needed, status__in=["PENDING"],date_created__lte=five_minutes_ago, date_created__gte=five_days_ago)

        print(all_trans)
        for send_trans in all_trans:

            escrow_instance = Escrow.objects.filter(escrow_id=send_trans.escrow_id).last()

            if escrow_instance:
                get_trans_with_escrow = Transaction.objects.filter(escrow_id=escrow_instance.escrow_id)
                if get_trans_with_escrow.count() > 1:
                    print("I was successful", escrow_instance.escrow_id)
                    for single_trans in get_trans_with_escrow:
                        single_trans.status = "SUCCESSFUL"
                        single_trans.save()
                        
                        if single_trans.transaction_type == "FUND_LOTTO_WALLET":
                            send_callback_out = send_callback_out_for_send_to_lotto.apply_async(
                                queue="processbulksheet",
                                kwargs={
                                    "instance_id": single_trans.id
                                }
                            )
                        elif single_trans.transaction_type in ["FUND_AJO_WALLET", "FUND_AJO_LOANS"]:
                            send_callback_out = send_callback_out_for_send_to_ajo(
                                instance_id=single_trans.id
                            )
                        
                        elif single_trans.transaction_type == "FUND_COLLECTION_ACCOUNT":
                            send_coll_trx = get_trans_with_escrow.filter(transaction_type="SEND_COLLECTION_ACCOUNT").first()
                            inflow_trx = Transaction.objects.filter(escrow_id=send_coll_trx.unique_reference, transaction_type="FUND_BANK_TRANSFER").first()
                            print(inflow_trx.beneficiary_nuban, "beneficiary nuban")

                            fund_callback_payload = {
                                "user_id": send_coll_trx.user.id,
                                "reference": escrow_instance.fund_liberty_reference,
                                "amount": send_coll_trx.amount,
                                "liberty_commission": 0,
                                "agent_phone": send_coll_trx.user.phone_number,
                                "account_number": inflow_trx.beneficiary_nuban,
                            }

                            single_trans.callback_payload = fund_callback_payload
                            single_trans.save()
                            single_trans.refresh_from_db()


                            send_callback_out = send_callback_out_for_other_account_fund(
                                instance_id=single_trans.id,
                                account_number=inflow_trx.beneficiary_nuban,
                                trans_owner_user_id=single_trans.user.id,
                            )


                else:
                    print("I am working now", escrow_instance.escrow_id)

                    sender_transaction = get_trans_with_escrow.first()
                    sender_user_instance = sender_transaction.user
                    amount = sender_transaction.amount
                    sender_wallet = WalletSystem.objects.filter(wallet_id=sender_transaction.wallet_id).first()
                    wallet = WalletSystem.objects.filter(wallet_id=sender_transaction.beneficiary_wallet_id).first()
                    buddy_user_instance = wallet.user
            
                    # Get debit_credit_record_id
                    if escrow_instance.debit_credit_record_id is not None:
                        debit_credit_record_id = DebitCreditRecordOnAccount.objects.filter(id=escrow_instance.debit_credit_record_id).last()
                        if debit_credit_record_id:
                            debit_credit_record_id.transaction_instance_id = sender_transaction.transaction_id
                            debit_credit_record_id.save()


                    if escrow_instance.transfer_type in ["SEND_LOTTO_WALLET", "SEND_AJO_WALLET", "SEND_AJO_LOANS", "SEND_COLLECTION_ACCOUNT", "SEND_BUDDY"]:
                        if escrow_instance.transfer_type == "SEND_LOTTO_WALLET":
                            sender_trans_type = "SEND_LOTTO_WALLET"
                            receiver_trans_type = "FUND_LOTTO_WALLET"
                            duo_nomenclature = "AGENT"

                        elif escrow_instance.transfer_type == "SEND_AJO_WALLET":
                            sender_trans_type = "SEND_AJO_WALLET"
                            receiver_trans_type = "FUND_AJO_WALLET"
                            duo_nomenclature = "AGENT"

                        elif escrow_instance.transfer_type == "SEND_AJO_LOANS":
                            sender_trans_type = "SEND_AJO_LOANS"
                            receiver_trans_type = "FUND_AJO_LOANS"
                            duo_nomenclature = "AGENT"

                        elif escrow_instance.transfer_type == "SEND_COLLECTION_ACCOUNT":
                            sender_trans_type = "SEND_COLLECTION_ACCOUNT"
                            receiver_trans_type = "FUND_COLLECTION_ACCOUNT"
                            duo_nomenclature = ""

                        else:
                            sender_trans_type = "SEND_BUDDY"
                            receiver_trans_type = "FUND_BUDDY"
                            duo_nomenclature = "BUDDY"

                        
                        if sender_user_instance.email in ["<EMAIL>"] and escrow_instance.narration in ["LOTTO_COMMISSION", "LOTTERY_WINNING_COMMISSION", "LOTTERY_PLAY_COMMISSION", "SUPER_AGENT_COMMISSION_REWARD"]:
                            
                            if buddy_user_instance.email == "<EMAIL>" and escrow_instance.narration == "SUPER_AGENT_COMMISSION_REWARD":
                                get_sales_rep = OtherCommissionsRecord.get_sales_rep_with_code(sales_rep_code="48404C999")
                                # get_supervisor = SuperAgentProfile.objects.filter(sales_rep_code="48404C999")
                            
                            else:
                                get_sales_rep = OtherCommissionsRecord.check_if_sales_rep_exists(downline_user=buddy_user_instance)


                            if get_sales_rep is None:
                                new_buddy_amount = amount
                                new_super_agent_comm = 0
                                sales_rep = False

                            else:
                                calc_super_agent_profit = (ConstantTable.get_constant_table_instance().lotto_super_agent_profit/100) * amount
                                new_buddy_amount = amount - calc_super_agent_profit
                                new_super_agent_comm = calc_super_agent_profit
                                sales_rep = True


                            # Handle Agent Profit On Cash Out
                            if (amount >= new_buddy_amount + new_super_agent_comm) and new_super_agent_comm > 0 and sales_rep == True:
                                handle_ro_commission = OtherCommissionsRecord.create_and_top_up_sales_rep_commissions(
                                    agent = buddy_user_instance,
                                    sales_rep = get_sales_rep.sales_rep,
                                    amount = new_super_agent_comm,
                                    transaction_id = sender_transaction.transaction_id,
                                    transaction_type = "LOTTO_PLAY",
                                    transaction_reason = f"Commission on LOTTO PLAY by {buddy_user_instance.bvn_first_name}" if buddy_user_instance.bvn_first_name else f"Commission on LOTTO PLAY by {buddy_user_instance.email}",
                                    total_profit = amount + escrow_instance.liberty_commission,
                                    liberty_profit = escrow_instance.liberty_commission,
                                    ro_cash_profit = new_super_agent_comm,
                                    agent_cash_profit = new_buddy_amount
                                )


                        else:
                            new_buddy_amount = amount
                            new_super_agent_comm = 0





                        fund_lotto_callback_payload = {
                            "user_id": sender_user_instance.id,
                            "reference": escrow_instance.fund_liberty_reference,
                            "amount": new_buddy_amount,
                            "liberty_commission": new_super_agent_comm,
                            "agent_phone": sender_user_instance.phone_number,
                            "account_number": None,
                        }

                        # Create transaction for Receiver
                        receiver_transaction = Transaction.objects.create(
                            user=wallet.user,
                            wallet_id=wallet.wallet_id,
                            wallet_type=wallet.wallet_type,
                            transaction_type=receiver_trans_type,
                            amount=new_buddy_amount,
                            liberty_reference=escrow_instance.fund_liberty_reference,
                            liberty_commission=new_super_agent_comm,
                            total_amount_received=amount,
                            escrow_id=escrow_instance.escrow_id,
                            source_account_name = sender_user_instance.bvn_full_name,
                            source_nuban = sender_user_instance.phone_number,
                            source_wallet_id=sender_wallet.wallet_id,
                            source_wallet_type=sender_wallet.wallet_type,
                            narration=escrow_instance.narration,
                            status="PENDING",
                            transaction_leg="EXTERNAL",
                            lotto_agent_user_id=escrow_instance.lotto_agent_user_id,
                            lotto_agent_user_phone=escrow_instance.lotto_agent_user_phone,
                            unique_reference=escrow_instance.customer_reference,
                            callback_payload=fund_lotto_callback_payload
                        )

                        Transaction.serializer_trans(receiver_transaction)


                        fund_buddy_balance = WalletSystem.fund_balance(
                            user=buddy_user_instance,
                            wallet=wallet,
                            amount=new_buddy_amount,
                            trans_type=receiver_trans_type,
                            transaction_instance_id=receiver_transaction.transaction_id,
                            unique_reference=escrow_instance.escrow_id
                        )

                        buddy_balance_before = fund_buddy_balance["balance_before"]

                        buddy_balance_after = WalletSystem.get_balance_after(
                            user = buddy_user_instance,
                            balance_before=buddy_balance_before,
                            total_amount=new_buddy_amount,
                            is_credit=True
                        )




                        sender_transaction.status = "SUCCESSFUL"
                        receiver_transaction.status = "SUCCESSFUL"
                        receiver_transaction.balance_before = buddy_balance_before
                        receiver_transaction.balance_after = buddy_balance_after
                        
                        sender_transaction.save()
                        receiver_transaction.save()


                        if escrow_instance.transfer_type == "SEND_LOTTO_WALLET":
                            send_callback_out = send_callback_out_for_send_to_lotto.apply_async(
                                queue="processbulksheet",
                                kwargs={
                                    "instance_id": receiver_transaction.id
                                }
                            )
                        elif escrow_instance.transfer_type in ["SEND_AJO_WALLET", "SEND_AJO_LOANS"]:
                            send_callback_out = send_callback_out_for_send_to_ajo.apply_async(
                                queue="processbulksheet",
                                kwargs={
                                    "instance_id": receiver_transaction.id
                                }
                            )



                        # debit_credit_record = fund_buddy_balance["record"]
                        # debit_credit_record.transaction_instance_id = receiver_transaction.transaction_id
                        # debit_credit_record.save()



                ############################################################################################################################
                        # DEBIT SENDER SMS

                        sender_not_token=sender_user_instance.firebase_key
                        sender_not_title="Transaction Successful"
                        sender_not_body=f"You have successfully transfered N{amount} to {buddy_user_instance.bvn_full_name}"
                        sender_not_data={"amount_sent": f"{amount}", "available_balance": f"{sender_wallet.available_balance}"}


                        send_out_notification = cloud_messaging.send_broadcast(
                            token=sender_not_token,
                            title=sender_not_title,
                            body=sender_not_body,
                            data=sender_not_data
                        )

                        InAppTransactionNotification.create_in_app_transaction_notification(
                            user=sender_user_instance,
                            title=sender_not_title,
                            message_body=sender_not_body
                        )


                        if sender_trans_type == "SEND_BUDDY":

                            WalletSystem.transaction_alert_notfication_manager(
                                user=sender_user_instance,
                                amount=float(amount),
                                cr_dr="DR",
                                narration=f"{escrow_instance.narration}-TO {duo_nomenclature}-{wallet.user.bvn_full_name}",
                                from_wallet_type=sender_wallet.wallet_type,
                                transaction_instance_id=sender_transaction.transaction_id
                            )


                ############################################################################################################################
                        # CREDIT RECIEVER SMS

                        receiver_not_token=buddy_user_instance.firebase_key
                        receiver_not_title="Payment Received"
                        receiver_not_body=f"You have recieved a CREDIT of N{new_buddy_amount} from {sender_user_instance.bvn_full_name}"
                        receiver_not_data={"amount_sent": f"{new_buddy_amount}", "available_balance": f"{wallet.available_balance}"}

                        send_out_notification = cloud_messaging.send_broadcast(
                            token=receiver_not_token,
                            title=receiver_not_title,
                            body=receiver_not_body,
                            data=receiver_not_data
                        )

                        InAppTransactionNotification.create_in_app_transaction_notification(
                            user=buddy_user_instance,
                            title=receiver_not_title,
                            message_body=receiver_not_body
                        )

                        WalletSystem.transaction_alert_notfication_manager(
                            user=buddy_user_instance,
                            amount=float(new_buddy_amount),
                            cr_dr="CR",
                            narration=f"{escrow_instance.narration}-FRM {duo_nomenclature}-{sender_wallet.user.bvn_full_name}",
                            from_wallet_type=wallet.wallet_type,
                            transaction_instance_id=receiver_transaction.transaction_id
                        )
                        


                    

                    elif escrow_instance.transfer_type in ["LOTTO_PLAY", "LIBERTY_LIFE_PAYMENT", "CREDI_LOAN", "AUTO_REFUND", "FUNDS_RETRIEVAL", "SAVINGS", "RETAIL_AUTO_DEBIT"]:
                    
                        if escrow_instance.transfer_type == "LOTTO_PLAY":
                            sender_trans_type = "LOTTO_PLAY"
                            receiver_trans_type = "LOTTO_PLAY_CR"

                        elif escrow_instance.transfer_type == "LIBERTY_LIFE_PAYMENT":
                            sender_trans_type = "LIBERTY_LIFE_PAYMENT"
                            receiver_trans_type = "LIBERTY_LIFE_PAYMENT_CR"

                        elif escrow_instance.transfer_type == "CREDI_LOAN":
                            sender_trans_type = "CREDI_LOAN"
                            receiver_trans_type = "CREDI_LOAN_CR"

                        elif escrow_instance.transfer_type == "AUTO_REFUND":
                            sender_trans_type = "AUTO_REFUND"
                            receiver_trans_type = "AUTO_REFUND_CR"

                        elif escrow_instance.transfer_type == "FUNDS_RETRIEVAL":
                            sender_trans_type = "FUNDS_RETRIEVAL"
                            receiver_trans_type = "FUNDS_RETRIEVAL_CR"

                        elif escrow_instance.transfer_type == "SAVINGS":
                            sender_trans_type = "SAVINGS"
                            receiver_trans_type = "SAVINGS_CR"

                        elif escrow_instance.transfer_type == "RETAIL_AUTO_DEBIT":
                            sender_trans_type = "RETAIL_AUTO_DEBIT"
                            receiver_trans_type = "RETAIL_AUTO_DEBIT_CR"

                        else:
                            sender_trans_type = escrow_instance.transfer_type
                            receiver_trans_type = escrow_instance.transfer_type



                                    
                        if escrow_instance.liberty_commission > 0:
                            service_comm = amount - escrow_instance.liberty_commission
                        else:
                            service_comm = amount


                        receiver_reference = Transaction.create_liberty_reference(suffix="LGLP_FND_BUDDY")

                        receiver_transaction = Transaction.objects.create(
                            user=buddy_user_instance,
                            wallet_id=wallet.wallet_id,
                            wallet_type=wallet.wallet_type,
                            transaction_type=receiver_trans_type,
                            amount=service_comm,
                            liberty_reference=receiver_reference,
                            unique_reference=sender_transaction.unique_reference,
                            liberty_commission=escrow_instance.liberty_commission,
                            total_amount_received=service_comm,
                            escrow_id=escrow_instance.escrow_id,
                            source_account_name = sender_user_instance.bvn_full_name,
                            source_wallet_id=sender_wallet.wallet_id,
                            source_wallet_type=sender_wallet.wallet_type,
                            narration=escrow_instance.narration,
                            status="PENDING",
                            transaction_leg="EXTERNAL",
                        )

                        fund_service_user_balance = WalletSystem.fund_balance(
                            user=buddy_user_instance,
                            wallet=wallet,
                            amount=service_comm,
                            trans_type="RETAIL_AUTO_DEBIT_CR",
                            transaction_instance_id = receiver_transaction.transaction_id,
                            unique_reference=escrow_instance.escrow_id
                        )

                        service_user_balance_before = fund_service_user_balance["balance_before"]

                        service_user_balance_after = WalletSystem.get_balance_after(
                            user = buddy_user_instance,
                            balance_before=service_user_balance_before,
                            total_amount=service_comm,
                            is_credit=True
                        )


                        sender_transaction.status = "SUCCESSFUL"
                        receiver_transaction.status = "SUCCESSFUL"
                        receiver_transaction.balance_before = service_user_balance_before
                        receiver_transaction.balance_after = service_user_balance_after

                        sender_transaction.save()
                        receiver_transaction.save()


                        new_response = {
                            "status": True,
                            "message": "Agent has been charged successfully",
                            "escrow_id": escrow_instance.escrow_id,
                            "user_trans_id": receiver_transaction.transaction_id,
                        }



                        # Give Agent Commission If there is
                        if escrow_instance.transfer_type == "LOTTO_PLAY" and sender_transaction.narration == "LOTTO_PLAY" and sender_user_instance.terminal_id is not None:
                            agent_comm  = amount * 0.15
                            if escrow_instance.transfer_type == "LOTTO_PLAY":

                                get_sales_rep = OtherCommissionsRecord.check_if_sales_rep_exists(downline_user=sender_user_instance)


                                if get_sales_rep is None:
                                    new_agent_comm = agent_comm
                                    new_super_agent_comm = 0
                                    sales_rep = False

                                else:
                                    calc_super_agent_profit = (ConstantTable.get_constant_table_instance().lotto_super_agent_profit/100) * amount
                                    new_agent_comm = agent_comm - calc_super_agent_profit
                                    new_super_agent_comm = calc_super_agent_profit
                                    sales_rep = True


                                # Handle Agent Profit On Cash Out
                                if amount >= new_agent_comm + new_super_agent_comm:

                                    OtherCommissionsRecord.create_and_top_up_other_commissions_for_agent(
                                        agent = sender_user_instance,
                                        sales_rep = None,
                                        amount = amount,
                                        transaction_id = sender_transaction.transaction_id,
                                        transaction_type = escrow_instance.transfer_type,
                                        transaction_reason = "Commission on LOTTO Play",
                                        total_profit=agent_comm + escrow_instance.liberty_commission,
                                        liberty_profit=escrow_instance.liberty_commission,
                                        agent_cash_profit=new_agent_comm
                                    )

                                    if new_super_agent_comm > 0 and sales_rep == True:
                                        handle_ro_commission = OtherCommissionsRecord.create_and_top_up_sales_rep_commissions(
                                            agent = sender_user_instance,
                                            sales_rep = get_sales_rep.sales_rep,
                                            amount = amount,
                                            transaction_id = sender_transaction.transaction_id,
                                            transaction_type = "LOTTO_PLAY",
                                            transaction_reason = f"Commission on LOTTO PLAY by {sender_user_instance.bvn_first_name}" if sender_user_instance.bvn_first_name else f"Commission on LOTTO PLAY by {sender_user_instance.email}",
                                            total_profit = agent_comm + escrow_instance.liberty_commission,
                                            liberty_profit = escrow_instance.liberty_commission,
                                            ro_cash_profit = new_super_agent_comm,
                                            agent_cash_profit = new_agent_comm
                                        )


                        # DEBIT SENDER SMS

                        sender_not_token=buddy_user_instance.firebase_key
                        sender_not_title="Transaction Successful"
                        sender_not_body=f"You have successfully paid N{amount} for {escrow_instance.transfer_type}"
                        sender_not_data={"amount_sent": f"{amount}", "available_balance": f"{sender_wallet.available_balance}"}


                        send_out_notification = cloud_messaging.send_broadcast(
                            token=sender_not_token,
                            title=sender_not_title,
                            body=sender_not_body,
                            data=sender_not_data
                        )

                        InAppTransactionNotification.create_in_app_transaction_notification(
                            user=sender_user_instance,
                            title=sender_not_title,
                            message_body=sender_not_body
                        )


                        WalletSystem.transaction_alert_notfication_manager(
                            user=sender_user_instance,
                            amount=float(amount),
                            cr_dr="DR",
                            narration=f"{escrow_instance.narration}-TO BUDDY-{sender_user_instance.bvn_full_name}",
                            from_wallet_type=sender_wallet.wallet_type,
                            transaction_instance_id=sender_transaction.transaction_id
                        )


                ############################################################################################################################
                        # CREDIT RECIEVER SMS

                        receiver_not_token=buddy_user_instance.firebase_key
                        receiver_not_title="Payment Received"
                        receiver_not_body=f"You have recieved a CREDIT of N{service_comm} from {sender_user_instance.bvn_full_name}"
                        receiver_not_data={"amount_sent": f"{service_comm}", "available_balance": f"{wallet.available_balance}"}

                        send_out_notification = cloud_messaging.send_broadcast(
                            token=receiver_not_token,
                            title=receiver_not_title,
                            body=receiver_not_body,
                            data=receiver_not_data
                        )

                        InAppTransactionNotification.create_in_app_transaction_notification(
                            user=buddy_user_instance,
                            title=receiver_not_title,
                            message_body=receiver_not_body
                        )

                        WalletSystem.transaction_alert_notfication_manager(
                            user=buddy_user_instance,
                            amount=float(service_comm),
                            cr_dr="CR",
                            narration=f"{escrow_instance.narration}-FRM AGENT-{sender_user_instance.bvn_full_name}",
                            from_wallet_type=wallet.wallet_type,
                            transaction_instance_id=receiver_transaction.transaction_id
                        )
