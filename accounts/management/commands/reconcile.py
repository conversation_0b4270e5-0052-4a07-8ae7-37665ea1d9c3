from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db.models import Q, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Value, When, Sum, Count
from django.db import transaction
from django.conf import settings
from django.contrib.auth.hashers import make_password
from main.models import User, UserOtherAccount
from accounts.models import CommissionsRecord, OtherServiceAccountSystem, Transaction, TransferVerificationObject, AccountSystem, WalletSystem, DebitCreditRecordOnAccount, UserDebt, PromoCodeData, Escrow, OutOfBookTransfer, SendMoneyDumpData
from accounts.helpers.vfdbank_manager import VFDBank
from accounts.helpers.helper_func import one_way_decrypt_trans_notify
from uuid import UUID



import datetime
import pandas as pd
import pytz


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        date_to_start = datetime.datetime(2024, 1, 1)
        amount_to_add = 0
        rotation = {}

        CommissionsRecord.objects.select_related("user").filter(date_created__gte=date_to_start).update(reconciled=False)

        while True:
            get_comm_trx_qs = CommissionsRecord.objects.select_related("user").filter(user__id=842, reconciled=False, date_created__gte=date_to_start)

            get_last_zero_trans = get_comm_trx_qs.filter(balance_before=0).first()
            if not get_last_zero_trans:
                break

            get_end_of_qs = get_comm_trx_qs.filter(id__gte=get_last_zero_trans.id + 2, balance_before=0).first()
            if not get_end_of_qs:
                break

            get_threshold_trx = get_comm_trx_qs.filter(id__gte=get_last_zero_trans.id, id__lt=get_end_of_qs.id)

            total_sum_accurate = get_threshold_trx.aggregate(Sum("total_cost_of_acq"))["total_cost_of_acq__sum"] or 0
            total_sum_recorded = get_threshold_trx.last().balance_after

            get_threshold_trx.update(reconciled=True)
            amount_to_add += total_sum_accurate - total_sum_recorded

            rotation[f"{get_last_zero_trans.date_created.isoformat()} - {get_end_of_qs.date_created.isoformat()}"] = total_sum_accurate
        
        print(amount_to_add)
        print(rotation)

        # print(ParallexBank.notify_create_user(email=, ip=))
        # print(ParallexBank.notify_webhook_login())
        # print(ParallexBank.notify_webhook_get_trans_det(trans_id=, bearer_token=))

        # pass


        # data_qs = DebitCreditRecordOnAccount.objects.filter(id__in=[3343902, 3280322])
        # for data in data_qs:
        #     from_wallet = data.wallet
        #     amount = data.amount

        #     if not from_wallet.wallet_type == "OTHERS":
        #         return
                
        #     user = data.user
        #     to_wallet, created = WalletSystem.objects.get_or_create(user=user, wallet_type="COLLECTION")

            

        #     with transaction.atomic():
        #         WalletSystem.deduct_balance(
        #             user=user,
        #             wallet=from_wallet,
        #             amount=amount,
        #             trans_type="TO_COLLECTION_WALLET",
        #         )

        #         WalletSystem.fund_balance(
        #             user=user,
        #             wallet=to_wallet,
        #             amount=amount,
        #             trans_type="FROM_OTHERS_WALLET",
        #         )

            




        # data_qs = DebitCreditRecordOnAccount.objects.filter(id__in=[3355508])
        # for data in data_qs:

        #     user_wallet = data.wallet
        #     user_wallet.available_balance -= data.amount

        #     user_wallet.save()
        #     data.delete()


        
        # email_addresses = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
        # # email_addresses = ["<EMAIL>", "<EMAIL>"]
        
        
        # list_of_accounts = []
        
        # for email in email_addresses:
        #     new_user = User.objects.get(email=email)

        #     start_num = "2351" if new_user.type_of_user == "AJO_AGENT" else "2341" if new_user.type_of_user == "LIBERTY_RETAIL" else "2342"
        #     user_list = []
        
        #     for _ in range(1):
        #         create_other_account, new_user_bvn_rel, new_user_others = UserOtherAccount.create_other_account(user=new_user, phone_number=None, password=None, start_num=start_num)
        #         bvn_rel = new_user_bvn_rel

        #         company_name = OtherServiceAccountSystem.get_company_name(company_base="LIBERTYPAY", email=new_user.email)

        #         create_corporate = OtherServiceAccountSystem.arrange_corporate_detail(user=new_user, new_user=new_user_others, corporate_id="A41D7E" if settings.ENVIRONMENT == "development" else "CF1F5A", suffix=None, get_location=None, company_name=company_name)

        #         response = {
        #         "message": "Account successfully created",
        #         "data": create_other_account}
        #         user_list.append(response)
                
        #     list_of_accounts.append(user_list)

        # print(list_of_accounts)


        # main_entity.other_users.add(user_to_add)




        # savings_user = User.objects.get(email="savings@<EMAIL>")

        # for acct in OtherServiceAccountSystem.objects.filter():
        #     get_all_others = AccountSystem.objects.filter(account_type="OTHERS", account_number=acct.account_number).last()

        #     if get_all_others:
        #         acct.true_account_type = "OTHERS"
        #         if acct.account_type == "AJO_USER_CORPORATE":
        #             acct.requested_by = savings_user

        #         acct.save()
 


        
        # df = pd.DataFrame(table_needed)
        # df.to_csv("bad_bills.csv")
        # df['date_created'] = df['date_created'].apply(lambda x: x.astimezone(pytz.utc).replace(tzinfo=None))



        # current_time = timezone.now()
        # one_hour_ago = current_time - timezone.timedelta(hours=24)
        
        # table_needed = []
        # count = 1

        # all_trans = Transaction.objects.filter(transaction_type="FUND_BANK_TRANSFER", date_created__gte=one_hour_ago)
        # all_trans_count = all_trans.count()

        # for trans in all_trans:
        #     print(count, "remaining", all_trans_count - count)

            
        #     if trans.narration == "From Liberty/QLP_IN_HOUSE":
        #         data = {
        #             "trans_id": trans.id,
        #             "trans_trans_id": trans.transaction_id,
        #             "liberty_reference": trans.unique_reference,
        #             "trans_escrow": trans.escrow_id,
        #             "deb_cred_id": trans.debit_credit_record_id,
        #             "user_email": trans.user.email,
        #             "amount": trans.amount,
        #             "amount_settled": trans.amount - trans.liberty_commission,
        #             "trans_type": trans.transaction_type,
        #             "date_created": trans.date_created
        #         }
                    
        #         table_needed.append(data)


        #     count += 1




                    
        # problem_ids = []
        # problem_escrows = []
        # for trans in TransferVerificationObject.objects.filter(date_added__gte=one_hour_ago, transaction_type="REVERSAL_BANK_TRANSFER", transaction_leg="REVERSAL", transaction_ver_status="SUCCESSFUL"):
        #     escrow_id = trans.escrow_id

        #     escrow_instance = Escrow.objects.get(escrow_id=escrow_id)

        #     get_deb_cred = DebitCreditRecordOnAccount.objects.filter(transaction_instance_id=trans.transaction_instance.transaction_id).first()

        #     if get_deb_cred:
        #         get_deb_cred.unique_reference = f"{escrow_id}_RVSL"
        #         get_deb_cred.save()

        #     else:

        #         escrow_instance.reversed = False
        #         escrow_instance.save()

        #         trans.is_finished_verification = False
        #         trans.transaction_ver_status = "NOT_INITIATED"
        #         trans.save()

        #         problem_ids.append(trans.id)
        #         problem_escrows.append(escrow_id)

            
        # print(problem_ids)
        # print(problem_escrows)
            


        # trans_type_needed = ["FUND_LOTTO_WALLET", "FUND_COLLECTION_ACCOUNT", "FUND_BUDDY", "LOTTO_PLAY_CR", "CREDI_LOAN_CR", "AUTO_REFUND_CR", "FUNDS_RETRIEVAL_CR", "SAVINGS_CR", "RETAIL_AUTO_DEBIT_CR"]



        # trans_type_needed = ["FUND_LOTTO_WALLET", "FUND_COLLECTION_ACCOUNT", "FUND_BUDDY", "LOTTO_PLAY_CR", "CREDI_LOAN_CR", "AUTO_REFUND_CR", "FUNDS_RETRIEVAL_CR", "SAVINGS_CR", "RETAIL_AUTO_DEBIT_CR"]

        # five_minutes_ago = timezone.now() - timezone.timedelta(minutes=15)
        # five_days_ago = timezone.now() - timezone.timedelta(days=2)

        # table_needed = []
        # count = 1

        # all_trans = Transaction.objects.filter(transaction_type__in=trans_type_needed, date_created__lte=five_minutes_ago, date_created__gte=five_days_ago)

        # all_trans_count = all_trans.count()
        # print(all_trans_count)

        # for trans in all_trans:

        #     print(count, "remaining", all_trans_count - count)

        #     if DebitCreditRecordOnAccount.objects.filter(transaction_instance_id=trans.transaction_id).exists():
        #         pass
        #     else:

        #         data = {
        #             "trans_id": trans.id,
        #             "trans_trans_id": trans.transaction_id,
        #             "trans_escrow": trans.escrow_id,
        #             "deb_cred_id": trans.debit_credit_record_id,
        #             "user_email": trans.user.email,
        #             "amount": trans.amount,
        #             "trans_type": trans.transaction_type,
        #             "date_created": trans.date_created
        #         }
                    
        #         table_needed.append(data)


        #     count += 1

        


        # df = pd.DataFrame(table_needed)
        # df.to_csv("bad_funding.csv")
        # df['date_created'] = df['date_created'].apply(lambda x: x.astimezone(pytz.utc).replace(tzinfo=None))


        

            # data_qs = DebitCreditRecordOnAccount.objects.filter(transaction_instance_id="61158e0f-9b25-48ad-a543-ec513a3dfb04", user__email="<EMAIL>", type_of_trans="FUND_BANK_TRANSFER").last()

            # user_wallet = data_qs.wallet
            # user_wallet.available_balance += data_qs.amount

            # user_wallet.save()
            #     data.delete()

        # new_data = WalletSystem.objects.filter(user__email="<EMAIL>", wallet_type="COLLECTION").last()
        # new_data.available_balance += float(497.01)
        # new_data.save()



        # amount = data.amount
        # user = data.user
        # wallet_type = data.wallet.wallet_type
        # trans_type = data.type_of_trans
        # trans_id = data.transaction_instance_id
        # reversal_trans_id = data.reversal_trans_id
        # unique_reference = data.unique_reference

        # data.delete()

        # fund_wallet = WalletSystem.fund_balance(
        #     user=user,
        #     wallet=walletasadasde,
        #     amount=amount,
        #     trans_type=trans_type,
        #     transaction_instance_id = trans_id,
        #     reversal_trans_id = reversal_trans_id,
        #     unique_reference = unique_reference,
        # )
        

        # for trans in UserDebt.objects.filter(is_active=True):
        #     UserDebt.debit_user_debt(trans=trans)

        # pass

        # today = datetime.datetime.now().date()

        # # for trans in DebitCreditRecordOnAccount.objects.filter(date_created__date=today, type_of_trans="SEND_BANK_EXTERNAL", transaction_instance_id=None):
        # for trans in DebitCreditRecordOnAccount.objects.filter(id__in=[3914281,3914270,3914193,3914149,3913985,3913012,3912340,3909773,3909753,3908818,3908816,3908596,3904534,3897602], type_of_trans="SEND_BANK_EXTERNAL", transaction_instance_id=None):
        #     amount = trans.amount
        #     user = trans.user
        #     wallet_instance = trans.wallet

        #     trans.transaction_instance_id = "FAILED"
        #     trans.save()

        #     new_trans_type = "MANUAL_RETURN"
        #     new_trans_id = f"MANUAL_RETURN-{trans.id}"

        #     try:
        #         escrow_instance = Escrow.objects.get(debit_credit_record_id=trans.id)
        #         escrow_instance.reversed = True
        #         escrow_instance.internal_escrow = False
        #         escrow_instance.external_escrow = False
        #         escrow_instance.save()

        #         fund_wallet = WalletSystem.fund_balance(
        #             user=user,
        #             wallet=wallet_instance,
        #             amount=amount,
        #             trans_type=new_trans_type,
        #             transaction_instance_id = new_trans_id,
        #             unique_reference = new_trans_id,
        #         )
        #     except:
        #         pass




        


            


        # hello = ParallexBank.get_all_banks()

        # start_date = datetime.date(2023, 7, 28)
        # end_date = datetime.date(2023, 8, 9)

        # today = datetime.datetime.now().date()
        # table_needed = []
        # # trans_qs = Transaction.objects.filter(transaction_type__in=["CARD_TRANSACTION_FUND", "CARD_TRANSACTION_FUND_TRANSFER"], date_created__date__lte=end_date, date_created__date__gte=start_date)

        # needed_ids = []
        
        
        # trans_qs = Transaction.objects.filter(transaction_id__in=needed_ids, transaction_type__in=["CARD_TRANSACTION_FUND", "CARD_TRANSACTION_FUND_TRANSFER"]).order_by('id')
        # # print(trans_qs)

        # main_trans = []
        # other_trans_list = []

        # all_trans_num = trans_qs.count()
        # print(all_trans_num)

        # count = 1


        # for trans in trans_qs:
        #     print(count)
        #     if "approved" in trans.provider_status.casefold():
        #         pass
        #     else:
        #         get_others = Transaction.objects.filter(unique_reference=trans.unique_reference, status="SUCCESSFUL")
        #         for one in get_others:
        #             one.status = "FAILED"
        #             one.save()


        # for trans in trans_qs:
        #     print(count)

        #     if trans.status == "FAILED":
        #         pass
        #     else:

        #         if trans not in other_trans_list:
        #             get_others = Transaction.objects.filter(unique_reference=trans.unique_reference, status="SUCCESSFUL")
        #             if get_others.count() > 1:
        #                 print("entered")
        #                 first_trans = get_others.first()
        #                 print(first_trans)
        #                 other_trans_list.extend(list(get_others.exclude(transaction_id=first_trans.transaction_id)))

        #                 if first_trans not in main_trans:
        #                     main_trans.append(first_trans)

        #                 # for tr in other_trans:
        #                 #     if tr not in other_trans_list:
        #                 #         other_trans_list.extend(other_trans)

        #                 data = {
        #                     "trans_id": trans.id,
        #                     "trans_trans_id": trans.transaction_id,
        #                     "trans_escrow": trans.escrow_id,
        #                     "deb_cred_id": trans.debit_credit_record_id,
        #                     "user_email": trans.user.email,
        #                     "unique_reference": trans.unique_reference,
        #                     "amount": trans.amount,
        #                     "count": get_others.count(),
        #                     "date_created": trans.date_created
        #                 }
                        
        #                 table_needed.append(data)

        #     count += 1
            
        
        # print(other_trans_list)
        
        




        # df = pd.DataFrame(table_needed)
        # # df['date_created'] = df['date_created'].apply(lambda x: x.astimezone(pytz.utc).replace(tzinfo=None))

        # df.to_csv("repeated_trans.csv")

        # from accounts.models import WalletSystem

        # new_table_needed = []

        # for deb in other_trans_list:
        #     amount = deb.amount
        #     user = deb.user
        #     wallet_instance = WalletSystem.objects.filter(user=user, wallet_type="COLLECTION").first()

        #     print(wallet_instance)

        #     if wallet_instance.available_balance >= amount:
        #         is_debited = True

        #         deduct_balance = WalletSystem.deduct_balance(
        #             user=user,
        #             wallet=wallet_instance,
        #             amount=amount,
        #             trans_type="FAILED_CASH_OUT_RETURN",
        #             transaction_instance_id=deb.transaction_id
        #         )

        #         debit_id = deduct_balance.get("debit_credit_record_id")

        #         deb.status = "FAILED"
        #         deb.save()

            
        #     else:
        #         is_debited = False
        #         debit_id = None


        #     data = {
        #         "trans_id": deb.id,
        #         "trans_trans_id": deb.transaction_id,
        #         "trans_escrow": deb.escrow_id,
        #         "deb_cred_id": deb.debit_credit_record_id,
        #         "user_email": deb.user.email,
        #         "unique_reference": deb.unique_reference,
        #         "amount": deb.amount,
        #         "date_created": deb.date_created,
        #         "is_debited": is_debited,
        #         "new_deb_cred_id": debit_id,
        #     }
            
        #     new_table_needed.append(data)

        # new_df = pd.DataFrame(new_table_needed)
        # new_df.to_csv("new_repeated_trans.csv")

















        # start_date = datetime.date(2023, 7, 28)
        # end_date = datetime.date(2023, 8, 9)

        # today = datetime.datetime.now().date()
        # table_needed = []
        # # trans_qs = Transaction.objects.filter(transaction_type__in=["CARD_TRANSACTION_FUND", "CARD_TRANSACTION_FUND_TRANSFER"], date_created__date__lte=end_date, date_created__date__gte=start_date)
        # trans_qs = DebitCreditRecordOnAccount.objects.filter(date_created__date=today, type_of_trans="FAILED_CASH_OUT_RETURN")



        # changed_users = []
        # reversed_users = []

        # all_trans_num = trans_qs.count()
        # print(all_trans_num)

        # count = 1



        # for trans in trans_qs:
        #     print(count)

        #     all_user_trans = DebitCreditRecordOnAccount.objects.filter(user=trans.user, date_created__date=today)
        #     if all_user_trans.exclude(type_of_trans="FAILED_CASH_OUT_RETURN").exists():


        #         if trans != all_user_trans.last():

        #             if trans.user.email not in changed_users:
        #                 changed_users.append(trans.user.email)

        #                 data = {
        #                     "trans_trans_id": trans.transaction_instance_id,
        #                     "deb_cred_id": trans.id,
        #                     "user_email": trans.user.email,
        #                     "amount": trans.amount,
        #                     "date_created": trans.date_created
        #                 }
                        
        #                 table_needed.append(data)
                
        #         else:
        #             get_needed_trans = Transaction.objects.filter(transaction_id=trans.transaction_instance_id).last()
        #             if get_needed_trans:
        #                 get_needed_trans.status = "SUCCESSFUL"
        #                 get_needed_trans.save()

        #                 user_wallet = trans.wallet
        #                 user_wallet.available_balance += trans.amount

        #                 user_wallet.save()
        #                 trans.delete()
                
            
        #     else:
        #         get_needed_trans = Transaction.objects.filter(transaction_id=trans.transaction_instance_id).last()
        #         if get_needed_trans:
        #             get_needed_trans.status = "SUCCESSFUL"
        #             get_needed_trans.save()

        #             user_wallet = trans.wallet
        #             user_wallet.available_balance += trans.amount

        #             user_wallet.save()
        #             trans.delete()


        #     count += 1
        
        


        # if table_needed:

        #     df = pd.DataFrame(table_needed)
        #     # df['date_created'] = df['date_created'].apply(lambda x: x.astimezone(pytz.utc).replace(tzinfo=None))

        #     df.to_csv("repeated1_trans.csv")


        

        # pass

        # # account_numbers_needed = OtherServiceAccountSystem.objects.filter(user__in=users_needed).values_list('account_number', flat=True)
        # # account_numbers_needed = OtherServiceAccountSystem.objects.filter(requested_by__email="<EMAIL>", user__in=users_needed).values_list('account_number', flat=True)
        # # users_needed = User.objects.filter(id__in=[1,3])

        

        # users_needed = User.objects.filter(type_of_user="LOTTO_AGENT")

        # one_day_ago = datetime.date.today() - datetime.timedelta(days=1)
        # # print(one_day_ago)

        # # today = datetime.datetime.now().date()

        # # yesterday = today - datetime.timedelta(days=1)

        # start_date = datetime.date(2023, 3, 31)
        # end_date = datetime.date(2023, 5, 1)

        # # .values('date_created', 'user__phone_number', 'user__email', 'user__id', 'amount', 'liberty_commission', 'transaction_type', 'beneficiary_nuban', 'liberty_reference', 'escrow_id')
        # # transactions_needed_v1 = Transaction.objects.filter(date_created__gte=start_date, date_created__lt=end_date).filter(transaction_type="REVERSAL_BANK_TRANSFER_IN")
        # transactions_needed_v1 = Transaction.objects.filter(date_created__gte=start_date, date_created__lt=end_date).filter(transaction_type="SEND_BANK_TRANSFER", is_reversed=True)
        
        # for trans in transactions_needed_v1:
        #     get_escrow_id = trans.escrow_id
        #     get_reverse_type

        #     transactions_needed = Transaction.objects.filter(date_created__gte=start_date, date_created__lt=end_date).filter(Q(transaction_type__in=["SEND_BANK_TRANSFER"]) | Q(transaction_type__in=["LOTTO="REMITTANCE_DEDUCTION")).values('date_created', 'user__phone_number', 'user__email', 'user__id', 'amount', 'liberty_commission', 'transaction_type', 'beneficiary_nuban', 'liberty_reference', 'escrow_id')

        # # df = pd.DataFrame(transactions_needed)
        # # df['date_created'] = df['date_created'].apply(lambda x: x.astimezone(pytz.utc).replace(tzinfo=None))

        # # df['amount_minus_commission'] = df['amount'] - (df['liberty_commission'] if df['liberty_commission'] is not None else 0)

        # # # Group transactions by user and transaction type, and calculate the sum of the 'amount' column for each group
        # # # grouped = df.groupby(['user__email', 'transaction_type']).agg({'amount_minus_commission': 'sum'})
        # # grouped = df.groupby(['user__email', 'transaction_type']).agg({'amount': 'sum', 'amount_minus_commission': 'sum'})


        # # # pivot_table = pd.pivot_table(grouped, values='amount_minus_commission', index=['user__email'], columns=['transaction_type'])
        # # pivot_table = pd.pivot_table(grouped, values=['amount', 'amount_minus_commission'], index=['user__email'], columns=['transaction_type'])


        # # # pivot_table.columns = [col + '_total' for col in pivot_table.columns]
        # # pivot_table.columns = [col[0] + '_' + col[1] + '_total' for col in pivot_table.columns]



        # # pivot_table.fillna(0, inplace=True)
        # # # pivot_table = pivot_table.assign(sending_total=pivot_table['SEND_LOTTO_WALLET_total'] + pivot_table['SEND_COLLECTION_ACCOUNT_total'])
        # # # pivot_table = pivot_table.assign(receiving_total=pivot_table['FUND_LOTTO_WALLET_total'] + pivot_table['FUND_COLLECTION_ACCOUNT_total'])

        # # pivot_table = pivot_table.assign(sending_total=pivot_table['amount_SEND_LOTTO_WALLET_total'] + pivot_table['amount_SEND_COLLECTION_ACCOUNT_total'])
        # # pivot_table = pivot_table.assign(receiving_total=pivot_table['amount_FUND_LOTTO_WALLET_total'] + pivot_table['amount_FUND_COLLECTION_ACCOUNT_total'])
        # # pivot_table = pivot_table.assign(plus_lotto_play_total=pivot_table['amount_SEND_LOTTO_WALLET_total'] + pivot_table['amount_SEND_COLLECTION_ACCOUNT_total'] + pivot_table['amount_LOTTO_PLAY_total'])

        # # pivot_table = pivot_table.assign(sending_total=pivot_table['amount_minus_commission_SEND_LOTTO_WALLET_total'] + pivot_table['amount_minus_commission_SEND_COLLECTION_ACCOUNT_total'])
        # # pivot_table = pivot_table.assign(receiving_total=pivot_table['amount_minus_commission_FUND_LOTTO_WALLET_total'] + pivot_table['amount_minus_commission_FUND_COLLECTION_ACCOUNT_total'])
        # # pivot_table = pivot_table.assign(plus_lotto_play_total=pivot_table['amount_minus_commission_SEND_LOTTO_WALLET_total'] + pivot_table['amount_minus_commission_SEND_COLLECTION_ACCOUNT_total'] + pivot_table['amount_minus_commission_LOTTO_PLAY_total'])



        # # pivot_table.to_excel("reconcile.xlsx")



        



























        # # one_day_ago = datetime.date.today() - datetime.timedelta(days=1)
        # # print(one_day_ago)
        # # start_date = datetime.date(2023, 4, 1)
        # # end_date = datetime.date(2023, 4, 30)


        # # transactions_needed = Transaction.objects.filter(date_created__lte=end_date, date_created__gte=start_date).filter(transaction_type="SEND_LIBERTY_COMMISSION").values('date_created', 'amount', 'liberty_commission', 'transaction_type', 'beneficiary_nuban')
        # df = pd.DataFrame(transactions_needed)
        # df['date_created'] = df['date_created'].apply(lambda x: x.astimezone(pytz.utc).replace(tzinfo=None))




        # df.to_excel("reconss.xlsx")




























        # account_numbers_needed = OtherServiceAccountSystem.objects.filter(user__in=users_needed).values_list('account_number', flat=True)
        # account_numbers_needed = OtherServiceAccountSystem.objects.filter(requested_by__email="<EMAIL>", user__in=users_needed).values_list('account_number', flat=True)
        # users_needed = User.objects.filter(id__in=[1,3])

        

        # users_needed = User.objects.filter(type_of_user="LOTTO_AGENT")

        # one_day_ago = datetime.date.today() - datetime.timedelta(days=1)
        # # print(one_day_ago)

        # # today = datetime.datetime.now().date()

        # # yesterday = today - datetime.timedelta(days=1)

        # start_date = datetime.date(2022, 5, 1)
        # end_date = datetime.date(2023, 5, 17)



        # transactions_needed_v1 = Transaction.objects.filter(date_created__gte=start_date, date_created__lt=end_date).filter(Q(transaction_type__in=["SEND_LIBERTY_COMMISSION"]))
        
        # escrow_ids = transactions_needed_v1.values_list('escrow_id', flat=True).distinct()
        # transactions_with_same_escrow = Transaction.objects.filter(escrow_id__in=escrow_ids).values('date_created', 'user__phone_number', 'user__email', 'user__id', 'amount', 'liberty_commission', 'transaction_type', 'transaction_sub_type', 'transaction_leg', 'beneficiary_nuban', 'liberty_reference', 'escrow_id', 'unique_reference')

        # transactions_needed = transactions_with_same_escrow.annotate(trans_new_type=Case(
        #     When(transaction_type="SEND_LIBERTY_COMMISSION", then='transaction_type'),
        #     default=Value(''),
        #     output_field=CharField(),
        # ))

        # transactions_needed = transactions_with_same_escrow.annotate(trans_new_type=Case(
        #     When(
        #         Q(escrow_id__in=escrow_ids) & Q(transaction_type="SEND_LIBERTY_COMMISSION"),
        #         then='transaction_type'
        #     ),
        #     default=Value(''),
        #     output_field=CharField(),
        # ))

        # transactions_needed = TransferVerificationObject.objects.exclude(transaction_type__in=["SEND_BUDDY", "FUND_BUDDY", "SEND_COLLECTION_ACCOUNT", "FUND_COLLECTION_ACCOUNT", "SEND_LOTTO_WALLET", "FUND_LOTTO_WALLET"]).filter(date_created__gte=datetime.date(2023, 3, 1), date_created__lt=datetime.date(2023, 3, 11)).values()
        aprstart_date = datetime.datetime(2023, 4, 1)
        aprend_date = datetime.datetime(2023, 5, 1)
        
        start_date = datetime.datetime(2023, 5, 1)
        end_date = datetime.datetime(2023, 6, 1)



        # first_transactions_needed = TransferVerificationObject.objects.exclude(Q(transaction_ver_status__in=["NOT_INITIATED", "NOT_FOUND"]) | Q(transaction_type="REVERSAL", transaction_leg="INTERNAL") | Q(verf_timestamp__isnull=False)).filter(date_added__gte=datetime.datetime(2023, 5, 1), date_added__lt=datetime.datetime(2023, 6, 16))

        # for transss in first_transactions_needed:
        #     print(transss.transaction_leg)
        #     print(transss.transaction_type)

        #     data = transss.verification_payload

        #     try:
        #         verf_timestamp = eval(data).get("data", {}).get("transactionDate")
        #     except TypeError:
        #         verf_timestamp = data.get("data", {}).get("transactionDate")
        #     except:
        #         verf_timestamp = None

        #     if verf_timestamp is None:
        #         print("no timestamp", transss.id, transss.transaction_instance.transaction_id)

        #     transss.verf_timestamp = verf_timestamp
        #     transss.save()




        # transactions_needed = TransferVerificationObject.objects.filter(date_added__gte=datetime.datetime(2023, 3, 1), date_added__lt=datetime.datetime(2023, 6, 23)).values()
        # df = pd.DataFrame(transactions_needed)

        # df['date_added'] = df['date_added'].apply(lambda x: x.astimezone(pytz.utc).replace(tzinfo=None))
        # df['last_updated'] = df['last_updated'].apply(lambda x: x.astimezone(pytz.utc).replace(tzinfo=None))
        # df['verf_timestamp'] = pd.to_datetime(df['verf_timestamp'])



        # aprfiltered_df = df[(df['verf_timestamp'] >= aprstart_date) & (df['verf_timestamp'] < aprend_date)]
        # filtered_df = df[(df['verf_timestamp'] >= start_date) & (df['verf_timestamp'] < end_date)]

        # aprfiltered_df.to_excel("trans_verf_apr.xlsx")
        # filtered_df.to_excel("trans_verf_may.xlsx")









        # table_needed = []
        # accounts_needed = AccountSystem.objects.filter(account_provider="VFD", account_type__in=["COLLECTION", "OTHERS"])
        # print(f"Total accounts: {accounts_needed.count()}")

        # # Counter variable to keep track of loop iteration
        # iteration_count = 0

        # for account in accounts_needed:
        #     iteration_count += 1

        #     print("We are at", iteration_count)

        #     vfd_balance = VFDBank.get_vfd_float_balance(account_number=account.account_number)

        #     if vfd_balance is not None and vfd_balance > 0:

        #         # get_transactions = Transaction.objects.filter(user=account.user, status__in=["PENDING", "IN_PROGRESS"]).filter(Q(transaction_type__in=["SEND_BANK_TRANSFER", "FUND_BANK_TRANSFER"]) | Q(transaction_type="SEND_LIBERTY_COMMISSION", transaction_sub_type="MAIN_TRSF_COMM"))
        #         # if get_transactions:

        #         #     transaction_sum = get_transactions.aggregate(total_amount=Sum('amount'))['total_amount']
        #         #     extra_fee_sum = get_transactions.aggregate(total_fee=Sum('extra_fee'))['total_fee']
        #         #     net_amount = transaction_sum - extra_fee_sum
        #         # else:
        #         #     net_amount = 0

                # data = {
                #     "user_id": account.user.id,
                #     "user_email": account.user.email,
                #     "account_name": account.account_name,
                #     "account_number": account.account_number,
                #     "available_balance": vfd_balance,
                #     # "pending_balance": net_amount,
                # }

        #         table_needed.append(data)
            
        #     else:
        #         pass


        # Create a dataframe from the list
        # df = pd.DataFrame(table_needed)
        # df.to_csv("accbalmain4.csv")








        # table_needed = []

        # for trans in TransferVerificationObject.objects.filter(transaction_type="RE_FAILED_TRANSFER_IN", transaction_ver_status="NOT_INITIATED"):
            
            
            # liberty_reference = trans.liberty_reference
            # get_internal_trans = TransferVerificationObject.objects.filter(escrow_id=trans.escrow_id, transaction_leg="INTERNAL").first()

            # verify_trans = VFDBank.vfd_transaction_verification_handler(reference=liberty_reference)

            # if isinstance(verify_trans, dict):
            #     trans_status_code = verify_trans.get("status") if not verify_trans.get("data") else verify_trans["data"].get("transactionStatus")
            #     get_account_num = get_internal_trans.transaction_instance.beneficiary_nuban

            #     get_balance = VFDBank.get_vfd_float_balance(account_number=get_account_num)


            #     data = {
            #         "trans_id": trans.id,
            #         "trans_escrow": trans.escrow_id,
            #         "internal_trans_ref": get_internal_trans.liberty_reference,
            #         "trans_status_code": trans_status_code,
            #         "account_number": get_account_num,
            #         "amount": trans.amount,
            #         "available_balance": get_balance,
            #         "pending_balance": get_balance-trans.amount,
            #     }
                
            #     table_needed.append(data)


            # trans.transaction_instance.liberty_reference = trans.liberty_reference
            # trans.transaction_instance.save()
                


        # Create a dataframe from the list
        # df = pd.DataFrame(table_needed)
        # df.to_csv("pending_vfd2.csv")



        # print(len(table_needed))



        # debit_record = DebitCreditRecordOnAccount.objects.filter(id__in=[2696124, 2699797, 2699789, 2699788], transaction_instance_id=None, type_of_trans="TRANSFER_BILLS_PAY_COMM")

        # debit_record = DebitCreditRecordOnAccount.objects.filter(id__in=[], type_of_trans="TRANSFER_BILLS_PAY_COMM")

        # for drcr in debit_record:
        #     print(drcr.id)
        #     user_wallet = drcr.wallet
        #     user_wallet.available_balance -= drcr.amount
        #     # drcr.transaction_instance_id = "FAILED"

        #     # drcr.save()
        #     user_wallet.save()
        #     drcr.delete()

        # for trans in TransferVerificationObject.objects.filter(transaction_leg="RE_INTERNAL"):
            
        #     escrow_id = trans.escrow_id 
        #     get_internal = TransferVerificationObject.objects.filter(escrow_id=escrow_id, transaction_leg="INTERNAL").first()
        #     if get_internal:
                
        #         try:
        #             verf_timestamp = eval(get_internal.raw_payload_response).get("data", {}).get("transactionDate")
        #         except TypeError:
        #             verf_timestamp = get_internal.raw_payload_response.get("data", {}).get("transactionDate")
        #         except:
        #             verf_timestamp = None

        #         get_internal.trans_status_code = "00"
        #         get_internal.verf_timestamp = verf_timestamp
        #         get_internal.save()

        #         print(get_internal.date_added)


        # now = datetime.datetime.now()

        # ten_minutes_before = timezone.now() - timezone.timedelta(days=2)

        # five_days_ago = timezone.now() - timezone.timedelta(days=100)

        # for transaction in TransferVerificationObject.objects.filter(transaction_leg="COMMISSIONS", date_added__lte=ten_minutes_before, date_added__gte=five_days_ago):
        #     transaction.transaction_sub_type = transaction.transaction_instance.transaction_sub_type
        #     transaction.save()


        # result =  one_way_decrypt_trans_notify('QhE5Ia71NiYQOo2re1OZJZ41C/LQ0sGDGSYlZ76IBax0MDZtQW44MVi7GH+tM8gp')

        # print(result)
        # print(type(result))

        # from accounts.tasks import handle_interval_reconciliation

        # data_list = TransferVerificationObject.objects.filter(liberty_reference__in=[])

        # for transaction_verf in data_list:

        #     escrow_id = transaction_verf.escrow_id


        #     handle_interval_reconciliation.apply_async(
        #         queue="intervrecon",
        #         kwargs={
        #             "escrow_id": escrow_id,
        #             "verf_id": transaction_verf.id
        #         }
        #     )



        pass

        







