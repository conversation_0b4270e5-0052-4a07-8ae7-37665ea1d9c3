import pandas as pd
from django.core.management.base import BaseCommand, CommandError
from accounts.models import Transaction
from main.helper.send_emails import send_email_with_parameters
from datetime import datetime, timedelta


class Command(BaseCommand):
    help = "Send a CSV of the previous day's transactions to the specified email address"

    def add_arguments(self, parser):
        parser.add_argument('--email', dest='email', type=str, help="Email address to send the CSV to", required=True)
        parser.add_argument('--date', dest='date', type=str, help="Optional: Date to get transactions for (format: DD-MM-YYYY). Defaults to previous day.")

    def handle(self, *args, **options):
        email = options.get('email')
        date_str = options.get('date')

        if not email:
            raise CommandError('Email address is required')

        # Determine the date to get transactions for
        if date_str:
            try:
                target_date = datetime.strptime(date_str, "%d-%m-%Y").date()
            except ValueError:
                raise CommandError('Invalid Date Format. Use DD-MM-YYYY')
        else:
            # Default to previous day
            target_date = datetime.now().date() - timedelta(days=1)

        self.stdout.write(self.style.SUCCESS(f'Fetching transactions for {target_date}'))

        # Get transactions for the specified date
        transactions = Transaction.objects.filter(date_created__date=target_date).select_related('user')

        if not transactions.exists():
            self.stdout.write(self.style.WARNING(f'No transactions found for {target_date}'))
            return

        # Create a DataFrame from the transactions
        transaction_data = []
        for transaction in transactions:
            transaction_data.append({
                'Transaction ID': str(transaction.transaction_id),
                'Date': transaction.date_created.strftime('%Y-%m-%d %H:%M:%S'),
                'User Email': transaction.user.email if transaction.user else 'N/A',
                'User ID': transaction.user.id if transaction.user else 'N/A',
                'Transaction Type': transaction.transaction_type,
                'Amount': transaction.amount,
                'Liberty Commission': transaction.liberty_commission,
                'Status': transaction.status,
                'Liberty Reference': transaction.liberty_reference,
                'Wallet Type': transaction.wallet_type if hasattr(transaction, 'wallet_type') else 'N/A',
                'Balance Before': transaction.balance_before if hasattr(transaction, 'balance_before') else 'N/A',
                'Balance After': transaction.balance_after if hasattr(transaction, 'balance_after') else 'N/A',
            })

        df = pd.DataFrame(transaction_data)

        # Format the date for the filename and email subject
        formatted_date = target_date.strftime('%Y-%m-%d')
        
        # Send the email with the CSV attachment
        message_body = f"Please find attached the transactions for {formatted_date}."
        
        self.stdout.write(self.style.SUCCESS(f'Sending email to {email}'))
        
        try:
            send_email_with_parameters(
                email=email,
                subject=f"Transactions Report for {formatted_date}",
                message_body=message_body,
                from_email="<EMAIL>",
                attachment=df,
                attachment_name=f"transactions_{formatted_date}"
            )
            self.stdout.write(self.style.SUCCESS(f'Email sent successfully to {email}'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Failed to send email: {str(e)}'))
