from django.utils import timezone
from django.core.management.base import BaseCommand
from django.conf import settings
from accounts.helpers.helper_func import notify_admin_whatsapp_on_pending_trans
from accounts.models import Transaction


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):

        ten_minutes_ago = timezone.now() - timezone.timedelta(minutes=10)
        five_days_ago = timezone.now() - timezone.timedelta(days=5)

        pending_in_progress_trans = Transaction.objects.exclude(transaction_type="SEND_LIBERTY_COMMISSION").exclude(transaction_leg="TEMP_EXTERNAL").filter(date_created__lte=ten_minutes_ago, date_created__gte=five_days_ago, status__in=["IN_PROGRESS", "PENDING"])
        # pending_in_progress_trans = Transaction.objects.filter(date_created__lte=ten_minutes_ago, date_created__gte=five_days_ago, status__in=["IN_PROGRESS", "PENDING"])

        if pending_in_progress_trans:
            pending_trans_id_list = [f"{trns.transaction_id} ({trns.transaction_type})" for trns in pending_in_progress_trans]
            
            list_of_types = [f"({trns.transaction_type})" for trns in pending_in_progress_trans]
            trans_count = [ f"{i} - {list_of_types.count(i)}" for i in set(list_of_types) ]
        

            for num in ["2347039516293", "2348077469471", "2348167631246", "2348031346306", "2347060900294", "2348168187776", "2348090643057"]:
                notify_admin_whatsapp_on_pending_trans(num, pending_trans_id_list, trans_count)
