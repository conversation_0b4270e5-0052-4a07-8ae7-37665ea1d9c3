from django.core.management.base import BaseCommand
from django.conf import settings
from accounts.models import AccountSystem, RawPayStack, PayStackTransaction
from accounts.helpers.vfdbank_manager import VFDBank
from accounts.helpers.general_account_manager import send_paystack_data_to_savings, send_paystack_data_to_marketing
from accounts.tasks import get_all_acct_balances
from main.models import User

import pandas as pd
import datetime
import json


class Command(BaseCommand):
    help = ""

    def add_arguments(self, parser):
        parser.add_argument('--incl', dest='incl', type=str, help="This contains a list of account numbers to be included.")

    def handle(self, *args, **options):
        include_accts = options.get('incl')

        get_all_acct_balances.apply_async(
            queue="sendbulktrans",
            kwargs={
                "include_accts": include_accts,
            }
        )




        # accounts_needed = AccountSystem.objects.filter(account_provider="VFD", account_type__in=["COLLECTION", "OTHERS"]).distinct("account_number")
        # print(f"Total accounts: {accounts_needed.count()}")




        # # Counter variable to keep track of loop iteration
        # table_needed = []
        # iteration_count = 0

        # for account in accounts_needed:
        #     iteration_count += 1

        #     print("We are at", iteration_count)

        #     vfd_balance = VFDBank.get_vfd_float_balance(account_number=account.account_number)

        #     if vfd_balance is not None and vfd_balance > 0:

        #         data = {
        #             "user_email": account.user.email,
        #             "account_name": account.account_name,
        #             "account_number": account.account_number,
        #             "available_balance": vfd_balance,
        #         }

        #         table_needed.append(data)
            
        #     else:
        #         pass


        # df = pd.DataFrame(table_needed)
        # df.to_csv("all_acct_bals.csv")