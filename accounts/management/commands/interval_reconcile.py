from django.core.management.base import BaseCommand
from django.conf import settings
from django.db import transaction
from django.db.models import Sum, Q

from horizon_pay.models import TerminalSerialTable, CardIssuerTable
from main.models import ConstantTable, User
from accounts.models import TransferVerificationObject, AirtimeToPinObject, AirtimeToPinParent, AccountSystem, CashOutChargeBand, Escrow, OtherCommissionsRecord, OutOfBookTransfer, Transaction, WalletSystem, DebitCreditRecordOnAccount, BillsPaymentDumpData
from accounts.helpers.vfdbank_manager import VFDBank
from accounts.helpers.woven_manager import Woven
from accounts.helpers.heritage_bank_manager import HeritageBank
from accounts.helpers.transaction_verf_script import push_out_new_legs_from_verf
from accounts.tasks import handle_interval_reconciliation
from django.contrib.auth.hashers import make_password, check_password
from kyc_app.models import BVNDetail, KYCTable, DocumentFaceMatchKYC2Detail

from django.utils import timezone
from datetime import datetime

import uuid
import json
import requests
import random
import pandas as pd

from py_jwt_validator import PyJwtValidator



class Command(BaseCommand):
    help = ""

    
    def handle(self, *args, **kwargs):
        start_date = datetime(2023, 5, 27)
        end_date = datetime(2023, 6, 2)

        twenty_minutes_before = timezone.now() - timezone.timedelta(minutes=5)
        days_ago = timezone.now() - timezone.timedelta(days=5)

        data_list = TransferVerificationObject.objects.filter(transaction_leg="INTERNAL", date_added__lte=twenty_minutes_before, date_added__gte=days_ago, transaction_ver_status__in=["REVERSAL"], duplicate_counted=False).order_by("-id")

        for i in data_list:
            i.duplicate_counted = True

        TransferVerificationObject.objects.bulk_update(data_list, fields=["duplicate_counted"])

        
        print(data_list)
        for transaction_verf in data_list:
            if settings.ENVIRONMENT == "development":
                is_test_trans = True
            elif settings.ENVIRONMENT == "production":
                is_test_trans = False
            
            liberty_reference = transaction_verf.liberty_reference
            new_liberty_reference = Transaction.create_liberty_reference_with_old_reference(liberty_reference=transaction_verf.liberty_reference, suffix="RVSL")
            amount = transaction_verf.amount
            escrow_id = transaction_verf.escrow_id
            transfer_charge = 0
            liberty_commission = 0
            narration = "RE_FAILED_TRANSFER_IN"
            user = transaction_verf.transaction_instance.user

            

            # verify_trans = VFDBank.vfd_transaction_verification_handler(reference=liberty_reference)


            # if isinstance(verify_trans, dict):

            #     transaction_verf.raw_payload_response = json.dumps(verify_trans)
            #     transaction_verf.checked_for_duplicate = True


            #     if verify_trans["status"] == "00":
            #         if verify_trans["data"].get("transactionStatus") == "00":

            #             transaction_verf.unique_reference = f'{verify_trans.get("data", {}).get("bankTransactionId")}'
            #             transaction_verf.has_duplicate = True

            #             transaction_verf.transaction_instance.unique_reference = f'{verify_trans.get("data", {}).get("bankTransactionId")}'
                        


            handle_interval_reconciliation.apply_async(
                queue="intervrecon",
                kwargs={
                    "escrow_id": escrow_id,
                    "verf_id": transaction_verf.id
                }
            )


                # transaction_verf.save()
                # transaction_verf.transaction_instance.save()
