from django.core.management.base import BaseCommand
from main.models import User
class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):

        """
        This Cron job runs every 12am to clear daily balance
        """

        get_all_users = User.objects.filter(bills_pay_comm_balance_daily__gt=0, other_comm_balance_daily__gt=0)
        
        for user in get_all_users:
            user.bills_pay_comm_balance_daily = 0.00
            user.other_comm_balance_daily = 0.00

            user.save()
