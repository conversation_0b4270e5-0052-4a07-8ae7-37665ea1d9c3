from django.core.management.base import BaseCommand
from django.conf import settings
from django.db import transaction
from django.db.models import Sum, Q

from horizon_pay.models import TerminalSerialTable, CardIssuerTable
from main.models import ConstantTable, User
from accounts.models import TransferVerificationObject, AirtimeToPinObject, AirtimeToPinParent, AccountSystem, CashOutChargeBand, Escrow, OtherCommissionsRecord, OutOfBookTransfer, Transaction, WalletSystem, DebitCreditRecordOnAccount, BillsPaymentDumpData
from accounts.helpers.vfdbank_manager import VFDBank
from accounts.helpers.woven_manager import Woven
from accounts.helpers.heritage_bank_manager import HeritageBank
from accounts.helpers.transaction_verf_script import push_out_new_legs_from_verf
from accounts.tasks import send_money_external_bank_transfer_task
from django.contrib.auth.hashers import make_password, check_password

from django.utils import timezone
from datetime import datetime

import pandas as pd

from py_jwt_validator import PyJwtValidator



class Command(BaseCommand):
    help = ""

    
    def handle(self, *args, **kwargs):
        start_date = datetime(2023, 5, 27)
        end_date = datetime(2023, 6, 2)

        twenty_minutes_before = timezone.now() - timezone.timedelta(minutes=5)
        days_ago = timezone.now() - timezone.timedelta(days=2)


        data_list = Transaction.objects.filter(transaction_type__in=["SEND_BANK_TRANSFER", "SEND_LIBERTY_COMMISSION", "SEND_BACK_TO_FLOAT_TRANSFER", "REVERSAL_BANK_TRANSFER"], date_created__lte=twenty_minutes_before, date_created__gte=days_ago, status="IN_PROGRESS", transaction_leg__in=["INTERNAL", "EXTERNAL", "REVERSAL", "COMMISSIONS", "INFLOW_TO_FLOAT"])


        # data_list = TransferVerificationObject.objects.filter(transaction_leg="INTERNAL", date_added__lte=twenty_minutes_before, date_added__gte=days_ago, transaction_ver_status__in=["REVERSAL"])


        for get_trans in data_list:

            get_verf = TransferVerificationObject.objects.filter(liberty_reference=get_trans.liberty_reference).last()
            if get_verf:
                pass

            else:
                escrow_instance = Escrow.objects.filter(escrow_id=get_trans.escrow_id).last()

                if get_trans.transaction_leg in ["INTERNAL", "REVERSAL"]:
                    amount = escrow_instance.amount + (escrow_instance.send_money_transfer_fee if escrow_instance.send_money_transfer_fee else escrow_instance.liberty_commission - escrow_instance.extra_fee)
                else:
                    amount = escrow_instance.amount


                verification_instance = dict(
                    transaction_instance = get_trans,
                    user_id = get_trans.user.id,
                    user_email = get_trans.user.email,
                    account_provider = get_trans.account_provider,
                    transaction_leg = get_trans.transaction_leg,
                    transaction_type = get_trans.transaction_type,
                    amount = amount,
                    escrow_id = get_trans.escrow_id,
                    liberty_reference = get_trans.liberty_reference
                )


                create_verf = TransferVerificationObject.objects.create(**verification_instance)
                



        # REMOVE TEMP EXTERNALS

        get_temp_ext = Transaction.objects.filter(transaction_leg="TEMP_EXTERNAL").order_by("id")[:15]

        for get_temp in get_temp_ext:
            get_rel_trans = Transaction.objects.filter(escrow_id=get_temp.escrow_id).last()

            if get_rel_trans and get_rel_trans == get_temp:
                pass
            else:
                get_temp.delete()



