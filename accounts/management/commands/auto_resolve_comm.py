from datetime import datetime
from django.utils import timezone
from django.core.management.base import BaseCommand
from django.conf import settings
from accounts.models import TransferVerificationObject, AccountSystem
from accounts.helpers.vfdbank_manager import VFDBank



class Command(BaseCommand):
    help = ""

    def add_arguments(self, parser):
        parser.add_argument('--backto', dest='backto', type=str, help="This is the date back to which to check for pending comm.")


    def handle(self, *args, **options):
        """
        """

        backto_str = options.get('backto')
        if backto_str:
            try:
                backto_datetime = datetime.strptime(backto_str, '%Y-%m-%d')
                ten_minutes_before = backto_datetime
            except ValueError:
                self.stderr.write(self.style.ERROR('Invalid datetime format. Please provide the datetime in the format: YYYY-MM-DD HH:MM:SS'))
                return
        else:
            backto_datetime = timezone.now() - timezone.timedelta(days=5)
            ten_minutes_before = timezone.now() - timezone.timedelta(minutes=10)

        not_data_list = TransferVerificationObject.objects.filter(num_of_checks__lt=1, transaction_type="SEND_LIBERTY_COMMISSION", date_added__lte=ten_minutes_before, date_added__gte=backto_datetime, transaction_ver_status__in=["NOT_INITIATED"]).order_by("-id")[:15]
        
        pending_data_list = TransferVerificationObject.objects.filter(transaction_type="SEND_LIBERTY_COMMISSION", date_added__lte=ten_minutes_before, date_added__gte=backto_datetime, transaction_ver_status__in=["PENDING"]).order_by("id")[:10]


        TransferVerificationObject.not_initiated_func(not_data_list)
        TransferVerificationObject.pending_comms_func(pending_data_list)


        # print("done for auto comm")

        # for comm in CommissionsRecord.objects.filter(checked=False, date_created__lte=ten_minutes_before, date_created__gte=five_days_ago):

        #     print(comm.transaction_id)

        #     get_trans = Transaction.objects.filter(transaction_id=comm.transaction_id).last()
        #     escrow_id = get_trans.escrow_id

        #     if get_trans.status == "SUCCESSFUL":
        #         check_comm_exist = Transaction.objects.filter(escrow_id=get_trans.escrow_id, transaction_type="SEND_LIBERTY_COMMISSION").exists()
                
        #         if check_comm_exist:
        #             pass
        #         else:

        #             from_provider_type = ConstantTable.default_account_provider()


        #             liberty_profit = comm.liberty_profit + get_trans.extra_fee
        #             user = get_trans.user

        #             if liberty_profit > 0:
                        
        #                 send_commission = WalletSystem.pay_commission_to_liberty(
        #                     user_id=user.id,
        #                     wallet_id=None,
        #                     wallet_type=None,
        #                     from_provider_type=from_provider_type,
        #                     liberty_commission=liberty_profit,
        #                     transaction_commission_id = comm.transaction_id,
        #                     transfer_leg="VAS_COMMISSIONS",
        #                     get_escrow_id=escrow_id,
        #                     transaction_sub_type="BILLSPAYCOMM"
        #                 )


        #         comm.checked = True
        #         comm.save()


            
