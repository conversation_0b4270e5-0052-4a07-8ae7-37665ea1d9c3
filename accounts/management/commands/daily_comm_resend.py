from django.core.management.base import BaseCommand
from django.conf import settings
from accounts.models import AccountSystem, Transaction
from main.model_choices import *
from accounts.helpers.vfdbank_manager import VFDBank
from accounts.tasks import resolve_108_task
import pandas as pd


class Command(BaseCommand):
    help = ""

    def add_arguments(self, parser):
        parser.add_argument('--excl', dest='excl', type=str, help="This contains a list of account numbers to be excluded.")

    def handle(self, *args, **options):
        resolve_comm = resolve_108_task()