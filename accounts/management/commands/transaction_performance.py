from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from accounts.models import TransactionPerformance, Transaction
from django.db.models import Q
from django.conf import settings
import pytz


class Command(BaseCommand):

    # help = "<PERSON>ron script to check performance for all bank transactions in the last 3 hours!"
    # timezone = pytz.timezone(settings.TIME_ZONE)

    # def parse_transactions(self):
    #     """
    #         QUERY ALL TRANSACTIONS IN THE LAST 3 HOURS THAT HAS BEEN COMPLETED
    #         COMPLETION IS MEASURED BY ONE OF THIS THREE
    #         -REVERSED, -FAILED, -SUCCESSFUL
    #     """
    #     base = {}
    #     d_now = datetime.now()
    #     d_prev = d_now - timedelta(hours=3)
    #     now = self.timezone.localize(d_now)
    #     prev = self.timezone.localize(d_prev)
    #     transactions = Transaction.objects.filter(date_created__lte=now).filter(date_created__gte=prev)
    #     if len(transactions) > 0:
    #         for transaction in transactions:
    #             source_bank = transaction.source_bank_name
    #             status = transaction.status
    #             if source_bank in base.keys():
    #                 if status in base[source_bank].keys():
    #                     base[source_bank][status] += 1
    #                 else:
    #                     base[source_bank][status] = 1
    #             else:
    #                 base[source_bank] = {status: 1}
    #     return base

    # def calculate_performance(self, base):
    #     # base = {"uba": {"succes": 1, "failure": 1, "reversed": -1}}
    #     for key, value in base.items():
    #         success_rate = 0
    #         total = 0
    #         for k, v in value.items():
    #             total += v
    #         if "SUCCESSFUL" in value.keys():
    #             success_rate = (value['SUCCESSFUL']/total) * 100
    #         try:
    #             bank = TransactionPerformance.objects.get(bank=key)
    #         except:
    #             bank = TransactionPerformance.objects.create(
    #                 bank = key
    #             )
    #         bank.previous_performance = bank.performance
    #         bank.performance = success_rate
    #         bank.save()
    #     return 1



    def handle(self, *args, **kwargs):
        pass
    #     base = self.parse_transactions()
    #     if len(base) > 0:
    #         self.calculate_performance(base)
