from datetime import datetime
from django.utils import timezone
from django.core.management.base import BaseCommand
from django.conf import settings
from accounts.models import TransferVerificationObject
from main.models import AvailableBalance


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        now = datetime.now()

        ten_minutes_before = timezone.now() - timezone.timedelta(minutes=10)

        five_days_ago = timezone.now() - timezone.timedelta(days=2)

        temp_constant = AvailableBalance.objects.filter(is_active=True, use_batch=True).last()

        if temp_constant:
            start_index = temp_constant.start_index
            batch_size = temp_constant.batch_size


            end_index = start_index + batch_size
            

            data_list = TransferVerificationObject.objects.exclude(transaction_type="SEND_LIBERTY_COMMISSION") \
                .filter(date_added__lte=ten_minutes_before, date_added__gte=five_days_ago, transaction_ver_status__in=["NOT_INITIATED", "PENDING"], auto_verify=True).order_by("-id")[start_index:end_index]

            if not data_list:
                temp_constant.start_index = 0
            else:
            
                temp_constant.start_index += batch_size

                if start_index >= len(data_list):
                    return
            
            temp_constant.save()

        else:
            data_list = TransferVerificationObject.objects.exclude(transaction_type="SEND_LIBERTY_COMMISSION") \
                .filter(date_added__lte=ten_minutes_before, date_added__gte=five_days_ago, transaction_ver_status__in=["NOT_INITIATED", "PENDING"], auto_verify=True)

        
        print(data_list)
        for transaction_verf in data_list:
            if settings.ENVIRONMENT == "development":
                is_test_trans = True
            elif settings.ENVIRONMENT == "production":
                is_test_trans = False
            
            liberty_reference = transaction_verf.liberty_reference
            
            verification_instance = dict(
                transaction_instance = transaction_verf.transaction_instance,
                user_id = transaction_verf.user_id,
                user_email = transaction_verf.user_email,
                account_provider = transaction_verf.account_provider,
                transaction_leg = transaction_verf.transaction_leg,
                transaction_type = transaction_verf.transaction_type,
                timestamp = str(datetime.now()),
                escrow_id = transaction_verf.escrow_id,
                amount = transaction_verf.amount,
                liberty_reference = liberty_reference,
                is_test = is_test_trans
            )

            verf_pending_trans = TransferVerificationObject.create_verfication_check(verification_instance)
