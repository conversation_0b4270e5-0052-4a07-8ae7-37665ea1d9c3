from asyncio import constants
from datetime import datetime
from encodings import utf_8
from time import sleep
from django.core.management.base import BaseCommand
from django.conf import settings
from django.db.models import Sum, Q
from horizon_pay.models import TerminalSerialTable
from main.models import ConstantTable, User
from accounts.models import AccountSystem, AirtimeToPinObject, AirtimeToPinParent, AllBankList, CashOutChargeBand, Escrow, OutOfBookTransfer, Transaction, WalletSystem
from accounts.helpers.vfdbank_manager import VFDBank
from accounts.helpers.woven_manager import Woven
from accounts.tasks import send_money_external_bank_transfer_task
from django.contrib.auth.hashers import make_password, check_password
from kyc_app.models import BVNDetail, KYCTable, DocumentFaceMatchKYC2Detail

import uuid
import json

import jwt

from py_jwt_validator import PyJwtValidator



class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):

        liberty_reference = f"LP-MAN-VFD-{uuid.uuid4()}"

