from typing import Union
from django.core.management.base import BaseCommand
from django.conf import settings
from accounts.models import AccountSystem, Transaction, OtherServiceAccountSystem
from main.model_choices import *
from accounts.helpers.vfdbank_manager import VFDBank
from accounts.tasks import flush_accounts_task
import pandas as pd
import os


class Command(BaseCommand):
    help = ""

    def add_arguments(self, parser):
        parser.add_argument('--excl', dest='excl', type=str, help="This contains a list of account numbers to be excluded.")
        parser.add_argument('--incl', dest='incl', type=str, help="This contains a list of account numbers to be included.")

    def handle(self, *args, **options):
        exclude_accts = options.get('excl')
        include_accts = options.get('incl')
        
        # from main.model_choices import AccountProviders, AccountTypes
        # import pandas as pd
        # import os

       
        # float_account = AccountSystem.get_float_account(from_wallet_type=AccountTypes.FLOAT, from_provider_type=AccountProviders.VFD_PROVIDER)
        # table_needed = []
        # count = 0

        # exclude_list = []
        # if exclude_accts:
        #     exclude_list = exclude_accts.split(',')

        # acc_queryset = AccountSystem.objects.exclude(user=float_account.user). \
        #     filter(account_provider=AccountProviders.VFD_PROVIDER)
                
        # other_acc_queryset = OtherServiceAccountSystem.objects. \
        #     filter(account_provider=AccountProviders.VFD_PROVIDER)

        # queryset = []


        # def process_account(account):
        #     if account.account_number:
        #         return {
        #             "user_id": account.user.id,
        #             "user_email": account.user.email,
        #             "account_name": account.account_name,
        #             "account_number": account.account_number,
        #             "user_bvn": account.user.bvn_number,
        #             "error": None
        #         }
        #     return None


        # def add_unique_account(account_number:str, data:dict):

        #     if not any(d.get("account_number") == account_number for d in queryset) \
        #         and account_number not in exclude_list:
        #         queryset.append(data)
    

        
        # def handle_querysets(queryset):
        #     for acct in queryset:
        #         acct: Union[AccountSystem, OtherServiceAccountSystem]
        #         data = process_account(acct)
        #         if data:
        #             add_unique_account(acct.account_number, data)


        # handle_querysets(acc_queryset)
        # handle_querysets(other_acc_queryset)


        # flush_accounts_task.apply_async(
        #     queue="sendmoney",
        #     kwargs={
        #         "queryset": queryset,
        #     }
        # )
        

        # return

        # def split_into_12_lists(lst):
        #     k, m = divmod(len(lst), 12)
        #     return [lst[i*k + min(i, m):(i+1)*k + min(i+1, m)] for i in range(12)]


        # split_lists = split_into_12_lists(queryset)

        # for i, sublist in enumerate(split_lists):
      