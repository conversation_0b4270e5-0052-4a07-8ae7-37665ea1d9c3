from django.core.management.base import BaseCommand
from accounts.helpers.helper_func import settle_terminal_commission
from accounts.models import TerminalPurchaseCommission


class Command(BaseCommand):
    help = "Calculates and pays commissions to agents and their supervisors for POS requests. This function should be scheduled to run every hour."

    def handle(self, *args, **options):

        commissions = TerminalPurchaseCommission.objects.filter(settled=False, user__isnull=False)
        if commissions:
            settle_terminal_commission(commissions)
