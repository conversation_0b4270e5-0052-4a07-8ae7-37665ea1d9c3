from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from accounts.models import ReconciledWalletData, DebitCreditRecordOnAccount
from main.models import User
from main.helper.send_emails import send_email_with_parameters
from datetime import datetime
import pandas as pd



class Command(BaseCommand):
    help = ""

    def add_arguments(self, parser):
        parser.add_argument('--user', dest='arg1', type=str, help="By Default, this runs for the user specified.")
        parser.add_argument('--start', dest='arg2', type=str, help="")

    def handle(self, *args, **options):
        user_id = options.get('arg1')
        start_date = options.get('arg2')

        if not user_id:
            raise CommandError('Missing User ID.')
        
        try:
            user = User.objects.get(id=int(user_id))
        except Exception as err:
            raise CommandError(f'Error: {err}')
            

        # Your custom logic goes here
        custom_date = datetime.now().date()
        formatted_custom_date = custom_date.strftime("%d-%m-%Y")

        table_needed = []
        debitcredit_data = DebitCreditRecordOnAccount.objects.select_related("user", "wallet").filter(user=user)

        if start_date:
            try:
                custom_date = datetime.strptime(start_date, "%d-%m-%Y").date()
                debitcredit_data = DebitCreditRecordOnAccount.objects.select_related("user", "wallet").filter(user=user, date_created__date__gte=custom_date)

            except ValueError:
                raise CommandError('Invalid Date Format. Use DD-MM-YYYY')
            

        
        bad_transactions = ReconciledWalletData.reconcile_line_to_line_debcred(debcred_qs=debitcredit_data)
        if bad_transactions:
            data = {
                "user_email": user.email,
                "account_name": user.bvn_full_name,
                "bad_tranactions": bad_transactions,
                "date": formatted_custom_date,

            }

            table_needed.append(data)
        
        if table_needed:

            df = pd.DataFrame(table_needed)
            
            current_datetime = datetime.now()
            formatted_datetime = current_datetime.strftime("%d-%m-%Y_%H-%M")


            if not settings.ENVIRONMENT == "development":
                email_list = [
                    "<EMAIL>",
                ]


            else:
                email_list = [
                    "<EMAIL>"
                ]


            for email in email_list:  
                message_body = f"Trust you're a having a great day! Attached is a report of unreconciled wallet statement for user {user.email} for the day ran at datetime {formatted_datetime}"    

                send_email_with_parameters(
                    email=email,
                    subject=f" User Wallet Recon for {formatted_custom_date}",
                    message_body=message_body,
                    from_email="<EMAIL>",
                    attachment=df,
                    attachment_name=f"user_wallet_recon_{formatted_custom_date}",
                    file_name="user_wallet_recon.xlsx"
                )


