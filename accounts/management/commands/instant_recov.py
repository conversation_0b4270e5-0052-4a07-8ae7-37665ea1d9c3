from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from django.db import transaction
from django.db.models import Sum, Q

from horizon_pay.models import TerminalSerialTable, CardIssuerTable
from main.models import ConstantTable, User
from accounts.models import TransferVerificationObject, AirtimeToPinObject, AirtimeToPinParent, AccountSystem, CashOutChargeBand, Escrow, OtherCommissionsRecord, OutOfBookTransfer, Transaction, WalletSystem, DebitCreditRecordOnAccount, BillsPaymentDumpData
from accounts.helpers.vfdbank_manager import VFDBank
from accounts.helpers.woven_manager import Woven
from accounts.helpers.heritage_bank_manager import HeritageBank
from accounts.helpers.transaction_verf_script import push_out_new_legs_from_verf
from accounts.tasks import main_handle_interval_recon
from django.contrib.auth.hashers import make_password, check_password
from kyc_app.models import BVNDetail, KYCTable, DocumentFaceMatchKYC2Detail

from django.utils import timezone
from datetime import datetime




class Command(BaseCommand):
    help = ""


    def add_arguments(self, parser):
        parser.add_argument('--user', dest='arg', type=int, help='user_id.')
        parser.add_argument('-dbf', dest='dbf', type=str, help='dbf.')
        parser.add_argument('--daf', dest='daf', type=str, help='daf.')

    
    def handle(self, *args, **options):
        user_id = options.get('arg')
        dbf = options.get('dbf')
        daf = options.get('daf')

        # Your custom logic goes here
        self.stdout.write(f'Argument 2: {user_id}')
        self.stdout.write(f'Argument 2: {dbf}')
        self.stdout.write(f'Argument 2: {daf}')

        if not user_id:
            raise CommandError('Missing User ID.')
        
        try:
            user = User.objects.get(id=int(user_id))
        except Exception as err:
            raise CommandError(f'Error: {err}')

        if dbf:
            try:
                minutes_before = datetime.strptime(dbf, "%d-%m-%Y").date()
            except ValueError:
                raise CommandError('Invalid Date Format. Use DD-MM-YYYY')
        
        else:
            minutes_before = timezone.now() - timezone.timedelta(minutes=5)


        if daf:
            try:
                days_ago = datetime.strptime(daf, "%d-%m-%Y").date()
            except ValueError:
                raise CommandError('Invalid Date Format. Use DD-MM-YYYY')
            
        else:
            days_ago = timezone.now() - timezone.timedelta(days=5)




        data_list = TransferVerificationObject.objects.filter(transaction_instance__user=user, transaction_leg="INTERNAL", date_added__lte=minutes_before, date_added__gte=days_ago, transaction_ver_status__in=["REVERSAL"], duplicate_counted=False).order_by("-id")

        for i in data_list:
            i.duplicate_counted = True

        TransferVerificationObject.objects.bulk_update(data_list, fields=["duplicate_counted"])

        
        print(data_list)
        for transaction_verf in data_list:
            if settings.ENVIRONMENT == "development":
                is_test_trans = True
            elif settings.ENVIRONMENT == "production":
                is_test_trans = False
            
            liberty_reference = transaction_verf.liberty_reference
            new_liberty_reference = Transaction.create_liberty_reference_with_old_reference(liberty_reference=transaction_verf.liberty_reference, suffix="RVSL")
            amount = transaction_verf.amount
            escrow_id = transaction_verf.escrow_id
            transfer_charge = 0
            liberty_commission = 0
            narration = "RE_FAILED_TRANSFER_IN"
            verf_id=transaction_verf.id

            

            main_handle_interval_recon(escrow_id=escrow_id, verf_id=verf_id)

