import datetime
from time import sleep
from django.core.management.base import BaseCommand
from django.conf import settings
from django.db import transaction
from django.db.models import Sum, Q
from horizon_pay.models import TerminalSerialTable, CardIssuerTable
from main.models import ConstantTable, User
from accounts.models import TransferVerificationObject, AirtimeToPinObject, AirtimeToPinParent, AccountSystem, CashOutChargeBand, Escrow, OtherCommissionsRecord, OutOfBookTransfer, Transaction, WalletSystem, DebitCreditRecordOnAccount, BillsPaymentDumpData, UserDebt
from accounts.helpers.vfdbank_manager import VFDBank
from accounts.helpers.woven_manager import Woven
from accounts.helpers.heritage_bank_manager import HeritageBank
# from accounts.helpers.parallex_manager import ParallexBank, Token
from accounts.tasks import send_money_external_bank_transfer_task
from django.contrib.auth.hashers import make_password, check_password
from kyc_app.models import BVNDetail, KYCTable, DocumentFaceMatchKYC2Detail
from main.helper.utils import generate_ussd_otp
from django.utils import timezone


import uuid
import json
import requests
import jwt
import pandas as pd

from py_jwt_validator import PyJwtValidator



class Command(BaseCommand):
    help = ""

    
    def handle(self, *args, **kwargs):
        pass


        # for trans in UserDebt.objects.filter(is_active=True):
        #     UserDebt.debit_user_debt(trans=trans)
            
    
        comm_data = AccountSystem.get_dynamic_float_account(from_wallet_type="COMMISSIONS", from_provider_type="VFD")

        liberty_reference = Transaction.create_liberty_reference(suffix="COMM_OUT")

        AccountSystem.handle_out_of_books(
            from_account=comm_data.account_number,
            to_account="**********",
            amount=754000,
            escrow_id=None,
            user_bvn_number=comm_data.user.bvn_number,
            transaction_instance=None,
            liberty_reference=liberty_reference,
            narration="TRANSFER OUT"
        )

        



        # all_new_trans = Transaction.objects.filter(escrow_id=0)
        
        # more_than_two = []
        # less_than_two = []
        # count = 1

        # all_trans_count = all_new_trans.count()

        # for trans in all_new_trans:
        #     print(count, "remaining", all_trans_count - count)

        #     escrow_id = trans.escrow_id
        #     float_account = AccountSystem.get_float_account(from_wallet_type="FLOAT", from_provider_type="VFD")
        #     commisisions_account = AccountSystem.get_float_account(from_wallet_type="COMMISSIONS", from_provider_type="VFD")

        #     get_trans = Transaction.objects.filter(escrow_id=escrow_id)
        #     if get_trans.count() > 3:
        #         print(escrow_id)
        #         more_than_two.append(escrow_id)

        #     elif get_trans.count() < 1:
        #         print(escrow_id)
        #         less_than_two.append(escrow_id)
            
        #     else:
        #         get_main = get_trans.filter(transaction_id=trans.transaction_id, transaction_type="FUND_BANK_TRANSFER").first()
        #         get_comm = get_trans.filter(transaction_type="SEND_LIBERTY_COMMISSION").first()

        #         if get_main:
        #             print("I am main", escrow_id)
        #             main_user = get_main.user
        #             main_amount = get_main.amount
        #             main_new_reference = Transaction.create_liberty_reference(suffix="LGLP_BFUNDRVM")

        #             beneficiary_nuban = get_main.beneficiary_nuban
        #             beneficiary_account_name = get_main.beneficiary_account_name
        #             beneficiary_bank_code = get_main.source_bank_code


        #             get_deb_cred = DebitCreditRecordOnAccount.objects.filter(transaction_instance_id=get_main.transaction_id).first()

        #             amount_settled = get_deb_cred.amount
        #             wallet = get_deb_cred.wallet

        #             UserDebt.create_user_debt(
        #                 user=main_user,
        #                 wallet=wallet,
        #                 amount=amount_settled,
        #                 trans_type="ERROR_FUNDING",
        #                 transaction_instance_id=get_main.transaction_id,
        #                 transaction_instance_new_status="FAILED",
        #                 ignore_history=True
        #             )

        #             get_main.status = "IGNORE_HISTORY"
        #             get_main.save()

        #             get_deb_cred.reversal_trans_id = "ERROR_FUNDING"
        #             get_deb_cred.save()


        #             if OutOfBookTransfer.objects.filter(escrow_id=escrow_id).exists():
        #                 pass
        #             else:

        #                 # For Main
        #                 record_sub_main = OutOfBookTransfer.objects.create(
        #                     type_of_transfer = "BAD_FUNDING_MAIN",
        #                     provider = "VFD",
        #                     amount = amount_settled,
        #                     from_account = float_account.account_number,
        #                     to_account = beneficiary_nuban,
        #                     created_liberty_reference = main_new_reference,
        #                     escrow_id = escrow_id,
        #                 )

        #                 reverse_sub_main = VFDBank.initiate_payout(
        #                     beneficiary_account_name=beneficiary_account_name,
        #                     beneficiary_nuban=beneficiary_nuban,
        #                     beneficiary_bank_code=beneficiary_bank_code,
        #                     source_account=float_account.account_number,
        #                     narration="REVERSE_WRONG_FUNDING",
        #                     amount=amount_settled,
        #                     transfer_type="intra",
        #                     user_bvn=get_main.user.bvn_number,
        #                     reference=main_new_reference,
        #                     transfer_leg="OUT_OF_BOOKS"
        #                 )


        #                 record_sub_main.send_payload = reverse_sub_main
        #                 record_sub_main.save()
            

        #                 if get_comm:

        #                     comm_amount = get_comm.amount
        #                     comm_new_reference = Transaction.create_liberty_reference(suffix="LGLP_BFUNDRVC")

        #                     # For Comm
        #                     record_sub_comm = OutOfBookTransfer.objects.create(
        #                         type_of_transfer = "BAD_FUNDING_COMM",
        #                         provider = "VFD",
        #                         amount = comm_amount,
        #                         from_account = commisisions_account.account_number,
        #                         to_account = beneficiary_nuban,
        #                         created_liberty_reference = comm_new_reference,
        #                         escrow_id = escrow_id,
        #                     )

        #                     reverse_sub_comm = VFDBank.initiate_payout(
        #                         beneficiary_account_name=beneficiary_account_name,
        #                         beneficiary_nuban=beneficiary_nuban,
        #                         beneficiary_bank_code=beneficiary_bank_code,
        #                         source_account=commisisions_account.account_number,
        #                         narration="REVERSE_WRONG_FUNDING",
        #                         amount=comm_amount,
        #                         transfer_type="intra",
        #                         user_bvn=get_main.user.bvn_number,
        #                         reference=comm_new_reference,
        #                         transfer_leg="OUT_OF_BOOKS1"
        #                     )


        #                     record_sub_comm.send_payload = reverse_sub_comm
        #                     record_sub_comm.save()
                
        #         else:
        #             pass
            

        #     count += 1
        


        
        # print(more_than_two)
        # print(less_than_two)






        #     get_credit_debit = DebitCreditRecordOnAccount.objects.filter(transaction_instance_id=trans.transaction_id).first()
            
        #     trans.balance_before = get_credit_debit.balance_before

        #     if trans.user.sms_subscription:
        #         trans.balance_after = get_credit_debit.balance_after - 2.99
        #     else:
        #         trans.balance_after = get_credit_debit.balance_after
    
        #     trans.save()



        # current_datetime = datetime.utcnow()
        
        # expiration_time = datetime(
        #     year=current_datetime.year,
        #     month=current_datetime.month,
        #     day=current_datetime.day,
        #     hour=23,
        #     minute=59,
        #     second=59
        # )
        
        # payload = {
        #     'exp': expiration_time,
        #     'service_name': "FUNDS_RETRIEVAL"
        # }
        
        # # Generate the JWT token
        # token = jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')
        
        # return token

        # for trans in Transaction.objects.filter(transaction_type="SAVINGS"):
        #     if trans.user.email == "<EMAIL>":
        #         trans.transaction_type = "SAVINGS_CR"
        #         trans.save()
        #     else:
        #         pass


        # terminals_overall_qs = User.objects.filter(Q(terminal_id__isnull=False) & ~Q(terminal_id=""))

        # terminals_details_list = []
        # for terminal in terminals_overall_qs:
        #     data = {
        #         "active_terminals": terminal.phone_number if terminal.terminal_status=="ACTIVE" else "NA",
        #         "inactive_terminals": terminal.phone_number if terminal.terminal_status=="INACTIVE" else "NA",
        #         "partially_inactive_terminals": terminal.phone_number if terminal.terminal_status=="PARTIALLY_INACTIVE" else "NA"
        #         }
        #     terminals_details_list.append(data)

        # df = pd.DataFrame()
        # terminals_details_sheet = df.from_dict(terminals_details_list)
        # terminals_excel_sheet = terminals_details_sheet.to_excel("term_det_sheet.xlsx")


        # yesterday = datetime.now() - timedelta(days=1)
        # for trans in Transaction.objects.filter(date_created__gte=yesterday, transaction_type="SEND_BANK_TRANSFER", transaction_leg="EXTERNAL", status="SUCCESSFUL"):
        #     escrow_id = trans.escrow_id
            
        #     get_10_comm = Transaction.objects.filter(escrow_id=escrow_id, transaction_type="SEND_LIBERTY_COMMISSION", amount=10).first()
        #     if get_10_comm:
        #         get_10_comm.transaction_sub_type = "MAIN_TRSF_COMM"
        #         get_10_comm.save()
            
