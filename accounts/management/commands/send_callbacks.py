from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.conf import settings
from main.models import User, CallbackSystem
from accounts.models import Transaction, CommissionsRecord
from datetime import datetime, timedelta

class Command(BaseCommand):
    help = ""


    def add_arguments(self, parser):
        parser.add_argument('--user', dest='arg', type=int, help='user_id.')
        parser.add_argument('--trans', dest='trans', type=str, help='trans.')

    
    def handle(self, *args, **options):
        user_id = options.get('arg')
        trans_id = options.get('trans')


        # Your custom logic goes here
        self.stdout.write(f'Argument 2: {user_id}')

        if user_id:
            try:
                user = User.objects.get(id=int(user_id))
            except Exception as err:
                raise CommandError(f'Error: {err}')

            for user_call in CallbackSystem.objects.filter(is_active=True):
                trans_type = user_call.transaction_type
                call_user = user_call.user

                get_trans = Transaction.objects.filter(user=call_user, status="SUCCESSFUL", transaction_type=trans_type, user__phone_number=user.phone_number).order_by("-id")[:10]
                
                for instance in get_trans:

                    if instance.transaction_type == "SEND_BANK_TRANSFER" and instance.transaction_leg == "INTERNAL":
                        pass
                    else:
                        if instance.callback_payload == None or  instance.callback_payload == ' ' or instance.callback_payload == '':
                            Transaction.serializer_trans(instance)

                        check_callback = CallbackSystem.objects.filter(
                            user = instance.user,
                            transaction_type = instance.transaction_type
                        )
                        
                        if check_callback:

                            send_callback_task = CallbackSystem.send_callback(
                                user = instance.user,
                                transaction_type = instance.transaction_type,
                                payload = instance.callback_payload,
                                transaction_instance = instance
                            )
        
        else:
            if settings.ENVIRONMENT == "development":
                ten_minutes_before = timezone.now() - timezone.timedelta(seconds=20)
            else:
                ten_minutes_before = timezone.now() - timezone.timedelta(minutes=5)

            five_days_ago = timezone.now() - timezone.timedelta(days=2)


            for user_call in CallbackSystem.objects.filter(is_active=True):
                trans_type = user_call.transaction_type
                call_user = user_call.user

                self.stdout.write(f'Trans List: {type(trans_id)}')
                if trans_id:
                    trans_list = trans_id.split(',')
                    int_trans_list = [int(id) for id in trans_list]
                    self.stdout.write(f'Trans List: {int_trans_list}')

                    get_trans = Transaction.objects.filter(id__in=int_trans_list)

                else:
 
                    get_trans = Transaction.objects.filter(date_created__lte=ten_minutes_before, date_created__gte=five_days_ago, user=call_user, callback_sent=False, status="SUCCESSFUL", transaction_type=trans_type).order_by("-id")[:10]
                
                for instance in get_trans:

                    if instance.transaction_type == "SEND_BANK_TRANSFER" and instance.transaction_leg == "INTERNAL":
                        pass
                    else:
                        if instance.callback_payload == None or  instance.callback_payload == ' ' or instance.callback_payload == '':
                            Transaction.serializer_trans(instance)

                        check_callback = CallbackSystem.objects.filter(
                            user = instance.user,
                            transaction_type = instance.transaction_type
                        )
                        
                        if check_callback:

                            send_callback_task = CallbackSystem.send_callback(
                                user = instance.user,
                                transaction_type = instance.transaction_type,
                                payload = instance.callback_payload,
                                transaction_instance = instance
                            )


##############################################################################################################################################################
##############################################################################################################################################################
##############################################################################################################################################################