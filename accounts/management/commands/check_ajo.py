from datetime import timedelta
from django.utils import timezone
from django.core.management.base import BaseCommand
from django.contrib.auth.hashers import make_password
from main.models import User
from accounts.helpers.helper_func import AjoClass
import random
import string



class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        for user in User.objects.filter(user__type_of_user="AJO_AGENT", first_name="Liberty", last_login__isnull=False):
            now = timezone.now()
            time_threshold = timedelta(hours=48)

            if not user.first_login:
                user.first_login = user.last_login
                user.save()

            if user.first_login > now - time_threshold:
                get_num_of_savers = AjoClass.get_agent_num_of_users(user_id=user.id)
                
                if get_num_of_savers and get_num_of_savers < 3:
                    pwd = "".join(random.choice(string.ascii_uppercase + string.digits) for _ in range(8))

                    user.password = make_password(pwd)
                    user.save()
                else:
                    pass


       