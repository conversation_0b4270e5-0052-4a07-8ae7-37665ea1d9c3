from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from accounts.models import ReconciledWalletData, DebitCreditRecordOnAccount
from main.helper.send_emails import send_email_with_parameters
from datetime import datetime
import pandas as pd



class Command(BaseCommand):
    help = ""

    def add_arguments(self, parser):
        parser.add_argument('--day', dest='arg1', type=str, help="By Default, this runs hourly for the current day. But passing day as DD-MM-YYYY will ensure it runs for the set date.")

    def handle(self, *args, **options):
        custom_day = options.get('arg1')

        # Your custom logic goes here
        custom_date = datetime.now().date()
        formatted_custom_date = custom_date.strftime("%d-%m-%Y")


        if custom_day:
            try:
                custom_date = datetime.strptime(custom_day, "%d-%m-%Y").date()
            except ValueError:
                raise CommandError('Invalid Date Format. Use DD-MM-YYYY')
            

        table_needed = []

        # day_debitcredit_data = DebitCreditRecordOnAccount.objects.select_related("user", "wallet").filter(date_created__date=custom_date)
        day_debitcredit_data = DebitCreditRecordOnAccount.objects.select_related("user", "wallet").exclude(user__email=settings.FLOAT_USER_EMAIL).filter(date_created__date=custom_date)
        
        user_querysets = {}

        for debit_credit in day_debitcredit_data:
            user = debit_credit.user
            if user not in user_querysets:
                user_querysets[user] = day_debitcredit_data.filter(user=user)


        for user, queryset in user_querysets.items():
            bad_transactions = ReconciledWalletData.reconcile_line_to_line_debcred(debcred_qs=queryset)

            if bad_transactions:
                data = {
                    "user_email": user.email,
                    "account_name": user.bvn_full_name,
                    "bad_tranactions": bad_transactions,
                    "date": formatted_custom_date,
    
                }

                table_needed.append(data)
            
            else:
                pass


        current_datetime = datetime.now()
        formatted_datetime = current_datetime.strftime("%d-%m-%Y_%H-%M")

        df = pd.DataFrame(table_needed)
     
        if table_needed:

            if not settings.ENVIRONMENT == "development":
                email_list = [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ]


            else:
                email_list = [
                    "<EMAIL>"
                ]


            for email in email_list:  
                message_body = f"Trust you're a having a great day! Attached is a report of unreconciled wallet statement for unique users for the day {formatted_custom_date} ran at datetime {formatted_datetime}"    

                send_email_with_parameters(
                    email=email,
                    subject=f"CRITICAL! Bad Wallet Transactions Noticed for {formatted_custom_date}",
                    message_body=message_body,
                    from_email="<EMAIL>",
                    attachment=df,
                    attachment_name=f"hourly_wallet_recon_{formatted_custom_date}"
                )

        pass

