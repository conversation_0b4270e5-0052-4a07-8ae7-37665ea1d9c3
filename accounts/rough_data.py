# import pandas as pd


# df = pd.read_csv('inflow_data.csv')

# # Extract the 'amount' value from the dictionary column

# df['payload'] = df['payload'].apply(eval)

# print(type(df['payload'][0]))
# df['Amount'] = df['payload'].apply(lambda x: x.get('amount', '') if isinstance(x, dict) else '')

# df_sorted = df.sort_values('Amount', ascending=False)

# # Print the sorted DataFrame
# print(df_sorted)






# balance_before = wallet.available_balance
        # balance_after = wallet.available_balance + 200
        # # wallet.available_balance = balance_after

        # hold_balance_before = wallet.hold_balance
        # hold_balance_after = wallet.hold_balance
        
        # wallet.save()

        # balance_before = wallet.available_balance
        # balance_after = wallet.available_balance + 200
        # # wallet.available_balance = balance_after

        # hold_balance_before = wallet.hold_balance
        # hold_balance_after = wallet.hold_balance
        
        # wallet.save()

        # transes = Transaction.objects.filter(transaction_type="FUND_COLLECTION_ACCOUNT", callback_sent=False)

        # for i in transes:
        #     i.callback_sent = True

        # Transaction.objects.bulk_update(transes, fields=["callback_sent"])

        # self.stdout.write(self.style.SUCCESS(f'Successfully deleted {len(old_instances)} instances older than 70 days.'))


######################################################################################################

#################################################################################################

        # beneficiary_bank_code = "999999"
        # liberty_reference = Transaction.create_liberty_reference(suffix="LGLP-VFD-OOB")

        # liberty_float_bank_account = AccountSystem.get_float_account(
        #     from_wallet_type="FLOAT", from_provider_type="VFD"
        # ) 

        # liberty_float_nuban = liberty_float_bank_account.account_number
        # liberty_float_bank_name = liberty_float_bank_account.account_name

        # user = User.objects.filter(email="").last()

        # beneficiary_account = AccountSystem.get_account_type(
        #     user=user,
        #     from_wallet_type="COLLECTION",
        #     from_provider_type="VFD",
        # )

        # beneficiary_account_name = beneficiary_account.account_name
        # beneficiary_nuban = beneficiary_account.account_number

        # amount = 
        # escrow_id = ""



        # record_sub_com = OutOfBookTransfer.objects.create(
        #     type_of_transfer = "OTHERS",
        #     provider = "VFD",
        #     amount = amount,
        #     from_account = liberty_float_nuban,
        #     to_account = beneficiary_nuban,
        #     created_liberty_reference = liberty_reference,
        #     escrow_id = escrow_id,
        # )

        # reverse_sub_comm = VFDBank.initiate_payout(
        #     beneficiary_account_name=beneficiary_account_name,
        #     beneficiary_nuban=beneficiary_nuban,
        #     beneficiary_bank_code=beneficiary_bank_code,
        #     source_account=liberty_float_nuban,
        #     narration=f"REVERSE DOUBLE PROCESSED REVERSAL - {escrow_id}",
        #     amount=amount,
        #     transfer_type="intra",
        #     user_bvn=user.bvn_number,
        #     reference=liberty_reference,
        #     transfer_leg="OUT_OF_BOOKS"
        # )


        # record_sub_com.send_payload = reverse_sub_comm
        # record_sub_com.save()




#################################################################################################
#################################################################################################



      

        # distinct_trans = []
        # distinct_trans_main = []
        # trans_ids = []

        # effected_users = []


        # for deb_trans in DebitCreditRecordOnAccount.objects.filter(type_of_trans="SEND_BANK_EXTERNAL", transaction_instance_id__in=all_ids).order_by('id'):

        #     if deb_trans.user in distinct_trans:
        #         effected_users.append(deb_trans.user.email)
                
        #     else:
        #         distinct_trans.append(deb_trans.user)
        #         trans_ids.append(deb_trans.transaction_instance_id)
        #         distinct_trans_main.append(
        #                 {
        #                     "id": deb_trans.id,
        #                     "user": deb_trans.user,
        #                     "transaction_instance": deb_trans.transaction_instance_id
        #                 })

        #         get_trans = Transaction.objects.filter(transaction_id=deb_trans.transaction_instance_id).last()
        #         get_external = Transaction.objects.filter(escrow_id=get_trans.escrow_id, transaction_leg="EXTERNAL").last()

        #         if get_external:
        #             get_whisper_charge = DebitCreditRecordOnAccount.objects.filter(transaction_instance_id=get_external.transaction_id).last()
        #             if get_whisper_charge:

        #                 total_amount = get_trans.sms_charge
        #                 get_whisper_charge.balance_before = deb_trans.balance_after
        #                 get_whisper_charge.amount = total_amount
        #                 get_whisper_charge.balance_after = get_whisper_charge.balance_before - total_amount
        #                 get_whisper_charge.save()

                


        # print(distinct_trans)
        # print(trans_ids)
        # print(distinct_trans_main)
        # print(effected_users)








            # if deb_trans.user in distinct_trans[]:
            # for dictionary in distinct_trans:
            #     if deb_trans.user == dictionary.get("user"):
            #         print("herrrr")
            #     else:
            #         distinct_trans.append(
            #             {
            #                 "id": deb_trans.id,
            #                 "user": deb_trans.user
            #             })


            # print(distinct_trans)





        # for user in user_sum:
        #     balance = user_balance.get(user=user['user'])
        #     result.append({
        #         'user_id': user['user'],
        #         'total_sum_owed': user['total_amount'],
        #         'available_balance': balance['available_balance'],
        #         "balance_to_debit": balance['available_balance'] - user['total_amount']
        #     })










        # for trans in DebitCreditRecordOnAccount.objects.filter(type_of_trans="SEND_BANK_EXTERNAL", date_created__date=today, amount=0):
        #     trans.user.send_money_status = False
        #     trans.user.save()
        #     trans.save()


        
        # for user in User.objects.all():
        #     user.save()
        #     user.vfd_bvn_acct_num_count += 12
        #     user.save()
    # def check_network_connection():
    #     host='https://google.com'
    #     try:
    #         requests.get(host) #Python 3.x
    #         print(True)
    #     except:
    #         print(False)

    # def main_func():
    #     check_network = check_network_connection()

    #     if check_network is True:
    #         # Do others

    #         return True

    #     else:
    #         return False
         



    # if main_func is False:
    #     sleep(5)
    #     main_func()
    # else:
    #     sleep(10)
    #     main_func()

        


        # ten_minutes_ago = timezone.now() - timezone.timedelta(minutes=10)
        # five_days_ago = timezone.now() - timezone.timedelta(days=5)
        # trans_changed_list = []

        # pending_in_progress_trans = Transaction.objects.filter(date_created__lte=ten_minutes_ago, date_created__gte=five_days_ago, transaction_type="SEND_BACK_TO_FLOAT_TRANSFER", status__in=["IN_PROGRESS", "PENDING"])
        # for trans in pending_in_progress_trans:
        #     get_trans_verf = TransferVerificationObject.objects.filter(liberty_reference=trans.liberty_reference, transaction_ver_status="NOT_INITIATED").last()
        #     if get_trans_verf:
        #         get_trans_verf.is_verified = False
        #         get_trans_verf.is_finished_verification = False
        #         get_trans_verf.save()

        #         trans_changed_list.append(get_trans_verf.id)
            
        # print(trans_changed_list)
        # print(len(trans_changed_list))

        # for trans in Transaction.objects.filter(transaction_leg="REVERSAL"):
        #     trans.is_reversed = True
        #     trans.save()


        # for user in User.objects.filter(phone__in=user):
        #     user.block_on_funding == True
        #     user.terminal_suspended
        #     user.save()



        # print(Token.generate_token())

        # get_banks = ParallexBank.get_all_banks()
        # print(get_banks)

        # for data in BillsPaymentConstant.objects.all():
        #     if data.bills_type == "BETTING":
        #         if data.biller == "BET9JA":
        #             data.provider_fee = 20
        #         else:
        #             data.provider_fee = 10
            
        #         data.liberty_profit = 5
        #         data.agent_profit = 5
            
        #     elif data.bills_type == "DATA_BUNDLE":
        #         if data.biller == "IPNX":
        #             data.provider_fee = 10
        #             data.liberty_profit = 5
        #             data.agent_profit = 5

        #     elif data.bills_type == "ELECTRICITY":
        #         if data.biller in ["APLE", "PRIVIDA", "LUMOS"]:
        #             data.provider_fee = 10
        #             data.liberty_profit = 5
        #             data.agent_profit = 5

        #     else:
        #         pass


        #     data.save()




        # for data in User.objects.filter():
        #     if AccountSystem.objects.filter(user=data, account_provider='VFD').exists():
        #         data.vfd_bvn_acct_num_count = 1
        #     else:
        #         data.vfd_bvn_acct_num_count = 0
            
        #     data.save()

        # pass

        # create_corporate = VFDBank.create_corporate_account(
        #     rc_number = "6515",
        #     company_name = "LibertyPay Limited",
        #     incorp_date = "10 August 2022",
        #     bvn = "**********"
        # )

        # for data in BillsPaymentConstant.objects.filter(commissions_share_type="CHARGE_AGENT"):
        #     data.liberty_profit -= 10
        #     data.provider_fee += 10
        #     data.save()


        # create_up_down = DailyUptimeDowntimeRecord.calculate_up_down_time(entry="CASH_OUT")
        # print(create_up_down)
        # pass
        # today_string = datetime.now().strftime("%d/%m/%Y")

        # create_heritage = HeritageBank.create_heritage_wallet(
        #     user = User.objects.get(id=3)
        # )

        # print(create_heritage)
        


        
        # password = User.custom_hash_values(passcode="123456")
        # print(password)

        # user = User.objects.get(id=145)
        # user.password = password
        # user.save()

        

        # pass

        # for user in User.objects.filter(terminal_id__isnull=False):
        #     name = user.bvn_first_name.capitalize() if user.bvn_first_name else user.first_name.capitalize()
        #     num = user.phone_number

        #     print(name)
        #     print(num)

        #     url = settings.WHISPER_URL
        #     payload = json.dumps(
        #         {
        #             "receiver": f"{num}",
        #             "template": "70173f77-fbb9-4ba1-9d3f-83e8bbc73257",
        #             "place_holders": {
        #                 "first_name": f"{name}",
        #             },
        #         }
        #     )
        #     headers = {
        #         "Authorization": f"Api_key {settings.WHISPER_KEY}",
        #         "Content-Type": "application/json",
        #     }

        #     response = requests.request("POST", url, headers=headers, data=payload)
        #     res = json.loads(response.text)

        #     print("SMS SENT TO", num)
        

        # transfer_leg = "FIRST_LEG" #For sending from float account, the "NO_LEG" for sending from user account

        # beneficiary_bank_code = "999999"
        # amount = None
        # liberty_reference = f"LGLP-VFD-{uuid.uuid4()}"


        # send_money_out_of_books = (
        #         beneficiary_account_name="LIBERTY PAY",
        #         beneficiary_nuban="",
        #         beneficiary_bank_code=beneficiary_bank_code,
        #         source_account="",
        #         narration="manual_send_out",
        #         amount=amount,
        #         transfer_type="intra",
        #         user_bvn="",
        #         reference=liberty_reference,
        #         transfer_leg="NO_LEG"
        #     )
        
        # print(send_money_out_of_books)


        # OutOfBookTransfer.objects.create(
        #     provider = "VFD",
        #     amount = amount,
        #     initiate_transaction_payload = send_money_out_of_books
        # )



        #num_list = ['*************']

        # num_list = ['*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '2347089496650', '2348024508567', '2349168550568', '2348082175003', '2348050224840', '2347030524764', '2348034064392', '2349162842264', '2348086040602', '2347039960872', '2348142475981', '2347012274135', '2349137124003', '2349152291800', '2349161514188', '2348130411164', '2348033486081', '2348034027571', '2348025448605', '2348180349134', '2348133757236', '2348053018489', '2348082523050', '2349029054805', '*************', '2349069090865']
        
        
        # print(len(num_list))
        # for num in num_list:
        #     user = User.objects.filter(phone_number=num).last()
        #     if user:
        
        #         name = user.bvn_first_name.capitalize() if user.bvn_first_name else user.first_name.capitalize()

        #         url = settings.WHISPER_URL
        #         payload = json.dumps(
        #             {
        #                 "receiver": f"{num}",
        #                 "template": "70173f77-fbb9-4ba1-9d3f-83e8bbc73257",
        #                 "place_holders": {
        #                     "first_name": f"{name}",
        #                 },
        #             }
        #         )
        #         headers = {
        #             "Authorization": f"Api_key {settings.WHISPER_KEY}",
        #             "Content-Type": "application/json",
        #         }
    
        #         response = requests.request("POST", url, headers=headers, data=payload)
        #         res = json.loads(response.text)
    
        #         print("SMS SENT TO", num)
                
        #     else:
        #       print(num)
        #       print("DIDNT FIND USER")




        # create_corporate = VFDBank.create_corporate_account(
        #     rc_number = "6515",
        #     company_name = "",
        #     incorp_date = "10 August 2022",
        #     bvn = "**********"
        # )

        # print(create_corporate)

       
    #    {'status': '00', 'message': 'Corporate account created successfully', 'data': {'accountNo': '**********', 'accountName': 'CHUKS COMPANY'}}
        # pass
       
       
       
       
       
       
       
       
       
       
        # vfd_create = VFDBank.create_vfd_wallet(
        #     user=user, user_phone_number=user_phone_number, retry_count=2
        # )

        # print(vfd_create)
       
       
       



       
       
       
       
        # for term in TerminalSerialTable.objects.filter(user__isnull=False):
        #     term.type_of_user = "AGENT"
        #     term.save()


        # constant = ConstantTable.get_constant_table_instance()
        # for i in CashOutChargeBand.objects.all():
        #     i.constant_instance = constant
        #     i.save()




        # accounts_not_found = []
        # for account in AccountSystem.objects.exclude(account_number="**********").filter(account_provider="VFD"):
        #     response = VFDBank.vfd_account_enquiry(account_number=account.account_number)
        #     # response = {
        #     #     "status": "00",
        #     #     "message": "Account Details",
        #     #     "data": {
        #     #         "accountNo": "**********",
        #     #         "accountBalance": "30.000000",
        #     #         "accountId": "1373250",
        #     #         "client": "Liberty-YETUNDE MORILIAT IGBENE",
        #     #         "clientId": "1207252",
        #     #         "savingsProductName": "Liberty Wallet Deposits"
        #     #     }
        #     # }

        #     print(account.account_number)
        #     if response["message"] == 'Account Not Found':
        #         accounts_not_found.append(account.account_number)
            
        #     else:
        #         account.available_balance = float(response["data"]["accountBalance"])
        #         account.save()
            
            
        # print(accounts_not_found)

        
        # get_trans = Transaction.objects.filter(id=1076).last()
        # get_trans.date_created = datetime.today()
        # get_trans.save()

   

        # for item in AllBankList.objects.all():
        #     if item.cbn_code == "-":
        #         item.cbn_code = None
        #     elif item.bank_short_name == "-" or item.bank_short_name == "":
        #         item.bank_short_name = None

        #     item.save()
            

        # for airt in AirtimeToPinParent.objects.all():
        #     last_pin_obj = airt.airtime_pin_children.filter(liberty_reference__isnull=False).last()
        #     if last_pin_obj is not None:
        #         print(last_pin_obj.liberty_reference)

        #         get_transaction = Transaction.objects.filter(liberty_reference=last_pin_obj.liberty_reference).last()
        #         if get_transaction:
        #             airt.user = get_transaction.user
        #             airt.save()
        #         else:
        #             airt.user = User.objects.filter(id=1).last()
        #             airt.save()
        # all_bank_codes = list(AllBankList.objects.values_list('bank_code', flat=True))
        # all_bank_codes = list(AllBankList.objects.values_list('bank_code', flat=True))
        # print(len(bank_list))


        # for item in bank_list:
        #     get_bank = AllBankList.objects.filter(bank_code=item["bank_code"]).last()
        #     if get_bank:
        #         get_bank.cbn_code = item["cbn_code"]
        #         get_bank.bank_short_name = item["bank_short_name"]
        #         get_bank.save()
        #     else:
        #         pass


            # if item["bank_code"] in in object.
            # bank_name = item["name"],
            # bank_code = item["code"]
    





        # for i in BVNDetail.objects.all():

        #     # kyc_level = KYCTable.check_user_kyc_level(i.user)
        #     # i.user.kyc_level = kyc_level
        #     i.save()

        # for i in DocumentFaceMatchKYC2Detail.objects.all():

        #     # kyc_level = KYCTable.check_user_kyc_level(i.user)
        #     # i.user.kyc_level = kyc_level
        #     i.save()






        # all_kyc = []
        # all_docs = []

        # for k in KYCTable.objects.all():
        #     all_kyc.append(k.user.email)
        
        # for i in User.objects.all():
        #     if i.email not in all_kyc:
        #         KYCTable.objects.create(user=i)




        # for i in DocumentFaceMatchKYC2Detail.objects.all():
        #     all_docs.append(i.kyc.user.email)

        # print(all_docs)

        # for k in KYCTable.objects.all():
        #     if k.user.email not in all_docs:
        #         # sleep(3)
        #         DocumentFaceMatchKYC2Detail.objects.create(kyc=k)
     
                


        # custom_signature = f"{settings.CARD_LOAD_TOKONE}"

        # token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NjAzMTIzNDIsImlhdCI6MTY2MDMxMjM0MX0.GOScOJlZxEuDN8D1HJFSTSvQkc4s0lf1463ruXsRoao"

        
        # try:
        #     print(jwt.decode(token, custom_signature, algorithms=["HS256"]))
        # except jwt.exceptions.PyJWTError as e:
        #     print(e)
       
       





# class MetamapAPIView(APIView):
#     permission_classes = [MetaMapViewWhitelist]

#     def post(self, request):
#         resp = request.data

#         metamap_rawdata = MetamapRawData.objects.create(
#             payload = str(resp)
#         )


#         flow_id = resp.get("flowId")
#         meta_data = resp.get("metadata")

#         event_name = resp.get("eventName")
#         if event_name == "verification_completed" or "verification_updated":
#             resource_url = resp.get("resource")

#             metamap_instance = MetaMap()

#             access_token = metamap_instance.authenticate_metamap()

            
#             retrieve_resource = metamap_instance.retrieve_resource(resource_url=resource_url, access_token=access_token)

#             resource_retreive_flow_id = retrieve_resource.get("flow").get("id")
#             resource_retreive_status = retrieve_resource.get("identity").get("status")

#             if meta_data:
#                 user_email = meta_data.get("user_email")
#                 user_type = meta_data.get("user_type")

#                 if user_type == "AGENT_BVN_CHECK":

#                     if resource_retreive_flow_id is not None and resource_retreive_status is not None:
#                         if resource_retreive_flow_id == flow_id:

#                             face_match_score = retrieve_resource.get("steps")[0]["data"]["facematchScore"]
#                             bvn_number = retrieve_resource.get("steps")[2]["data"]["bvn"]
                                
#                             get_bvn_check_error = None

#                             if face_match_score > 70:
#                                 bvn_rel.verification_status = "SUCCESSFUL"
#                                 bvn_rel.is_verified = True
#                                 bvn_rel.date_added = datetime.now()
#                                 bvn_rel.payload = str(retrieve_resource)
#                                 bvn_rel.save()

                            
#                             else:
#                                 get_bvn_check_error = "Face Does not match"

#                                 bvn_rel.failure_reason = get_bvn_check_error
#                                 bvn_rel.save()
                            

#                         else:
#                             pass
#                     else:
#                         pass
                
#                 else:
#                     pass

#         return Response({"status": "True"}, status=status.HTTP_200_OK)



  # def test_save(self):
    #     with transaction.atomic():

    #         wallet = WalletSystem.objects.select_for_update().filter(wallet_type="COLLECTION", user__id=3).first()

    #         amount = 200
    #         balance_before = wallet.available_balance
    #         balance_after = wallet.available_balance + amount

    #         wallet.available_balance = balance_after

    #         hold_balance_before = wallet.hold_balance
    #         hold_balance_after = wallet.hold_balance
            
    #         wallet.save()


    #     record = DebitCreditRecordOnAccount.objects.create(
    #         user = wallet.user,
    #         entry = "CREDIT",
    #         wallet = wallet,
    #         wallet_type = "COLLECTION",
    #         balance_before = balance_before,
    #         amount = amount,
    #         balance_after = balance_after,
    #         type_of_trans = "TEST_SAVE",
    #         hold_balance_before = hold_balance_before,
    #         hold_balance_after = hold_balance_after,
    #         transaction_instance_id = None
    #     )


    # def handle(self, *args, **kwargs):
    #     import time

    #     start_time = time.time()

    #     # base = self.test_save()
    #     # base = self.test_save()
    #     # base = self.test_save()

    #     end_time = time.time()
    #     elapsed_time = end_time - start_time

    #     print(elapsed_time)
    #     # self.handle()
    #     # self.handle()

       