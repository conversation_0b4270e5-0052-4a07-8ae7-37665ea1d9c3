# import pytest
# from mock import patch

# from mixer.backend.django import mixer

# from django.urls import reverse

# from rest_framework.test import APIClient
# from rest_framework import status
# from rest_framework.test import APITestCase

# from main.helper.helper_function import passcode_generator

# pytestmark = pytest.mark.django_db
# client = APIClient()
# PHONE = 2349070010012
# EMAIL = '<EMAIL>'


# class InflowTest(APITestCase):

#     @patch('pos_onboarding.views.send_email')
#     def test_vfd_funding_callback(self, send_email):
#         mixer.blend('main.User', phone_number=PHONE)
#         mixer.blend('pos_onboarding.AgentProfile', phone_no=PHONE)
#         send_email.return_value = True
#         url = reverse('pos:update_profile', kwargs={'phone': PHONE})
#         data = {
#             "username": "testuser",
#             "first_name": "testuser",
#             "last_name": "oluwadamilare",
#             "email": "<EMAIL>",
#             "state": "lagos",
#             "lga": "Alimosho",
#             "nearestlandmark": "idimu....",
#             "street": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer nisi tortor"
#         }
#         data2 = {}
#         response = client.put(url, data, format='json')
#         response2 = client.put(url, data2, format='json')
#         assert response.status_code == status.HTTP_200_OK
#         assert response2.status_code == status.HTTP_400_BAD_REQUEST

#     def test_passcode_generator(self):
#         data = passcode_generator()
#         assert len(data) == 6

#     @patch('pos_onboarding.views.authenticate')
#     def test_auth_user(self, auth):
#         obj = mixer.blend('main.User', email=EMAIL, password='128793')
#         url = reverse('pos:verify_passcode', kwargs={'email': obj.email})
#         data = {"passcode": obj.password}

#         auth.return_value = True
#         reponse = client.post(url, data, format='json')
#         assert reponse.status_code == status.HTTP_202_ACCEPTED
