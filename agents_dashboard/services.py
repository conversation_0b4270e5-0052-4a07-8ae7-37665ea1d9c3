from accounts.models import (
    Transaction, AccountSystem, WalletSystem, OtherCommissionsRecord, 
    SalesRepCommissionTable, BillsPaymentDumpData)
from admin_dashboard.models import (
    TerminalTypePrice, BunchOfBalance, TerminalRequest, Stock, Zone, SalesRep, StockRequest, Branch,
    AdminDashboardLogHistory, Dispute
    )
from kyc_app.models import BVNDetail, GuarantorDetail
from horizon_pay.models import TerminalSerialTable, ConstantTable

from django.contrib.auth import get_user_model
User = get_user_model()

from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd
import os, json, calendar

from django.conf import settings
from django.utils import timezone

from django.db.models import Sum, Count, Q, Avg, F

from admin_dashboard.helpers.helpers import (
    send_agent_activity_email_task, get_percentage_diff, date_utility, Paginator
    )

# Date utilities
filter_date = date_utility(datetime=datetime)
previous_month_start = filter_date.get("previous_month_start")
previous_month_end = filter_date.get("previous_month_end")
start_of_all_users = filter_date.get("start_of_all_transactions")
previous_year_current_month_start = filter_date.get("previous_year_current_month_start")
previous_year_current_month_end = filter_date.get("previous_year_current_month_end")
previous_year_current_previous_month = filter_date.get("previous_year_current_previous_month")
year_end = filter_date.get("year_end")
year_start = filter_date.get("year_start")
init_start = filter_date.get("init_start")
previous_year_current_following_month = filter_date.get("previous_year_current_following_month")
start_of_all_transactions = filter_date.get("start_of_all_transactions")
month_start = filter_date.get("month_start")
today = filter_date.get("today")
previous_day = filter_date.get("previous_day") 
previous_year_end = filter_date.get("previous_year_end") 
previous_year_start = filter_date.get("previous_year_start")
week_start = filter_date.get("week_start")
date_from = filter_date.get("date_from")
date_today = filter_date.get("date_today")
month_start = filter_date.get("month_start")
month_ago = filter_date.get("month_ago")

# Create an array of the month keys
months_list = list(range(previous_year_current_month_start.month, (previous_year_current_month_start.month+13-previous_year_current_month_start.month)))
months_list2 = list(range(1, 12-(12-previous_year_current_month_start.month)+1))
months_list_names = [ 
                      f"{calendar.month_name[month][:3]} {previous_year_current_month_start.year}" 
                      for month in months_list
                    ]
months_list2_names = [f"{calendar.month_name[month][:3]} {today.year}" for month in months_list2]
months_list_names = months_list_names + months_list2_names
months_list = months_list + months_list2


all_possible_transaction_list = [
                                "SEND_BUDDY", "SEND_BANK_TRANSFER", "FUND_BUDDY",
                                "FUND_BANK_TRANSFER", "FUND_PAYSTACK", "CARD_TRANSACTION_FUND",
                                "CARD_TRANSACTION_FUND_TRANSFER", "CARD_PURCHASE"
                                ]

inflow_list = ["FUND_BANK_TRANSFER", "CARD_TRANSACTION_FUND_TRANSFER", "FUND_PAYSTACK"]




# Dashboard Data
class DashData:
    def get_dashboard_data(start_date, end_date, request):
        user = request.user
        base_transactions = Transaction.objects.filter(user=user, status="SUCCESSFUL")

        # try:
        #     float_balance = WalletSystem.objects.filter(user=user, wallet_type="COLLECTION").last().available_balance
        # except:
        #     float_balance = 0
        try:
            whisper_balance = WalletSystem.objects.filter(user=user, wallet_type="WHISPER").last().available_balance
        except:
            whisper_balance = 0

        # Wallets
        wallets_overall = list(WalletSystem.objects.filter(user=user).aggregate(Sum('available_balance')).values())[0]
        pos_wallets = list(WalletSystem.objects.filter(user__terminal_id__isnull=False, user=user).aggregate(Sum('available_balance')).values())[0]
        mobile_wallets = list(WalletSystem.objects.filter(user__terminal_id__isnull=True, user=user).aggregate(Sum('available_balance')).values())[0]

        #INFLOWS
        overall_inflow_qs = base_transactions.filter(date_created__range=[start_date, end_date], transaction_type__in=inflow_list)
        overall_inflow_today_qs = base_transactions.filter(date_created__date=datetime.now().date(), transaction_type__in=inflow_list)
        overall_inflow_year_qs = base_transactions.filter(date_created__range=[year_start, today], transaction_type__in=inflow_list)
        overall_inflow_month_qs = base_transactions.filter(date_created__range=[month_start, today], transaction_type__in=inflow_list)
        overall_inflow_week_qs = base_transactions.filter(date_created__gte=week_start, date_created__lte=today, transaction_type__in=inflow_list)
        overall_inflow_previous_year_qs = base_transactions.filter(date_created__gte=previous_year_start, date_created__lte=previous_year_end, transaction_type__in=inflow_list)
        overall_inflow_previous_day_qs = base_transactions.filter(date_created__gte=previous_day, date_created__lt=today, transaction_type__in=inflow_list)

        overall_inflow_amount = overall_inflow_qs.aggregate(Sum("amount"))["amount__sum"]
        overall_inflow_amount_year = overall_inflow_year_qs.aggregate(Sum("amount"))["amount__sum"]
        overall_inflow_amount_today = overall_inflow_today_qs.aggregate(Sum("amount"))["amount__sum"]
        overall_inflow_amount_month = overall_inflow_month_qs.aggregate(Sum("amount"))["amount__sum"]
        overall_inflow_amount_week = overall_inflow_week_qs.aggregate(Sum("amount"))["amount__sum"]
        overall_inflow_amount_previous_year = overall_inflow_previous_year_qs.aggregate(Sum("amount"))["amount__sum"]
        overall_inflow_amount_previous_day = overall_inflow_previous_day_qs.aggregate(Sum("amount"))["amount__sum"]

        # Total Users
        # total_users_qs = User.objects.filter(date_joined__range=[start_date, end_date])
        # total_users_current = total_users_qs.count()

        # total_users_previous_qs = User.objects.filter(date_joined__range=[previous_month_start, previous_month_end])
        # total_users_previous = total_users_previous_qs.count()

        # total_users_change = get_percentage_diff(previous = total_users_previous, current = total_users_current)

        # total_users_till_last_month_end = User.objects.filter(date_joined__range=[start_date, previous_month_end]).count()
        # total_new_users_till_last_month_end = total_users_till_last_month_end - total_users_previous

        # current_month_new_users = total_users_current - total_users_till_last_month_end
        # current_month_new_users = User.objects.filter(date_joined__range=[month_start, today]).count()
        # new_users_change = get_percentage_diff(previous=total_users_previous, current=current_month_new_users)

        #Total Sales POS
        # total_sales_pos_amount_current = TerminalTypePrice.objects.filter(Q(date_created__range=[start_date,end_date])).\
        #     aggregate(Sum("price"))["price__sum"]
        # total_sales_pos_amount_previous_month = TerminalTypePrice.objects.filter(date_created__range=[previous_month_end,previous_month_start]).\
        #     aggregate(Sum("price"))
        # total_pos_sales_change = get_percentage_diff(total_sales_pos_amount_previous_month,total_sales_pos_amount_current)

        # KYC Success Rate
        # total_kyc_users = total_users_current
        # complete_kyc_users = total_users_qs.filter(kyc_level__gte="2").count()
        # incomplete_kyc_users = total_users_qs.exclude(kyc_level__gte="2").count()

        # complete_kyc_users_change = float("{:.1f}".format(complete_kyc_users / total_kyc_users * 100))

        terminals_overall_qs = TerminalSerialTable.objects.filter(date_created__range=[start_date, end_date], user=user)
        terminals_overall = terminals_overall_qs.count()
        active_terminals = terminals_overall_qs.filter().count()
        partially_inactive_terminals = terminals_overall_qs.filter().count()
        inactive_terminals = terminals_overall_qs.filter().count()
        lotto_terminals = terminals_overall_qs.filter().count()
        agency_banking_terminals = terminals_overall - lotto_terminals if lotto_terminals else 0

        if terminals_overall:
            terminals_percentage_active = active_terminals/terminals_overall * 100
            terminals_percentage_inactive = inactive_terminals/terminals_overall * 100
            terminals_percentage_partially_inactive = partially_inactive_terminals/terminals_overall * 100
        else:
            terminals_percentage_active = 0
            terminals_percentage_inactive = 0
            terminals_percentage_partially_inactive = 0

        midnight_float_account_balance = "-"
        midnight_overall_wallet_balance = "-"
        midnight_pos_wallet_balance = "-"
        midnight_mobile_wallet_balance = "-"

        for balance in BunchOfBalance.objects.all():
            # if balance.balance_name == "midnight_agent_account_balance":
            #     midnight_float_account_balance = balance.amount

            if balance.balance_name == "midnight_overall_agent_wallet_balance":
                midnight_overall_wallet_balance = balance.amount

            elif balance.balance_name == "midnight_agent_pos_wallet_balance":
                midnight_pos_wallet_balance = balance.amount

            elif balance.balance_name == "midnight_agent_mobile_wallet_balance":
                midnight_mobile_wallet_balance = balance.amount

        response = {
            "dashData": {
                # "float_balance": float_balance,
                # "midnight_float_account_balance": midnight_float_account_balance,

                "whisper_balance": whisper_balance,

                # "totalSalesPOS": {
                #     "total": total_sales_pos_amount_current if total_sales_pos_amount_current is not None else 0,
                #     "percentage": total_pos_sales_change.get("percentage"),
                #     "change": total_pos_sales_change.get("change")
                # },

                "totalTerminal":{
                    "total": terminals_overall,
                    "active_terminals": active_terminals,
                    "inactive_terminals": inactive_terminals,
                    "partially_inactive_terminals": partially_inactive_terminals,
                    "active_terminals_percentage": terminals_percentage_active,
                    "inactive_terminals_percentage": terminals_percentage_inactive,
                    "partially_inactive_terminals_percentage": terminals_percentage_partially_inactive,
                    "agency_banking_terminals": agency_banking_terminals,
                    "lotto_agents_terminals": lotto_terminals
                },

                "SmsNotificationWalletBalance": {
                    "total": whisper_balance
                },

                "overall_wallets_balance": {
                    "overall": wallets_overall if wallets_overall is not None else 0,
                    "pos": pos_wallets if pos_wallets is not None else 0,
                    "mobile": mobile_wallets if mobile_wallets is not None else 0,

                    "midnight_overall_wallet_balance": midnight_overall_wallet_balance,
                    "midnight_pos_wallet_balance": midnight_pos_wallet_balance,
                    "midnight_mobile_wallet_balance": midnight_mobile_wallet_balance,
                },

                # "users": {
                #     "total": total_users_current,
                #     "total_new_customers": current_month_new_users,
                #     "new_users_percentage": str(new_users_change.get("percentage")) if type(new_users_change.get("percentage")) is not float else 0.0,
                #     "new_users_change": str(new_users_change.get("change")),
                #     "percentage": str(total_users_change.get("percentage")),
                #     "change": total_users_change.get("change")
                # },

                "totalInflow": {
                    "overall_count": overall_inflow_year_qs.count() + overall_inflow_previous_year_qs.count(),
                    "today": overall_inflow_amount_today if overall_inflow_amount_today else 0.0,
                    "this_month": overall_inflow_amount_month if overall_inflow_amount_month else 0.0,
                    "this_year": overall_inflow_amount_year if overall_inflow_amount_year else 0.0,
                    "week": overall_inflow_amount_week if overall_inflow_amount_week else 0.0,
                    "previous_year": overall_inflow_amount_previous_year if overall_inflow_amount_previous_year else 0.0,
                    "yesterday": overall_inflow_amount_previous_day if overall_inflow_amount_previous_day else 0.0
                },

                # "kycSuccessRate": {
                #     "total": total_kyc_users,
                #     "completed": complete_kyc_users,
                #     "incomplete": incomplete_kyc_users,
                #     "percentage_complete": complete_kyc_users_change
                # },
            }
        }

        return response
    

class TransactionComparatives:
    def get_transaction_comparatives(start_date, end_date, lower_amount, upper_amount, user):
        # user = WalletSystem.get_user()
        base_transactions = Transaction.objects.filter(user=user)

        all_possible_transactions_qs = base_transactions.filter(
            transaction_type__in=all_possible_transaction_list, is_reversed=False, status="SUCCESSFUL"
            ).exclude(transaction_type="SEND_BANK_TRANSFER", transaction_leg__in=["INTERNAL", "COMMISSIONS"])

        #Total Transaction Amount
        send_money_transaction_type_list = ["SEND_BANK_TRANSFER", "SEND_BUDDY"]
        cashout_transaction_type_list = ["CARD_TRANSACTION_FUND_TRANSFER", "CARD_TRANSACTION_FUND"]

        # Total Transaction Amount
        total_transaction_amount_qs = all_possible_transactions_qs.filter(date_created__range=[start_date, end_date])
        total_transaction_amount = total_transaction_amount_qs.aggregate(Sum("amount"))["amount__sum"]
        
        data_trns_amount = BillsPaymentDumpData.objects.filter(
            user=user,
            date_added__range=[start_date, end_date], 
            status="SUCCESSFUL", biller__endswith="DATA"
            ).aggregate(Sum("amount"))["amount__sum"]

        total_transaction_amount_previous_month_qs = all_possible_transactions_qs.filter(date_created__range=[previous_month_start,previous_month_end])
        total_transaction_amount_previous_month = total_transaction_amount_previous_month_qs.aggregate(Sum("amount"))["amount__sum"]

        # Total Agent Amount
        total_agent_transaction_amount_qs = all_possible_transactions_qs.filter(date_created__range=[start_date,end_date], user__terminal_id__isnull=False)
        total_agent_transaction_amount = total_agent_transaction_amount_qs.aggregate(Sum("amount"))["amount__sum"]

        total_agent_transaction_amount_previous_month_qs = all_possible_transactions_qs.filter(date_created__range=[previous_month_start, previous_month_end], user__terminal_id__isnull=False)
        total_agent_transaction_amount_previous_month = total_agent_transaction_amount_previous_month_qs.aggregate(Sum("amount"))["amount__sum"]

        # Total Mobile Amount
        total_transaction_amount_mobile = (total_transaction_amount if total_agent_transaction_amount else 0) - (total_agent_transaction_amount if total_agent_transaction_amount else 0)

        total_transaction_amount_change = get_percentage_diff(previous=total_transaction_amount_previous_month, current=total_transaction_amount)

        total_transaction_count = total_transaction_amount_qs.count()
        total_transaction_count_pos = total_agent_transaction_amount_qs.count()
        total_transaction_count_mobile = total_transaction_count if total_transaction_count else 0 - total_transaction_count_pos if total_transaction_count_pos else 0
        total_transaction_count_previous_month = total_transaction_amount_previous_month_qs.count()

        total_transaction_count_change = get_percentage_diff(previous=total_transaction_count_previous_month,current=total_transaction_count)

        # AverageTransactionCOuntPerAgent
        total_number_of_agents = User.objects.filter(Q(date_joined__range=[start_date, end_date], terminal_id__isnull=False) & ~Q(terminal_id="")).count()
        total_number_of_agents_till_previous_month = User.objects.filter(Q(date_joined__range=[start_date, previous_month_end], terminal_id__isnull=False) & ~Q(terminal_id="")).count()

        try:
            average_transaction_count_per_agent = (total_transaction_count_pos / total_number_of_agents)
        except Exception:
            average_transaction_count_per_agent = 0

        try:
            average_transaction_count_previous_month = total_transaction_count_previous_month / total_number_of_agents_till_previous_month
        except:
            average_transaction_count_previous_month = 0

        average_transaction_per_agent_change = get_percentage_diff(previous=average_transaction_count_previous_month, current=average_transaction_count_per_agent)

        # Average Agent Transactions
        average_agent_transaction_amount = (total_agent_transaction_amount if total_agent_transaction_amount else 0) / (total_number_of_agents if total_number_of_agents else 0)
        try:
            average_agent_transaction_amount_previous = total_agent_transaction_amount_previous_month / total_number_of_agents_till_previous_month
        except:
            average_agent_transaction_amount_previous = 0

        average_transaction_amount_per_agent_change = get_percentage_diff(current=average_agent_transaction_amount,
        previous=average_agent_transaction_amount_previous)

        # Comparatives
        pos_withdrawals = base_transactions.filter(Q(date_created__range=[start_date, end_date])
            & Q(transaction_type__in=cashout_transaction_type_list) & Q(status="SUCCESSFUL")) \
                .aggregate(Sum('amount'))["amount__sum"]

        pos_withdrawals_count = base_transactions.filter(Q(date_created__range=[start_date, end_date])
            & Q(transaction_type__in=cashout_transaction_type_list) & Q(status="SUCCESSFUL")) \
                .count()

        send_money_trns_qs = base_transactions.filter(date_created__range=[start_date, end_date]) \
            .filter(Q(transaction_type__in=send_money_transaction_type_list, transaction_leg__in=["COMMISSIONS", "INTERNAL"]) & Q(status="SUCCESSFUL"))

        send_money_trns = send_money_trns_qs.aggregate(Sum('amount'))["amount__sum"]
        send_money_count = base_transactions.count()

        #Average Workdays
        agent_work_days = base_transactions.filter(date_created__range=[start_date, end_date], terminal_id__isnull=False).\
            exclude(terminal_id="").values("date_created__year", "date_created__year", "date_created__month", "date_created__day") \
                .distinct("date_created__day").filter(amount__isnull=False).count()

        try:
            average_agent_work_days = agent_work_days / total_number_of_agents
        except:
            average_agent_work_days = 0

        agent_work_days_previous_month = base_transactions.filter(Q(date_created__range=[start_date, previous_month_end], terminal_id__isnull=False)). \
            exclude(terminal_id="").values("date_created__year", "date_created__year", "date_created__month", "date_created__day") \
                .distinct("date_created__day").filter(amount__isnull=False).count()
        try:
            average_agent_work_days_previous_month = agent_work_days_previous_month / total_number_of_agents_till_previous_month
        except:
            average_agent_work_days_previous_month = 0

        agent_average_work_days_change = get_percentage_diff(previous=average_agent_work_days_previous_month, current=average_agent_work_days)

        # CASHOUT / WITDRAWALS
        cashout_today_qs = base_transactions.filter(
            Q(date_created__date=datetime.now().date())
            & Q(transaction_type__in=cashout_transaction_type_list, status="SUCCESSFUL"))

        cashout_month_qs = base_transactions.filter(Q(date_created__range=[month_start, today])
             & Q(transaction_type__in=cashout_transaction_type_list, status="SUCCESSFUL"))

        cashout_year_qs =base_transactions.filter(Q(date_created__range=[year_start, today])
             & Q(transaction_type__in=cashout_transaction_type_list, status="SUCCESSFUL"))

        cashout_week_qs =base_transactions.filter(Q(date_created__gte=week_start, date_created__lte=today)
             & Q(transaction_type__in=cashout_transaction_type_list, status="SUCCESSFUL"))

        cashout_prev_year_qs =base_transactions.filter(Q(date_created__range=[previous_year_start, previous_year_end])
             & Q(transaction_type__in=cashout_transaction_type_list, status="SUCCESSFUL"))

        cashout_prev_day_qs =base_transactions.filter(Q(date_created__gte=previous_day, date_created__lt=today)
             & Q(transaction_type__in=cashout_transaction_type_list, status="SUCCESSFUL"))


        cashout_amount_today = cashout_today_qs.aggregate(Sum("amount"))["amount__sum"]
        cashout_amount_month = cashout_month_qs.aggregate(Sum("amount"))["amount__sum"]
        cashout_amount_year = cashout_year_qs.aggregate(Sum("amount"))["amount__sum"]
        cashout_amount_prev_year = cashout_prev_year_qs.aggregate(Sum("amount"))["amount__sum"]
        cashout_amount_week = cashout_week_qs.aggregate(Sum("amount"))["amount__sum"]
        cashout_amount_yesterday = cashout_prev_day_qs.aggregate(Sum("amount"))["amount__sum"]

        base_failed_transactions_qs = base_transactions.filter(date_created__range=[start_date, end_date], status="FAILED")

        failed_cashout_transactions_qs = base_failed_transactions_qs \
            .filter(transaction_type__in=cashout_transaction_type_list)

        failed_sendmoney_transactions_qs = base_failed_transactions_qs \
            .filter(transaction_type__in=send_money_transaction_type_list) \
                .exclude(transaction_leg__in=["COMMISSIONS", "INTERNAL"])


        failed_cashout_transactions_amount = failed_cashout_transactions_qs.aggregate(Sum("amount"))["amount__sum"]
        failed_sendmoney_transactions_amount = failed_sendmoney_transactions_qs.aggregate(Sum("amount"))["amount__sum"]

        failed_cashout_transactions_count = failed_cashout_transactions_qs.count()
        failed_sendmoney_transactions_count = failed_sendmoney_transactions_qs.count()

        # TOTAL TRANSFERS
        transfers_today_qs = base_transactions.filter(
            Q(date_created__date=datetime.now().date(), 
            transaction_type = "SEND_BANK_TRANSFER", status="SUCCESSFUL", transaction_leg="EXTERNAL")
                                                    )

        transfers_month_qs = base_transactions.filter(
            Q(date_created__range=[month_start, today], 
            transaction_type = "SEND_BANK_TRANSFER", status="SUCCESSFUL", transaction_leg="EXTERNAL")
                                                 )

        transfers_year_qs = base_transactions.filter(
            Q(date_created__range=[year_start, today], 
            transaction_type = "SEND_BANK_TRANSFER", status="SUCCESSFUL", transaction_leg="EXTERNAL")
                                                )

        transfers_week_qs = base_transactions.filter(
            Q(date_created__range=[week_start, today], 
            transaction_type = "SEND_BANK_TRANSFER", status="SUCCESSFUL", transaction_leg="EXTERNAL")
                                                )

        transfers_previous_year_qs = base_transactions.filter(
            Q(date_created__range=[previous_year_start, previous_year_end], transaction_type = "SEND_BANK_TRANSFER", status="SUCCESSFUL", transaction_leg="EXTERNAL")
                                                )

        transfers_previous_day_qs = base_transactions.filter(
            Q(date_created__gte=previous_day, date_created__lt=today, transaction_type = "SEND_BANK_TRANSFER", status="SUCCESSFUL", transaction_leg="EXTERNAL")
                                                )

        transfers_amount_today = transfers_today_qs.aggregate(Sum("amount"))["amount__sum"]

        transfers_amount_month = transfers_month_qs.aggregate(Sum("amount"))["amount__sum"]
        transfers_amount_year = transfers_year_qs.aggregate(Sum("amount"))["amount__sum"]
        transfers_amount_week = transfers_week_qs.aggregate(Sum("amount"))["amount__sum"]
        transfers_amount_previous_year = transfers_previous_year_qs.aggregate(Sum("amount"))["amount__sum"]
        transfers_amount_previous_day = transfers_previous_day_qs.aggregate(Sum("amount"))["amount__sum"]


        # TOP4 TRANSACTIONS RANGE
        top10_transactions_qs = base_transactions.filter(Q(date_created__range=[start_date, end_date])
            & Q(transaction_type__in=all_possible_transaction_list, status="SUCCESSFUL"))

        top10_1k_5k = top10_transactions_qs.filter(amount__gte=1000, amount__lt=5000).count()
        top10_5k_10k = top10_transactions_qs.filter(amount__gte=5000, amount__lt=10000).count()
        top10_10k_20k = top10_transactions_qs.filter(amount__gte=10000, amount__lt=20000).count()
        top10_20k_50k = top10_transactions_qs.filter(amount__gte=20000, amount__lt=50000).count()
        top10_50k_100k = top10_transactions_qs.filter(amount__gte=50000, amount__lt=100000).count()
        top10_100k_150k = top10_transactions_qs.filter(amount__gte=100000, amount__lt=150000).count()
        top10_150k_200k = top10_transactions_qs.filter(amount__gte=150000, amount__lt=200000).count()
        top10_200k_250k = top10_transactions_qs.filter(amount__gte=200000, amount__lt=250000).count()
        top10_250k_500k = top10_transactions_qs.filter(amount__gte=250000, amount__lt=500000).count()
        top10_500k_1m = top10_transactions_qs.filter(amount__gte=500000, amount__lt=1000000).count()
        top10_lower_upper_amt = top10_transactions_qs.filter(amount__gte=lower_amount, amount__lte=upper_amount).count()

        top4_5k_20k_qs = base_transactions.filter(Q(date_created__range=[start_date, end_date])
            & Q(transaction_type__in=all_possible_transaction_list, status="SUCCESSFUL")
                        & Q(amount__gte=5000, amount__lte=20000))

        top4_50k_100k_qs = base_transactions.filter(Q(date_created__range=[start_date, end_date])
                            & Q(transaction_type__in=all_possible_transaction_list, status="SUCCESSFUL")
                        & Q(amount__gte=50000, amount__lte=100000))

        top4_100k_200k_qs = base_transactions.filter(Q(date_created__range=[start_date, end_date])
                            & Q(transaction_type__in=all_possible_transaction_list, status="SUCCESSFUL")
                            & Q(amount__gt=100000, amount__lte=200000))

        top4_200k_500k_qs = base_transactions.filter(Q(date_created__range=[start_date, end_date]) & Q(status="SUCCESSFUL")
                            & Q(transaction_type__in=all_possible_transaction_list, status="SUCCESSFUL")
                        & Q(amount__gte=200000, amount__lte=500000))

        top4_5k_20k_count = top4_5k_20k_qs.count()
        top4_50k_100k_count = top4_50k_100k_qs.count()
        top4_200k_500k_count = top4_200k_500k_qs.count()
        top4_100k_200k_count = top4_100k_200k_qs.count()

        # AIRTIME - DATA - UTILITIES
        airtime_trns_amount = base_transactions.filter(Q(transaction_type="AIRTIME_PIN")
                        & Q(date_created__range=[start_date,end_date]) & Q(status="SUCCESSFUL")).aggregate(Sum("amount"))["amount__sum"]

        bills_trns_amount = base_transactions.filter(Q(transaction_type="BILLS_AND_PAYMENT")
                        & Q(date_created__range=[start_date,end_date]) & Q(status="SUCCESSFUL")).aggregate(Sum("amount"))["amount__sum"]

        transaction_comparatives_list = [
                                        pos_withdrawals if pos_withdrawals else 0,
                                        send_money_trns if send_money_trns else 0,
                                        airtime_trns_amount if airtime_trns_amount else 0,
                                        data_trns_amount if data_trns_amount else 0,
                                        bills_trns_amount if bills_trns_amount else 0
                                        ]

        response = {
            # Transaction Comparatives
                "transactionComparatives": {
                    "pos_withdrawals": pos_withdrawals if pos_withdrawals else 0,
                    "send_money": send_money_trns if send_money_trns is not None else 0,
                    "pos_withdrawals_count": pos_withdrawals_count if pos_withdrawals_count is not None else 0,
                    "send_money_count": send_money_count if send_money_count is not None else 0,
                    "airtime_pin": airtime_trns_amount if airtime_trns_amount is not None else 0,
                    "data_bundle": data_trns_amount if data_trns_amount else 0,
                    "utilities": bills_trns_amount if bills_trns_amount is not None else 0
                },

                #Total Transactions
                "totalTransactionsAmount": {
                    "total":total_transaction_amount,
                    "terminal_transactions": total_agent_transaction_amount,
                    "mobile_transactons": total_transaction_amount_mobile if total_transaction_amount_mobile is not None else 0,
                    "percentage": str(total_transaction_amount_change.get("percentage")),
                    "change": total_transaction_amount_change.get("change")
                },

                #Total Transaction Count
                "totalTransactionsCount": {
                    "total":total_transaction_count,
                    "terminal_transactions": total_transaction_count_pos,
                    "mobile_transactions": total_transaction_count_mobile,
                    "percentage": str(total_transaction_count_change.get("percentage")),
                    "change": total_transaction_count_change.get("change")
                },
                # Average Agent Transaction
                "averageAgentTransactions": {
                    "total": average_agent_transaction_amount,
                    "percentage": str(average_transaction_amount_per_agent_change.get("percentage")),
                    "change": average_transaction_amount_per_agent_change.get("change")
                },
                # Not Done --> Done
                "averageWorkDays": {
                    "total": average_agent_work_days,
                    "percentage": str(agent_average_work_days_change.get("percentage")),
                    "change": agent_average_work_days_change.get("change")
                },

                 # Not Done ---> Done
                "averageTransactionCountPerAgent": {
                    "total": average_transaction_count_per_agent,
                    "percentage": str(average_transaction_per_agent_change.get("percentage")),
                    "change": average_transaction_per_agent_change.get("change")
                },

                # Cashout Amount
                "cashOut": {
                    "today": cashout_amount_today if cashout_amount_today else 0.0,
                    "month": cashout_amount_month if cashout_amount_month else 0.0,
                    "year": cashout_amount_year if cashout_amount_year else 0.0,
                    "week": cashout_amount_week if cashout_amount_week else 0.0,
                    "previous_year": cashout_amount_prev_year if cashout_amount_prev_year else 0.0,
                    "yesterday":  cashout_amount_yesterday if cashout_amount_yesterday else 0.0
                },

                # Top4 Transaction Amount
                "top4_range": {
                            "top4_5k_20k_count" : top4_5k_20k_count,
                            "top4_50k_100k_count" : top4_50k_100k_count,
                            "top4_100k_200k_count" : top4_100k_200k_count,
                            "top4_200k_500k_count" : top4_200k_500k_count
                         },

                "top10_range": {
                                "top10_1k_5k": top10_1k_5k,
                                "top10_5k_10k": top10_5k_10k,
                                "top10_10k_20k": top10_10k_20k,
                                "top10_20k_50k": top10_20k_50k,
                                "top10_50k_100k": top10_50k_100k,
                                "top10_100k_150k": top10_100k_150k,
                                "top10_150k_200k": top10_150k_200k,
                                "top10_200k_250k": top10_200k_250k,
                                "top10_250k_500k": top10_250k_500k,
                                "top10_500k_1m": top10_500k_1m,
                                "top10_lower_upper_amt": top10_lower_upper_amt if top10_lower_upper_amt else ""
                                },

                "total_transfer": {
                            "today": transfers_amount_today if transfers_amount_today else 0.0,
                            "month": transfers_amount_month if transfers_amount_month else 0.0,
                            "year": transfers_amount_year if transfers_amount_year else 0.0,
                            "week": transfers_amount_week if transfers_amount_week else 0.0,
                            "previous_year": transfers_amount_previous_year if transfers_amount_previous_year else 0.0,
                            "yesterday": transfers_amount_previous_day if transfers_amount_previous_day else 0.0
                        },

                "failed_transactions": {
                            "pos_withdrawal": failed_cashout_transactions_amount if failed_cashout_transactions_amount else 0,
                            "send_money": failed_sendmoney_transactions_amount if failed_sendmoney_transactions_amount else 0,
                            "pos_withdrawal_count": failed_cashout_transactions_count if failed_cashout_transactions_count else 0,
                            "send_money_count": failed_sendmoney_transactions_count if failed_sendmoney_transactions_count else 0
                },

                "transactionComparativesBarChart": transaction_comparatives_list
                }
        return response


class DashDataTwo:
    def get_dashboard_data_two(start_date, end_date, user):
        # user = WalletSystem.get_user()
        try:
            agent_balance = WalletSystem.objects.filter(user=user, wallet_type="FLOAT").last().available_balance
        except:
            agent_balance = 0

        base_transactions = Transaction.objects.filter(user=user)

        overall_total_commissions_in_qs = base_transactions.filter(date_created__range=[start_date, end_date], liberty_commission__isnull=False)
        total_commissions_in_amt = list(overall_total_commissions_in_qs.aggregate(Sum('liberty_commission')).values())[0]

        wallets_overall = list(WalletSystem.objects.exclude(user=user).filter(wallet_type="COLLECTION").aggregate(Sum('available_balance')).values())[0]
        pos_wallets_overall = list(WalletSystem.objects.exclude(user=user).filter(user__terminal_id__isnull=False).aggregate(Sum('available_balance')).values())[0]
        mobile_wallets_overall = list(WalletSystem.objects.exclude(user=user).filter(user__terminal_id__isnull=True).aggregate(Sum('available_balance')).values())[0]

        total_transaction_count = base_transactions.filter(Q(date_created__range=[start_date, end_date]) &
            Q(transaction_type__in = all_possible_transaction_list, transaction_leg="EXTERNAL", status="SUCCESSFUL")
                    ).count()

        total_transaction_count_pos = base_transactions.filter(Q(date_created__range=[start_date,end_date]) & Q(user__terminal_id__isnull=False) &
            Q(transaction_type__in = all_possible_transaction_list, transaction_leg="EXTERNAL", status="SUCCESSFUL", user_wallet_type="POS")
                ).count()

        total_transaction_count_mobile = base_transactions.filter(Q(date_created__range=[start_date,end_date]) & Q(user__terminal_id__isnull=True) &
            Q(transaction_type__in = all_possible_transaction_list, transaction_leg="EXTERNAL", status="SUCCESSFUL") & Q(status="SUCCESSFUL")
                    ).count()
        try:
            agent_wallet_overall_commission = WalletSystem.objects.filter(Q(user=user) & Q(wallet_type="COMMISSIONS")).last().available_balance
        except:
            agent_wallet_overall_commission = 0

        try:
            sms_wallet_balance = WalletSystem.objects.filter(Q(user=user) & Q(wallet_type="WHISPER")).last().available_balance
        except:
            sms_wallet_balance = 0

        response = {
            "overall_wallet_balance": {
            "overall": wallets_overall if wallets_overall else 0,
            "pos_wallets": pos_wallets_overall if pos_wallets_overall else 0,
            "mobile_wallets": mobile_wallets_overall if mobile_wallets_overall else 0
            },
            "agent_wallet": agent_balance,
            "commissions_in": total_commissions_in_amt if total_commissions_in_amt is not None else "0",
            "SmsNotificationWalletBalance": sms_wallet_balance,
            "TransactionCount": {
                "overall": total_transaction_count,
                "mobile": total_transaction_count_mobile,
                "pos": total_transaction_count_pos
            }
        }
        return response


class TransactionAmountChart:
    def get_transactions_chart(start_date, end_date, user):
        base_transactions = Transaction.objects.filter(user=user).exclude(transaction_type="SEND_BANK_TRANSFER", transaction_leg__in=["INTERNAL", "COMMISSIONS"])

        total_transactions_amount  =  base_transactions.filter(
           Q(transaction_type__in=all_possible_transaction_list, status="SUCCESSFUL")
            ).aggregate(Sum("amount"))["amount__sum"]

        total_transactions_til_previous_month  =  base_transactions.filter(Q(date_created__range=[start_of_all_transactions, previous_month_end])
           & Q(transaction_type__in=all_possible_transaction_list, status="SUCCESSFUL")
                ).aggregate(Sum("amount"))["amount__sum"]

        total_transactions_amount_monthly_chart = base_transactions.filter(
            Q(date_created__gte=previous_year_current_month_start) &
            Q(transaction_type__in=all_possible_transaction_list, status="SUCCESSFUL")
            ).values("date_created__year", "date_created__month").annotate(total_amount = Sum("amount")
            ).order_by("date_created__year")
        transaction_amount_past_year_list = [(obj["date_created__year"], obj["date_created__month"], obj["total_amount"]) for obj in total_transactions_amount_monthly_chart]

        total_transactions_change = get_percentage_diff(
                                    previous=total_transactions_til_previous_month,
                                    current=total_transactions_amount
                                    )

        all_month_transactions = []
        for month in months_list[:-1]:
            month_transactions = []
            month_sum = 0
            if transaction_amount_past_year_list:
                for transaction in (
                    transaction_amount_past_year_list[:len(transaction_amount_past_year_list)-1] 
                    if transaction_amount_past_year_list[-1][1]==date_today.month 
                    else transaction_amount_past_year_list
                    ):
                    if transaction[1] == month:
                        month_transactions.append(transaction[2])
                    month_sum = sum(month_transactions)
            all_month_transactions.append(month_sum)
        current_month_transaction_amount = transaction_amount_past_year_list[-1][2] if transaction_amount_past_year_list and transaction_amount_past_year_list[-1][1]==date_today.month else 0
        all_month_transactions.append(current_month_transaction_amount)

        response = {
                "overview": {
                "total_transactions": total_transactions_amount,
                "percentage_change": total_transactions_change.get("percentage"),
                "change": total_transactions_change.get("change")
                },
                "chart_labels": months_list_names,
                "chart_data": all_month_transactions,
            }
        return response


class TransactionCountChart:
    def get_transaction_count_chart(start_date, end_date, user):
        base_transactions = Transaction.objects.filter(user=user).exclude(transaction_type="SEND_BANK_TRANSFER", transaction_leg__in=["INTERNAL", "COMMISSIONS"])

        total_transactions_count  =  base_transactions.filter(
            Q(transaction_type__in=all_possible_transaction_list, status="SUCCESSFUL")).count()

        total_transactions_til_previous_month  =  base_transactions.filter(Q(date_created__range=[start_of_all_transactions,previous_month_end])
            & Q(transaction_type__in=all_possible_transaction_list, status="SUCCESSFUL")).count()

        total_transactions_count_monthly_chart = base_transactions.filter(
            Q(date_created__gte=previous_year_current_month_start) &
            Q(transaction_type__in=all_possible_transaction_list, status="SUCCESSFUL")).values("date_created__year", "date_created__month").annotate(total_count = Count("amount")).order_by("date_created__year")
        transaction_count_past_year_list = [(obj["date_created__year"], obj["date_created__month"], obj["total_count"]) for obj in total_transactions_count_monthly_chart]

        total_transactions_count_change = get_percentage_diff(previous=total_transactions_til_previous_month, current=total_transactions_count)

        all_month_transactions_count = []
        for month in months_list[:-1]:
            month_transactions = []
            month_sum = 0
            if transaction_count_past_year_list:
                for transaction in (
                    transaction_count_past_year_list[:len(transaction_count_past_year_list)-1]
                    if transaction_count_past_year_list[-1][1]==date_today.month
                    else transaction_count_past_year_list
                    ):
                    if transaction[1] == month:
                        month_transactions.append(transaction[2])
                    month_sum = sum(month_transactions)
            all_month_transactions_count.append(month_sum)
        current_month_transaction_count = transaction_count_past_year_list[-1][2] if transaction_count_past_year_list and transaction_count_past_year_list[-1][1]==date_today.month else 0
        all_month_transactions_count.append(current_month_transaction_count)

        response = {
                "overview": {"total_transactions_count": total_transactions_count,
                "percentage_change": str(total_transactions_count_change.get("percentage")),
                "change": total_transactions_count_change.get("change")
                },
                "chart_labels": months_list_names,
                "chart_data": all_month_transactions_count
                }
        return response
    

class CommissionCharts:
    def get_commission_charts(start_date, end_date, user):
        all_commissions_qs = OtherCommissionsRecord.objects.filter(transaction_owner="AGENT", agent=user)

        total_commission_amount = all_commissions_qs.values().aggregate(Sum("agent_cash_profit"))["agent_cash_profit__sum"]
        total_commision_amount_til_previous_month  =  all_commissions_qs.filter(date_created__lte=previous_month_end).aggregate(Sum("agent_cash_profit"))["agent_cash_profit__sum"]
        
        total_commisions_in_amount_monthly_chart = all_commissions_qs.filter(date_created__gte=previous_year_current_month_start).values(
                "date_created__year", "date_created__month", "agent_cash_profit").annotate(total_commission = Sum("agent_cash_profit")).order_by("date_created__year")
        total_commissions_in_past_year_list = [(obj["date_created__year"], obj["date_created__month"], obj["total_commission"]) for obj in total_commisions_in_amount_monthly_chart]

        total_cards_commisions_in_amount_monthly_chart = all_commissions_qs.filter(date_created__gte=previous_year_current_month_start, transaction_type="CASH_OUT").values(
            "date_created__year", "date_created__month", "agent_cash_profit").annotate(total_commission = Sum("agent_cash_profit")).order_by("date_created__year")
        total_cards_commissions_in_past_year_list = [(obj["date_created__year"], obj["date_created__month"], obj["total_commission"]) for obj in total_cards_commisions_in_amount_monthly_chart]

        # total_commisions_out_amount_monthly_chart = all_commissions_qs.filter(date_created__gte=previous_year_current_month_start
        #         ).values("date_created__year", "date_created__month", "liberty_profit").annotate(total_commission =
        #         Sum("liberty_profit"), commissions_out_amount=F("ro_cash_profit")+F("agent_cash_profit")).order_by("date_created__year")
        # total_commissions_out_past_year_list = [(obj["date_created__year"], obj["date_created__month"], obj["commissions_out_amount"], obj["total_commission"]) for obj in total_commisions_out_amount_monthly_chart]

        # total_cards_commisions_out_amount_monthly_chart = all_commissions_qs.filter(date_created__gte=previous_year_current_month_start, 
        #         transaction_type="CASH_OUT").values("date_created__year", "date_created__month", "liberty_profit").annotate(
        #         total_commission = Sum("liberty_profit"), commissions_out_amount=F("ro_cash_profit")+F("agent_cash_profit")).order_by("date_created__year")
        # total_cards_commissions_out_past_year_list = [(obj["date_created__year"], obj["date_created__month"], obj["commissions_out_amount"]) for obj in total_commisions_out_amount_monthly_chart]

        # total_commission_out_amount = total_commisions_out_amount_monthly_chart.aggregate(total=Sum("commissions_out_amount"))["total"]

        total_commission_amount_change = get_percentage_diff(
                                        previous=total_commision_amount_til_previous_month,
                                        current=total_commission_amount
                                        )

        all_month_commission = []
        for month in months_list[:-1]:
            month_commission = []
            month_sum = 0
            if total_commissions_in_past_year_list:
                for commision in (
                    total_commissions_in_past_year_list[:len(total_commissions_in_past_year_list)-1]
                    if total_commissions_in_past_year_list[-1][1]==date_today.month
                    else total_commissions_in_past_year_list
                    ):
                    if commision[2] and commision[1] == month:
                        month_commission.append(commision[2])
                month_sum = sum(month_commission)
            all_month_commission.append(month_sum)
        current_month_commission_in = total_commissions_in_past_year_list[-1][2] if total_commissions_in_past_year_list and total_commissions_in_past_year_list[-1][1]==date_today.month else 0
        all_month_commission.append(current_month_commission_in)


        # all_month_commission_out = []
        # for month in months_list[:-1]:
        #     month_commission = []
        #     month_sum = 0
        #     for commision in (
        #         total_commissions_out_past_year_list[:len(total_commissions_out_past_year_list)-1]
        #         if total_commissions_out_past_year_list[-1][1]==date_today.month
        #         else total_commissions_out_past_year_list):
        #         if commision[2] and commision[1] == month:
        #             comm_out_amount = commision[2] if commision[2] else 0
        #             month_commission.append(comm_out_amount)
        #         month_sum = sum(month_commission)
        #     all_month_commission_out.append(month_sum)
        # current_month_commission_out = total_commissions_out_past_year_list[-1][2] if total_commissions_out_past_year_list[-1][1]==date_today.month else 0
        # all_month_commission_out.append(current_month_commission_out)
        

        all_month_cards_commission_in = []
        for month in months_list[:-1]:
            month_commission = []
            month_sum = 0
            if total_cards_commissions_in_past_year_list:
                for commision in (
                    total_cards_commissions_in_past_year_list[:len(total_cards_commissions_in_past_year_list)-1]
                    if total_cards_commissions_in_past_year_list[-1][1]==date_today.month
                    else total_cards_commissions_in_past_year_list
                    ):
                    if commision[2] and commision[1] == month:
                        month_commission.append(commision[2])
                    month_sum = sum(month_commission)
            all_month_cards_commission_in.append(month_sum)
        current_month_cards_commission_in = total_cards_commissions_in_past_year_list[-1][2] if total_cards_commissions_in_past_year_list and total_cards_commissions_in_past_year_list[-1][1]==date_today.month else 0
        all_month_cards_commission_in.append(current_month_cards_commission_in)

        # all_month_cards_commission_out = []
        # for month in months_list[:-1]:
        #     month_commission = []
        #     month_sum = 0
        #     for commision in (
        #         total_cards_commissions_out_past_year_list[:len(total_cards_commissions_out_past_year_list)-1]
        #         if total_cards_commissions_out_past_year_list[-1][1]==date_today.month
        #         else total_cards_commissions_out_past_year_list):
        #         if commision[2] and commision[1] == month:
        #             month_commission.append(commision[2])
        #         month_sum = sum(month_commission)
        #     all_month_cards_commission_out.append(month_sum)
        # current_month_cards_commission_out = total_cards_commissions_out_past_year_list[-1][2] if total_cards_commissions_out_past_year_list[-1][1]==date_today.month else 0
        # all_month_cards_commission_out.append(current_month_cards_commission_out)

        response = {
                "overview": {
                "total_commission_amount": total_commission_amount,
                # "total_commission_out": total_commission_out_amount,
                "percentage_change": total_commission_amount_change.get("percentage"),
                "change": total_commission_amount_change.get("change")
                },
                "chart_labels": months_list_names,
                "chart_data": all_month_commission,
                # "commission_out_chart": all_month_commission_out,
                "cards_commission_in_chart": all_month_cards_commission_in,
                # "cards_commission_out_chart": all_month_cards_commission_out
                }
        return response

class AverageCommissionChart:
    def get_average_commission_chart(start_date, end_date, user):
        all_commissions_qs = OtherCommissionsRecord.objects.filter(transaction_owner="AGENT", agent=user)
        # Average Commission
        total_average_commissions_in_amount = all_commissions_qs.values().aggregate(Avg("agent_cash_profit"))["agent_cash_profit__avg"]

        # total_average_commissions_out_amount = all_commissions_qs.values("agent_cash_profit", "ro_cash_profit").annotate(total_commission =
        #         Sum("liberty_profit"), commissions_out_amount=F("ro_cash_profit")+F("agent_cash_profit")).aggregate(Avg("commissions_out_amount"))["commissions_out_amount__avg"]

        total_average_commissions_in_amount_till_previous_month  =  all_commissions_qs.filter(date_created__range=[init_start, previous_month_end]).values("agent_cash_profit", "ro_cash_profit").annotate(total_commission =
                Sum("agent_cash_profit"), commissions_out_amount=F("ro_cash_profit")+F("agent_cash_profit")).aggregate(Avg("agent_cash_profit"))["agent_cash_profit__avg"]

        # total_average_commissions_out_amount_till_previous_month  =  all_commissions_qs.filter(date_created__range=[init_start, previous_month_end]).values("agent_cash_profit", "ro_cash_profit").annotate(total_commission =
        #         Sum("liberty_profit"), commissions_out_amount=F("ro_cash_profit")+F("agent_cash_profit")).aggregate(Avg("commissions_out_amount"))["commissions_out_amount__avg"]

        total_avg_commissions_in_amount_monthly_chart = all_commissions_qs.filter(date_created__gte=previous_year_current_month_start).values("date_created__year", "date_created__month").annotate(average_commission =
                Avg("agent_cash_profit")).order_by("date_created__year")
        total_avg_commissions_in_past_year_list = [(obj["date_created__year"], obj["date_created__month"], obj["average_commission"]) for obj in total_avg_commissions_in_amount_monthly_chart]

        total_avg_cards_commissions_in_amount_monthly_chart = all_commissions_qs.filter(date_created__gte=previous_year_current_month_start, transaction_type="CASH_OUT").values("date_created__year", "date_created__month").annotate(average_commission =
                Avg("agent_cash_profit")).order_by("date_created__year")
        total_avg_cards_commissions_in_past_year_list = [(obj["date_created__year"], obj["date_created__month"], obj["average_commission"]) for obj in total_avg_cards_commissions_in_amount_monthly_chart]

        # total_average_commissions_out_amount_monthly_chart = all_commissions_qs.filter(date_created__gte=previous_year_current_month_start).values("date_created__year", "date_created__month").annotate(commissions_out_amount=(F("ro_cash_profit")+F("agent_cash_profit"))).annotate(total_out_average=Avg("commissions_out_amount")
        # ).values("date_created__year", "date_created__month", "total_out_average").order_by("date_created__year")
        # total_average_commissions_out_past_year_list = [(obj["date_created__year"], obj["date_created__month"], obj["total_out_average"]) for obj in total_average_commissions_out_amount_monthly_chart]

        # total_commission_out_amount = total_commisions_out_amount_monthly_chart.aggregate(total=Sum("commissions_out_amount"))["total"]

        total_average_commissions_in_amount_change = get_percentage_diff(
                                        previous=total_average_commissions_in_amount_till_previous_month,
                                        current=total_average_commissions_in_amount
                                        )

        # total_average_commissions_out_amount_change = get_percentage_diff(
        #                                 previous=total_average_commissions_out_amount_till_previous_month,
        #                                 current=total_average_commissions_out_amount
        #                                 )

        all_month_avg_commission_in = []
        for month in months_list[:-1]:
            month_commission = []
            month_sum = 0
            if total_avg_commissions_in_past_year_list:
                for commision in (
                    total_avg_commissions_in_past_year_list[:len(total_avg_commissions_in_past_year_list)-1]
                    if total_avg_commissions_in_past_year_list[-1][1]==date_today.month
                    else total_avg_commissions_in_past_year_list
                    ):
                    if commision[1] == month:
                        avg_commission = commision[2]
                        month_commission.append(avg_commission)
                month_sum = sum(month_commission)
            all_month_avg_commission_in.append(month_sum)
        current_avg_commission_in = total_avg_commissions_in_past_year_list[-1][2] if total_avg_commissions_in_past_year_list and total_avg_commissions_in_past_year_list[-1][1]==date_today.month else 0
        all_month_avg_commission_in.append(current_avg_commission_in)

        # all_month_avg_commission_out= []
        # for month in months_list[:-1]:
        #     month_commission = []
        #     month_avg = 0
        #     for commision in (
        #         total_average_commissions_out_past_year_list[:len(total_average_commissions_out_past_year_list)-1]
        #         if total_average_commissions_out_past_year_list[-1][1]==date_today.month
        #         else total_average_commissions_out_past_year_list
        #         ):
        #         if commision[1] == month:
        #             avg_commission = commision[2] if commision[2] else 0
        #             month_commission.append(avg_commission)
        #         month_comm_len = len(month_commission)
        #         if month_comm_len >= 1:
        #             month_avg = sum(month_commission) // len(month_commission)
        #         else:
        #             month_avg = 0
        #     all_month_avg_commission_out.append(month_avg)
        # current_month_avg_commission_out = total_average_commissions_out_past_year_list[-1][2] if total_average_commissions_out_past_year_list and total_average_commissions_out_past_year_list[-1][1]==date_today.month else 0
        # all_month_avg_commission_out.append(current_month_avg_commission_out)

        all_month_avg_cards_commission_in = []
        for month in months_list[:-1]:
            month_commission = []
            month_sum = 0
            if total_avg_cards_commissions_in_past_year_list:
                for commision in (
                    total_avg_cards_commissions_in_past_year_list[:len(total_avg_cards_commissions_in_past_year_list)-1]
                    if total_avg_cards_commissions_in_past_year_list[-1][1]==date_today.month
                    else total_avg_cards_commissions_in_past_year_list
                    ):
                    if commision[1] == month:
                        avg_commission = commision[2]
                        month_commission.append(avg_commission)
                month_sum = sum(month_commission)
            all_month_avg_cards_commission_in.append(month_sum)
        current_month_avg_cards_commission_in = total_avg_cards_commissions_in_past_year_list[-1][2] if total_avg_cards_commissions_in_past_year_list and total_avg_cards_commissions_in_past_year_list[-1][1]==date_today.month else 0
        all_month_avg_cards_commission_in.append(current_month_avg_cards_commission_in)


        response = {
                "overview": {
                "total_average_commission_amount": total_average_commissions_in_amount,
                "percentage_change": total_average_commissions_in_amount_change.get("percentage"),
                "change": total_average_commissions_in_amount_change.get("change")
                },

                # "average_commissions_out_overview": {
                # "total_average_commission_out_amount": total_average_commissions_out_amount,
                # "percentage": total_average_commissions_out_amount_change.get("percentage"),
                # "change": total_average_commissions_out_amount_change.get("change"),
                # },
                "chart_labels": months_list_names,
                "chart_data": all_month_avg_commission_in,
                # "average_commission_out_chart": all_month_avg_commission_out,
                "average_cards_commission_in_chart": all_month_avg_cards_commission_in
        }
        return response


class TransactionList:
    def get_transactions_list(serializer_class, request, start_date, end_date, user):
        # user = WalletSystem.get_user()
        base_transaction_qs = Transaction.objects.filter(transaction_type__in=all_possible_transaction_list, user=user).exclude(user=user, transaction_leg="INTERNAL")

        #Total Transactions
        total_transactions_qs = base_transaction_qs
        total_transactions_amount = total_transactions_qs.aggregate(Sum("amount"))["amount__sum"]
        total_transactions_amount_today = total_transactions_qs.filter(Q(date_created__range=[previous_day, date_today])).aggregate(Sum("amount"))["amount__sum"]

        previous_day_total_transaction_amount = total_transactions_qs.filter(Q(date_created__range=[init_start,previous_day])).aggregate(Sum("amount")).values()
        previous_month_total_transaction_amount = total_transactions_qs.filter(Q(date_created__range=[init_start, previous_month_end])).aggregate(Sum("amount")).values()
        # previous_day_total_transaction_amount_daily = total_transactions_qs.filter(Q(date_created = previous_day)).aggregate(Sum("amount")).values()

         #Transaction Counts
        total_transaction_counts = base_transaction_qs.count()
        total_transaction_counts_today = base_transaction_qs.filter(date_created__range=[previous_day, date_today]).count()

        previous_day_total_transaction_count = base_transaction_qs.filter(Q(date_created__range=[init_start, previous_day])).count()
        previous_month_total_transaction_count = base_transaction_qs.filter(Q(date_created__range=[init_start, previous_month_end])).count()

        total_transactions_counts_change = get_percentage_diff(previous=previous_month_total_transaction_count, current=total_transaction_counts)
        total_transaction_counts_change_daily = get_percentage_diff(previous=previous_day_total_transaction_count, current=total_transaction_counts)

        total_transactions_amount_change = get_percentage_diff(
            previous=(previous_month_total_transaction_amount if previous_month_total_transaction_amount else 0), current=total_transactions_amount
            )

        total_transactions_amount_change_daily = get_percentage_diff(
            previous=previous_day_total_transaction_amount, current=total_transactions_amount
            )

        overview = {
            #Transactions Amount
            "total_transaction_amount": {
                "amount": total_transactions_amount,
                "percentage": str(total_transactions_amount_change.get("percentage")),
                "change": str(total_transactions_amount_change.get("change"))
                },
            "today_transactions": {
                "amount": total_transactions_amount_today if total_transactions_amount_today else 0,
                "percentage": str(total_transactions_amount_change_daily.get("percentage")),
                "change": total_transactions_amount_change_daily.get("change")
                },
            "total_transaction_counts": {
                "count": total_transaction_counts,
                "percentage": str(total_transactions_counts_change.get("percentage")),
                "change": total_transactions_counts_change.get("change")
            },
            "today_transaction_counts": {
                "count": total_transaction_counts_today if total_transaction_counts_today else 0,
                "percentage": str(total_transaction_counts_change_daily.get("percentage")),
                "change": total_transaction_counts_change_daily.get("change")
            }
            }

        transactions = base_transaction_qs.order_by("-date_created")
        transactions = Paginator.paginate(request=request, queryset=transactions)

        serializer = serializer_class(transactions, many=True)

        response = {
            "overview": overview,
            "transactions": serializer.data,
            "count": len(serializer.data)
        }
        return response

class OtherCommission:
    def get_other_commission(queryset, serializer_class, request, start_date, end_date, user):
        other_commissions_qs = queryset.filter(date_created__range=[start_date, end_date], agent=user).order_by("date_created")
        other_commissions_qs = Paginator.paginate(request=request, queryset=other_commissions_qs)

        # Total Commissions In
        total_commissions_in = queryset.filter(date_created__range=[init_start, date_today], agent=user).aggregate(Sum("agent_cash_profit"))["agent_cash_profit__sum"]
        total_commission_in_till_previous_month_end = list(queryset.filter(date_created__range=[init_start, previous_month_end], agent=user).aggregate(Sum("agent_cash_profit")).values())[0]

        total_commissions_in_change = get_percentage_diff(previous=total_commission_in_till_previous_month_end, current=total_commissions_in)

        # Today Commission In
        today_commissions_in = queryset.filter(date_created__range=[previous_day, date_today], agent=user).aggregate(Sum("agent_cash_profit"))["agent_cash_profit__sum"]
        total_commissions_in_till_previous_day = queryset.filter(date_created__range=[init_start, previous_day], agent=user).aggregate(Sum("agent_cash_profit"))["agent_cash_profit__sum"]
        total_commissions_in_daily_change = get_percentage_diff(current=total_commissions_in, previous=total_commissions_in_till_previous_day)
        serializer = serializer_class(other_commissions_qs, many=True)

        # Total Commissions Out
        # total_commissions_out = queryset.filter(date_created__range=[init_start, date_today]).annotate(
        #     commissions_out_amount = F("agent_cash_profit")+F("ro_cash_profit")).aggregate(Sum("commissions_out_amount"))["commissions_out_amount__sum"]

        # total_commission_out_till_previous_month_end = queryset.filter(date_created__range=[init_start, previous_month_end]).annotate(
        #     commissions_out_amount= F("agent_cash_profit")+F("ro_cash_profit")).aggregate(Sum("commissions_out_amount"))["commissions_out_amount__sum"]

        # total_commissions_out_change = get_percentage_diff(previous=total_commission_out_till_previous_month_end, current=total_commissions_out)

        # Today Commission Out
        # today_commissions_out = queryset.filter(date_created__range=[previous_day, date_today]).annotate(
        #     commissions_out_amount= (F("agent_cash_profit")+F("ro_cash_profit"))).aggregate(Sum("commissions_out_amount"))["commissions_out_amount__sum"]

        # total_commissions_out_till_previous_day = queryset.filter(date_created__range=[init_start, previous_day]).annotate(
        #     commissions_out_amount= (F("agent_cash_profit")+F("ro_cash_profit"))).aggregate(Sum("commissions_out_amount"))["commissions_out_amount__sum"]

        # total_commissions_out_daily_change = get_percentage_diff(current=total_commissions_out, previous=total_commissions_out_till_previous_day)

        overview = {
            "total_commissions_in": {
                "amount": total_commissions_in if total_commissions_in else 0,
                "percentage": total_commissions_in_change.get("percentage"),
                "change": total_commissions_in_change.get("change")
                },
            "today_commissions_in": {
                "amount": today_commissions_in,
                "percentage": total_commissions_in_daily_change.get("percentage"),
                "change": total_commissions_in_daily_change.get("change")
                },

            # "total_commissions_out": {
            #     "amount": total_commissions_out if total_commissions_out else 0,
            #     "percentage": total_commissions_out_change.get("percentage"),
            #     "change": total_commissions_out_change.get("change")
            #     },
            # "today_commissions_out": {
            #     "amount": today_commissions_out if today_commissions_out else 0,
            #     "percentage": total_commissions_out_daily_change.get("percentage"),
            #     "change": total_commissions_out_daily_change.get("change")
            #     }
        }

        commissions_list = []
        for commission in other_commissions_qs:
            data = {
                "id": commission.id,
                "amount": commission.amount,
                "commission_in": commission.agent_cash_profit,
                # "commission_out": (commission.ro_cash_profit if commission.ro_cash_profit else 0) + (commission.agent_cash_profit if commission.agent_cash_profit else 0),
                "reference_id": commission.liberty_reference,
                "date_time": commission.date_created,
                "status": "success"
            }
            commissions_list.append(data)

        response = {
            "overview": overview,
            "commissions_list": commissions_list,
            "count": len(commissions_list)
        }
        return response


class PosAgentsDisputesList:
    def get_pos_agents_disputes_list(request, queryset, start_date, end_date, user):
        disputes = Dispute.objects.filter(Q(date_created__range=[start_date, end_date]) & Q(user=user))
        disputes = Paginator.paginate(request=request, queryset=disputes)

        all_disputes = []
        for dispute in disputes:
            data = {
                "id": dispute.id,
                "support_id": dispute.support_id,
                "dispute_type": dispute.dispute_type,
                "disputed_on": dispute.date_created,
                "respond_time": dispute.respond_time,
                "resolved_by": dispute.resolved_by,
                "status": "Resolved" if dispute.is_resolved else "Need Response"
            }
            all_disputes.append(data)
        response = {
            "agent_disputes": all_disputes,
            "count": len(all_disputes)
        }
        return response
