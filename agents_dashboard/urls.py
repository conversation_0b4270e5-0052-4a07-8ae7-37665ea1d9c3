from django.urls import path
from agents_dashboard import views


urlpatterns = [
    path("dash-data", views.AgentDashboardDataView.as_view()),
    path("dashboard_transaction_comparatives", views.DashboardTransactionComparativesView.as_view()),
    path("dashboard_two_data", views.DashboardTwoView.as_view()),
    path("dashboard_chart_transactions_amount", views.DashboardChartsTransactionAmountView.as_view()),
    path("dashboard_chart_transactions_count", views.DashboardChartsTransactionCountView.as_view()),
    path("dashboard_chart_commissions", views.DashboardChartsCommissionView.as_view()),
    path("dashboard_chart_average_commissions", views.DashboardChartsAverageCommissionView.as_view()),
    path("transactions_lists", views.TransactionListView.as_view()),
    path("other_commissions", views.OtherCommissionView.as_view()),
    path("pos_agent_disputes_list", views.PosAgentDisputesListView.as_view()),

]