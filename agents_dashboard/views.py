from django.shortcuts import render
from django.contrib.auth import get_user_model
User = get_user_model()

from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework import status, generics, filters
from rest_framework.response import Response
from rest_framework import generics

from agents_dashboard.services import (
    DashData, TransactionComparatives, DashDataTwo, TransactionAmountChart, TransactionCountChart,
    CommissionCharts, AverageCommissionChart, TransactionList, OtherCommission, PosAgentsDisputesList
    )
from admin_dashboard.helpers.helpers import filter_by_date_two, Paginator
from admin_dashboard.serializers import CustomerTransactionSerializer, OtherCommissionsRecordSerializer, DisputeSerializer
from admin_dashboard.models import Dispute
from main.permissions import OtherServiceOtherPermissions, CustomIsAuthenticated
from accounts.models import OtherCommissionsRecord
from main.views import CustomPagination

from datetime import datetime


class AgentDashboardDataView(APIView):
    permission_classes = [CustomIsAuthenticated]

    def get(self, request):
        date_filter = filter_by_date_two(request=request, user=User, datetime=datetime)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        try:
            response  =  DashData.get_dashboard_data(start_date, end_date, request)
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DashboardTransactionComparativesView(APIView):
    permission_classes = [CustomIsAuthenticated]
    filter_backends = (filters.SearchFilter,)
    pagination_class = CustomPagination

    def get(self, request):
        """This endpoint it to provide the JSon data for the Transactions
        Comparative barchart which displays total withdrawals, send money, airtime, data and utilities"""

        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")
      
        lower_amount = request.GET.get("lower_amount")
        upper_amount = request.GET.get("upper_amount")

        if not lower_amount:
            lower_amount = 0
        if not upper_amount:
            upper_amount = 0
        try:
            response = TransactionComparatives.get_transaction_comparatives(
                            start_date=start_date, 
                            end_date=end_date, 
                            lower_amount=lower_amount, 
                            upper_amount=upper_amount,
                            user=request.user
                        )
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DashboardTwoView(APIView):
    serializer_class = CustomerTransactionSerializer
    permission_classes = [CustomIsAuthenticated]

    def get(self, request):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        try:
            response = DashDataTwo.get_dashboard_data_two(start_date=start_date, end_date=end_date, user=request.user)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DashboardChartsTransactionAmountView(APIView):
    serializer_class = CustomerTransactionSerializer
    permission_classes = [CustomIsAuthenticated]

    def get(self, request):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        try:
            response = TransactionAmountChart.get_transactions_chart(start_date=start_date, end_date=end_date, user=request.user)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

class DashboardChartsTransactionCountView(APIView):
    serializer_class = CustomerTransactionSerializer
    permission_classes = [CustomIsAuthenticated]

    def get(self, request):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        try:
            response = TransactionCountChart.get_transaction_count_chart(start_date=start_date, end_date=end_date, user=request.user)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DashboardChartsCommissionView(APIView):
    serializer_class = CustomerTransactionSerializer
    permission_classes = [CustomIsAuthenticated]

    def get(self, request):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")
        
        try:
            response = CommissionCharts.get_commission_charts(start_date=start_date, end_date=end_date, user=request.user)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        
class DashboardChartsAverageCommissionView(APIView):
    serializer_class = CustomerTransactionSerializer
    permission_classes = [CustomIsAuthenticated]

    def get(self, request):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        try:
            response = AverageCommissionChart.get_average_commission_chart(start_date=start_date, end_date=end_date, user=request.user)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

class TransactionListView(APIView):
    serializer_class = CustomerTransactionSerializer
    # filter_backends = (filters.SearchFilter,)
    permission_classes = [CustomIsAuthenticated]
    # pagination_class = CustomPagination

    def get(self, request):
        """
        Returns a list of all transactions
        """
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")
        serializer_class =  self.serializer_class
        
        try:
            response = TransactionList.get_transactions_list(serializer_class, self.request, start_date, end_date, user=request.user)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)       
        

# COMMISSIONS
class OtherCommissionView(APIView):
    serializer_class = OtherCommissionsRecordSerializer
    permission_classes = [CustomIsAuthenticated]
    queryset = OtherCommissionsRecord.objects.all()

    def get(self, request):
        date_filter = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        queryset = self.queryset
        serializer_class = self.serializer_class

        try:
            response = OtherCommission.get_other_commission(queryset, serializer_class, request, start_date, end_date, user=request.user)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DisputeListView(APIView):
    serializer_class = DisputeSerializer
    permission_classes = [CustomIsAuthenticated]
    queryset = Dispute.objects.all()

    def get(self,request):
        """
        This view provides a list of all existing disputes
        """
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        disputes = self.queryset.filter(date_created__range=[start_date, end_date], user__terminal_id__isnull=False)

        disputes = Paginator.paginate(request=request, queryset=disputes)
        serializer = self.serializer_class(disputes,  many=True)
        data = serializer.data
        response = {
            "data": data,
            "count": len(data)
        }
        return Response(response, status=status.HTTP_200_OK)
    

class PosAgentDisputesListView(generics.RetrieveAPIView):
    permission_classes = [CustomIsAuthenticated]
    queryset = User.objects.all()
    lookup_field = "id"

    def get(self, request):
        date_filter = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")
        queryset = self.queryset

        try:
            response = PosAgentsDisputesList.get_pos_agents_disputes_list(request, queryset, start_date, end_date, user=request.user)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)