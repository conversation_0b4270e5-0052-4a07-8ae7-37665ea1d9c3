from celery import shared_task
from ussd.helpers.helper_functions import ussd_bills_pay_request

from horizon_pay.helpers.helper_function import decrypt_trans_pin



@shared_task
def send_out_bills_pay_ussd(phone_number, amount, biller, user_tid, session_key, pin):

    decrypted_pin = decrypt_trans_pin(pin)

    send_out_request = ussd_bills_pay_request(
        phone_number=phone_number,
        amount=amount,
        biller=biller,
        user_tid=user_tid,
        session_key=session_key,
        pin = decrypted_pin
    )

    return f"{send_out_request}"


