from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportMixin, ImportExportModelAdmin

from ussd.models import *


###############################################################################
# RESOURCES


class SendMoneyTableResource(resources.ModelResource):
    class Meta:
        model = SendMoneyTable

class BillsPayTableResource(resources.ModelResource):
    class Meta:
        model = BillsPayTable




#######################################################################
# ADMINS


class SendMoneyTableAdmin(ImportExportModelAdmin):
    resource_class = SendMoneyTableResource
    # search_fields = ['admin_user__email', 'account_affected', 'action', 'date_created']
    # list_filter = (
    #     ('date_created',)
    # )
    # date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class BillsPayTableResourceAdmin(ImportExportModelAdmin):
    resource_class = BillsPayTableResource
    search_fields = ['phone_number', "session_key", "task_id"]
    list_filter = (
        ('date_created', 'request_processed', 'biller')
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]



admin.site.register(SendMoneyTable, SendMoneyTableAdmin)
admin.site.register(BillsPayTable, BillsPayTableResourceAdmin)