from django.db import models
from main.models import User

# Create your models here.

class SendMoneyTable(models.Model):
    payload = models.TextField()
    date = models.DateTimeField(auto_now_add=True)


class BillsPayTable(models.Model):
    phone_number = models.CharField(max_length=100, null=True, blank=True)
    amount = models.CharField(max_length=100, null=True, blank=True)
    ip_addr = models.CharField(max_length=100)
    source_ip = models.CharField(max_length=100, null=True, blank=True)
    network_code = models.CharField(max_length=100, null=True, blank=True)
    biller = models.Char<PERSON>ield(max_length=100, null=True, blank=True)
    payload = models.TextField(null=True, blank=True)
    session_key = models.CharField(max_length=300, null=True, blank=True)
    task_id = models.CharField(max_length=300, null=True, blank=True)
    request_processed = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)