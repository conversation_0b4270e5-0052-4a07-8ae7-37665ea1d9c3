# from django.contrib.auth import get_user_model, authenticate
# from django.contrib.auth.models import update_last_login
# from django.conf import settings
# from djoser.serializers import TokenCreateSerializer, UserCreateSerializer
# from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
# from rest_framework_simplejwt.views import TokenObtainPairView
# from rest_framework import status, exceptions
# from djoser.conf import settings as djoser_settings
# from rest_framework.exceptions import ValidationError
# from rest_framework import serializers
# from kyc_app.models import BVNDetail, KYCTable

# from main.models import *
# from accounts.models import AccountSystem, WalletSystem, Transaction

# from django.utils import timezone

# User = get_user_model()


# # class USSDPhonePinSerializer(serializers.Serializer):
# #     phone_num = serializers.Char<PERSON>ield(required=True, max_length=100)
# #     lga = serializers.Char<PERSON>ield(required=True, max_length=100)
# #     nearest_landmark = serializers.CharField(required=True, max_length=100)
# #     street = serializers.Char<PERSON><PERSON>(required=True, max_length=100)

