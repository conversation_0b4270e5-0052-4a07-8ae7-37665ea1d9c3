import json
import requests
from base64 import b64encode, b64decode

from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad

from django.conf import settings


# Wema Bank Restful API technical specification.
class WemaBank:
    # Note: Account prefix is '842' [sub-account(s) creation].
    """
        Merchant settlement integration.
        The Request(s) and Response(s) are encrypted with:
        - AES(AES/CBC/PKCS5PAdding) algorithm.
        The connecting IMTO is expected to encrypt their request -
        and decrypt the response.
    """
    live_base_url = "https://***********/WemaAPIService/api"
    username = "Liberty"
    password = settings.WEMA_PASSWORD
    vendor_id = "Liberty"

    # Shared secret.
    KEY = settings.WEMA_BANK_AES_KEY
    IV = settings.WEMA_BANK_AES_IV

    headers = {
        "Content-Type": "application/json",
        "VendorID": vendor_id
    }

    def encrypt(self, data):
        """Encrypts the request payload.
        Args:
            Payload (data) to be sent.
        """
        cipher = AES.new(
            self.KEY.encode("utf-8"), AES.MODE_CBC, self.IV.encode("utf-8")
        )
        cipher_bytes = cipher.encrypt(
            pad(str(data).encode("utf-8"), AES.block_size)
        )
        cipher_text = b64encode(cipher_bytes).decode("utf-8")
        return cipher_text

    def decrypt(self, data):
        """Decrypts the response data.
        Args:
            Payload (data) received via API call.
        """
        cipher = AES.new(
            self.KEY.encode("utf-8"), AES.MODE_CBC, self.IV.encode("utf-8")
        )
        plain_text = unpad(
            cipher.decrypt(b64decode(data)), AES.block_size
        ).decode("utf-8")
        return plain_text

    def authenticate(self):
        """
            Returns vendor access and refresh tokens.
        """
        absolute_url = f"{self.live_base_url}/Authentication/authenticate/"
        payload = json.dumps(
            {
                "username": self.username,
                "password": self.password
            }
        )
        response = requests.request(
            "POST", absolute_url, headers=self.headers, data=payload
        )
        return response.json()

    def nip_fund_transfer(self):
        """Debit and Credit transactions are applied to respective accounts.
        Payment reference should be unique per transaction.
            Successful request returns transaction reference.
        """
        absolute_url = f"{self.live_base_url}/WMServices/NIPFundTransfer"

        bank_code = input("Enter destination bank code:     ")
        account_number = input("Enter destination account number:       ")
        account_name = input("Enter destination account name:       ")
        originator_name = input("Enter originator/sender name here:     ")
        narration = input("Enter your description/narration here:       ")
        reference = input("Enter your transaction reference here:       ")
        amount = input("Enter amount here:      ")
        source_account = input("Enter source account here:      ")

        data = {
            "myDestinationBankCode": bank_code,
            "myDestinationAccountNumber": account_number,
            "myAccountName": account_name,
            "myOriginatorName": originator_name,
            "myNarration": narration,
            "myPaymentReference": reference,
            "myAmount": amount,
            "sourceAccountNo": source_account
        }
        encrypted_request = self.encrypt(data=data)
        bearer_token = self.authenticate()
        self.headers["Authorization"] = f"Bearer {bearer_token.get('token')}"

        payload = json.dumps(
            {
                "fundTransferRequest": encrypted_request
            }
        )
        response = requests.request(
            "POST", absolute_url, headers=self.headers, data=payload
        )
        return self.decrypt(data=response.text)

    def get_transaction_status(self):
        """Fetch transaction status.
        Args:
            Transaction reference.
        """
        absolute_url = f"{self.live_base_url}/WMServices/GetTransactionStatus"
        reference = input("Enter transfer reference here:       ")

        encrypted_request = self.encrypt(data=reference)
        bearer_token = self.authenticate()
        self.headers["Authorization"] = f"Bearer {bearer_token.get('token')}"

        payload = json.dumps(
            {}
        )
        response = requests.request(
            "GET",
            f"{absolute_url}?TransactionReference={encrypted_request}",
            headers=self.headers,
            data=payload
        )
        return response.text
