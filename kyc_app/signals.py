from django.conf import settings
from django.db.models.signals import post_save
from django.dispatch import receiver
from kyc_app.models import BVNDetail, DocumentFaceMatchKYC2Detail, GuarantorDetail, KYCTable


@receiver(post_save, sender=BVNDetail)
def update_kyc_level_on_bvn_save(sender, instance: BVNDetail, created, **kwargs):
    
    kyc_level = KYCTable.check_user_kyc_level(instance.kyc.user)
    instance.kyc.user.kyc_level = kyc_level
    instance.kyc.user.kyc_one_progress = instance.verification_status
    instance.kyc.user.save()


@receiver(post_save, sender=DocumentFaceMatchKYC2Detail)
def update_kyc_level_on_docsuplaod_save(sender, instance: DocumentFaceMatchKYC2Detail, created, **kwargs):
    
    kyc_level = KYCTable.check_user_kyc_level(instance.kyc.user)

    instance.kyc.user.kyc_level = kyc_level
    instance.kyc.user.kyc_two_progress = instance.verification_status
    instance.kyc.user.save()


@receiver(post_save, sender=GuarantorDetail)
def update_kyc_level_on_guarantor_save(sender, instance: GuarantorDetail, created, **kwargs):
    
    if instance.kyc:
        kyc_level = KYCTable.check_user_kyc_level(instance.kyc.user)

        instance.kyc.user.kyc_level = kyc_level
        instance.kyc.user.kyc_three_progress = instance.verification_status
        instance.kyc.user.save()