from django.conf import settings

import time
import requests
import json
import boto3
import io
import contextlib


def create_doc_verfication_reference():
    epoch = int(time.time())
    reference = "LP" + str(epoch)[-10:]
    return reference


def verify_bvn_with_hadada(bvn_number, last_name, func_count):

    url = "https://reverse.dev.hadada.co/api/identity/premium"
    headers = {
        "Authorization": f"Bearer {settings.HADADA_SECRET_KEY}",
        "Content-Type": "application/json",
        "accept": "application/json",
    }
    payload = {"ref": bvn_number, "type": "bvn", "lastname": last_name}

    try:
        response = requests.request("POST", url=url, headers=headers, json=payload, timeout=10)
        resp = response.json()

        if isinstance(resp, dict):

            new_resp = {
                "success": True,
                "statusCode": 201,
                "verified_by": "hadada",
                "data": resp
            }

            return new_resp
            
        else:
            if func_count == 0:
                new_resp = {
                    "success": False,
                    "statusCode": 400,
                    "verified_by": "hadada",
                    "data": None
                }
                return new_resp
            else:
                # Return a recursive function to retry woven account creation            
                return verify_bvn_with_hadada(bvn_number, last_name, func_count-1)
   
    except requests.exceptions.ReadTimeout:
        new_resp = {
            "success": False,
            "statusCode": 400,
            "verified_by": "hadada",
            "data": None
        }
        return new_resp
    # print(resp)


# def verify_bvn_with_uverify(bvn_number, last_name, func_count):

#     url = "http://api.youverify.co/v2/api/addresses/candidates/identity"

#     headers = {
#         'token': settings.UVERIFY_TOKEN,
#         'Content-Type': 'application/json'
#     }

#     payload = json.dumps({
#         "type": "bvn",
#         "idNumber": bvn_number,
#         "subjectConsent": True
#     }) 

#     response = requests.request("POST", url=url, headers=headers, data=payload)
#     print(response.text)
#     resp = json.loads(response.text)

#     return resp


def verify_bvn_with_uverify(bvn_number, last_name=None):

    url = "https://api.youverify.co/v2/api/identity/ng/bvn"

    headers = {
        'token': settings.UVERIFY_TOKEN,
        'Content-Type': 'application/json'
    }

    payload = json.dumps({
        "id": bvn_number,
        "isSubjectConsent": True
    }) 

    try:
        response = requests.request("POST", url=url, headers=headers, data=payload, timeout=20)
        
        resp = json.loads(response.text)

        if resp.get("success") == True and resp.get("statusCode") == 200:

            new_resp = {
                "success": True,
                "statusCode": 200,
                "verified_by": "youverify",
                "data": resp
            }
        
        else:
            new_resp = {
                "success": False,
                "statusCode": 400,
                "verified_by": "youverify",
                "data": resp
            }

        # else:
            # new_resp = verify_bvn_with_hadada(
            #     bvn_number=bvn_number, last_name=last_name, func_count=5
            # )

        return new_resp

    except requests.exceptions.RequestException:
        new_resp = {
            "success": False,
            "statusCode": 400,
            "verified_by": "hadada",
            "data": None
        }

        # new_resp = verify_bvn_with_hadada(
        #     bvn_number=bvn_number, last_name=last_name, func_count=5
        # )

        return new_resp


def verify_nin_with_uverify(nin_number):

    url = "https://api.youverify.co/v2/api/identity/ng/nin"
    headers = {
        'token': settings.UVERIFY_TOKEN,
        'Content-Type': 'application/json'
    }

    payload = {
        "id": nin_number,
        "isSubjectConsent": True
    }

    try:
        response = requests.request("POST", url=url, headers=headers, json=payload, timeout=20)
        
        resp = response.json()
        
        if resp.get("success") == True and resp.get("statusCode") == 200:
            new_resp = {
                "success": True,
                "statusCode": 200,
                "verified_by": "youverify",
                "data": resp
            }

        else:
            new_resp = {
                "success": False,
                "statusCode": 400,
                "verified_by": "hadada",
                "data": None
            }

    except requests.exceptions.RequestException as err:
        new_resp = {
            "success": False,
            "statusCode": 400,
            "verified_by": "hadada",
            "data": None
        }

    return new_resp


def send_out_verification_status(phone_number, verification_status):
    url = f"{settings.LIBERTY_MARKETING_URL}/salesrep/kyc_verification/"

    headers = {
        "Authorization": f"Token {settings.POS_AUTH_TOKEN}"
    }

    payload = {
        "phone_number": phone_number,
        "verification_status": verification_status
    }

    response = requests.request("POST", url=url, headers=headers, data=payload)
    resp = response.json()

    return resp


def send_data_to_lotto_back_function(data):

    url = f"{settings.LIBERTY_MARKETING_URL}/api/kyc3/guarantor_details/"

    headers = {
        # 'token': settings.UVERIFY_TOKEN,
        'Content-Type': 'application/json'
    }

    payload = data

    try:
        response = requests.request("POST", url=url, headers=headers, json=payload, timeout=20)
        resp = response.json()
    except requests.exceptions.RequestException as e:
        resp = f"{e}"

    return resp


def upload_image(url, user, kyc=None):
    # Get the service client
    s3 = boto3.client('s3')

    # Rember to se stream = True.
    with contextlib.closing(requests.get(url, stream=True, verify=False)) as response:
        # Set up file stream from response content.
        fp = io.StringIO(response.content)
        # Upload data to S3
        if kyc is not None:
            dir = f"media/users/{user.customer_id}/profile_pics/{kyc}/{user.unique_id}_profile_pic.png"
        else:
            dir = f"media/users/{user.customer_id}/profile_pics/{user.unique_id}_profile_pic.png"

        s3.upload_fileobj(fp, f'{settings.AWS_STORAGE_BUCKET_NAME}', '/' + dir)

    final_dir = f"{settings.AWS_S3_ENDPOINT_URL}/{dir}"
    return final_dir


def verify_bvn_with_dojah(bvn_number):
    header = {"AppId": str(settings.DOJAH_APP_ID), "Authorization": str(settings.DOJAH_SECRET_KEY)}
    url = f"https://api.dojah.io/api/v1/kyc/bvn/advance?bvn={bvn_number}"

    try:
        response = requests.request("GET", url=url, headers=header, timeout=20)
        if response.status_code == 200:
            resp = response.json()
            new_resp = {
                "success": True,
                "statusCode": 200,
                "verified_by": "dojah",
                "data": resp
            }

        else:
            new_resp = {
                "success": False,
                "statusCode": 400,
                "verified_by": "",
                "data": None
            }
    except requests.exceptions.RequestException as err:
        new_resp = {
            "success": False,
            "statusCode": 400,
            "verified_by": "",
            "data": None
        }

    return new_resp


def verify_nin_with_dojah(nin_number):
    header = {"AppId": str(settings.DOJAH_APP_ID), "Authorization": str(settings.DOJAH_SECRET_KEY)}
    url = f"https://api.dojah.io/api/v1/kyc/nin?nin={nin_number}"

    try:
        response = requests.request("GET", url=url, headers=header, timeout=20)
        if response.status_code == 200:
            resp = response.json()
            new_resp = {
                "success": True,
                "statusCode": 200,
                "verified_by": "dojah",
                "data": resp
            }

        else:
            new_resp = {
                "success": False,
                "statusCode": 400,
                "verified_by": "",
                "data": None
            }
    except requests.exceptions.RequestException as err:
        new_resp = {
            "success": False,
            "statusCode": 400,
            "verified_by": "",
            "data": None
        }

    return new_resp

