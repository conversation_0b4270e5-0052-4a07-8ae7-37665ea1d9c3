from django.conf import settings
from time import sleep
# from django.contrib.auth.hashers import make_password, check_password

import requests
import json
import uuid
import base64


# from main.models import ConstantTable
# settings.configure()




class MetaMap:

    base_64_auth = base64.b64encode(f"{settings.METAMAP_CLIENT_ID}:{settings.METAMAP_CLIENT_SECRET}".encode()).decode()
    base_url = "https://api.getmati.com"

    auth_headers = {
        "Authorization": f"Basic {base_64_auth}"
    }





    @classmethod
    def authenticate_metamap(cls):

        filter_url = "/oauth"
        url = cls.base_url + filter_url
       
        body = {
            "grant_type":"client_credentials"
        }

        response = requests.request("POST", url=url, data=body, headers=cls.auth_headers)
        resp = response.json()
        return resp.get("access_token")

    @classmethod
    def retrieve_resource(cls, resource_url, access_token):
       
        headers ={
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }


        response = requests.request("GET", url=resource_url, headers=headers)
        
        return response
    

    @classmethod
    def verify_bvn_with_metamap(cls, bvn_number, first_name, last_name):


        call_back_url = f"{settings.BASE_URL}/kyc/metamap/webhook/bvn_response"

        access_token = cls.authenticate_metamap()

        filter_url = "/govchecks/v1/ng/bvn"
        url = cls.base_url + filter_url


        headers ={
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        payload = json.dumps({
            "documentNumber": bvn_number, 
            "firstName": first_name, 
            "lastName": last_name, 
            "callbackUrl": call_back_url
        })


        response = requests.request("POST", url=url, data=payload, headers=headers)


    @classmethod
    def retrieve_webhook_resource(cls, verification_id, access_token):

        url = f"https://api.getmati.com/v2/verifications/{verification_id}"

        headers ={
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }

        response = requests.request("GET", url=url, headers=headers)

        return response.json()
