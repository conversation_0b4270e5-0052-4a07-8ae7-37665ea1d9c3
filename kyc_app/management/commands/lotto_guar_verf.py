from django.core.management.base import BaseCommand
from kyc_app.models import GuarantorDetail
from kyc_app.tasks import send_data_to_lotto_back_task_without_delay
from django.conf import settings

class Command(BaseCommand):
    def handle(self, *args, **kwargs):
        guarantors = GuarantorDetail.objects.filter(is_lotto_user=True, is_verified=False)

        for get_guarantor_rel in guarantors:
            user_instance = get_guarantor_rel.kyc.user

            lotto_verf_data = {
                "verification_unique_id": get_guarantor_rel.verification_unique_id,
                "agent_name": user_instance.bvn_full_name if user_instance.bvn_first_name else user_instance.full_name,
                "agent_phone": user_instance.phone_number,
                "agent_email": user_instance.email,
                "guarantor_name": get_guarantor_rel.guarantor_name,
                "guarantor_email": get_guarantor_rel.guarantor_email,
                "guarantor_phone": get_guarantor_rel.guarantor_phone_number,
                "guarantor_occupation": get_guarantor_rel.guarantor_occupation,
                "guarantor_address": get_guarantor_rel.guarantor_address
            }
            
            
            send_data_to_lotto_back = send_data_to_lotto_back_task_without_delay(data=lotto_verf_data, guarantor_id=get_guarantor_rel.id)

        print("done")
