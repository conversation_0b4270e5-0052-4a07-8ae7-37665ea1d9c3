
from django.core.management.base import BaseCommand

from django.conf import settings
from base64 import b64decode
from urllib.parse import urlparse
from PIL import Image
from io import BytesIO
from storages.backends.s3boto3 import S3Boto3Storage

import requests
import os
import json
import uuid
import ast
from pprint import pprint

class Command(BaseCommand):

    def handle(self, *args, **kwargs):
        import boto3
        import jwt
        from botocore.client import Config

        


        s3 = boto3.client('s3', aws_access_key_id=settings.AWS_S3_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_S3_SECRET_ACCESS_KEY,
            endpoint_url=settings.AWS_S3_ENDPOINT_URL
        )


        # List all buckets
        response = s3.list_buckets()

        # Extract bucket names from the response
        buckets = [bucket['Name'] for bucket in response['Buckets']]
        print(buckets)

        from botocore.exceptions import NoCredentialsError


        directory_prefix = 'media/sfs/docs/'

        # You can either remove the Prefix parameter for all or leave it to target a specific folder

        try:
            response = s3.list_objects_v2(Bucket=settings.AWS_STORAGE_BUCKET_NAME, Prefix=directory_prefix)
            for obj in response.get('Contents', []):
                print(obj['Key'])
        except s3.exceptions.NoSuchKey as e:
            print(f"The specified key or prefix does not exist: {e}")
        except NoCredentialsError:
            print('Credentials not available')
