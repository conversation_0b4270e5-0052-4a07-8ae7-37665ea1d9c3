import json

from django import forms
from django.contrib import admin, messages
from django.utils.html import format_html
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from accounts.tasks import create_wallets_and_accounts_task
from kyc_app.models import *


# Register your models here.


class KYCTableResource(resources.ModelResource):
    class Meta:
        model = KYCTable


class BVNDetailResource(resources.ModelResource):
    class Meta:
        model = BVNDetail

class DocumentFaceMatchKYC2DetailResource(resources.ModelResource):
    class Meta:
        model = DocumentFaceMatchKYC2Detail

class GuarantorDetailResource(resources.ModelResource):
    class Meta:
        model = GuarantorDetail


class MetamapRawDataResource(resources.ModelResource):
    class Meta:
        model = MetamapRawData

class BlacklistedBVNResource(resources.ModelResource):
    class Meta:
        model = BlacklistedBVN

class CreateReferenceForVerificationResource(resources.ModelResource):
    class Meta:
        model = CreateReferenceForVerification


#########################################################################################


class KYCTableResourceAdmin(ImportExportModelAdmin):
    search_fields = ["user__email"]
    resource_class = KYCTableResource
    # search_fields = ["icontains"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


# class BVNDetailResourceAdmin(ImportExportModelAdmin):
class BVNDetailResourceAdmin(admin.ModelAdmin):
    resource_class = BVNDetailResource
    # autocomplete_fields = ["kyc"]
    list_filter = (
        ("verification_status", "channel", "verified_by", "is_verified")
    )
    search_fields = ["kyc__user__email", "bvn_number", "bvn_phone_number"]

    # exclude = ('bvn_birthdate', 'payload')


    def get_list_display(self, request):
        if not request.user.is_superuser:
            return [field.name if field.name not in ["bvn_birthdate", "payload"] else "bvn_number" for field in self.model._meta.concrete_fields]
        else:
            return [field.name for field in self.model._meta.concrete_fields]

    def get_exclude(self, request, obj=None):
        if not request.user.is_superuser:
            return ('bvn_birthdate', 'payload')
        else:
            return super().get_exclude(request, obj)



# class DocumentFaceMatchKYC2DetailResourceAdmin(ImportExportModelAdmin):
class DocumentFaceMatchKYC2DetailResourceAdmin(admin.ModelAdmin):

    search_fields = ["kyc__user__email"]
    list_filter = (
        ("verification_status",)
    )
    resource_class = DocumentFaceMatchKYC2DetailResource


    def get_list_display(self, request):
        if not request.user.is_superuser:
            return [field.name if field.name not in ["payload"] else "resource_id" for field in self.model._meta.concrete_fields]
        else:
            return [field.name for field in self.model._meta.concrete_fields]

    def get_exclude(self, request, obj=None):
        if not request.user.is_superuser:
            return ('payload',)
        else:
            return super().get_exclude(request, obj)



class GuarantorDetailResourceAdmin(ImportExportModelAdmin):
    readonly_fields = ["verification_unique_id"]
    search_fields = ["kyc__user__email", "verification_unique_id"]
    list_filter = (
        ("verification_status", "kyc__user__type_of_user", "verf_started", "is_lotto_user", "is_verified", "guarantor_id_is_verified", "date_started", "date_verified")
    )
    resource_class = GuarantorDetailResource
    date_hierarchy = 'date_verified'


    def get_list_display(self, request):

        return [field.name if field.name not in ["guarantor_id_verification_payload"] else "verification_status" for field in self.model._meta.concrete_fields]


class MetamapRawDataResourceAdmin(ImportExportModelAdmin):
    resource_class = MetamapRawDataResource
    search_fields = ["payload"]
    list_filter = (
        ("verification_type", "date_added")
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class BlacklistedBVNResourceAdmin(ImportExportModelAdmin):
    search_fields = ["bvn_number", "bvn_names"]
    resource_class = BlacklistedBVNResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class CreateReferenceForVerificationResourceAdmin(ImportExportModelAdmin):
    search_fields = ["reference"]
    resource_class = CreateReferenceForVerificationResource
    list_filter = (
        ("for_kyc_level", "date_created")
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UserImageAdminForm(forms.ModelForm):
    snapshot_file = forms.FileField(required=False, help_text="Upload an image to convert to base64")

    class Meta:
        model = UserImage
        fields = '__all__'

    def clean_snapshot_file(self):
        snapshot_file = self.cleaned_data.get('snapshot_file')
        if snapshot_file:
            # Convert uploaded file to base64
            try:
                file_data = snapshot_file.read()
                base64_data = base64.b64encode(file_data).decode()
                return f"data:image/{snapshot_file.content_type.split('/')[-1]};base64,{base64_data}"
            except Exception as e:
                raise forms.ValidationError(f"Error converting image to base64: {str(e)}")
        return None

    def save(self, commit=True):
        instance = super().save(commit=False)
        snapshot_file = self.cleaned_data.get('snapshot_file')
        if snapshot_file:
            instance.snapshot = self.cleaned_data['snapshot_file']
        if commit:
            instance.save()
        return instance

class UserImageAdmin(admin.ModelAdmin):
    form = UserImageAdminForm
    list_display = ('user_email', 'bvn_number', 'has_snapshot', 'is_match', 'date_created')
    list_filter = ('is_match', 'date_created')
    search_fields = ('user__email', 'bvn_number')
    raw_id_fields = ('user',)
    readonly_fields = ('date_created', 'date_updated', 'preview_snapshot') #, 'verification_response', 'is_match', 'preview_snapshot')
    actions = ['verify_images']

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = 'User Email'

    def has_snapshot(self, obj):
        return bool(obj.snapshot)
    has_snapshot.boolean = True
    has_snapshot.short_description = 'Has Snapshot'

    def preview_snapshot(self, obj):
        if obj.snapshot:
            return format_html('<img src="{}" width="150" height="150" />', obj.snapshot)
        return "No snapshot uploaded"
    preview_snapshot.short_description = 'Snapshot Preview'

    fieldsets = (
        ('User Information', {
            'fields': ('user', 'bvn_number')
        }),
        ('Image Data', {
            'fields': ('snapshot_file', 'preview_snapshot', 'bvn_photo', 'app_photo')
        }),
        ('Verification Results', {
            'fields': ('verification_response', 'is_match', 'bvn_payload')
        }),
        ('Timestamps', {
            'fields': ('date_created', 'date_updated')
        })
    )

    @admin.action(description="Verify selected customers")
    def verify_images(self, request, queryset):
        for user_image in queryset:
            try:
                if not user_image.snapshot:
                    raise ValueError("No snapshot available")

                # Step 1: Try to get existing BVN payload
                try:
                    kyc = KYCTable.objects.get(user=user_image.user)
                    bvn_detail = kyc.bvn_rel

                    payload = None
                    if bvn_detail.payload:
                        try:
                            payload = json.loads(bvn_detail.payload)
                            # Validate payload structure
                            if not payload.get('data', {}).get('data', {}).get('image') and \
                            not payload.get('data', {}).get('data', {}).get('photo'):
                                payload = None
                        except json.JSONDecodeError:
                            payload = None

                    # Step 2: If no valid payload, fetch from YouVerify
                    if not payload and user_image.bvn_number:
                        youverify_response = verify_bvn_with_uverify(
                            bvn_number=user_image.bvn_number
                        )

                        if youverify_response.get("success") == True and \
                        youverify_response.get("statusCode") == 200:
                            payload = youverify_response

                    # Step 3: Perform verification
                    if payload:
                        # Update the model with base64 images
                        user_image.app_photo = user_image.snapshot
                        verification_result = user_image.verify_user_image(payload)

                        self.message_user(
                            request,
                            f"Verification completed for {user_image.user.email}: Match={verification_result}"
                        )
                    else:
                        raise ValueError("Unable to retrieve BVN details")

                except KYCTable.DoesNotExist:
                    raise ValueError("KYC record not found for the user")

            except Exception as e:
                user_image.is_match = False
                user_image.save()
                self.message_user(
                    request,
                    f"Error verifying {user_image.user.email}: {str(e)}",
                    level="ERROR"
                )


class UpdateKYCModelAdmin(admin.ModelAdmin):
    list_display = ["user", "kyc_one_completed", "kyc_two_completed", "kyc_three_completed"]
    search_fields = ["user__email"]
    raw_id_fields = ["user"]
    list_filter = ["kyc_one_completed", "kyc_two_completed", "kyc_three_completed"]
    actions = ["run_kyc_one", "run_kyc_two", "run_kyc_three", "run_account_generation"]

    @admin.action(description="Run Account Generation")
    def run_account_generation(self, request, queryset):
        for item in queryset:
            kyc, _ = KYCTable.objects.get_or_create(user=item.user)
            if item.user.kyc_level < 1:
                ValueError(f"User KYC Level must be greater than or equal to 1 for {item.user.email}")
            if not item.kyc_one_completed:
                ValueError(f"KYC Level 1 must completed for {item.user.email}")
            bvn_details = BVNDetail.objects.filter(kyc=kyc, is_verified=True)
            if bvn_details:
                bvn_detail = bvn_details.last()
                try:
                    create_wallets_and_accounts_task(bvn_detail.id)
                except Exception as err:
                    ValueError(f"Error occurred while creating account for {item.user.email}\nError: {err}")
                self.message_user(request, f"Account(s) generated successfully for {item.user.email}", level=messages.SUCCESS)
            ValueError(f"Verified BVN details not found for {item.user.email}")

    @admin.action(description="Run KYC Level 1")
    def run_kyc_one(self, request, queryset):
        from django.core.files.base import ContentFile
        for item in queryset:
            kyc, _ = KYCTable.objects.get_or_create(user=item.user)
            if item.kyc_one_completed:
                self.message_user(request, f"KYC Level 1 already completed for {item.user.email}", level=messages.ERROR)

            if not item.bvn:
                self.message_user(request, f"BVN is required to update KYC Level 1 for {item.user.email}", level=messages.ERROR)

            if not item.phone_bvn_match:
                self.message_user(request, f"Please confirm phone number matches with BVN for {item.user.email}", level=messages.ERROR)

            bvn_detail, created = BVNDetail.objects.get_or_create(kyc=kyc)
            if not bvn_detail.is_verified:
                # Update bvn number and re-verify
                bvn_detail.bvn_number = item.bvn
                bvn_detail.save()
                response = BVNDetail.verify_bvn(bvn_number=item.bvn, user=item.user)
                base64_string = None

                if response["status"] is False:
                    self.message_user(request, f"Unable to BVN status for {item.user.email} at the moment", level=messages.ERROR)

                if response["status"] is True and response["data"]["verified_by"] == "youverify":
                    if response["status"] is True and response["data"]["data"]["success"] is True:
                        if "status" in response["data"]["data"]["data"] and response["data"]["data"]["data"]["status"] == "found":
                            base64_string = response["data"]["data"]["data"]["image"]

                if response["status"] is True and response["data"]["verified_by"] == "dojah":
                    if response["status"] is True and response["data"]["success"] is True:
                        if "entity" in response["data"]["data"]:
                            base64_string = response["data"]["data"]["entity"]["image"]

                if base64_string is not None:
                    if "data:" in base64_string and ";base64," in base64_string:
                        header, base64_string = base64_string.split(";base64,")

                    image_data = base64.b64decode(base64_string)
                    file_name = f"{uuid.uuid4()}.png"
                    image_file = ContentFile(image_data, name=file_name)

                    item.bvn_image.save(file_name, image_file, save=True)

                    item.user.kyc_one_progress = "SUCCESSFUL"
                    item.user.kyc_level = 1
                    item.user.save()

                    bvn_detail.is_verified = True
                    bvn_detail.verification_status = "SUCCESSFUL"
                    bvn_detail.save()

                    item.kyc_one_completed = True
                    item.kyc_one_verified_by = request.user
                    item.save()

                    try:
                        create_wallets_and_accounts_task(bvn_detail.id)
                    except Exception as err:
                        self.message_user(request, f"Error occurred while creating account\nError: {err}", level=messages.ERROR)

                else:
                    self.message_user(request, f"Unable to validate BVN for {item.user.email}", level=messages.ERROR)

    @admin.action(description="Run KYC Level 2")
    def run_kyc_two(self, request, queryset):
        from base64 import b64encode
        from django.core.files.base import ContentFile
        from .utils import compare_images
        from .helpers.helper_functions import verify_nin_with_uverify, verify_nin_with_dojah
        for item in queryset:
            kyc, _ = KYCTable.objects.get_or_create(user=item.user)
            if item.kyc_two_completed:
                self.message_user(request, f"KYC Level 2 already completed for {item.user.email}", level=messages.ERROR)

            if not all([item.nin, item.first_selfie, item.second_selfie]):
                self.message_user(request, f"NIN, First and Second selfies are required for {item.user.email}", level=messages.ERROR)

            face_doc, created = DocumentFaceMatchKYC2Detail.objects.get_or_create(kyc=kyc)
            if not face_doc.is_verified:
                # Call You verify to validate NIN
                response = verify_nin_with_uverify(item.nin)
                base64_image_str = "data:"
                request_passed = False
                if response["success"] is True and response["data"]["success"] is True:
                    if "status" in response["data"]["data"] and response["data"]["data"]["status"] == "found":
                        base64_image_str = response["data"]["data"]["image"]
                        request_passed = True
                else:
                    # Try Dojah
                    response = verify_nin_with_dojah(item.nin)
                    if response["success"] is True:
                        if response["data"] and "entity" in response["data"]:
                            data = response["data"]["entity"]
                            base64_image_str = data["photo"]
                            request_passed = True

                if request_passed:
                    if "data:" in base64_image_str and ";base64," in base64_image_str:
                        header, base64_image_str = base64_image_str.split(";base64,")

                    image_data = base64.b64decode(base64_image_str)
                    file_name = f"{uuid.uuid4()}.png"
                    image_file = ContentFile(image_data, name=file_name)
                    item.nin_image.save(file_name, image_file, save=True)

                    base_image_base64_str = f"data:image/png;base64,{b64encode(item.nin_image.file.read()).decode('utf-8')}"
                    first_image_base64_str = f"data:image/png;base64,{b64encode(item.first_selfie.file.read()).decode('utf-8')}"
                    second_image_base64_str = f"data:image/png;base64,{b64encode(item.second_selfie.file.read()).decode('utf-8')}"

                    # Compare Base Image with First and Second Images
                    base_image_bytes = decode_and_validate_image(base_image_base64_str)
                    first_image_byte = decode_and_validate_image(first_image_base64_str)
                    second_image_byte = decode_and_validate_image(second_image_base64_str)

                    is_match = compare_images(base_image_bytes, first_image_byte)
                    if not is_match:
                        # Compare second image
                        is_match = compare_images(base_image_bytes, second_image_byte)
                    if not is_match:
                        ValueError(f"Submitted images mismatched base image for {item.user.email}")

                    face_doc.is_verified = True
                    face_doc.verification_status = "SUCCESSFUL"
                    face_doc.save()

                    item.user.kyc_two_progress = "SUCCESSFUL"
                    item.user.kyc_level = 2
                    item.user.save()

                    item.kyc_two_completed = True
                    item.kyc_two_verified_by = request.user
                    item.save()
                    self.message_user(request, f"NIN: {item.nin} for {item.user.email}, verified successfully", level=messages.SUCCESS)
                else:
                    self.message_user(request, f"NIN: {item.nin} not found for {item.user.email}", level=messages.ERROR)
            else:
                self.message_user(request, f"Unable to validate NIN for {item.user.email}", level=messages.ERROR)

    @admin.action(description="Run KYC Level 3")
    def run_kyc_three(self, request, queryset):
        queryset.update(status="active")


admin.site.register(UserImage, UserImageAdmin)
admin.site.register(UpdateKYC, UpdateKYCModelAdmin)
admin.site.register(KYCTable, KYCTableResourceAdmin)
admin.site.register(BVNDetail, BVNDetailResourceAdmin)
admin.site.register(DocumentFaceMatchKYC2Detail, DocumentFaceMatchKYC2DetailResourceAdmin)
admin.site.register(GuarantorDetail, GuarantorDetailResourceAdmin)
admin.site.register(MetamapRawData, MetamapRawDataResourceAdmin)
admin.site.register(BlacklistedBVN, BlacklistedBVNResourceAdmin)
admin.site.register(CreateReferenceForVerification, CreateReferenceForVerificationResourceAdmin)
