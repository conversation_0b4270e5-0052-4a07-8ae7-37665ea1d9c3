import json

from django.shortcuts import get_object_or_404
from main.models import ConstantTable, User
from kyc_app.models import DocumentFaceMatchKYC2Detail
from django.template.response import TemplateResponse
from django.contrib.auth.decorators import login_required, user_passes_test



def admin_required(user):
    return user.is_authenticated and user.is_staff

@login_required
@user_passes_test(admin_required)
def user_list_view(request):
    constant_table = ConstantTable.get_constant_table_instance()
    users = User.objects.filter(id__in=constant_table.kyc_visualisable_ids)
    return TemplateResponse(
        request, 
        'kyc_details/user_list.html', 
        {'users': users}
    )

@login_required
@user_passes_test(admin_required)
def user_detail_view(request, id):
    user = get_object_or_404(User, pk=id)
    user_detail = get_object_or_404(DocumentFaceMatchKYC2Detail, kyc__user=user)
    
    context = {
        'user': user,
        'data': json.loads(user_detail.payload),
    }
    return TemplateResponse(
        request,
        'kyc_details/user_detail.html',
        context,
    )

