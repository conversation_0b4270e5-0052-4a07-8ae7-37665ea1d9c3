import base64
from io import BytesIO

import boto3
from PIL import Image
from django.conf import settings


def transform_rekognition_response(rekognition_response):
    """
    Transform Rekognition response to a more readable format
    """
    matches = rekognition_response.get('FaceMatches', [])
    if matches:
        similarity = matches[0]['Similarity']
        return {
            'is_match': similarity >= 60, 
            'similarity': similarity,
            'status': 'SUCCESSFUL' if similarity >= 60 else 'FAILED'
        }
    return {
        'is_match': False,
        'similarity': 0,
        'status': 'FAILED'
    }


def validate_image(base64_string):
    """
    Validate and prepare base64 string for decoding.
    Handles both formats with and without comma separators.
    """
    if not base64_string:
        raise ValueError("Empty base64 string provided")
    
    # Check if the base64 string contains a comma (standard data URI format)
    if "," in base64_string:
        try:
            # Split the data URI to get the base64 part
            _, base64_data = base64_string.split(",", 1)
        except ValueError:
            raise ValueError("The provided base64 string does not contain a valid data URI format.")
    else:
        # If no comma, use the entire string as base64 data
        base64_data = base64_string
    
    # Decode the base64 data
    try:
        # Try padding the string if needed before decoding
        padding_needed = len(base64_data) % 4
        if padding_needed:
            base64_data += '=' * (4 - padding_needed)
            
        decoded_image = base64.b64decode(base64_data)
    except Exception as e:
        raise ValueError(f"An error occurred while decoding base64 data: {e}")
    
    return decoded_image


def decode_and_validate_image(base64_image):
    """
    Decode and validate a base64 image, returning bytes ready for AWS Rekognition.
    Handles images with or without data URI prefix.
    """
    try:
        # Decode the image
        image_data = validate_image(base64_image)
        
        # Try to open it as a PIL image 
        try:
            image = Image.open(BytesIO(image_data))
            
            # Convert to a standard format if necessary
            if image.mode == 'RGBA':
                image = image.convert('RGB')
            
            # Save to bytes in a supported format (e.g., JPEG)
            output = BytesIO()
            image.save(output, format='JPEG')
            return output.getvalue()
        except Exception as e:
            # If PIL can't process the image but we have valid binary data,
            # return the raw decoded data (some base64 data may already be in a valid format)
            return image_data
            
    except Exception as e:
        raise ValueError(f"Invalid or unsupported image data: {e}")


def compare_images(source_byte, target_byte):
    # Rekognition client
    rekognition_client = boto3.client(
        "rekognition",
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
        region_name=settings.AWS_REGION,
    )

    # Compare faces
    rekognition_response = rekognition_client.compare_faces(
        SourceImage={"Bytes": source_byte},
        TargetImage={"Bytes": target_byte},
        SimilarityThreshold=60,
    )

    # Process results
    verification_result = transform_rekognition_response(rekognition_response)
    return verification_result["is_match"]



