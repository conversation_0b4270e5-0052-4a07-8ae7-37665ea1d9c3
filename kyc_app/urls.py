from django.urls import path, include
from kyc_app.views import views, user_list_view, user_detail_view


# URLCOnf

bvn_urls = [
    path("user/validate-bvn-number/", views.GetBVNNumberAPIView.as_view()),
    path("user/verify_bvn/", views.VerifyBVNAPIView.as_view()),
    path("user/v2/verify_bvn/", views.V2VerifyBVNAPIView.as_view()),
    path("user/verify_bvn_non_user/", views.VerifyBVNAPIViewForNoneUser.as_view()),
    path("user/confirm_bvn_otp/", views.ConfirmBVNOTPVerifyAPIView.as_view()),
    path("user/confirm_bvn_otp_non_user/", views.NonUserConfirmBVNOTPVerifyAPIView.as_view()),
    path("user/verify_bvn_image/", views.UserImageVerificationView.as_view()),
    path("user/verification/callback/", views.VerificationCallBackAPIView.as_view()),
]

metmap_urls = [
    path("metamap/webhook/response", views.MetamapAPIView.as_view()),
    path("metamap/webhook/bvn_response", views.MetaMapBVNAPIView.as_view()),
    path("dojah/webhook/bvn_response", views.DojahAPIView.as_view()),
]


doc_face_urls = [
    path("idpass/webhook/response", views.IdentityPassAPIView.as_view()),
]

guarantor_urls = [
    path("submit_guarantor_form", views.GuarantorDetailAPIView.as_view()),
    path("guarantor/verify/<str:user_mobile>", views.GuarantorVerificationForm.as_view()),
    path("guarantor-admin/verify/<str:email>", views.GuarantorDetailAdminVerificationAPIView.as_view()),
    # path("guarantor/bvn/verify", views.GuarantorBvnVerification.as_view()),
    # path("guarantor/cancel", views.GuarantorVerificationRejection.as_view()),
    path("verify/guarantor/lotto", views.RecieveLottoVerfAPIView.as_view()),
]


other_urls = [
    path("create_verf_ref/", views.CreateVerificatioReferenceAPIView.as_view()),
]


kyc_template_urls = [
    path('users/', user_list_view, name='user_list'),
    path('user/<int:id>/', user_detail_view, name='user_detail'),

]


urlpatterns = [
    *bvn_urls,
    *metmap_urls,
    *guarantor_urls,
    *doc_face_urls,
    *other_urls,
    *kyc_template_urls,
]



