import os
from string import Template

from celery import shared_task
from django.conf import settings

from liberty_pay.celery import celery
from main.helper.send_emails import new_send_email
from main.models import User
from django.core.management import call_command
from kyc_app.models import GuarantorDetail
from kyc_app.helpers.helper_functions import send_data_to_lotto_back_function


@shared_task
def send_data_to_lotto_back_task(data, guarantor_id):

    get_guarantor_det = GuarantorDetail.objects.filter(id=guarantor_id).last()

    send_to_lotto = send_data_to_lotto_back_function(data=data)

    get_guarantor_det.back_to_lotto_response = send_to_lotto
    get_guarantor_det.save()

    return "DONE"


# @shared_task
def send_data_to_lotto_back_task_without_delay(data, guarantor_id):

    get_guarantor_det = GuarantorDetail.objects.filter(id=guarantor_id).last()

    get_guarantor_det.verf_started = True
    get_guarantor_det.initial_payload = data

    send_to_lotto = send_data_to_lotto_back_function(data=data)

    get_guarantor_det.back_to_lotto_response = send_to_lotto
    
    get_guarantor_det.save()

    return "DONE"


# @shared_task
def send_email_to_guarantor(guarantor_id):
    guarantor_obj = GuarantorDetail.objects.filter(id=guarantor_id, email_sms_sent=False, is_verified=False, guarantor_email__isnull=False).last()
    if guarantor_obj:
        template_path = os.path.join('templates/', 'guarantors_form.html')
        email_template = os.path.abspath(template_path)
        email = str(guarantor_obj.guarantor_email)
        if guarantor_obj.kyc:
            name = str(guarantor_obj.kyc.user.get_full_name()).capitalize()
            phone_no = str(guarantor_obj.kyc.user.phone_number)
        else:
            name = str(guarantor_obj.kyc_extra.user.get_full_name()).capitalize()
            phone_no = str(guarantor_obj.kyc_extra.user.phone_number)
        form_url = settings.GUARANTOR_FORM_LINK
        if guarantor_obj.kyc.user.type_of_user in ["STAFF_AGENT", "DMO_AGENT"]:
            form_url = settings.GUARANTOR_FORM_LINK_AGENT
        cancel_link = settings.GUARANTOR_CANCEL_LINK

        # Send email
        with open(email_template, 'r') as f:
            html = f.read()
        template = Template(html).safe_substitute(name=name, phone=phone_no, link=str(form_url) + "/" + email, cancelLink=cancel_link + email)
        subject = "Liberty Pay Guarantor Verification"
        receiver = email
        new_send_email(receiver, template, subject, meta_data="")

        # Update Guarantor OBJ
        guarantor_obj.email_sms_sent = True
        guarantor_obj.save()

    return True


# @shared_task
def send_guarantor_detail_to_admin_email(first_name, last_name, address, state, city, user_first_name, user_last_name, email, guarantor_email):

    template_path = os.path.join('templates/', 'guarantor_admin_verification.html')
    email_template = os.path.abspath(template_path)
    receiver = settings.GUARANTOR_FORM_ADMIN_VERIFICATION_EMAIL

    # Send email
    with open(email_template, 'r') as f:
        html = f.read()
    template = Template(html).safe_substitute(
        email=email, user_first_name=user_first_name, user_last_name=user_last_name, first_name=first_name, last_name=last_name, address=address,
        guarantor_email=guarantor_email, state=state, city=city
    )
    subject = "Guarantor Verification Request"
    new_send_email(receiver, template, subject, meta_data="")

    return True

