from django.conf import settings
from django.db.models import Sum, Q, F
from retail.models import RetailSystem, LedgerTableModel
from main.models import UserFlag, User
from celery import shared_task
from admin_dashboard.helpers.helpers import (
    send_pos_agent_email, 
    send_sms_to_pos_agent, 
    send_agent_activity_email_task,
    date_utility,
    send_retail_agents_inactivity_email,
    send_retail_agents_performance_email
)

from pathlib import Path
from datetime import datetime, timedelta

import pandas as pd
import os


# date_filter = date_utility(datetime=datetime)
# previous_day = date_filter.get("previous_day")

retail_system_email_exclude_list = [
                                    "<EMAIL>",
                                    "<EMAIL>",
                                    "<EMAIL>",
                                    "<EMAIL>"
                                    ]
all_possible_transaction_list = [
                                "SEND_BANK_TRANSFER",
                                "FUND_BANK_TRANSFER", "FUND_PAYSTACK", "CARD_TRANSACTION_FUND",
                                "CARD_TRANSACTION_FUND_TRANSFER", "CARD_PURCHASE"
                                ]

existing_fund_transfer = LedgerTableModel.objects.filter(
                    transaction_type="FUND_BANK_TRANSFER"
                    ).values("user").distinct("user")
funded_retail_accounts = [system["user"] for system in existing_fund_transfer]


@shared_task
def run_retail_agent_previous_day_check():
    """
    On daily basis, checks the performance of retail agents against their prefunding.
    - Agents with more cash in hand with a disproportionate revenue generation are suspended
    - Provides previous day performance highlights/summary
    - Provides a summary for inactive retail agents in the previous day
    """
    previous_day = datetime.now() - timedelta(days=1)

    suspended_agents_supervisors_email = []
    suspended_agents_supervisors = []
    retail_system_inactive_agents = []
    supervisors_list = []

    retail_system_qs = RetailSystem.objects.filter(
                                ~Q(retail_account__email__in=retail_system_email_exclude_list)
                                & Q(retail_account__in=funded_retail_accounts)
                                ).filter()
    retail_accounts_qs = list(retail_system_qs.values_list("retail_account", flat=True))
    retail_accounts_list = [user for user in retail_accounts_qs]

    ledger_qs = LedgerTableModel.objects.filter(
                user__in=retail_accounts_list
                )
    previous_day_transactions = ledger_qs.filter(date_created__date=previous_day.date())
    revenue_qs = previous_day_transactions.filter(
                ~Q(charge__lte=0)).annotate(
                actual_charge=F("charge") - F("liberty_commission")
                )


    for system in retail_system_qs:
        if system.retail_account:
            retail_agent = system.retail_account
            supervisor = system.supervisor
            agent_branch = system.location.location

            date_assigned = system.date_assigned
            if date_assigned:
                days_since_assigned = (datetime.now().date() - date_assigned.date()).days
                days_since_assigned_less_weekends = days_since_assigned - (days_since_assigned // 7)
            else:
                days_since_assigned = 0
                days_since_assigned_less_weekends = 0

            previous_day_agent_transactions_qs = previous_day_transactions.filter(user=retail_agent)
            overall_agent_transactions = ledger_qs.filter(user=retail_agent)
            previous_day_agent_retail_auto_debit = previous_day_agent_transactions_qs.filter(transaction_type="RETAIL_AUTO_DEBIT")
            previous_day_agent_revenue_qs = previous_day_agent_transactions_qs.filter(
                                    ~Q(charge__lte=0)
                                    ).annotate(actual_charge=F("charge") - F("liberty_commission")
                                    )
            last_retail_auto_debit = ledger_qs.filter(user=retail_agent, transaction_type="RETAIL_AUTO_DEBIT").last()
            agent_last_transaction = ledger_qs.filter(user=retail_agent).last()
            previous_day_transaction_count = previous_day_agent_transactions_qs.count()

            previous_day_highest_cash_at_bank_qs = previous_day_agent_transactions_qs.order_by("cash_at_bank").last() # The peak of the amount they had in their account for the previous day

            previous_day_wallet_balance_greater_than_80k = previous_day_agent_transactions_qs.filter(cash_at_bank__gte=98000)

            previous_day_agent_revenue_amount = list(previous_day_agent_revenue_qs.aggregate(Sum("actual_charge")).values())[0]
            previous_day_agent_revenue_amount = previous_day_agent_revenue_amount if previous_day_agent_revenue_amount else 0.00
            cash_in_hand = agent_last_transaction.cash_in_hand if agent_last_transaction and agent_last_transaction.cash_in_hand else 0.00
            cash_at_bank = agent_last_transaction.cash_at_bank if agent_last_transaction and agent_last_transaction.cash_at_bank else 0.00
            cash_available = agent_last_transaction.cash_available_total if agent_last_transaction and agent_last_transaction.cash_available_total else 1.00
            total_agent_revenue_generated = list(overall_agent_transactions.filter(
                                                ~Q(charge__lte=0)
                                                ).annotate(actual_charge=F("charge") - F("liberty_commission")
                                                ).aggregate(Sum("actual_charge")).values())[0]

            revenue_target_percentage = ((previous_day_agent_revenue_amount if previous_day_agent_revenue_amount else 0) / 3000) * 100
            revenue_capital_percentage = ((previous_day_agent_revenue_amount if previous_day_agent_revenue_amount else 0) / cash_available) * 100
            cash_capital_percentage = ((cash_in_hand if cash_in_hand else 0) / cash_available) * 100

            average_daily_revenue = (
                                    (total_agent_revenue_generated if total_agent_revenue_generated else 0) / 
                                    (days_since_assigned_less_weekends if days_since_assigned_less_weekends else 1)
                                    )

            if previous_day_agent_revenue_amount > 2500:
                performance_level = "High"
                retail_agent.terminal_status = "ACTIVE"
            elif previous_day_agent_revenue_amount < 2500 and previous_day_agent_revenue_amount > 1500:
                performance_level = "Mid-low"
                retail_agent.terminal_status = "PARTIALLY_INACTIVE"
            else:
                performance_level = "low"
                retail_agent.terminal_status = "INACTIVE"

            if previous_day_agent_revenue_amount < 1000 and cash_capital_percentage > 60:
                reason="RETAIL AGENT with more cash at hand and low revenue"
                if previous_day_wallet_balance_greater_than_80k.count() < 1 and datetime.now().weekday() == 4:

                    User.suspend_user(
                        user=retail_agent,
                        reason=reason,
                        request=None
                        )

                    system.consecutive_weeks_suspension_count += 1

                    # Get Supervisors Details
                    if supervisor:
                        supervisor_agent_tup = (supervisor.phone_number, retail_agent.terminal_id)
                        suspended_agents_supervisors_email.append((supervisor.email, retail_agent.terminal_id))
                        suspended_agents_supervisors.append(supervisor_agent_tup)
                else:
                    pass

                data = {
                    "agent_name": retail_agent.get_full_name(),
                    "phone_number": retail_agent.phone_number,
                    "email": retail_agent.email,
                    "branch": agent_branch,
                    "supervisor_name": supervisor.get_full_name() if supervisor else None,
                    "age": days_since_assigned_less_weekends,
                    "terminal_id": retail_agent.terminal_id,
                    "previous_day_revenue": previous_day_agent_revenue_amount if previous_day_agent_revenue_amount else 0.00,
                    "previous_day_highest_cash_at_bank": previous_day_highest_cash_at_bank_qs.cash_at_bank if previous_day_highest_cash_at_bank_qs and previous_day_highest_cash_at_bank_qs.cash_at_bank else 0.00,
                    "cash_at_hand": cash_in_hand if cash_in_hand else 0.00,
                    "cash_in_bank": cash_at_bank if cash_at_bank else 0.00,
                    "cash_available": cash_available if cash_available else 0.00,
                    "previous_day_retail_auto_debit_exists": "Yes" if previous_day_agent_retail_auto_debit else "No",
                    "cash_to_capital_percentage": cash_capital_percentage if cash_capital_percentage else 0.00,
                    "previous_day_transaction_count": previous_day_transaction_count,
                    "daily_register": "Present" if previous_day_agent_revenue_amount else "Absent",
                    "average_daily_revenue": average_daily_revenue,
                    "total_revenue_generated": total_agent_revenue_generated if total_agent_revenue_generated else 0.00,
                    "performance_level": performance_level,
                    "marked_for_retrieval": "Yes" if system.consecutive_weeks_suspension_count >= 3 else "No"
                }
                retail_system_inactive_agents.append(data)
            else:
                system.consecutive_weeks_suspension_count = 0
        system.save()
        retail_agent.save()

    number_of_suspended_agents = retail_system_qs.filter(
                                 Q(retail_account__is_suspended=True) |
                                 ~Q(retail_account__terminal_suspended=True
                                )).count()
    number_of_inactive_agents = len(retail_system_inactive_agents)
    number_of_retrievals = retail_system_qs.filter(
                           consecutive_weeks_suspension_count__gte=3
                           ).count()

    df = pd.DataFrame()
    inactive_agents_df = df.from_dict(retail_system_inactive_agents)

    file_path = os.path.join(settings.BASE_DIR, 'media/retail/')
    last_file = os.path.join(f"{file_path}Inactive Retail Agents_{datetime.now().date()}.xlsx")

    my_file = Path(last_file)
    if my_file.is_file():
        os.remove(last_file)

    try:
        os.mkdir(file_path)
    except Exception:
        pass

    inactive_agents_df.to_excel(f"{file_path}Inactive Retail Agents_{datetime.now().date()}.xlsx")

    # Send email notification to suspended agents supervisors
    supervisor_terminals_list = []
    for supervisor in supervisors_list:
        if supervisor:
            for record in suspended_agents_supervisors_email:
                if supervisor.email == record[0]:
                    supervisor_terminals_list.append(record[1])

            if len(supervisor_terminals_list) < 2 and len(supervisor_terminals_list) > 0:
                send_pos_agent_email(
                    email=supervisor.email,
                    files="",
                    template="pos_agent_email.html",
                    email_subject="Terminal Suspension Notification",
                    message=f"""<p>
                    Dear supervisor,</p>
                    <p>Your retail agents with the following terminal id: {"".join(supervisor_terminals_list)} 
                    has been suspended due to poor performance in transactions activity. <br/>
                    Kindly contact them for resolutions.</p>
                    """
                )

            elif len(supervisor_terminals_list) >= 2:
                send_pos_agent_email(
                    email=supervisor.email,
                    files="",
                    template="pos_agent_email.html",
                    email_subject="Terminal Suspension Notification",
                    message=f"""<p>
                    Dear supervisor,</p>
                    <p>Your retail agents with the following terminal ids: {', '.join(supervisor_terminals_list)}
                    have been suspended due to poor performance in transactions activity. <br/>
                    Kindly contact your them for immediate resolution.
                    </p>"""
                )

    # last_file = os.path.join(settings.BASE_DIR, 'media/agent_report/agent_activity_last.xlsx')

    with open(last_file, "rb") as excel_file:
        agent_activities = excel_file.read()
        excel_file.close()

    if not settings.ENVIRONMENT == "development":
        agent_activity_email_list = [
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>"
                            ]
    else:
        agent_activity_email_list = [
                        "<EMAIL>",
                        "<EMAIL>",
                        ]

    for email in agent_activity_email_list:             
        send_retail_agents_inactivity_email(
            message = "This is a list of inactive retail agents for the past day",
            file = agent_activities,
            file_name = f"Inactive Retail Agents_{datetime.now().date()}.xlsx",
            email_subject = "Retail Agents Inactivity List",
            email=email,
            number_of_inactive_agents = number_of_inactive_agents,
            number_of_suspended_agents = number_of_suspended_agents,
            total_cash_at_hand = total_cash_at_hand,
            total_cash_in_bank = total_cash_in_bank,
            total_revenue = total_revenue,
            number_of_retrievals = number_of_retrievals,
            total_cash_available = total_cash_available,
            date = datetime.now().date()
            )


    # Send sms notification to supervisors of suspended terminals
    supervisor_terminals_list = []
    for supervisor in supervisors_list:
        if supervisor:
            for record in suspended_agents_supervisors:
                if supervisor.phone_number == record[0]:
                    supervisor_terminals_list.append(record[1])


            if len(supervisor_terminals_list) < 2 and len(supervisor_terminals_list) > 0:
                send_sms_to_pos_agent(
                    phone_number=supervisor.phone_number, 
                    message = f"""
                        Dear Supervisor, Your retail agent with the following terminal_id; {"".join(supervisor_terminals_list)} 
                        has been suspended due to inactivity. 
                        Kindly contact them for immediate resolution.
                        """
                    )
            elif len(supervisor_terminals_list) >= 2:
                send_sms_to_pos_agent(
                    phone_number=supervisor.phone_number, 
                    message = f"""
                    Dear Supervisor, Your retail agents with the following terminal_ids; {', '.join(supervisor_terminals_list)}, 
                    have been suspended due to inactivity. Kindly contact them for resolution.
                    """
                    )

    return "DONE"


@shared_task
def send_retail_agents_inactivity_check_email():
    """
    Retrieves the most recently generated retail agents daily
    activites report then dispatches it.
    """
    previous_day = datetime.now() - timedelta(days=1)

    file_path = os.path.join(settings.BASE_DIR, 'media/retail/') 
    last_file = os.path.join(f"{file_path}Inactive Retail Agents_{datetime.now().date()}.xlsx")

    with open(last_file, "rb") as excel_file:
        agent_activities = excel_file.read()
        excel_file.close()

    if not settings.ENVIRONMENT == "development":
        agent_activity_email_list = [
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>"
                            ]
    else:
        agent_activity_email_list = [
                        "<EMAIL>",
                        "<EMAIL>"
                        ]

    for email in agent_activity_email_list:             
        send_agent_activity_email_task(
            message = "This is a list of inactive retail agents for the past day",
            file = agent_activities,
            file_name = f"Inactive Retail Agents_{datetime.now().date()}.xlsx",
            email_subject = "Retail Agents Inactivity List",
            email=email
            )  
    return "DONE"


@shared_task
def run_retail_agents_performance_report():
    """
    Checks retail agents performance for the previous day
    and generates a report for it.
    """
    previous_day = datetime.now() - timedelta(days=1)

    retail_system_qs = RetailSystem.objects.filter(
                                ~Q(retail_account__email__in=retail_system_email_exclude_list)
                                & Q(retail_account__in=funded_retail_accounts)
                                ).filter()

    retail_accounts_qs = list(retail_system_qs.values_list("retail_account", flat=True))
    retail_accounts_list = [user for user in retail_accounts_qs]

    ledger_qs = LedgerTableModel.objects.filter(
                user__in=retail_accounts_list
                )
    previous_day_transactions = ledger_qs.filter(date_created__date=previous_day.date())
    revenue_qs = previous_day_transactions.filter(
                ~Q(charge__lte=0)).annotate(
                actual_charge=F("charge") - F("liberty_commission")
                )

    total_cash_at_bank_list = []
    total_cash_in_hand_list = []
    total_cash_available_list = []
    total_revenue_amount = list(revenue_qs.filter(date_created__date=previous_day.date()).aggregate(Sum("actual_charge")).values())[0]
    previous_day_highest_cash_at_bank_total_list = []


    retail_agents_list = []
    for system in retail_system_qs:
        retail_agent = system.retail_account
        agent_branch = system.location.location
        supervisor = system.supervisor
        date_assigned = system.date_assigned

        if date_assigned:
            days_since_assigned = (datetime.now().date() - date_assigned.date()).days
            days_since_assigned_less_weekends = days_since_assigned - (days_since_assigned / 7)
        else:
            days_since_assigned = 0
            days_since_assigned_less_weekends = 0

        if retail_agent:
            agent_transaction_qs = ledger_qs.filter(user=retail_agent)
            agent_last_transaction = agent_transaction_qs.last()
            agent_last_retail_auto_debit = agent_transaction_qs.filter(transaction_type="RETAIL_AUTO_DEBIT")
            previous_day_agent_retail_auto_debit = agent_transaction_qs.filter(transaction_type="RETAIL_AUTO_DEBIT")
            agent_revenue_qs = agent_transaction_qs.filter(
                                    ~Q(charge__lte=0)).annotate(
                                    actual_charge=F("charge") - F("liberty_commission")
                                    )

            previous_day_agent_transactions = previous_day_transactions.filter(user=retail_agent)
            previous_day_agent_revenue_qs = agent_revenue_qs.filter(
                                            date_created__date=previous_day.date(),
                                            user=retail_agent
                                            )

            total_agent_transactions_amount = list(agent_transaction_qs.aggregate(Sum("amount")).values())[0]
            total_agent_revenue_amount = list(agent_revenue_qs.aggregate(Sum("actual_charge")).values())[0]
            
            previous_day_total_agent_revenue_amount = list(previous_day_agent_revenue_qs.aggregate(
                                                      Sum("actual_charge")).values())[0]

            cash_at_bank = agent_last_transaction.cash_at_bank if agent_last_transaction else 0.00
            cash_in_hand = agent_last_transaction.cash_in_hand if agent_last_transaction else 0.00
            cash_available = agent_last_transaction.cash_available_total if agent_last_transaction else 0.00
            previous_day_agent_revenue_amount = list(previous_day_agent_transactions.filter(
                                    ~Q(charge__lte=0)).annotate(actual_charge=F("charge") - F("liberty_commission")
                                    ).aggregate(Sum("actual_charge")).values())[0]

            cash_capital_percentage = ((cash_in_hand if cash_in_hand else 0) / 
                                       (cash_available if cash_available else 1)) * 100

            average_daily_revenue = (
                                    (total_agent_revenue_amount if total_agent_revenue_amount else 0) / 
                                     (days_since_assigned_less_weekends if 
                                      days_since_assigned_less_weekends else 1)
                                    )
            previous_day_agent_highest_cash_at_bank_qs = previous_day_agent_transactions.order_by("cash_at_bank").last()
            if previous_day_agent_highest_cash_at_bank_qs is None:
                previous_day_agent_highest_cash_at_bank_qs = agent_transaction_qs.last()
            previous_day_agent_highest_cash_at_bank = previous_day_agent_highest_cash_at_bank_qs.cash_at_bank if previous_day_agent_highest_cash_at_bank_qs and previous_day_agent_highest_cash_at_bank_qs.cash_at_bank else 0.00

            if average_daily_revenue > 2500:
                performance_level = "High"
            elif average_daily_revenue < 2500 and average_daily_revenue > 1500:
                performance_level = "Mid-low"
            else:
                performance_level = "Low"

            # Append Sum List
            total_cash_at_bank_list.append(cash_at_bank)
            total_cash_in_hand_list.append(cash_in_hand)
            total_cash_available_list.append(cash_available)
            previous_day_highest_cash_at_bank_total_list.append(previous_day_agent_highest_cash_at_bank)

            agent_data = {
                "agent_name": retail_agent.get_full_name(),
                "phone_number": retail_agent.phone_number,
                "email": retail_agent.email,
                "branch": agent_branch,
                "supervisor_name": supervisor.get_full_name() if supervisor else None,
                "age": days_since_assigned_less_weekends,
                "terminal_id": retail_agent.terminal_id,
                "total_revenue": total_agent_revenue_amount if total_agent_revenue_amount else 0.00,
                "previous_day_revenue_amount": previous_day_total_agent_revenue_amount if previous_day_total_agent_revenue_amount else 0.00,
                "cash_in_hand": cash_in_hand if cash_in_hand else 0.00,
                "cash_at_bank": cash_at_bank if cash_at_bank else 0.00,
                "cash_available": cash_available if cash_available else 0.00,
                "total_transactions_amount": total_agent_transactions_amount if total_agent_transactions_amount else 0.00,
                "previous_day_retail_auto_debit_exists": "Yes" if previous_day_agent_retail_auto_debit else "No",
                "cash_to_capital_percentage": cash_capital_percentage if cash_capital_percentage else 0.00,
                "previous_day_transaction_count": previous_day_agent_transactions.count(),
                "daily_register": "Present" if previous_day_agent_revenue_amount else "Absent",
                "average_daily_revenue": average_daily_revenue,
                "performance_level": performance_level,
                "agent_suspended": "Yes" if retail_agent.is_suspended else "No",
                "marked_for_retrieval": "Yes" if system.consecutive_weeks_suspension_count >= 3 else "No",
                "previous_day_balance_peak": previous_day_agent_highest_cash_at_bank_qs.cash_at_bank if previous_day_agent_highest_cash_at_bank_qs and previous_day_agent_highest_cash_at_bank_qs.cash_at_bank else 0.00,
                "tf": "NA"
            }
            retail_agents_list.append(agent_data)

    number_of_suspended_agents = retail_system_qs.filter(retail_account__isnull=False).filter(
                                 retail_account__is_suspended=True
                                ).count()

    number_of_retrievals = retail_system_qs.filter(consecutive_weeks_suspension_count__gte=3).count()
    number_of_inactive_agents = retail_system_qs.filter(
                                retail_account__isnull=False,
                                retail_account__terminal_status__in=["INACTIVE", "PARTIALLY_INACTIVE"]
                                ).count()
    
    number_of_active_agents = retail_system_qs.filter(
                                retail_account__isnull=False, 
                                retail_account__terminal_status="ACTIVE"
                                ).count()

    total_cash_in_hand = sum(total_cash_in_hand_list)
    total_cash_at_bank = sum(total_cash_at_bank_list)
    total_cash_available = sum(total_cash_available_list)
    previous_day_highest_cash_at_bank_total = sum(previous_day_highest_cash_at_bank_total_list)
    number_of_retail_agents = len(retail_accounts_list)

    df = pd.DataFrame()
    retail_agents_df = df.from_dict(retail_agents_list)

    base_path = os.path.join(settings.BASE_DIR, "media/retail/")
    last_file_path = f"{base_path}Retail Agents Performance Report_{datetime.now().date()}.xlsx"
    last_perf_file = Path(last_file_path)

    if last_perf_file.is_file():
        os.remove(last_perf_file)

    try:
        os.mkdir(base_path)
    except Exception:
        pass

    retail_agents_df.to_excel(f"{base_path}/Retail Agents Performance Report_{datetime.now().date()}.xlsx")

    with open(f"{base_path}/Retail Agents Performance Report_{datetime.now().date()}.xlsx", "rb") as excel_file:
        retail_report_file = excel_file.read()
        excel_file.close()

    # Send Report to Management Staff
    if settings.ENVIRONMENT == "development":
        admin_email_list = [
                    "<EMAIL>"
                    ]
        phone_number_list = ["*************", "*************"]
    else:
        admin_email_list = [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>"
                    ]
        phone_number_list = ["*************", "2348077469471", "2348090643057"]

    for email in admin_email_list:
        send_retail_agents_performance_email(
                message="This is the retail agent performance report for the previous day",
                file=retail_report_file,
                file_name=f"Retail Agents Performance Report_{datetime.now().date()}.xlsx",
                email_subject="Retail Agents Performance Report",
                email=email,
                number_of_inactive_agents=number_of_inactive_agents,
                number_of_active_agents=number_of_active_agents,
                number_of_retail_agents=number_of_retail_agents,
                number_of_suspended_agents=number_of_suspended_agents,
                total_cash_at_hand=total_cash_in_hand if total_cash_in_hand else 0.00,
                total_cash_in_bank=total_cash_at_bank if total_cash_at_bank else 0.00,
                total_revenue=total_revenue_amount if total_revenue_amount else 0.00,
                number_of_retrievals=number_of_retrievals,
                total_cash_available=total_cash_available if total_cash_available else 0.00,
                date=datetime.now().date(),
                peak_cash_at_bank=previous_day_highest_cash_at_bank_total
            )
        
    # Send Sms summary to management staff
    message = f"""Retail summary for the previous day:\nPeak Cash in Bank: {previous_day_highest_cash_at_bank_total}\nTotal Revenue Generated: {total_revenue_amount if total_revenue_amount else 0.00}\nTotal Number of Agents: {len(retail_accounts_list)}\nNumber of Inactive Agents: {number_of_inactive_agents}"""
    for phone_number in phone_number_list:
        send_sms_to_pos_agent(phone_number=phone_number, message=message)


@shared_task
def send_sms_to_retail_agents(phone_number, message):
    send_sms_to_pos_agent(phone_number=phone_number, message=message)


@shared_task
def monitor_retail_agents_balances():
    # retail_agents_qs = RetailSystem.objects.values_list("retail_account", flat=True)
    """
    Fetch retail agents balances that are below 30k.
    Prompt the agents by email and by sms to topup their wallets.
    """
    ledger_qs = LedgerTableModel.objects.filter(
                cash_at_bank__lt=30000,
                user__type_of_user="LIBERTY_RETAIL",
                ).order_by(
                "user",
                "-date_created").distinct(
                # "date_created",
                "user")

    for record in ledger_qs:
        retail_agent = record.user

        # Send Email Prompt to agent
        send_pos_agent_email(
                    email=retail_agent.email,
                    files="",
                    template="pos_agent_email.html",
                    email_subject="Low Wallet Balance",
                    message=f"""<p>
                    Dear agent,</p>
                    <p>Your wallet balance is less than 30k. Fund your wallet now to ensure smooth operation.
                    Thank you.
                    </p>"""
                )

        # Send SMS Prompt
        send_sms_to_pos_agent(
                    phone_number=retail_agent.phone_number,
                    message = f"""
                        Your wallet balance is less than 30k. Fund your wallet now to ensure smooth operation.
                        Thank you
                        """
                    )

    return "DONE!!!"