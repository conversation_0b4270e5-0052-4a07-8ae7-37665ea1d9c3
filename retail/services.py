from django.utils import timezone
from django.db.models import Sum, Q, Count, F
from django.contrib.auth import get_user_model
from django.contrib.sessions.models import Session
from retail.models import (
    LedgerTableModel, 
    RetailSystem, 
    SendAgentsSmsDumpData, 
    RetailPerformance
    )
from retail.serializers import SendSmsSerializer
from retail.tasks import send_sms_to_retail_agents
from admin_dashboard.helpers.helpers import date_utility, get_percentage_diff
from main.models import NewLocationList
from accounts.models import WalletSystem, Transaction

from datetime import datetime, timedelta
from math import floor
import calendar

User = get_user_model()

retail_system_email_exclude_list = ["<EMAIL>", 
                                    "<EMAIL>", 
                                    "<EMAIL>"
                                    ]
cashout_transaction_type_list = ["CARD_TRANSACTION_FUND_TRANSFER", 
                                 "CARD_TRANSACTION_FUND"]
transfer_transaction_type_list = ["SEND_BANK_TRANSFER", "SEND_BUDDY", 
                                  "FUND_BANK_TRANSFER"]
all_possible_transaction_list = [
                                "SEND_BANK_TRANSFER",
                                "FUND_BANK_TRANSFER", "FUND_PAYSTACK", 
                                "CARD_TRANSACTION_FUND",
                                "CARD_TRANSACTION_FUND_TRANSFER"
                                ]
leaderboard_transactions_list = [
                                "SEND_BANK_TRANSFER", 
                                "CARD_TRANSACTION_FUND",
                                "CARD_TRANSACTION_FUND_TRANSFER", 
                                "FUND_BANK_TRANSFER"
                                ]

inflow_transactions_list = ["FUND_BANK_TRANSFER", "FUND_PAYSTACK"]

existing_fund_transfer = LedgerTableModel.objects.filter(
                    transaction_type="FUND_BANK_TRANSFER"
                    ).values("user").distinct("user")
funded_retail_accounts = [system["user"] for system in existing_fund_transfer]

class DateUtility:
    def __init__(self):
        filter_date = date_utility(datetime=datetime)
        self.previous_month_start = filter_date.get("previous_month_start")
        self.previous_month_end = filter_date.get("previous_month_end")
        self.previous_year_current_month_start = filter_date.get("previous_year_current_month_start")
        self.previous_year_current_month_end = filter_date.get("previous_year_current_month_end")
        self.previous_year_current_previous_month = filter_date.get("previous_year_current_previous_month")
        self.year_end = filter_date.get("year_end")
        self.year_start = filter_date.get("year_start")
        self.init_start = filter_date.get("init_start")
        self.previous_year_current_following_month = filter_date.get("previous_year_current_following_month")
        self.start_of_all_transactions = filter_date.get("start_of_all_transactions")
        self.month_start = filter_date.get("month_start")
        self.today = filter_date.get("today")
        self.previous_day = filter_date.get("previous_day") 
        self.previous_year_end = filter_date.get("previous_year_end") 
        self.previous_year_start = filter_date.get("previous_year_start")
        self.week_start = filter_date.get("week_start")
        self.date_today = filter_date.get("date_today")
        self.month_ago = filter_date.get("month_ago")
        self.date_today_date = filter_date.get("date_today_date")
        self.midnight_time = filter_date.get("midnight_time")
        self.day_ago = filter_date.get("day_ago")
        self.previous_week_start = filter_date.get("previous_week_start")
        self.previous_week_end = filter_date.get("previous_week_end")
        self.current_date = filter_date.get("current_date")

        # Create an array of the month keys
        months_list1 = list(range(self.previous_year_current_month_start.month, 
                        (self.previous_year_current_month_start.month+13
                        -self.previous_year_current_month_start.month)))
        months_list2 = list(range(1, 12-
                    (12-self.previous_year_current_month_start.month)+1))
        months_list_names = [ 
                            f"{calendar.month_name[month][:3]} {self.previous_year_current_month_start.year}" 
                            for month in months_list1
                            ]
        months_list2_names = [f"{calendar.month_name[month][:3]} {self.today.year}" for month in months_list2]
        self.months_list_names = months_list_names + months_list2_names
        months_list = months_list1 + months_list2
        self.year_months_tuple_list = [(self.previous_year_current_month_start.year, month) for month in months_list1] + \
            [(self.today.year, month) for month in months_list2]

        # days list for day labels
        last_week_days = [(calendar.day_name[day])[:3] for day in range(self.current_date.weekday(), 7)]
        current_week_days = [(calendar.day_name[day])[:3] for day in range(self.current_date.weekday()+1)]
        self.days_label = last_week_days + current_week_days

        # Create an array of dates starting from exactly 1 month ago
        if self.current_date.month > 1:
            self._previous_month_current_month_date = self.current_date.replace(month=self.current_date.month-1)
        else:
            self._previous_month_current_month_date = self.current_date.replace(year=self.current_date.year-1, month=12)
        # self._previous_month_current_month_date = self.current_date.replace(
        #                                         month=self.current_date.month-1, 
        #                                         day=calendar.monthrange(self.current_date.year,
        #                                         self.current_date.month-1)[1])
        if self.current_date.month > 1:
            if self.current_date.month == 3 and self.current_date.day > 28:
                self._previous_month_current_month_date = self.current_date.replace(month=self.current_date.month-1,
                day=calendar.monthrange(self.current_date.year, self.current_date.month-1)[1])
            else:
                self._previous_month_current_month_date = self.current_date.replace(month=self.current_date.month-1)
            _last_month_name = calendar.month_name[(self.current_date.replace(month=self.current_date.month-1, 
                                                    day=calendar.monthrange(self.current_date.year,
                                                    self.current_date.month-1)[1])).month][:3]
        else:
            _last_month_name = calendar.month_name[(self.current_date.replace(month=12, 
                                                    day=calendar.monthrange(self.current_date.year-1,
                                                    12)[1])).month][:3]

        self._previous_week_current_week_date = self.current_date - timedelta(days=7)
        _current_month_name = calendar.month_name[self.current_date.month]
        _current_month_day_date = self.current_date.day
        if self.current_date.month > 1:
            _last_month_num_days = calendar.monthrange(self.current_date.year, self.current_date.month-1)[1]
        else:
            _last_month_num_days = calendar.monthrange(self.current_date.year-1, 12)[1]
        if _current_month_day_date == 31:
            _last_month_dates = [f"{_last_month_name} {day}" for day in range(30, _last_month_num_days+1)]
        else:
            _last_month_dates = [f"{_last_month_name} {day}" for day in range(_current_month_day_date, _last_month_num_days+1)]
        _current_month_date = [f"{_current_month_name} {day}" for day in range(1, _current_month_day_date+1)]
        self._month_2_month_dates_list = _last_month_dates + _current_month_date


    def get_date_filter(self, request):
        """Will always return a date filter"""

        filter_value = request.GET.get("filter")
        if filter_value is not None:
            if filter_value == "today":
                self.date_filter = {"date_created__date": timezone.now().date()}
            elif filter_value == "this_week":
                self.date_filter = {"date_created__gte": self.week_start}
            elif filter_value == "last_week":
                self.date_filter = {"date_created__gte": self.previous_week_start, "date_created__lte": self.previous_week_end}
            elif filter_value == "this_month":
                self.date_filter = {"date_created__gte": self.month_start}
            elif filter_value == "last_month":
                self.date_filter = {"date_created__gte": self.previous_month_start, "date_created__lte": self.previous_month_end}
            elif filter_value == "this_year":
                self.date_filter = {"date_created__gte": self.year_start}
            elif filter_value == "last_year":
                self.date_filter = {"date_created__gte": self.previous_year_start, "date_created__lte": self.previous_year_end}
            elif filter_value == "yesterday":
                self.date_filter = {"date_created__date": self.previous_day.date()}
            else:
                self.date_filter = {"date_created__gte": self.start_of_all_transactions}
        else:
            self.date_filter = {"date_created__gte": self.start_of_all_transactions}
        return self.date_filter

class Maindashboard:
    def __init__(self, request):
        # Initialize dates
        self.previous_day = DateUtility().previous_day
        self.previous_week_end = DateUtility().previous_week_end
        self.previous_month_end = DateUtility().previous_month_end
        self.previous_year_end = DateUtility().previous_year_end
        self.week_start = DateUtility().week_start
        self.month_start = DateUtility().month_start
        self.year_start = DateUtility().year_start
        self.previous_year_start = DateUtility().previous_year_start
        self.previous_week_start = DateUtility().previous_week_start
        self.previous_month_start = DateUtility().previous_month_start
        self.midnight_time = DateUtility().midnight_time

        self.agent_filter = request.GET.get("agent-filter")
        self.active_user = request.user
        self.location_qs = NewLocationList.objects.filter()
        
        self.retail_system_qs = RetailSystem.objects.filter(
                             ~Q(retail_account__email__in=retail_system_email_exclude_list)
                             & Q(retail_account__in=funded_retail_accounts)
                            )
        
        if self.agent_filter == "my_agents":
            self.retail_system_qs = self.retail_system_qs.filter(supervisor=self.active_user)
        elif self.agent_filter in [location.location for location in self.location_qs]:
            self.retail_system_qs = self.retail_system_qs.filter(location__location=self.agent_filter)
        
        self.retail_system_users = [system.retail_account for system in self.retail_system_qs]            
        self.ledger_qs = LedgerTableModel.objects.filter(
                        Q(user__in=self.retail_system_users,
                        transaction_type__in=all_possible_transaction_list) &
                        Q(user__in=funded_retail_accounts)
                        )
        
        self.expected_salary_qs = RetailPerformance.objects.filter(
                        retail_account__retail_account__in=[agent for agent in self.retail_system_users]
                        )
        
        self.bills_utility_airtime_qs = LedgerTableModel.objects.filter(user__in=self.retail_system_users, 
                                        transaction__status="SUCCESSFUL", transaction__is_reversed=False)
        self.cashout_qs = self.ledger_qs.filter(transaction_type__in=cashout_transaction_type_list)
        self.transfer_qs = self.ledger_qs.filter(transaction_type__in=transfer_transaction_type_list)
        self.inflow_qs = self.ledger_qs.filter(transaction_type__in=inflow_transactions_list)
        self.successful_transactions_qs = self.ledger_qs.filter(transaction__status="SUCCESSFUL", transaction__is_reversed=False)
        self.successful_cashout_qs = self.cashout_qs.filter(transaction__status="SUCCESSFUL", transaction__is_reversed=False)
        self.successful_transfer_qs = self.transfer_qs.filter(transaction__status="SUCCESSFUL", transaction__is_reversed=False)
        self.successful_inflow_qs = self.inflow_qs.filter(transaction__status="SUCCESSFUL", transaction__is_reversed=False)
        self.wallet_system_qs = WalletSystem.objects.filter(
                                                    Q(user__in=self.retail_system_users) &
                                                    Q(user__in=funded_retail_accounts)
                                                    )

        if self.retail_system_qs:
            first_retail_transaction_date = self.retail_system_qs.first().date_created.date()
        else:
            first_retail_transaction_date = RetailSystem.objects.first().date_created.date()
        self.number_of_days_since_first_trans = (timezone.now().date() - first_retail_transaction_date).days

        # number of users
        self.activated_retail_agents_qs = self.retail_system_qs.filter(
                                            retail_account__is_suspended=False
                                            )
        self.number_of_retail_agents_count = self.activated_retail_agents_qs.count()
        self.number_of_retail_agents_till_yesterday = self.activated_retail_agents_qs.filter(
                                                    date_created__date__lte=self.previous_day.date()
                                                    ).count()
        self.number_of_retail_agents_till_last_week = self.activated_retail_agents_qs.filter(
                                                        date_created__lte=self.previous_week_end
                                                        ).count()
        self.number_of_retail_agents_till_last_month = self.activated_retail_agents_qs.filter(
                                                        date_created__lte=self.previous_month_end
                                                        ).count()
        self.number_of_retail_agents_till_last_year = self.activated_retail_agents_qs.filter(
                                                        date_created__lte=self.previous_year_end
                                                        ).count()

        # number of days since start of retail
        if self.retail_system_qs:
            numbers_day_since_retail_start = (timezone.now().date() - self.retail_system_qs.first().date_created.date()).days
        else:
            numbers_day_since_retail_start = (timezone.now().date() - RetailSystem.objects.first().date_created.date()).days
        self.numbers_day_since_retail_start_less_weekends = numbers_day_since_retail_start - floor(numbers_day_since_retail_start / 7)

    def get_transactions_overview_one(self):
        # Transaction Amount
        total_transactions_amount = list(self.successful_transactions_qs.aggregate(Sum("amount")).values())[0]
        total_transactions_amount_today = list(self.successful_transactions_qs.filter(date_created__date=timezone.now().date()).aggregate(
                                                                        Sum("amount")).values())[0]
        total_transactions_amount_yesterday = list(self.successful_transactions_qs.filter(date_created__date=self.previous_day.date()).aggregate(
                                                                        Sum("amount")).values())[0]
        total_transactions_amount_this_week = list(self.successful_transactions_qs.filter(date_created__gte=self.week_start).aggregate(
                                                                        Sum("amount")).values())[0]
        total_transactions_amount_this_month = list(self.successful_transactions_qs.filter(date_created__gte=self.month_start).aggregate(
                                                                        Sum("amount")).values())[0]
        total_transactions_amount_this_year = list(self.successful_transactions_qs.filter(date_created__gte=self.year_start).aggregate(
                                                                        Sum("amount")).values())[0]
        total_transactions_amount_last_year = list(self.successful_transactions_qs.filter(date_created__gte=self.previous_year_start, 
                                                date_created__lte=self.previous_year_end).aggregate(Sum("amount")).values())[0]
        total_transactions_amount_last_week = list(self.successful_transactions_qs.filter(date_created__gte=self.previous_week_start, 
                                                date_created__lte=self.previous_week_end).aggregate(Sum("amount")).values())[0]
        total_transactions_amount_last_month = list(self.successful_transactions_qs.filter(date_created__gte=self.previous_month_start, 
                                                date_created__lte=self.previous_month_end).aggregate(Sum("amount")).values())[0]
        
        # Average Transaction Amount
        average_daily_transaction_amount = (
                                        (total_transactions_amount if total_transactions_amount else 0.00) /
                                        (self.number_of_days_since_first_trans if self.number_of_days_since_first_trans else 1)
                                        )
        # average_daily_transaction_amount = (total_transactions_amount if total_transactions_amount else 0) / \
        #                                    (self.number_of_days_since_first_trans if self.number_of_days_since_first_trans else 1)
        average_daily_transaction_amount_today = (total_transactions_amount_today if total_transactions_amount_today else 0) / 1
        average_daily_transaction_amount_yesterday = (total_transactions_amount_yesterday if total_transactions_amount_yesterday else 0) / 1
        average_daily_transaction_amount_this_week = (total_transactions_amount_this_week if total_transactions_amount_this_week else 0) / 7
        average_daily_transaction_amount_last_week = (total_transactions_amount_last_week if total_transactions_amount_last_week else 0) / 7
        average_daily_transaction_amount_this_month = (total_transactions_amount_this_month if total_transactions_amount_this_month else 0) / 30
        average_daily_transaction_amount_last_month = (total_transactions_amount_last_month if total_transactions_amount_last_month else 0) / 30
        average_daily_transaction_amount_this_year = (total_transactions_amount_this_year if total_transactions_amount_this_year else 0) / \
                                                        (datetime.now()-self.year_start).days if (datetime.now()-self.year_start).days else 1
        average_daily_transaction_amount_last_year = (total_transactions_amount_last_year if total_transactions_amount_last_year else 0) / 365
                                            
        # Float Amount
        float_balance = list(self.wallet_system_qs.aggregate(Sum("available_balance")).values())[0]
        float_balance_12_am = list(self.wallet_system_qs.filter(last_updated__lte=self.midnight_time
                              ).aggregate(Sum("available_balance")).values())[0]
        
        # capital_amount_qs = list(self.ledger_qs.values("user", "cash_available_total").order_by("user", "-date_created").distinct("user"))
        # last_sum_list = [data["cash_available_total"] for data in capital_amount_qs]
        # capital_sum = sum(last_sum_list)

        # New capital sum
        funding_qs = self.ledger_qs.filter(
                                           transaction_type="FUND_BANK_TRANSFER", 
                                           charge_type__exact="MANUAL", 
                                           transaction__source_nuban="**********"
                                           ).filter(transaction__status="SUCCESSFUL", 
                                           transaction__is_reversed=False)
        
        capital_sum = list(funding_qs.aggregate(Sum("amount")).values())[0]

        float_balance_capital_percentage = ((float_balance if float_balance else 0) / (capital_sum if capital_sum else 1)) * 100
        
        float_balance_today = list(self.wallet_system_qs.filter(last_updated__date=timezone.now().date()
                                    ).aggregate(Sum("available_balance")).values())[0]        
        float_balance_yesterday = list(self.wallet_system_qs.filter(last_updated__date=self.previous_day.date()
                                         ).aggregate(Sum("available_balance")).values())[0]        
        float_balance_this_week = list(self.wallet_system_qs.filter(last_updated__gte=self.week_start).aggregate(
                                                                        Sum("available_balance")).values())[0]
        float_balance_this_month = list(self.wallet_system_qs.filter(last_updated__gte=self.month_start).aggregate(
                                                                        Sum("available_balance")).values())[0]
        float_balance_this_year = list(self.wallet_system_qs.filter(last_updated__gte=self.year_start).aggregate(
                                                                        Sum("available_balance")).values())[0]
        float_balance_last_year = list(self.wallet_system_qs.filter(last_updated__gte=self.previous_year_start, 
                                                last_updated__lte=self.previous_year_end).aggregate(Sum("available_balance")).values())[0]
        float_balance_last_week = list(self.wallet_system_qs.filter(last_updated__gte=self.previous_week_start, 
                                                last_updated__lte=self.previous_week_end).aggregate(Sum("available_balance")).values())[0]
        float_balance_last_month = list(self.wallet_system_qs.filter(last_updated__gte=self.previous_month_start, 
                                                last_updated__lte=self.previous_month_end).aggregate(Sum("available_balance")).values())[0]
        
        # Cashout Amount
        total_cashout_amount = list(self.successful_cashout_qs.aggregate(Sum("amount")).values())[0]        
        total_cashout_amount_today = list(self.successful_cashout_qs.filter(date_created__date=timezone.now().date()
                                    ).aggregate(Sum("amount")).values())[0]        
        total_cashout_amount_yesterday = list(self.successful_cashout_qs.filter(date_created__date=self.previous_day.date()
                                         ).aggregate(Sum("amount")).values())[0]        
        total_cashout_amount_this_week = list(self.successful_cashout_qs.filter(date_created__gte=self.week_start).aggregate(
                                                                        Sum("amount")).values())[0]
        total_cashout_amount_this_month = list(self.successful_cashout_qs.filter(date_created__gte=self.month_start).aggregate(
                                                                        Sum("amount")).values())[0]
        total_cashout_amount_this_year = list(self.successful_cashout_qs.filter(date_created__gte=self.year_start).aggregate(
                                                                        Sum("amount")).values())[0]
        total_cashout_amount_last_year = list(self.successful_cashout_qs.filter(date_created__gte=self.previous_year_start, 
                                                date_created__lte=self.previous_year_end).aggregate(Sum("amount")).values())[0]
        total_cashout_amount_last_week = list(self.successful_cashout_qs.filter(date_created__gte=self.previous_week_start, 
                                                date_created__lte=self.previous_week_end).aggregate(Sum("amount")).values())[0]
        total_cashout_amount_last_month = list(self.successful_cashout_qs.filter(date_created__gte=self.previous_month_start, 
                                                date_created__lte=self.previous_month_end).aggregate(Sum("amount")).values())[0]
        
        # Transfer Amount
        total_transfer_amount = list(self.successful_transfer_qs.aggregate(Sum("amount")).values())[0]        
        total_transfer_amount_today = list(self.successful_transfer_qs.filter(date_created__date=timezone.now().date()
                                    ).aggregate(Sum("amount")).values())[0]        
        total_transfer_amount_yesterday = list(self.successful_transfer_qs.filter(date_created__date=self.previous_day.date()
                                         ).aggregate(Sum("amount")).values())[0]        
        total_transfer_amount_this_week = list(self.successful_transfer_qs.filter(date_created__gte=self.week_start).aggregate(
                                                                        Sum("amount")).values())[0]
        total_transfer_amount_this_month = list(self.successful_transfer_qs.filter(date_created__gte=self.month_start).aggregate(
                                                                        Sum("amount")).values())[0]
        total_transfer_amount_this_year = list(self.successful_transfer_qs.filter(date_created__gte=self.year_start).aggregate(
                                                                        Sum("amount")).values())[0]
        total_transfer_amount_last_year = list(self.successful_transfer_qs.filter(date_created__gte=self.previous_year_start, 
                                                date_created__lte=self.previous_year_end).aggregate(Sum("amount")).values())[0]
        total_transfer_amount_last_week = list(self.successful_transfer_qs.filter(date_created__gte=self.previous_week_start, 
                                                date_created__lte=self.previous_week_end).aggregate(Sum("amount")).values())[0]
        total_transfer_amount_last_month = list(self.successful_transfer_qs.filter(date_created__gte=self.previous_month_start, 
                                                date_created__lte=self.previous_month_end).aggregate(Sum("amount")).values())[0]
        
        # INFLOW
        total_inflow_amount = list(self.successful_inflow_qs.aggregate(Sum("amount")).values())[0]        
        total_inflow_amount_today = list(self.successful_inflow_qs.filter(date_created__date=timezone.now().date()
                                    ).aggregate(Sum("amount")).values())[0]        
        total_inflow_amount_yesterday = list(self.successful_inflow_qs.filter(date_created__date=self.previous_day.date()
                                         ).aggregate(Sum("amount")).values())[0]        
        total_inflow_amount_this_week = list(self.successful_inflow_qs.filter(date_created__gte=self.week_start).aggregate(
                                                                        Sum("amount")).values())[0]
        total_inflow_amount_this_month = list(self.successful_inflow_qs.filter(date_created__gte=self.month_start).aggregate(
                                                                        Sum("amount")).values())[0]
        total_inflow_amount_this_year = list(self.successful_inflow_qs.filter(date_created__gte=self.year_start).aggregate(
                                                                        Sum("amount")).values())[0]
        total_inflow_amount_last_year = list(self.successful_inflow_qs.filter(date_created__gte=self.previous_year_start, 
                                                date_created__lte=self.previous_year_end).aggregate(Sum("amount")).values())[0]
        total_inflow_amount_last_week = list(self.successful_inflow_qs.filter(date_created__gte=self.previous_week_start, 
                                                date_created__lte=self.previous_week_end).aggregate(Sum("amount")).values())[0]
        total_inflow_amount_last_month = list(self.successful_inflow_qs.filter(date_created__gte=self.previous_month_start, 
                                                date_created__lte=self.previous_month_end).aggregate(Sum("amount")).values())[0]

        data = {
            "overall_transactions": {
                "amount": total_transactions_amount if total_transactions_amount else 0.00,
                "amount_today": total_transactions_amount_today if total_transactions_amount_today else 0.00,
                "amount_this_week": total_transactions_amount_this_week if total_transactions_amount_this_week else 0.00,
                "amount_this_month": total_transactions_amount_this_month if total_transactions_amount_this_month else 0.00,
                "amount_last_month": total_transactions_amount_last_month if total_transactions_amount_last_month else 0.00,
                "amount_yesterday": total_transactions_amount_yesterday if total_transactions_amount_yesterday else 0.00,
                "amount_last_week": total_transactions_amount_last_week if total_transactions_amount_last_week else 0.00,
                "amount_this_year": total_transactions_amount_this_year if total_transactions_amount_this_year else 0.00,
                "amount_last_year": total_transactions_amount_last_year if total_transactions_amount_last_year else 0.00,
            },
            "float_balance": {
                "overall_amount": float_balance if float_balance else 0.00,
                "12am_balance": float_balance_12_am if float_balance_12_am else 0.00,
                "current_balance": float_balance if float_balance else 0.00,
                "amount_today": float_balance_today if float_balance_today else 0.00,
                "amount_this_week": float_balance_this_week if float_balance_this_week else 0.00,
                "amount_this_month": float_balance_this_month if float_balance_this_month else 0.00,
                "amount_last_month": float_balance_last_month if float_balance_last_month else 0.00,
                "amount_yesterday": float_balance_yesterday if float_balance_yesterday else 0.00,
                "amount_last_week": float_balance_last_week if float_balance_last_week else 0.00,
                "amount_this_year": float_balance_this_year if float_balance_this_year else 0.00,
                "amount_last_year": float_balance_last_year if float_balance_last_year else 0.00,
                "float_balance_capital_percentage": float_balance_capital_percentage,
                "capital_sum": capital_sum if capital_sum else 0.00
            },
            "total_cashout": {
                "overall_amount": total_cashout_amount if total_cashout_amount else 0.00,
                "amount_today": total_cashout_amount_today if total_cashout_amount_today else 0.00,
                "amount_this_week": total_cashout_amount_this_week if total_cashout_amount_this_week else 0.00, 
                "amount_this_month": total_cashout_amount_this_month if total_cashout_amount_this_month else 0.00,
                "amount_last_month": total_cashout_amount_last_month if total_cashout_amount_last_month else 0.00,
                "amount_yesterday": total_cashout_amount_yesterday if total_cashout_amount_yesterday else 0.00,
                "amount_last_week": total_cashout_amount_last_week if total_cashout_amount_last_week else 0.00,
                "amount_this_year": total_cashout_amount_this_year if total_cashout_amount_this_year else 0.00,
                "amount_last_year": total_cashout_amount_last_year if total_cashout_amount_last_year else 0.00
            },
            "total_transfer": {
                "overall_amount": total_transfer_amount if total_transfer_amount else 0.00,
                "amount_today": total_transfer_amount_today if total_transfer_amount_today else 0.00,
                "amount_this_week": total_transfer_amount_this_week if total_transfer_amount_this_week else 0.00, 
                "amount_this_month": total_transfer_amount_this_month if total_transfer_amount_this_month else 0.00,
                "amount_last_month": total_transfer_amount_last_month if total_transfer_amount_last_month else 0.00,
                "amount_yesterday": total_transfer_amount_yesterday if total_transfer_amount_yesterday else 0.00,
                "amount_last_week": total_transfer_amount_last_week if total_transfer_amount_last_week else 0.00,
                "amount_this_year": total_transfer_amount_this_year if total_transfer_amount_this_year else 0.00,
                "amount_last_year": total_transfer_amount_last_year if total_transfer_amount_last_year else 0.00
            },
            "average_transactions_amount": {
                "average_transaction_amount_daily": average_daily_transaction_amount,
                "average_daily_transaction_amount": average_daily_transaction_amount,
                "average_daily_transaction_amount_today": average_daily_transaction_amount_today,
                "average_daily_transaction_amount_yesterday": average_daily_transaction_amount_yesterday,
                "average_daily_transaction_amount_this_week": average_daily_transaction_amount_this_week,
                "average_daily_transaction_amount_last_week": average_daily_transaction_amount_last_week,
                "average_daily_transaction_amount_this_month": average_daily_transaction_amount_this_month,
                "average_daily_transaction_amount_last_month": average_daily_transaction_amount_last_month,
                "average_daily_transaction_amount_this_year": average_daily_transaction_amount_this_year,
                "average_daily_transaction_amount_last_year": average_daily_transaction_amount_last_year,
            },
            "inflow": {
                "overall_amount": total_inflow_amount if total_inflow_amount else 0.00,
                "amount_today": total_inflow_amount_today if total_inflow_amount_today else 0.00,
                "amount_this_week": total_inflow_amount_this_week if total_inflow_amount_this_week else 0.00, 
                "amount_this_month": total_inflow_amount_this_month if total_inflow_amount_this_month else 0.00,
                "amount_last_month": total_inflow_amount_last_month if total_inflow_amount_last_month else 0.00,
                "amount_yesterday": total_inflow_amount_yesterday if total_inflow_amount_yesterday else 0.00,
                "amount_last_week": total_inflow_amount_last_week if total_inflow_amount_last_week else 0.00,
                "amount_this_year": total_inflow_amount_this_year if total_inflow_amount_this_year else 0.00,
                "amount_last_year": total_inflow_amount_last_year if total_inflow_amount_last_year else 0.00
            }
        }
        return data


    def get_transactions_overview_two(self):
        # Inflow Amount        
        total_inflow_count = list(self.successful_inflow_qs.aggregate(Count("amount")).values())[0]        
        total_inflow_count_today = list(self.successful_inflow_qs.filter(date_created__date=timezone.now().date()
                                    ).aggregate(Count("amount")).values())[0]        
        total_inflow_count_yesterday = list(self.successful_inflow_qs.filter(date_created__date=self.previous_day.date()
                                         ).aggregate(Count("amount")).values())[0]        
        total_inflow_count_this_week = list(self.successful_inflow_qs.filter(date_created__gte=self.week_start).aggregate(
                                                                        Count("amount")).values())[0]
        total_inflow_count_this_month = list(self.successful_inflow_qs.filter(date_created__gte=self.month_start).aggregate(
                                                                        Count("amount")).values())[0]
        total_inflow_count_this_year = list(self.successful_inflow_qs.filter(date_created__gte=self.year_start).aggregate(
                                                                        Count("amount")).values())[0]
        total_inflow_count_last_year = list(self.successful_inflow_qs.filter(date_created__gte=self.previous_year_start, 
                                                date_created__lte=self.previous_year_end).aggregate(Count("amount")).values())[0]
        total_inflow_count_last_week = list(self.successful_inflow_qs.filter(date_created__gte=self.previous_week_start, 
                                                date_created__lte=self.previous_week_end).aggregate(Count("amount")).values())[0]
        total_inflow_count_last_month = list(self.successful_inflow_qs.filter(date_created__gte=self.previous_month_start, 
                                                date_created__lte=self.previous_month_end).aggregate(Count("amount")).values())[0]


        # Total Transaction Count
        total_transaction_count = self.successful_transactions_qs.count()
        total_transactions_count_today = list(self.successful_transactions_qs.filter(date_created__date=timezone.now().date()).aggregate(
                                                                        Count("amount")).values())[0]
        total_transactions_count_yesterday = list(self.successful_transactions_qs.filter(date_created__date=self.previous_day.date()).aggregate(
                                                                        Count("amount")).values())[0]
        total_transactions_count_this_week = list(self.successful_transactions_qs.filter(date_created__gte=self.week_start).aggregate(
                                                                        Count("amount")).values())[0]
        total_transactions_count_this_month = list(self.successful_transactions_qs.filter(date_created__gte=self.month_start).aggregate(
                                                                        Count("amount")).values())[0]
        total_transactions_count_this_year = list(self.successful_transactions_qs.filter(date_created__gte=self.year_start).aggregate(
                                                                        Count("amount")).values())[0]
        total_transactions_count_last_year = list(self.successful_transactions_qs.filter(date_created__gte=self.previous_year_start, 
                                                date_created__lte=self.previous_year_end).aggregate(Count("amount")).values())[0]
        total_transactions_count_last_week = list(self.successful_transactions_qs.filter(date_created__gte=self.previous_week_start, 
                                                date_created__lte=self.previous_week_end).aggregate(Count("amount")).values())[0]
        total_transactions_count_last_month = list(self.successful_transactions_qs.filter(date_created__gte=self.previous_month_start, 
                                                date_created__lte=self.previous_month_end).aggregate(Count("amount")).values())[0]
        
        previous_day_transaction_count = list(self.successful_transactions_qs.filter(date_created__date=self.previous_day.date()
                                        ).aggregate(Count("amount")).values())[0]
        total_transaction_count_change = get_percentage_diff(current=total_transactions_count_today, 
                                        previous=previous_day_transaction_count)

        # Average Transaction Count
        average_daily_transaction_count = total_transactions_count_today / (self.number_of_days_since_first_trans if self.number_of_days_since_first_trans else 1)
        average_daily_transaction_count_today = total_transactions_count_today / 1
        average_daily_transaction_count_yesterday = total_transactions_count_yesterday / 1
        average_daily_transaction_count_this_week = total_transactions_count_this_week / 7
        average_daily_transaction_count_last_week = total_transactions_count_last_week / 7
        average_daily_transaction_count_this_month = total_transactions_count_this_month / 30
        average_daily_transaction_count_last_month = total_transactions_count_last_month / 30
        average_daily_transaction_count_this_year = total_transactions_count_this_year / (datetime.now()-self.year_start).days
        average_daily_transaction_count_last_year = total_transactions_count_last_year / 365

        # Failed/Successful Transactions Count
        failed_transactions_count = self.ledger_qs.filter(Q(transaction__status="FAILED") | Q(transaction__is_reversed=True)).count()
        successful_transactions_count = self.successful_transactions_qs.count()
        
        # Cashout Count
        total_cashout_count = list(self.successful_cashout_qs.aggregate(Count("amount")).values())[0]        
        total_cashout_count_today = list(self.successful_cashout_qs.filter(date_created__date=timezone.now().date()
                                    ).aggregate(Count("amount")).values())[0]        
        total_cashout_count_yesterday = list(self.successful_cashout_qs.filter(date_created__date=self.previous_day.date()
                                         ).aggregate(Count("amount")).values())[0]        
        total_cashout_count_this_week = list(self.successful_cashout_qs.filter(date_created__gte=self.week_start).aggregate(
                                                                        Count("amount")).values())[0]
        total_cashout_count_this_month = list(self.successful_cashout_qs.filter(date_created__gte=self.month_start).aggregate(
                                                                        Count("amount")).values())[0]
        total_cashout_count_this_year = list(self.successful_cashout_qs.filter(date_created__gte=self.year_start).aggregate(
                                                                        Count("amount")).values())[0]
        total_cashout_count_last_year = list(self.successful_cashout_qs.filter(date_created__gte=self.previous_year_start, 
                                                date_created__lte=self.previous_year_end).aggregate(Count("amount")).values())[0]
        total_cashout_count_last_week = list(self.successful_cashout_qs.filter(date_created__gte=self.previous_week_start, 
                                                date_created__lte=self.previous_week_end).aggregate(Count("amount")).values())[0]
        total_cashout_count_last_month = list(self.successful_cashout_qs.filter(date_created__gte=self.previous_month_start, 
                                                date_created__lte=self.previous_month_end).aggregate(Count("amount")).values())[0]
   
        previous_day_cashout_count = list(self.successful_cashout_qs.filter(date_created__date=self.previous_day.date()
                                                                            ).aggregate(Count("amount")).values())[0]
        total_cashout_change  = get_percentage_diff(current=total_cashout_count_today, previous=previous_day_cashout_count)

        # Transfer Count
        previous_day_total_transfer_count = list(self.successful_transfer_qs.filter(date_created__date=self.previous_day.date()
                                             ).aggregate(Count("amount")).values())[0]

        total_transfer_count = list(self.successful_transfer_qs.aggregate(Count("amount")).values())[0]        
        total_transfer_count_today = list(self.successful_transfer_qs.filter(date_created__date=timezone.now().date()
                                    ).aggregate(Count("amount")).values())[0]        
        total_transfer_count_yesterday = list(self.successful_transfer_qs.filter(date_created__date=self.previous_day.date()
                                         ).aggregate(Count("amount")).values())[0]        
        total_transfer_count_this_week = list(self.successful_transfer_qs.filter(date_created__gte=self.week_start).aggregate(
                                                                        Count("amount")).values())[0]
        total_transfer_count_this_month = list(self.successful_transfer_qs.filter(date_created__gte=self.month_start).aggregate(
                                                                        Count("amount")).values())[0]
        total_transfer_count_this_year = list(self.successful_transfer_qs.filter(date_created__gte=self.year_start).aggregate(
                                                                        Count("amount")).values())[0]
        total_transfer_count_last_year = list(self.successful_transfer_qs.filter(date_created__gte=self.previous_year_start, 
                                                date_created__lte=self.previous_year_end).aggregate(Count("amount")).values())[0]
        total_transfer_count_last_week = list(self.successful_transfer_qs.filter(date_created__gte=self.previous_week_start, 
                                                date_created__lte=self.previous_week_end).aggregate(Count("amount")).values())[0]
        total_transfer_count_last_month = list(self.successful_transfer_qs.filter(date_created__gte=self.previous_month_start, 
                                                date_created__lte=self.previous_month_end).aggregate(Count("amount")).values())[0]

        total_transfer_change = get_percentage_diff(current=total_transfer_count_today, previous=previous_day_total_transfer_count)

        data = {
            "total_inflow": {
                "overall_count": total_inflow_count,
                "count_today": total_inflow_count_today,
                "count_this_week": total_inflow_count_this_week, 
                "count_this_month": total_inflow_count_this_month,
                "count_last_month": total_inflow_count_last_month,
                "count_yesterday": total_inflow_count_yesterday,
                "count_last_week": total_inflow_count_last_week,
                "count_this_year": total_inflow_count_this_year,
                "count_last_year": total_inflow_count_last_year
            },
            "transfer_count": {
                "overall_count": total_transfer_count,
                "percentage": str(total_transfer_change.get("percentage")),
                "change": total_transfer_change.get("change"),
                "count_today": total_transfer_count_today,
                "count_this_week": total_transfer_count_this_week, 
                "count_this_month": total_transfer_count_this_month,
                "count_last_month": total_transfer_count_last_month,
                "count_yesterday": total_transfer_count_yesterday,
                "count_last_week": total_transfer_count_last_week,
                "count_this_year": total_transfer_count_this_year,
                "count_last_year": total_transfer_count_last_year
            },
             "cashout_count": {
                "overall_count": total_cashout_count,
                "percentage": str(total_cashout_change.get("percentage")),
                "change": total_cashout_change.get("change"),
                "count_today": total_cashout_count_today,
                "count_this_week": total_cashout_count_this_week, 
                "count_this_month": total_cashout_count_this_month,
                "count_last_month": total_cashout_count_last_month,
                "count_yesterday": total_cashout_count_yesterday,
                "count_last_week": total_cashout_count_last_week,
                "count_this_year": total_cashout_count_this_year,
                "count_last_year": total_cashout_count_last_year
            },
             "transaction_count": {
                "overall_count": total_transaction_count,
                "percentage": total_transaction_count_change.get("percentage"),
                "change": total_transaction_count_change.get("change"),
                "failed_transactions": failed_transactions_count,
                "successful_transactions": successful_transactions_count,
                "count_today": total_transactions_count_today,
                "count_this_week": total_transactions_count_this_week, 
                "count_this_month": total_transactions_count_this_month,
                "count_last_month": total_transactions_count_last_month,
                "count_yesterday": total_transactions_count_yesterday,
                "count_last_week": total_transactions_count_last_week,
                "count_this_year": total_transactions_count_this_year,
                "count_last_year": total_transactions_count_last_year
            },
            "average_transactions_count": {
                "average_daily_transaction_count": average_daily_transaction_count,
                "average_daily_transaction_count_today": average_daily_transaction_count_today,
                "average_daily_transaction_count_yesterday": average_daily_transaction_count_yesterday,
                "average_daily_transaction_count_this_week": average_daily_transaction_count_this_week,
                "average_daily_transaction_count_last_week": average_daily_transaction_count_last_week,
                "average_daily_transaction_count_this_month": average_daily_transaction_count_this_month,
                "average_daily_transaction_count_last_month": average_daily_transaction_count_last_month,
                "average_daily_transaction_count_this_year": average_daily_transaction_count_this_year,
                "average_daily_transaction_count_last_year": average_daily_transaction_count_last_year,
            }
            }
        return data
    

    def get_utilities_overview(self):
        airtime_sales_qs = self.bills_utility_airtime_qs.filter(transaction_type = "AIRTIME_PIN")
        bills_sales_qs = self.bills_utility_airtime_qs.filter(transaction_type = "BILLS_AND_PAYMENT")

        # Airtime Sales
        airtime_sales_amount = list(airtime_sales_qs.aggregate(Sum("amount")).values())[0]

        airtime_sales_amount_today = list(airtime_sales_qs.filter(date_created__date=timezone.now().date()).aggregate(
                                                                        Sum("amount")).values())[0]
        airtime_sales_amount_yesterday = list(airtime_sales_qs.filter(date_created__date=self.previous_day.date()).aggregate(
                                                                        Sum("amount")).values())[0]
        airtime_sales_amount_this_week = list(airtime_sales_qs.filter(date_created__gte=self.week_start).aggregate(
                                                                        Sum("amount")).values())[0]
        airtime_sales_amount_this_month = list(airtime_sales_qs.filter(date_created__gte=self.month_start).aggregate(
                                                                        Sum("amount")).values())[0]
        airtime_sales_amount_this_year = list(airtime_sales_qs.filter(date_created__gte=self.year_start).aggregate(
                                                                        Sum("amount")).values())[0]
        airtime_sales_amount_last_year = list(airtime_sales_qs.filter(date_created__gte=self.previous_year_start, 
                                                date_created__lte=self.previous_year_end).aggregate(Sum("amount")).values())[0]
        airtime_sales_amount_last_week = list(airtime_sales_qs.filter(date_created__gte=self.previous_week_start, 
                                                date_created__lte=self.previous_week_end).aggregate(Sum("amount")).values())[0]
        airtime_sales_amount_last_month = list(airtime_sales_qs.filter(date_created__gte=self.previous_month_start, 
                                                date_created__lte=self.previous_month_end).aggregate(Sum("amount")).values())[0]
        
        airtime_sales_change = get_percentage_diff(current=airtime_sales_amount_today, previous=airtime_sales_amount_yesterday)

        # Bills Sales
        bills_sales_amount = list(bills_sales_qs.aggregate(Sum("amount")).values())[0]
        bills_sales_amount_today = list(bills_sales_qs.filter(date_created__date=timezone.now().date()).aggregate(
                                                                        Sum("amount")).values())[0]
        bills_sales_amount_yesterday = list(bills_sales_qs.filter(date_created__date=self.previous_day.date()).aggregate(
                                                                        Sum("amount")).values())[0]
        bills_sales_amount_this_week = list(bills_sales_qs.filter(date_created__gte=self.week_start).aggregate(
                                                                        Sum("amount")).values())[0]
        bills_sales_amount_this_month = list(bills_sales_qs.filter(date_created__gte=self.month_start).aggregate(
                                                                        Sum("amount")).values())[0]
        bills_sales_amount_this_year = list(bills_sales_qs.filter(date_created__gte=self.year_start).aggregate(
                                                                        Sum("amount")).values())[0]
        bills_sales_amount_last_year = list(bills_sales_qs.filter(date_created__gte=self.previous_year_start, 
                                                date_created__lte=self.previous_year_end).aggregate(Sum("amount")).values())[0]
        bills_sales_amount_last_week = list(bills_sales_qs.filter(date_created__gte=self.previous_week_start, 
                                                date_created__lte=self.previous_week_end).aggregate(Sum("amount")).values())[0]
        bills_sales_amount_last_month = list(bills_sales_qs.filter(date_created__gte=self.previous_month_start, 
                                                date_created__lte=self.previous_month_end).aggregate(Sum("amount")).values())[0]
        
        previous_month_bills_sales_amount = list(bills_sales_qs.filter(date_created__lte=self.previous_month_end
                                            ).aggregate(Sum("amount")).values())[0]
        bills_sales_change = get_percentage_diff(current=bills_sales_amount, previous=bills_sales_amount_yesterday)

        data = { 
            "airtime_sales": {
                "amount": airtime_sales_amount if airtime_sales_amount else 0.00,
                "percentage": airtime_sales_change.get("percentage"),
                "change": airtime_sales_change.get("change"),
                "amount_today": airtime_sales_amount_today if airtime_sales_amount_today else 0.00,
                "amount_this_week": airtime_sales_amount_this_week if airtime_sales_amount_this_week else 0.00, 
                "amount_this_month": airtime_sales_amount_this_month if airtime_sales_amount_this_month else 0.00,
                "amount_last_month": airtime_sales_amount_last_month if airtime_sales_amount_last_month else 0.00,
                "amount_yesterday": airtime_sales_amount_yesterday if airtime_sales_amount_yesterday else 0.00,
                "amount_last_week": airtime_sales_amount_last_week if airtime_sales_amount_last_week else 0.00,
                "amount_this_year": airtime_sales_amount_this_year if airtime_sales_amount_this_year else 0.00,
                "amount_last_year": airtime_sales_amount_last_year if airtime_sales_amount_last_year else 0.00
                },
            "data_sales": {
                "amount": 0,
                "percentage": 0,
                "change": "no change",
                "amount_today": 0.00,
                "amount_this_week": 0.00, 
                "amount_this_month": 0.00,
                "amount_last_month": 0.00,
                "amount_yesterday": 0.00,
                "amount_last_week": 0.00,
                "amount_this_year": 0.00,
                "amount_last_year": 0.00
                
                },
            "bills_sales": {
                "amount": bills_sales_amount if bills_sales_amount else 0.00,
                "percentage": bills_sales_change.get("percentage"),
                "change": bills_sales_change.get("change"),

                "amount_today": bills_sales_amount_today if bills_sales_amount_today else 0.00,
                "amount_this_week": bills_sales_amount_this_week if bills_sales_amount_this_week else 0.00, 
                "amount_this_month": bills_sales_amount_this_month if bills_sales_amount_this_month else 0.00,
                "amount_last_month": bills_sales_amount_last_month if bills_sales_amount_last_month else 0.00,
                "amount_yesterday": bills_sales_amount_yesterday if bills_sales_amount_yesterday else 0.00,
                "amount_last_week": bills_sales_amount_last_week if bills_sales_amount_last_week else 0.00,
                "amount_this_year": bills_sales_amount_this_year if bills_sales_amount_this_year else 0.00,
                "amount_last_year": bills_sales_amount_last_year if bills_sales_amount_last_year else 0.00
                },
            "cable_tv_sales": {
                "amount": 0,
                "percentage": 0,
                "change": "no change",
                "amount_today": 0.00,
                "amount_this_week": 0.00, 
                "amount_this_month": 0.00,
                "amount_last_month": 0.00,
                "amount_yesterday": 0.00,
                "amount_last_week": 0.00,
                "amount_this_year": 0.00,
                "amount_last_year": 0.00
                }
        }
        return data
    
    def get_agents_overview(self):
        agents_count = self.retail_system_qs.count()
        active_agents_qs = self.retail_system_qs.filter(retail_account__isnull=False, 
                            retail_account__terminal_status="ACTIVE")
        inactive_agents_qs = self.retail_system_qs.filter(retail_account__isnull=False, 
                            retail_account__terminal_status="INACTIVE")
        partially_inactive_agents_qs = self.retail_system_qs.filter(retail_account__isnull=False, 
                                        retail_account__terminal_status="PARTIALLY_INACTIVE")
        suspended_agents_qs = self.retail_system_qs.filter(retail_account__isnull=False).filter(
                            Q(retail_account__is_suspended=True) | Q(retail_account__terminal_suspended=True))

        active_agents_count = active_agents_qs.count()
        inactive_agents_count = inactive_agents_qs.count()
        partially_inactive_agents_count = partially_inactive_agents_qs.count()
        suspended_agents_count = suspended_agents_qs.count()

        data = {
            "agents_count": agents_count,
            "active_agents_count": active_agents_count,
            "inactive_agents_count": inactive_agents_count + partially_inactive_agents_count,
            "partially_inactive_agents_count": partially_inactive_agents_count,
            "suspended_agents_count": suspended_agents_count
        }
        return data

    def get_revenue_overview(self):
        revenue_qs = self.successful_transactions_qs.filter(~Q(charge__lte=0)
                    ).annotate(actual_charge = F("charge") - F("liberty_commission"))
        total_revenue_count = list(revenue_qs.aggregate(Count("actual_charge")).values())[0]

        # Expected Revenue vs Actual Revenue Fix
        total_retail_auto_debits = list(Transaction.objects.filter(transaction_type="RETAIL_AUTO_DEBIT_CR", 
                                   status="SUCCESSFUL").aggregate(Sum("amount")).values())[0]
        total_expected_revenue_today = list(self.successful_transactions_qs.filter(date_created__date=timezone.now().date()
                                       ).annotate(actual_charge_sum=F("charge_sum")-F("liberty_commission_sum")
                                        ).aggregate(Sum("actual_charge_sum")).values())[0]
        overall_total_expected_revenue = ((total_retail_auto_debits if total_retail_auto_debits else 0) + 
                                  (total_expected_revenue_today if total_expected_revenue_today else 0)
                                  )

  
        last_charge_sum_qs = self.ledger_qs.values("user", "charge_sum", "liberty_commission_sum"
                            ).order_by("user", "-date_created").distinct("user")
        last_charge_sum_list = [data["charge_sum"]-data["liberty_commission_sum"] for data in last_charge_sum_qs]
        current_expected_last_charge_sum = sum(last_charge_sum_list)       
   
        # Revenue Amount
        total_revenue_amount = list(revenue_qs.aggregate(Sum("actual_charge")).values())[0]
        
        total_revenue_amount_today = list(revenue_qs.filter(
                                date_created__date=timezone.now().date()
                                ).aggregate(Sum("actual_charge")).values())[0]
        
        total_revenue_amount_yesterday = list(revenue_qs.filter(
                                date_created__date = self.previous_day.date()
                                ).aggregate(Sum("actual_charge")).values())[0]      
                
        total_revenue_amount_this_week = list(revenue_qs.filter(
                                date_created__gte = self.week_start
                                ).aggregate(Sum("actual_charge")).values())[0]     
        
        total_revenue_amount_this_month = list(revenue_qs.filter(
                                date_created__gte = self.month_start
                                ).aggregate(Sum("actual_charge")).values())[0]    
        
        total_revenue_amount_this_year = list(revenue_qs.filter( 
                                date_created__gte=self.year_start
                                ).aggregate(Sum("actual_charge")).values())[0]       
        
        total_revenue_amount_last_year = list(revenue_qs.filter(
                                date_created__gte=self.previous_year_start, 
                                date_created__lte=self.previous_year_end
                                ).aggregate(Sum("actual_charge")).values())[0]     
        
        total_revenue_amount_last_week = list(revenue_qs.filter(
                                date_created__gte=self.previous_week_start, 
                                date_created__lte=self.previous_week_end
                                ).aggregate(Sum("actual_charge")).values())[0]      
        
        total_revenue_amount_last_month = list(revenue_qs.filter(
                                date_created__gte=self.previous_month_start, 
                                date_created__lte=self.previous_month_end
                                ).aggregate(Sum("actual_charge")).values())[0]    
        
        # Revenue Count
        total_revenue_count = list(revenue_qs.aggregate(Count("actual_charge")).values())[0]
        total_revenue_count_today = list(revenue_qs.filter(
                                    date_created__date=timezone.now().date()
                                    ).aggregate(Count("actual_charge")).values())[0]        
        total_revenue_count_yesterday = list(revenue_qs.filter(
                                        date_created__date=self.previous_day.date()
                                         ).aggregate(Count("actual_charge")).values())[0]        
        total_revenue_count_this_week = list(revenue_qs.filter(
                                        date_created__gte=self.week_start).aggregate(
                                        Count("actual_charge")).values())[0]
        total_revenue_count_this_month = list(revenue_qs.filter(
                                        date_created__gte=self.month_start).aggregate(
                                        Count("actual_charge")).values())[0]
        total_revenue_count_this_year = list(revenue_qs.filter(
                                        date_created__gte=self.year_start).aggregate(
                                        Count("actual_charge")).values())[0]
        total_revenue_count_last_year = list(revenue_qs.filter(
                                        date_created__gte=self.previous_year_start, 
                                        date_created__lte=self.previous_year_end
                                        ).aggregate(Count("actual_charge")).values())[0]
        total_revenue_count_last_week = list(revenue_qs.filter(
                                        date_created__gte=self.previous_week_start, 
                                        date_created__lte=self.previous_week_end
                                        ).aggregate(Count("actual_charge")).values())[0]
        total_revenue_count_last_month = list(revenue_qs.filter(
                                        date_created__gte=self.previous_month_start, 
                                        date_created__lte=self.previous_month_end
                                        ).aggregate(Count("actual_charge")).values())[0]
        
        # Revenue Target Amount
        daily_target_amount = 3000
        overall_revenue_target_amount = (daily_target_amount * 
                                         self.numbers_day_since_retail_start_less_weekends * 
                                         self.number_of_retail_agents_count)
        total_revenue_target_amount_today = daily_target_amount * self.number_of_retail_agents_count      
        total_revenue_target_amount_yesterday = daily_target_amount * self.number_of_retail_agents_till_yesterday
        total_revenue_target_amount_this_week = total_revenue_target_amount_today * 6
        total_revenue_target_amount_this_month = total_revenue_target_amount_today * 24
        total_revenue_target_amount_this_year = total_revenue_target_amount_today * 24 * 12
        total_revenue_target_amount_last_year = daily_target_amount * self.number_of_retail_agents_till_last_year * 24 * 12
        total_revenue_target_amount_last_week = daily_target_amount * self.number_of_retail_agents_till_last_week * 6
        total_revenue_target_amount_last_month = daily_target_amount * self.number_of_retail_agents_till_last_month * 24
        
        # Revenue Target Count
        daily_target_count = 13
        overall_revenue_target_count = (daily_target_count * 
                                         self.numbers_day_since_retail_start_less_weekends * 
                                         self.number_of_retail_agents_count)
        total_revenue_target_count_today = daily_target_count * self.number_of_retail_agents_count      
        total_revenue_target_count_yesterday = daily_target_count * self.number_of_retail_agents_till_yesterday
        total_revenue_target_count_this_week = total_revenue_target_count_today * 6
        total_revenue_target_count_this_month = total_revenue_target_count_today * 24
        total_revenue_target_count_this_year = total_revenue_target_count_today * 24 * 12
        total_revenue_target_count_last_year = daily_target_count * self.number_of_retail_agents_till_last_year * 24 * 12
        total_revenue_target_count_last_week = daily_target_count * self.number_of_retail_agents_till_last_week * 6
        total_revenue_target_count_last_month = daily_target_count * self.number_of_retail_agents_till_last_month * 24
        
        average_daily_revenue_amount = (
                                 (total_revenue_amount if total_revenue_amount else 0.00) / 
                                 (self.number_of_days_since_first_trans if self.number_of_days_since_first_trans else 1)
                                 )
        average_daily_revenue_count = (
                                 total_revenue_count / 
                                 (self.number_of_days_since_first_trans if self.number_of_days_since_first_trans else 1)
                                 )
        
        # Expected Salary        
        expected_salary_qs = self.expected_salary_qs.filter(
                            date_created__gte=DateUtility().month_start
                            ).order_by("retail_account", "-date_created").distinct(
                                "retail_account"
                            ).values("expected_salary")
        
        expected_salary_amount = sum([agent["expected_salary"] for agent in expected_salary_qs])
    
        data = {
            "revenue_amount": {
                "total_amount": total_revenue_amount if total_revenue_amount else 0.00,
                "average_daily_amount": average_daily_revenue_amount,

                "amount_today": total_revenue_amount_today if total_revenue_amount_today else 0.00,
                "amount_this_week": total_revenue_amount_this_week if total_revenue_amount_this_week else 0.00, 
                "amount_this_month": total_revenue_amount_this_month if total_revenue_amount_this_month else 0.00,
                "amount_last_month": total_revenue_amount_last_month if total_revenue_amount_last_month else 0.00,
                "amount_yesterday": total_revenue_amount_yesterday if total_revenue_amount_yesterday else 0.00,
                "amount_last_week": total_revenue_amount_last_week if total_revenue_amount_last_week else 0.00,
                "amount_this_year": total_revenue_amount_this_year if total_revenue_amount_this_year else 0.00,
                "amount_last_year": total_revenue_amount_last_year if total_revenue_amount_last_year else 0.00,
                "current_expected_last_charge_sum": current_expected_last_charge_sum if current_expected_last_charge_sum else 0.00,
                "overall_total_expected_revenue": overall_total_expected_revenue if overall_total_expected_revenue else overall_total_expected_revenue
            },
            "revenue_count": {
                "total_count": total_revenue_count,
                "average_daily_count": average_daily_revenue_count,
                "count_today": total_revenue_count_today,
                "count_this_week": total_revenue_count_this_week, 
                "count_this_month": total_revenue_count_this_month,
                "count_last_month": total_revenue_count_last_month,
                "count_yesterday": total_revenue_count_yesterday,
                "count_last_week": total_revenue_count_last_week,
                "count_this_year": total_revenue_count_this_year,
                "count_last_year": total_revenue_count_last_year
            },
            "target_revenue_amount":{
                "overall_amount": overall_revenue_target_amount,
                "amount_today": total_revenue_target_amount_today if total_revenue_target_amount_today else 0.00,
                "amount_this_week": total_revenue_target_amount_this_week if total_revenue_target_amount_this_week else 0.00, 
                "amount_this_month": total_revenue_target_amount_this_month if total_revenue_target_amount_this_month else 0.00,
                "amount_last_month": total_revenue_target_amount_last_month if total_revenue_target_amount_last_month else 0.00,
                "amount_yesterday": total_revenue_target_amount_yesterday if total_revenue_target_amount_yesterday else 0.00,
                "amount_last_week": total_revenue_target_amount_last_week if total_revenue_target_amount_last_week else 0.00,
                "amount_this_year": total_revenue_target_amount_this_year if total_revenue_target_amount_this_year else 0.00,
                "amount_last_year": total_revenue_target_amount_last_year if total_revenue_target_amount_last_year else 0.00
            },
            "target_revenue_count": {
                "total_count": overall_revenue_target_count,
                "count_today": total_revenue_target_count_today,
                "count_this_week": total_revenue_target_count_this_week, 
                "count_this_month": total_revenue_target_count_this_month,
                "count_last_month": total_revenue_target_count_last_month,
                "count_yesterday": total_revenue_target_count_yesterday,
                "count_last_week": total_revenue_target_count_last_week,
                "count_this_year": total_revenue_target_count_this_year,
                "count_last_year": total_revenue_target_count_last_year
            },
            "average_daily_amount": {
                "average_daily_revenue_amount": average_daily_revenue_amount
            },
            "expected_salary_amount":{
                "overall_amount": expected_salary_amount if expected_salary_amount else 0.00
            }
        }
        return data


class MainDashboardCharts:
    def __init__(self, request):
        self._month_2_month_dates_list = DateUtility()._month_2_month_dates_list
        self.days_label = DateUtility().days_label
        self.months_list_names = DateUtility().months_list_names
        self.year_months_tuple_list = DateUtility().year_months_tuple_list
        self._previous_week_current_week_date = DateUtility()._previous_week_current_week_date
        self._previous_month_current_month_date = DateUtility()._previous_month_current_month_date
        self.previous_month_end = DateUtility().previous_month_end
        self.previous_month_start = DateUtility().previous_month_start
        self.previous_year_current_month_start = DateUtility().previous_year_current_month_start
        self.previous_week_end = DateUtility().previous_week_end
        self.previous_week_start = DateUtility().previous_week_start

        self.filter = request.GET.get("filter")

        self.retail_system_qs = RetailSystem.objects.filter(~Q(retail_account__email__in=retail_system_email_exclude_list)
                                                            & Q(retail_account__in=funded_retail_accounts)
                                                            )
        retail_system_users = [system.retail_account for system in self.retail_system_qs]    
        self.ledger_qs = LedgerTableModel.objects.filter(
                        Q(user__in=retail_system_users,
                        transaction_type__in=all_possible_transaction_list) &
                        Q(user__in=funded_retail_accounts)
                        )
        self.cashout_qs = self.ledger_qs.filter(transaction_type__in=cashout_transaction_type_list)
        self.transfer_qs = self.ledger_qs.filter(transaction_type__in=transfer_transaction_type_list)
        self.successful_cashout_qs = self.cashout_qs.filter(transaction__status="SUCCESSFUL", transaction__is_reversed=False)
        self.successful_transfer_qs = self.transfer_qs.filter(transaction__status="SUCCESSFUL", transaction__is_reversed=False)
        self.successful_transactions_qs = self.ledger_qs.filter(transaction__status="SUCCESSFUL", transaction__is_reversed=False)


    def get_charts(self):
        total_transactions_amount = list(self.successful_transactions_qs.aggregate(Sum("amount")).values())[0]
        previous_month_total_transactions_amount = list(self.successful_transactions_qs.filter(
                                                    date_created__lte=self.previous_month_end
                                                    ).aggregate(Sum("amount")).values())[0]
        total_transactions_amount_change = get_percentage_diff(current=total_transactions_amount, 
                                            previous=previous_month_total_transactions_amount)
        

        past_year_transactions_amt_qs = self.successful_transactions_qs.filter(
                                date_created__gte=self.previous_year_current_month_start
                                ).values("date_created__year", "date_created__month"
                                    ).annotate(total_amount = Sum("amount")                                                           
                                ).order_by("date_created__year")
        past_year_transactions_list = [
                                        (data["date_created__year"], data["date_created__month"], 
                                         data["total_amount"]) for 
                                        data in past_year_transactions_amt_qs
                                       ]
        
        # Transactions
        past_year_transactions_final_list = []
        for item in self.year_months_tuple_list:
            if item in [(tup[0], tup[1]) for tup in past_year_transactions_list]:
                for trans in past_year_transactions_list:
                    if item == (trans[0], trans[1]):
                        past_year_transactions_final_list.append(trans)
                        break
            else:
                past_year_transactions_final_list.append((0, 0, 0))
        transacions_amount_values_list = [trans[2] for trans in past_year_transactions_final_list]

        # Withdrawals Values List
        total_cashout_amount = list(self.successful_cashout_qs.aggregate(Sum("amount")).values())[0]
        previous_month_cashout_amount = list(self.ledger_qs.filter(date_created__lte=self.previous_month_end
                                                    ).aggregate(Sum("amount")).values())[0]
        cashout_amount_change = get_percentage_diff(current=total_cashout_amount, 
                                            previous=previous_month_cashout_amount)
        
        past_year_cashout_amt_qs = self.successful_cashout_qs.filter(
                                date_created__gte=self.previous_year_current_month_start
                                ).values("date_created__year", "date_created__month"
                                    ).annotate(total_amount = Sum("amount")                                                              
                                ).order_by("date_created__year")
        
        past_year_cashout_list = [
                                        (data["date_created__year"], data["date_created__month"], 
                                         data["total_amount"]) for 
                                        data in past_year_cashout_amt_qs
                                       ]
        
        past_year_cashout_final_list = []
        for item in self.year_months_tuple_list:
            if item in [(tup[0], tup[1]) for tup in past_year_cashout_list]:
                for trans in past_year_cashout_list:
                    if item == (trans[0], trans[1]):
                        past_year_cashout_final_list.append(trans)
                        break
            else:
                past_year_cashout_final_list.append((0, 0, 0))
        cashout_amount_values_list = [trans[2] for trans in past_year_cashout_final_list]

        # Transfer Values List
        total_transfer_amount = list(self.successful_transfer_qs.aggregate(Sum("amount")).values())[0]
        previous_month_transfer_amount = list(self.successful_transfer_qs.filter(
                                            date_created__lte=self.previous_month_end
                                        ).aggregate(Sum("amount")).values())[0]
        
        transfer_amount_change = get_percentage_diff(current=total_transfer_amount, 
                                                    previous=previous_month_transfer_amount
                                                    )
        
        past_year_transfer_amt_qs = self.successful_transfer_qs.filter(
                                date_created__gte=self.previous_year_current_month_start
                                ).values("date_created__year", "date_created__month"
                                    ).annotate(total_amount = Sum("amount")                                                                
                                ).order_by("date_created__year")
        
        past_year_transfer_list = [
                                    (data["date_created__year"], data["date_created__month"], 
                                    data["total_amount"]) for 
                                    data in past_year_transfer_amt_qs
                                    ]
        
        past_year_transfer_final_list = []
        for item in self.year_months_tuple_list:
            if item in [(tup[0], tup[1]) for tup in past_year_transfer_list]:
                for trans in past_year_transfer_list:
                    if item == (trans[0], trans[1]):
                        past_year_transfer_final_list.append(trans)
                        break
            else:
                past_year_transfer_final_list.append((0, 0, 0))
        transfer_amount_values_list = [trans[2] for trans in past_year_transfer_final_list]
        
        # Revenue Values List
        revenue_qs = self.successful_transactions_qs.filter(~Q(charge__lte=0)
                     ).annotate(actual_charge = F("charge") - F("liberty_commission"))
        total_revenue_amount = list(revenue_qs.aggregate(Sum("actual_charge")).values())[0]
        previous_month_revenue_amount = list(revenue_qs.filter(date_created__lte = self.previous_month_end
                                                    ).aggregate(Sum("actual_charge")).values())[0]
        revenue_amount_change = get_percentage_diff(current = total_revenue_amount, 
                                            previous = previous_month_revenue_amount)
        
        past_year_revenue_amt_qs = revenue_qs.filter(date_created__gte=self.previous_year_current_month_start
                                ).values("date_created__year", "date_created__month"
                                ).annotate(total_revenue = Sum("actual_charge")                                                                
                                ).order_by("date_created__year")
        
        past_year_revenue_list = [
                                (data["date_created__year"], data["date_created__month"], 
                                data["total_revenue"]) for 
                                data in past_year_revenue_amt_qs
                                ]
        
        past_year_revenue_final_list = []
        for item in self.year_months_tuple_list:
            if item in [(tup[0], tup[1]) for tup in past_year_revenue_list]:
                for trans in past_year_revenue_list:
                    if item == (trans[0], trans[1]):
                        past_year_revenue_final_list.append(trans)
                        break
            else:
                past_year_revenue_final_list.append((0, 0, 0))
        revenue_amount_values_list = [trans[2] for trans in past_year_revenue_final_list]

        
        # Past One Week Transactions Charts
        """
        This section provides data for transactions through the past 8 days.
        Let us say today is Thursday it would provide a list a daily transactions amount sums
        from last week's Thursday till today.
        """
        # Sales/Transactions Amount
        past_one_week_transactions_qs = self.successful_transactions_qs.filter(date_created__date__gte =
                                        self._previous_week_current_week_date).values("date_created__date"
                                        ).annotate(transaction_amount = Sum("amount"))
        total_transactions_amount_past_week = list(past_one_week_transactions_qs.aggregate(Sum("transaction_amount")).values())[0]
        last_week_transactions_amount = list(self.successful_transactions_qs.filter(date_created__date__gte=self.previous_week_start,
                                    date_created__date__lte=self.previous_week_end).aggregate(Sum("amount")).values())[0]
        
        week_days_transactions_change = get_percentage_diff(
                                        current = total_transactions_amount_past_week, 
                                        previous = last_week_transactions_amount
                                        )
        
        try:
            if past_one_week_transactions_qs:
                week_days_transactions_list = [transaction["transaction_amount"] for transaction in past_one_week_transactions_qs]
            else:
                week_days_transactions_list = [0] * 8
        except Exception as e:
            week_days_transactions_list = [0] * 8

        # Withdrawals
        past_one_week_withdrawal_qs = self.successful_cashout_qs.filter(date_created__date__gte = self._previous_week_current_week_date
                                        ).values("date_created__date").annotate(transaction_amount = Sum("amount"))
        
        total_withdrawal_amount_past_week = list(past_one_week_withdrawal_qs.aggregate(Sum("transaction_amount")).values())[0]
        last_week_withdrawal_amount = list(self.successful_cashout_qs.filter(date_created__date__gte = self.previous_week_start,
                                    date_created__date__lte = self.previous_week_end).aggregate(Sum("amount")).values())[0]
        
        try:
            if past_one_week_withdrawal_qs:
                week_days_withdrawal_list = [transaction["transaction_amount"] for transaction in past_one_week_withdrawal_qs]
            else:
                week_days_withdrawal_list = [0] * 8
        except Exception as e:
            week_days_withdrawal_list = [0] * 8

        week_days_withdrawal_change = get_percentage_diff(
                                      current = total_withdrawal_amount_past_week, 
                                      previous = last_week_withdrawal_amount
                                      )
        
        # Transfers
        past_one_week_transfer_qs = self.successful_transfer_qs.filter(date_created__date__gte=self._previous_week_current_week_date
                                        ).values("date_created__date").annotate(transaction_amount=Sum("amount"))
        
        total_transfer_amount_past_week = list(past_one_week_transfer_qs.aggregate(Sum("transaction_amount")).values())[0]
        last_week_transfer_amount = list(self.successful_transfer_qs.filter(date_created__date__gte=self.previous_week_start,
                                    date_created__date__lte=self.previous_week_end).aggregate(Sum("amount")).values())[0]
        
        try:
            if past_one_week_transfer_qs:
                week_days_transfer_list = [transaction["transaction_amount"] for transaction in past_one_week_transfer_qs]
            else:
                week_days_transfer_list = [0] * 8
        except Exception as e:
            week_days_transfer_list = [0] * 8

        week_days_transfer_change = get_percentage_diff(
                                      current = total_transfer_amount_past_week, 
                                      previous = last_week_transfer_amount
                                      )
        
        # Revenue
        past_one_week_revenue_qs = revenue_qs.filter(date_created__date__gte = self._previous_week_current_week_date
                                        ).values("date_created__date").annotate(revenue_amount=Sum("actual_charge"))
        
        total_revenue_amount_past_week = list(past_one_week_revenue_qs.aggregate(Sum("revenue_amount")).values())[0]
        last_week_revenue_amount = list(revenue_qs.filter(date_created__date__gte=self.previous_week_start,
                                    date_created__date__lte=self.previous_week_end).aggregate(Sum("actual_charge")).values())[0]
        
        try:
            if past_one_week_revenue_qs:
                week_days_revenue_list = [transaction["revenue_amount"] for transaction in past_one_week_revenue_qs]
            else:
                week_days_revenue_list = [0] * 8
        except Exception as e:
            week_days_revenue_list = [0] * 8

        week_days_revenue_change = get_percentage_diff(
                                      current = total_revenue_amount_past_week, 
                                      previous = last_week_revenue_amount
                                      )

        # Past One Month Transactions Charts
        """
        This section provides data for transactions through the past 30/31/28/29 days.
        Let us say today is July 3rd it would provide a list of daily transactions amount sums
        from last months's July 3rd till today.
        """
        # Sales/Transactions Amount
        past_one_month_transactions_qs = self.successful_transactions_qs.filter(date_created__date__gte=
                                        self._previous_month_current_month_date).values("date_created__date"
                                        ).annotate(transaction_amount = Sum("amount")).values("transaction_amount")
        total_transactions_amount_past_month = list(past_one_month_transactions_qs.aggregate(Sum("transaction_amount")).values())[0]
        last_month_transactions_amount = list(self.successful_transactions_qs.filter(date_created__date__gte=self.previous_month_start,
                                    date_created__date__lte=self.previous_month_end).aggregate(Sum("amount")).values())[0]
        
        month_days_transactions_change = get_percentage_diff(
                                        current = total_transactions_amount_past_month, 
                                        previous = last_month_transactions_amount
                                        )
        
        try:
            if past_one_month_transactions_qs:
                month_days_transactions_list = [transaction["transaction_amount"] for transaction in past_one_month_transactions_qs]
            else:
                month_days_transactions_list = [0] * 8
        except Exception as e:
            month_days_transactions_list = [0] * 8

        # Withdrawals
        past_one_month_withdrawal_qs = self.successful_cashout_qs.filter(date_created__date__gte=self._previous_month_current_month_date
                                        ).values("date_created__date").annotate(transaction_amount=Sum("amount"))
        
        total_withdrawal_amount_past_month = list(past_one_month_withdrawal_qs.aggregate(Sum("transaction_amount")).values())[0]
        last_month_withdrawal_amount = list(self.successful_cashout_qs.filter(date_created__date__gte=self.previous_month_start,
                                    date_created__date__lte = self.previous_month_end).aggregate(Sum("amount")).values())[0]
        
        try:
            if past_one_month_withdrawal_qs:
                month_days_withdrawal_list = [transaction["transaction_amount"] for transaction in past_one_month_withdrawal_qs]
            else:
                month_days_withdrawal_list = [0] * 8
        except Exception as e:
            month_days_withdrawal_list = [0] * 8

        month_days_withdrawal_change = get_percentage_diff(
                                      current = total_withdrawal_amount_past_month, 
                                      previous = last_month_withdrawal_amount
                                      )
        
        # Transfers
        past_one_month_transfer_qs = self.successful_transfer_qs.filter(date_created__date__gte = self._previous_month_current_month_date
                                        ).values("date_created__date").annotate(transaction_amount=Sum("amount"))
        
        total_transfer_amount_past_month = list(past_one_month_transfer_qs.aggregate(Sum("transaction_amount")).values())[0]
        last_month_transfer_amount = list(self.successful_transfer_qs.filter(date_created__date__gte=self.previous_month_start,
                                    date_created__date__lte = self.previous_month_end).aggregate(Sum("amount")).values())[0]
        
        try:
            if past_one_month_transfer_qs:
                month_days_transfer_list = [transaction["transaction_amount"] for transaction in past_one_month_transfer_qs]
            else:
                month_days_transfer_list = [0] * 8
        except Exception as e:
            month_days_transfer_list = [0] * 8

        month_days_transfer_change = get_percentage_diff(
                                      current=total_transfer_amount_past_month, 
                                      previous=last_month_transfer_amount
                                      )
        
        # Revenue
        past_one_month_revenue_qs = revenue_qs.filter(date_created__date__gte=self._previous_month_current_month_date
                                        ).values("date_created__date").annotate(revenue_amount=Sum("actual_charge"))
        
        total_revenue_amount_past_month = list(past_one_month_revenue_qs.aggregate(Sum("revenue_amount")).values())[0]
        last_month_revenue_amount = list(revenue_qs.filter(date_created__date__gte=self.previous_month_start,
                                    date_created__date__lte=self.previous_month_end).aggregate(Sum("actual_charge")).values())[0]
        
        try:
            if past_one_month_revenue_qs:
                month_days_revenue_list = [transaction["revenue_amount"] for transaction in past_one_month_revenue_qs]
            else:
                month_days_revenue_list = [0] * 8
        except Exception as e:
            month_days_revenue_list = [0] * 8

        month_days_revenue_change = get_percentage_diff(
                                    current = total_revenue_amount_past_month, 
                                    previous = last_month_revenue_amount
                                    )

        chart_data = {
            "months_list": self.days_label if self.filter == "weekly" else self._month_2_month_dates_list if \
                            self.filter == "monthly" else self.months_list_names,
            "sales": {
                "total_sales": total_transactions_amount_past_week if total_transactions_amount_past_week else 0.00,
                "sales_chart_values": week_days_transactions_list,
                "percentage": week_days_transactions_change.get("percentage"),
                "change": week_days_transactions_change.get("change")
                } if self.filter == "weekly" 
                else {
                        "total_sales": total_transactions_amount_past_month if total_transactions_amount_past_month else 0.00,
                        "sales_chart_values": month_days_transactions_list,
                        "percentage": month_days_transactions_change.get("percentage"),
                        "change": month_days_transactions_change.get("change")
                    } if self.filter == "monthly" 
                else {
                        "total_sales": total_transactions_amount if total_transactions_amount else 0.00,
                        "sales_chart_values": transacions_amount_values_list,
                        "percentage": total_transactions_amount_change.get("percentage"),
                        "change": total_transactions_amount_change.get("change")
                    },
            "withdrawals": {
                "total_withdrawals": total_withdrawal_amount_past_week if total_withdrawal_amount_past_week else 0.00,
                "withdrawals_chart_values": week_days_withdrawal_list,
                "percentage": week_days_withdrawal_change.get("percentage"),
                "change": week_days_withdrawal_change.get("change")
            } if self.filter == "weekly" 
                else {
                    "total_withdrawals": total_withdrawal_amount_past_month if total_withdrawal_amount_past_month else 0.00,
                    "withdrawals_chart_values": month_days_withdrawal_list,
                    "percentage": cashout_amount_change.get("percentage"),
                    "change": cashout_amount_change.get("change")
                } if self.filter == "monthly" 
                    else {
                    "total_withdrawals": total_cashout_amount if total_cashout_amount else 0.00,
                    "withdrawals_chart_values": cashout_amount_values_list,
                    "percentage": month_days_withdrawal_change.get("percentage"),
                    "change": month_days_withdrawal_change.get("change")
                },
            "transfers": {
                "total_transfers": total_transfer_amount_past_week if total_transfer_amount_past_week else 0.00,
                "transfers_chart_values": week_days_transfer_list,
                "percentage": week_days_transfer_change.get("percentage"),
                "change": week_days_transfer_change.get("change")
                } if self.filter == "weekly" 
                else {
                    "total_transfers": total_transfer_amount_past_month if total_transfer_amount_past_month else 0.00,
                    "transfers_chart_values": month_days_transfer_list,
                    "percentage": month_days_transfer_change.get("percentage"),
                    "change": month_days_transfer_change.get("change")
                    } if self.filter == "monthly" 
                else {
                    "total_transfers": total_transfer_amount if total_transfer_amount else 0.00,
                    "transfers_chart_values": transfer_amount_values_list,
                    "percentage": transfer_amount_change.get("percentage"),
                    "change": transfer_amount_change.get("change")
                },
                "revenue": {
                "total_revenue": total_revenue_amount_past_week if total_revenue_amount_past_week else 0.00,
                "revenue_chart_values": week_days_revenue_list,
                "percentage": week_days_revenue_change.get("percentage"),
                "change": week_days_revenue_change.get("change")
                } if self.filter == "weekly" 
                else {
                    "total_revenue": total_revenue_amount_past_month if total_revenue_amount_past_month else 0.00,
                    "revenue_chart_values": month_days_revenue_list,
                    "percentage": month_days_revenue_change.get("percentage"),
                    "change": month_days_revenue_change.get("change")
                    } if self.filter == "monthly" 
                else {
                    "total_revenue": total_revenue_amount if total_revenue_amount else 0.00,
                    "revenue_chart_values": revenue_amount_values_list,
                    "percentage": revenue_amount_change.get("percentage"),
                    "change": revenue_amount_change.get("change")
                }
            }
        return chart_data


class Agent:
    def __init__(self, request):
        self.week_start = DateUtility().week_start
        self.previous_week_start = DateUtility().previous_week_start
        self.previous_week_end = DateUtility().previous_week_end        
        self.month_start  = DateUtility().month_start     
        self.previous_month_start = DateUtility().previous_month_start
        self.previous_month_end = DateUtility().previous_month_end 
        self.year_start = DateUtility().year_start
        self.previous_year_start = DateUtility().previous_year_start
        self.previous_year_end = DateUtility().previous_year_end 
        self.previous_day = DateUtility().previous_day
        self.start_of_all_transactions = DateUtility().start_of_all_transactions

        self.date_filter = DateUtility().get_date_filter(request=request)
        self.agent_filter = request.GET.get("agent-filter")
        self.request = request

        self.retail_system_qs = RetailSystem.objects.filter(
                                ~Q(retail_account__email__in=retail_system_email_exclude_list)
                                & Q(retail_account__in=funded_retail_accounts)
                                ).filter()
        
        self.retail_system_users = [system.retail_account for system in self.retail_system_qs]            
        self.agents_qs = self.retail_system_qs.values("retail_account", "user_assigned", "location")
        self.ledger_qs = LedgerTableModel.objects.filter(
                        Q(user__in=self.retail_system_users,
                        transaction_type__in=all_possible_transaction_list) &
                        Q(user__in=funded_retail_accounts)
                        ).order_by("-date_created")
        
        self.successful_transactions_qs = self.ledger_qs.filter(
                                          transaction__status="SUCCESSFUL", 
                                          transaction__is_reversed=False)
        
        self.location_qs = NewLocationList.objects.filter()

    def get_agent_overview(self):
        active_user = self.request.user
        if self.agent_filter == "my_agents":
            self.agents_qs = self.agents_qs.filter(supervisor = active_user)
        elif self.agent_filter in [location.location for location in self.location_qs]:
            self.agents_qs = self.agents_qs.filter(location__location = self.agent_filter)

        all_agents_count = self.agents_qs.count()
        previous_month_all_agents_count = self.agents_qs.filter(
                                          date_created__lte=self.previous_month_end
                                          )
        new_agents_count = self.agents_qs.filter(date_created__gte=self.month_start)
        previous_month_new_agents_count = self.agents_qs.filter(
                                          date_created__gte=self.previous_month_start, 
                                          date_created__lte=self.previous_month_end)
        
        agents_change = get_percentage_diff(
                        current=all_agents_count, 
                        previous=previous_month_all_agents_count.count()
                        )
        new_agents_change = get_percentage_diff(
                            current=new_agents_count.count(), 
                            previous=previous_month_new_agents_count.count()
                            )
        active_agents_count = self.agents_qs.filter(
                              retail_account__terminal_status="ACTIVE"
                              ).count()
        previous_month_active_agents_count = self.agents_qs.filter(
                                            retail_account__terminal_status="ACTIVE", 
                                            date_created__lte=self.previous_month_end
                                            )
        active_agents_change = get_percentage_diff(
                                current=active_agents_count,
                                previous=previous_month_active_agents_count.count()
                                )

        inactive_agents_count = self.agents_qs.filter(
                                retail_account__terminal_status="INACTIVE"
                                ).count()
        previous_month_inactive_agents_count = self.agents_qs.filter(
                                                retail_account__terminal_status="INACTIVE", 
                                                date_created__lte=self.previous_month_end)
        inactive_agents_change = get_percentage_diff(
                                current=inactive_agents_count, 
                                previous=previous_month_inactive_agents_count.count()
                                )
       
        partially_inactive_agents_count = self.agents_qs.filter(
                                            retail_account__terminal_status="PARTIALLY_INACTIVE"
                                            ).count()
        previous_month_partially_inactive_agents_count = self.agents_qs.filter(
                                                        retail_account__terminal_status="PARTIALLY_INACTIVE", 
                                                        date_created__lte=self.previous_month_end)
        partially_inactive_agents_change = get_percentage_diff(
                                            current=partially_inactive_agents_count, 
                                            previous=previous_month_partially_inactive_agents_count.count()
                                            )
       
        suspended_agents_count = self.agents_qs.filter(
                                Q(retail_account__is_suspended=True) |
                                Q(retail_account__terminal_suspended=True)
                                ).count()
        previous_month_suspended_agents_count = self.agents_qs.filter(
                                                Q(retail_account__is_suspended=True) |
                                                Q(retail_account__terminal_suspended=True),
                                                date_created__lte=self.previous_month_end
                                                )
        suspended_agents_change = get_percentage_diff(
                                current=suspended_agents_count, 
                                previous=previous_month_suspended_agents_count.count()
                                )
        
        data = {
            "all_agents": {
                "count": all_agents_count,
                "percentage": str(agents_change.get("percentage")),
                "change": agents_change.get("change")
            },
            "new_agents": {
                "count": new_agents_count.count(),
                "percentage": str(new_agents_change.get("percentage")),
                "change": new_agents_change.get("change")
            },
            "active_agents": {
                "count": active_agents_count,
                "percentage": str(active_agents_change.get("percentage")),
                "change": active_agents_change.get("change")
            },
            "inactive_agents": {
                "count": inactive_agents_count + partially_inactive_agents_count,
                "percentage": str(inactive_agents_change.get("percentage")),
                "change": inactive_agents_change.get("change")
            },
            "suspended_agents": {
                "count": suspended_agents_count,
                "percentage": str(suspended_agents_change.get("percentage")),
                "change": suspended_agents_change.get("change")
            },
             "partially_inactive_agents": {
                "count": partially_inactive_agents_count,
                "percentage": str(partially_inactive_agents_change.get("percentage")),
                "change": partially_inactive_agents_change.get("change")
            }
        }
        return data
    
    def get_top_agents(self):
        active_user = self.request.user
        if self.agent_filter == "my_agents":
            self.retail_system_users = [
                                        system.retail_account for system in 
                                        self.retail_system_qs.filter(
                                        supervisor=active_user)
                                        ]

        elif self.agent_filter in [location.location for location in self.location_qs]:
            self.retail_system_users = [
                                        system.retail_account for system in 
                                        self.retail_system_qs.filter(
                                        location__location=self.agent_filter)
                                        ]
        
        # Top 10
        discrete_transaction_amounts_overall = []
        for agent in self.retail_system_users:
            total_transactions_amount = list(self.successful_transactions_qs.filter(
                                        user=agent, **self.date_filter
                                        ).aggregate(Sum("amount")).values())[0]
            total_transactions_count = list(self.successful_transactions_qs.filter(
                                       user=agent, **self.date_filter
                                       ).aggregate(Count("amount")).values())[0]
            agent_system = self.retail_system_qs.filter(retail_account=agent).last()
            data = {
                "id": agent.id,
                "agent_id": agent.terminal_id,
                "agent_name": agent.get_full_name(),
                "agent_email": agent.email,
                "transaction_amount": total_transactions_amount if total_transactions_amount else 0.00,
                "transaction_count": total_transactions_count if total_transactions_count else 0.00,
                "agent_location": agent_system.location.location if agent_system and agent_system.location else ""
                }
            discrete_transaction_amounts_overall.append(data)
        amounts_sorted_list = sorted(discrete_transaction_amounts_overall, key=lambda d: d["transaction_amount"])[::-1][:10]
        counts_sorted_list = sorted(discrete_transaction_amounts_overall, key=lambda d: d["transaction_count"])[::-1][:10]
        
        data = {
            "transactions_amount": amounts_sorted_list,
            "transactions_count": counts_sorted_list
        }
        return data
    
    def get_agents_table(self):
        active_user = self.request.user
        if self.agent_filter == "my_agents":
            self.agents_qs = self.agents_qs.filter(supervisor = active_user)
        elif self.agent_filter in [location.location for location in self.location_qs]:
            self.agents_qs = self.agents_qs.filter(location__location = self.agent_filter)

        agents_list = []
        for agent in self.agents_qs:
            user_agent = User.objects.filter(id=agent["user_assigned"]).last()
            retail_agent = User.objects.filter(id=agent["retail_account"]).last()
            retail_agent_location = NewLocationList.objects.filter(id=agent["location"]).last().location

            if user_agent or retail_agent:
                agent_transactions_amount = list(self.successful_transactions_qs.filter(user=retail_agent, 
                                            **self.date_filter).aggregate(Sum("amount")).values())[0]
                last_balances = self.ledger_qs.filter(user=retail_agent, **self.date_filter).first()

                agent_details = {
                                "name": retail_agent.get_full_name(),
                                "id": retail_agent.id,
                                "email": retail_agent.email,
                                "branch": "",
                                "location": retail_agent_location,
                                "phone_number":  retail_agent.phone_number, 
                                "transactions": agent_transactions_amount if agent_transactions_amount else 0.00, 
                                "status": retail_agent.terminal_status,
                                "cash_in_hand": last_balances.cash_in_hand if last_balances and last_balances.cash_in_hand else 0.00,
                                "cash_at_bank": last_balances.cash_at_bank if last_balances and last_balances.cash_at_bank else 0.00
                                }
                agents_list.append(agent_details)
            else:
                pass
       
        data = {
            "agents_list": agents_list,
            "count": len(agents_list)
            }
        return data
    
    def get_active_agents_table(self):
        active_user = self.request.user
        if self.agent_filter == "my_agents":
            self.agents_qs = self.agents_qs.filter(supervisor = active_user)
        elif self.agent_filter in [location.location for location in self.location_qs]:
            self.agents_qs = self.agents_qs.filter(location__location = self.agent_filter)

        active_agents_list = []
        
        for agent in self.agents_qs:
            # user_agent = User.objects.filter(id=agent["user_assigned"]).last()
            retail_agent = User.objects.filter(id=agent["retail_account"]).last()
            retail_agent_location = NewLocationList.objects.filter(id=agent["location"]).last().location

            if retail_agent.terminal_status == "ACTIVE":
                agent_transactions_amount = list(self.successful_transactions_qs.filter(
                                        user=retail_agent, **self.date_filter).aggregate(Sum("amount")).values())[0]
                last_balances = self.ledger_qs.filter(user=retail_agent, **self.date_filter).first()

                agent_details = {
                                "name": retail_agent.get_full_name(), 
                                "id": retail_agent.id, 
                                "email": retail_agent.email, 
                                "branch": "", 
                                "location": retail_agent_location, 
                                "phone_number":  retail_agent.phone_number, 
                                "transactions": agent_transactions_amount if agent_transactions_amount else 0.00, 
                                "status": retail_agent.terminal_status,
                                "cash_in_hand": last_balances.cash_in_hand if last_balances and last_balances.cash_in_hand else 0.00,
                                "cash_at_bank": last_balances.cash_at_bank if last_balances and last_balances.cash_at_bank else 0.00
                                }
                active_agents_list.append(agent_details)
            else:
                pass
       
        data = {
            "active_agents_list": active_agents_list,
            "count": len(active_agents_list)
            }
        return data
    
    def get_inactive_agents_table(self):
        active_user = self.request.user
        if self.agent_filter == "my_agents":
            self.agents_qs = self.agents_qs.filter(supervisor = active_user)
        elif self.agent_filter in [location.location for location in self.location_qs]:
            self.agents_qs = self.agents_qs.filter(location__location = self.agent_filter)

        inactive_agents_list = []
        
        for agent in self.agents_qs:
            user_agent = User.objects.filter(id=agent["user_assigned"]).last()
            retail_agent = User.objects.filter(id=agent["retail_account"]).last()
            retail_agent_location = NewLocationList.objects.filter(id=agent["location"]).last().location

            if retail_agent.terminal_status == "INACTIVE" or retail_agent.terminal_status == "PARTIALLY_INACTIVE":
                agent_transactions_amount = list(self.successful_transactions_qs.filter(user=retail_agent,
                                                **self.date_filter).aggregate(Sum("amount")).values())[0]
                last_balances = self.ledger_qs.filter(user=retail_agent, **self.date_filter).first()

                agent_details = {
                                "name": retail_agent.get_full_name(), 
                                "id": retail_agent.id, 
                                "email": retail_agent.email, 
                                "branch": "", 
                                "location": retail_agent_location, 
                                "phone_number":  retail_agent.phone_number, 
                                "transactions": agent_transactions_amount if agent_transactions_amount else 0.00, 
                                "status": retail_agent.terminal_status,
                                "cash_in_hand": last_balances.cash_in_hand if last_balances and last_balances.cash_in_hand else 0.00,
                                "cash_at_bank": last_balances.cash_at_bank if last_balances and last_balances.cash_at_bank else 0.00
                                }
                inactive_agents_list.append(agent_details)
            else:
                pass
       
        data = {
            "inactive_agents_list": inactive_agents_list,
            "count": len(inactive_agents_list)
            }
        return data


    def get_suspended_agents_table(self):
        active_user = self.request.user
        if self.agent_filter == "my_agents":
            self.agents_qs = self.agents_qs.filter(supervisor = active_user)
        elif self.agent_filter in [location.location for location in self.location_qs]:
            self.agents_qs = self.agents_qs.filter(location__location = self.agent_filter)

        inactive_agents_list = []
        
        for agent in self.agents_qs:
            user_agent = User.objects.filter(id=agent["user_assigned"]).last()
            retail_agent = User.objects.filter(id=agent["retail_account"]).last()
            retail_agent_location = NewLocationList.objects.filter(id=agent["location"]).last().location

            if retail_agent.is_suspended == True or retail_agent.terminal_suspended == True:
                agent_transactions_amount = list(self.successful_transactions_qs.filter(user=retail_agent,
                                                **self.date_filter).aggregate(Sum("amount")).values())[0]
                last_balances = self.ledger_qs.filter(user=retail_agent, **self.date_filter).first()
                
                agent_details = {
                                "name": retail_agent.get_full_name(), 
                                "id": retail_agent.id, 
                                "email": retail_agent.email, 
                                "branch": "", 
                                "location": retail_agent_location, 
                                "phone_number":  retail_agent.phone_number, 
                                "transactions": agent_transactions_amount if agent_transactions_amount else 0.00, 
                                "status": retail_agent.terminal_status,
                                "cash_in_hand": last_balances.cash_in_hand if last_balances and last_balances.cash_in_hand else 0.00,
                                "cash_at_bank": last_balances.cash_at_bank if last_balances and last_balances.cash_at_bank else 0.00
                                }
                inactive_agents_list.append(agent_details)
            else:
                pass
       
        data = {
            "inactive_agents_list": inactive_agents_list,
            "count": len(inactive_agents_list)
            }
        return data
    
    
    def send_sms_to_agents(self):
        serializer = SendSmsSerializer(data = self.request.data)

        if serializer.is_valid(raise_exception=True):
            recipient = serializer.validated_data.get("recipient")
            subject = serializer.validated_data.get("subject")
            message = serializer.validated_data.get("message")
            send_date = serializer.validated_data.get("send_date")
            send_later = serializer.validated_data.get("send_later")

            if recipient == "All agents":
                retail_agents_qs = self.retail_system_qs
            elif recipient == "Active agents":
                retail_agents_qs = self.retail_system_qs.filter(retail_account__terminal_status = "ACTIVE")
            elif recipient == "In-active agents":
                retail_agents_qs = self.retail_system_qs.filter(retail_account__terminal_status = "INACTIVE")
            elif recipient == "Suspended agents":
                retail_agents_qs = self.retail_system_qs.filter(retail_account__is_suspended = True)
            else:
                pass
            
            try:
                for system in retail_agents_qs:
                    try:
                        if send_date and send_date > timezone.now().date() and send_later==True:
                            send_sms_to_retail_agents.apply_async(
                                kwargs = {"phone_number": system.retail_account.phone_number,
                                        "message": message
                                        },
                                eta = send_date
                                )
                            return {"message": f"sms delivery scheduled for {send_date}"}
                        else:
                            send_sms_to_retail_agents(phone_number = system.retail_account.phone_number, message = message)
                            return {"message": "sms delivery successful"}
                    finally:
                        SendAgentsSmsDumpData.create_send_sms_record(
                                                                recipient = recipient,
                                                                subject = subject,
                                                                message = message,
                                                                status = "successful",
                                                                sent_by = self.request.user
                                                                )
            except Exception as e:
                SendAgentsSmsDumpData.create_send_sms_record(
                                                            recipient = recipient,
                                                            subject = subject,
                                                            message = message,
                                                            status = "Failed",
                                                            sent_by = self.request.user
                                                            )
                return {"message": "sms delivery failed"}                  


class AgentDetails:
    def __init__(self, request):
        self.week_start = DateUtility().week_start
        self.previous_week_start = DateUtility().previous_week_start
        self.previous_week_end = DateUtility().previous_week_end        
        self.month_start  = DateUtility().month_start     
        self.previous_month_start = DateUtility().previous_month_start
        self.previous_month_end = DateUtility().previous_month_end 
        self.year_start = DateUtility().year_start
        self.previous_year_start = DateUtility().previous_year_start
        self.previous_year_end = DateUtility().previous_year_end 
        self.previous_day = DateUtility().previous_day
        self.start_of_all_transactions = DateUtility().start_of_all_transactions

        self.date_filter = DateUtility().get_date_filter(request=request)       
        
        self.agent_qs = User.objects.filter()
        self.retail_system_qs = RetailSystem.objects.filter(
                                ~Q(retail_account__email__in=retail_system_email_exclude_list)
                                & Q(retail_account__in=funded_retail_accounts)
                                ).filter()
        retail_system_users = [system.retail_account for system in self.retail_system_qs]            
        self.ledger_qs = LedgerTableModel.objects.filter(
                        Q(user__in=retail_system_users,
                        transaction_type__in=all_possible_transaction_list) &
                        Q(user__in=funded_retail_accounts)
                        ).order_by("-date_created")
        
        self.cashout_qs = self.ledger_qs.filter(transaction_type__in=cashout_transaction_type_list)
        self.transfer_qs = self.ledger_qs.filter(transaction_type__in=transfer_transaction_type_list)
        self.sessions_qs = Session.objects.filter()
        self.successful_transactions_qs = self.ledger_qs.filter(
                                          transaction__status = "SUCCESSFUL", 
                                          transaction__is_reversed = False)
        
        self.successful_cashout_qs = self.cashout_qs.filter(
                                     transaction__status="SUCCESSFUL", 
                                     transaction__is_reversed=False)
        
        self.successful_transfer_qs = self.transfer_qs.filter(
                                      transaction__status="SUCCESSFUL", 
                                      transaction__is_reversed=False)

    def get_agent_detail(self, id):
        agent = self.agent_qs.get(id=id)
        active_sessions_qs = self.sessions_qs.filter(expire_date__lte = timezone.now())
        acive_sessions_ids = [data.get_decoded().get("_auth_user_id", "-") for data in active_sessions_qs]

        data = {
            "personal_data": {
                "id": agent.id,
                "agent_name": agent.get_full_name(),
                "phone_number": agent.phone_number,
                "last_seen": agent.last_login,
                "status": "online" if agent.id in acive_sessions_ids else "offline",
                "email": agent.email,
                "terminal_status": agent.terminal_status
            }
        }
        return data
    
    def get_agent_wallets(self, id):
        agent = self.agent_qs.get(id=id)
        
        airtime_sales_qs = self.successful_transactions_qs.filter(
                           transaction_type = "AIRTIME_PIN", 
                           user = agent, **self.date_filter)
        
        bills_sales_qs = self.successful_transactions_qs.filter(
                         transaction_type = "BILLS_AND_PAYMENT", 
                         user = agent, **self.date_filter)
        
        cashout_qs = self.successful_cashout_qs.filter(
                     user = agent, **self.date_filter)
        
        transfer_qs = self.successful_transfer_qs.filter(
                      user = agent, **self.date_filter)

        total_transactions_amount = list(self.successful_transactions_qs.filter(
                                    user=agent, **self.date_filter
                                    ).aggregate(Sum("amount")).values())[0]
        
        total_transactions_amount_today = list(self.successful_transactions_qs.filter(
                                          date_created__date=timezone.now().date(), 
                                          user=agent).aggregate(Sum("amount")).values())[0]
        
        total_withdrawal_amount = list(cashout_qs.aggregate(Sum("amount")).values())[0]
        total_withdrawal_amount_today = list(cashout_qs.filter(date_created__date=timezone.now().date()
                                                               ).aggregate(Sum("amount")).values())[0]
        
        total_transfer_amount = list(transfer_qs.aggregate(Sum("amount")).values())[0]
        total_transfer_amount_today = list(transfer_qs.filter(date_created__date=timezone.now().date()
                                                              ).aggregate(Sum("amount")).values())[0]
        
        total_withdrawal_count = list(cashout_qs.aggregate(Count("amount")).values())[0]
        total_withdrawal_count_today = list(cashout_qs.filter(date_created__date=timezone.now().date()
                                                              ).aggregate(Count("amount")).values())[0]
        
        total_transfer_count = list(transfer_qs.aggregate(Count("amount")).values())[0]
        total_transfer_count_today = list(transfer_qs.filter(date_created__date=timezone.now().date()
                                                             ).aggregate(Count("amount")).values())[0]

        total_transactions_count = list(self.successful_transactions_qs.filter(user=agent, **self.date_filter
                                                            ).aggregate(Count("amount")).values())[0]
        total_transactions_count_today = list(self.successful_transactions_qs.filter(
                                                            date_created__date=timezone.now().date(), 
                                                            user=agent).aggregate(Count("amount")).values())[0]
        
        airtime_transactions_amount = list(airtime_sales_qs.filter(user=agent).aggregate(Sum("amount")).values())[0]
        bills_transactions_amount = list(bills_sales_qs.filter(user=agent).aggregate(Sum("amount")).values())[0]

        last_balances = self.ledger_qs.filter(user=agent, **self.date_filter).first()


        data = {
            "general": {
                "total_transactions": total_transactions_amount if total_transactions_amount else 0.00,
                "cash_in_hand": last_balances.cash_in_hand if last_balances else 0.00,
                "cash_in_bank": last_balances.cash_at_bank if last_balances else 0.00,
                "total_commission": last_balances.liberty_commission_sum if last_balances else 0.00
            },
            "others": {
                "total_transactions": total_transactions_amount if total_transactions_amount else 0.00,
                "total_transactions_amount_today": total_transactions_amount_today if total_transactions_amount_today else 0.00,
                "total_withdrawal": total_withdrawal_amount if total_withdrawal_amount else 0.00,
                "total_withdrawal_today": total_withdrawal_amount_today if total_withdrawal_amount_today else 0.00,
                "transaction_counts": total_transactions_count if total_transactions_count else 0,
                "transaction_counts_today": total_transactions_count_today if total_transactions_count_today else 0,
                "total_commission": last_balances.liberty_commission_sum if last_balances else 0.00,    
                "airtime_data": airtime_transactions_amount if airtime_transactions_amount else 0.00,    
                "cable_tv": 0.00,    
                "subscriptions": 0.00,    
                "bills": bills_transactions_amount if bills_transactions_amount else 0.00,
                "total_transfer_amount": total_transfer_amount if total_transfer_amount else 0.00,   
                "total_transfer_amount_today": total_transfer_amount_today if total_transfer_amount_today else 0.00,
                "total_withdrawal_count": total_withdrawal_count if total_withdrawal_count else 0,
                "total_withdrawal_count_today": total_withdrawal_count_today if total_withdrawal_count_today else 0.00,
                "total_transfer_count": total_transfer_count if total_transfer_count else 0,
                "total_transfer_count_today": total_transfer_count_today if total_transfer_count_today else 0
            }
        }
        return data

    def get_agent_transactions(self, id):
        agent = self.agent_qs.get(id=id)
        agent_transactions = self.ledger_qs.filter(user=agent, **self.date_filter)

        agent_transactions_list = []
        for transaction in agent_transactions:
            transactions_data = {   
                    "id": transaction.id,
                    "description": "Cashout" if transaction.transaction_type in cashout_transaction_type_list else \
                            "Transfer" if transaction.transaction_type in transfer_transaction_type_list else
                            "Bills and Utilities" if transaction.transaction_type in ["BILLS_AND_UTILITIES", "AIRTIME_PIN"] else "Others",
                    "amount": transaction.amount,
                    "transaction_count": 1,
                    "refrence_id": transaction.transaction.unique_reference if transaction.transaction.unique_reference else "",
                    "date": transaction.date_created,
                    "receiver": transaction.beneficiary if transaction.beneficiary else "",
                    "status": transaction.transaction.status,
                    "reference_id": transaction.transaction.unique_reference if transaction.transaction.unique_reference else "",
                    "date": transaction.date_created
                }
            agent_transactions_list.append(transactions_data)

        data = {
            "transactions_table": agent_transactions_list,
            "count": len(agent_transactions_list)
        }
        return data
    
    def get_transaction_detail(self, id):
        transaction = self.ledger_qs.get(id=id)

        transaction_details = {
            "status": (transaction.transaction.status).title() if transaction.transaction.status else "",
            "reason_for_decline": transaction.error_msg,
            "description": "Cashout" if transaction.transaction_type in cashout_transaction_type_list else \
                            "Transfer" if transaction.transaction_type in transfer_transaction_type_list else
                            "Bills and Utilities" if transaction.transaction_type in ["BILLS_AND_UTILITIES", "AIRTIME_PIN"] else "Others",
            "sender": transaction.transaction.source_account_name if transaction.transaction.source_account_name else "",
            "receiver_name": transaction.beneficiary if transaction.beneficiary else "",
            "receiver_bank": transaction.transaction.beneficiary_nuban if transaction.transaction.beneficiary_nuban else "",
            "receiver_account_number": transaction.transaction.beneficiary_nuban if transaction.transaction.beneficiary_nuban else "",
            "amount": transaction.amount,
            "transaction_method": (" ".join(transaction.transaction.transaction_mode.split("_"))).title() \
                                    if transaction.transaction.transaction_mode else "",
            "narration": transaction.transaction.narration if transaction.transaction.narration else "",
            "charges": transaction.charge_sum if transaction.charge_sum else "",
            "reference_id": transaction.transaction.unique_reference if transaction.transaction.unique_reference else "",
            "date": transaction.date_created
        }
        return transaction_details

    def get_agent_active_session(self, id):
        agent = self.agent_qs.get(id=id)
        agent_sessions = self.sessions_qs.order_by("-expire_date")
        agent_location = self.retail_system_qs.filter(retail_account=agent).last()

        agent_sessions_list = []
        for session in agent_sessions:
            user_id = session.get_decoded().get("_auth_user_id", "-")
            if user_id == id:
                data = {   
                    "id": id,
                    "log_time": session.expire_date - timezone.timedelta(days=12),
                    "session_duration": "2 hours 3 mins",
                    "device": "",
                    "location": agent_location.location.location if agent_location.location else "",
                }
                agent_sessions_list.append(data)
            else:
                pass        

        data = {
            "active_sessions": agent_sessions_list,
            "count": len(agent_sessions_list)
        }
        return data
    
    def get_agent_revenue_overview(self, id):
        """
        Provides metrics for Revenue generation of Liberty Retail Agents.
        The daily revenue amount is derived from the LedgerModelTable model.
        A field named "charge_sum" holds the value for every revenue generated
        per transaction record.
        """
        agent = self.agent_qs.get(id=id)
        retail_sysem_account = self.retail_system_qs.filter(retail_account=agent).last()
        agent_date_joined = retail_sysem_account.date_created
        number_of_days_since_joined = (datetime.now().date() - agent_date_joined.date()).days
        number_of_days_since_joined_less_weekends = (number_of_days_since_joined - floor(number_of_days_since_joined / 7) +
                                                    (number_of_days_since_joined % 7))

        agent_transactions = self.successful_transactions_qs.filter(user=agent)
        revenue_qs = agent_transactions.exclude()

        total_revenue_count = list(revenue_qs.aggregate(Count("charge")).values())[0]

        # Revenue Amount
        total_revenue_amount = list(revenue_qs.filter(**self.date_filter).aggregate(Sum("charge")).values())[0]
        total_revenue_amount_today = list(revenue_qs.filter(date_created__date=timezone.now().date()
                                    ).aggregate(Sum("charge")).values())[0]        
        total_revenue_amount_yesterday = list(revenue_qs.filter(date_created__date=self.previous_day.date()
                                         ).aggregate(Sum("charge")).values())[0]        
        total_revenue_amount_this_week = list(revenue_qs.filter(date_created__gte=self.week_start).aggregate(
                                                                        Sum("charge")).values())[0]
        total_revenue_amount_this_month = list(revenue_qs.filter(date_created__gte=self.month_start).aggregate(
                                                                        Sum("charge")).values())[0]
        total_revenue_amount_this_year = list(revenue_qs.filter(date_created__gte=self.year_start).aggregate(
                                                                        Sum("charge")).values())[0]
        total_revenue_amount_last_year = list(revenue_qs.filter(date_created__gte=self.previous_year_start, 
                                                date_created__lte=self.previous_year_end).aggregate(Sum("charge")).values())[0]
        total_revenue_amount_last_week = list(revenue_qs.filter(date_created__gte=self.previous_week_start, 
                                                date_created__lte=self.previous_week_end).aggregate(Sum("charge")).values())[0]
        total_revenue_amount_last_month = list(revenue_qs.filter(date_created__gte=self.previous_month_start, 
                                                date_created__lte=self.previous_month_end).aggregate(Sum("charge")).values())[0]
        
        # Revenue Count
        total_revenue_count = list(revenue_qs.filter(**self.date_filter).aggregate(Count("charge")).values())[0]
        total_revenue_count_today = list(revenue_qs.filter(date_created__date=timezone.now().date()
                                    ).aggregate(Count("charge")).values())[0]        
        total_revenue_count_yesterday = list(revenue_qs.filter(date_created__date=self.previous_day.date()
                                         ).aggregate(Count("charge")).values())[0]        
        total_revenue_count_this_week = list(revenue_qs.filter(date_created__gte=self.week_start).aggregate(
                                                                        Count("charge")).values())[0]
        total_revenue_count_this_month = list(revenue_qs.filter(date_created__gte=self.month_start).aggregate(
                                                                        Count("charge")).values())[0]
        total_revenue_count_this_year = list(revenue_qs.filter(date_created__gte=self.year_start).aggregate(
                                                                        Count("charge")).values())[0]
        total_revenue_count_last_year = list(revenue_qs.filter(date_created__gte=self.previous_year_start, 
                                                date_created__lte=self.previous_year_end).aggregate(Count("charge")).values())[0]
        total_revenue_count_last_week = list(revenue_qs.filter(date_created__gte=self.previous_week_start, 
                                                date_created__lte=self.previous_week_end).aggregate(Count("charge")).values())[0]
        total_revenue_count_last_month = list(revenue_qs.filter(date_created__gte=self.previous_month_start, 
                                                date_created__lte=self.previous_month_end).aggregate(Count("charge")).values())[0]
        
        # Revenue Target Amount
        daily_target_amount = 3000
        overall_revenue_target_amount = (daily_target_amount * number_of_days_since_joined_less_weekends)
        total_revenue_target_amount_today = daily_target_amount     
        total_revenue_target_amount_yesterday = daily_target_amount
        total_revenue_target_amount_this_week = total_revenue_target_amount_today * 6
        total_revenue_target_amount_this_month = total_revenue_target_amount_today * 24
        total_revenue_target_amount_this_year = total_revenue_target_amount_today * 24 * 12
        total_revenue_target_amount_last_year = daily_target_amount * 24 * 12
        total_revenue_target_amount_last_week = daily_target_amount * 6
        total_revenue_target_amount_last_month = daily_target_amount * 24
        
        # Revenue Target Count
        daily_target_count = 13
        overall_revenue_target_count = daily_target_count * number_of_days_since_joined_less_weekends
        total_revenue_target_count_today = daily_target_count      
        total_revenue_target_count_yesterday = daily_target_count
        total_revenue_target_count_this_week = total_revenue_target_count_today * 6
        total_revenue_target_count_this_month = total_revenue_target_count_today * 24
        total_revenue_target_count_this_year = total_revenue_target_count_today * 24 * 12
        total_revenue_target_count_last_year = daily_target_count * 24 * 12
        total_revenue_target_count_last_week = daily_target_count * 6
        total_revenue_target_count_last_month = daily_target_count * 24
        
        average_daily_revenue_amount = (
                                 (total_revenue_amount if total_revenue_amount else 0.00) / 
                                 (number_of_days_since_joined_less_weekends if number_of_days_since_joined_less_weekends else 1)
                                 )
        average_daily_revenue_count = (
                                 total_revenue_count / 
                                 (number_of_days_since_joined_less_weekends if number_of_days_since_joined_less_weekends else 1)
                                 )
        data = {
            "revenue_amount": {
                "total_amount": total_revenue_amount if total_revenue_amount else 0.00,
                "average_daily_amount": average_daily_revenue_amount,

                "amount_today": total_revenue_amount_today if total_revenue_amount_today else 0.00,
                "amount_this_week": total_revenue_amount_this_week if total_revenue_amount_this_week else 0.00, 
                "amount_this_month": total_revenue_amount_this_month if total_revenue_amount_this_month else 0.00,
                "amount_last_month": total_revenue_amount_last_month if total_revenue_amount_last_month else 0.00,
                "amount_yesterday": total_revenue_amount_yesterday if total_revenue_amount_yesterday else 0.00,
                "amount_last_week": total_revenue_amount_last_week if total_revenue_amount_last_week else 0.00,
                "amount_this_year": total_revenue_amount_this_year if total_revenue_amount_this_year else 0.00,
                "amount_last_year": total_revenue_amount_last_year if total_revenue_amount_last_year else 0.00
            },
            "revenue_count": {
                "total_count": total_revenue_count,
                "average_daily_count": average_daily_revenue_count,
                "count_today": total_revenue_count_today,
                "count_this_week": total_revenue_count_this_week, 
                "count_this_month": total_revenue_count_this_month,
                "count_last_month": total_revenue_count_last_month,
                "count_yesterday": total_revenue_count_yesterday,
                "count_last_week": total_revenue_count_last_week,
                "count_this_year": total_revenue_count_this_year,
                "count_last_year": total_revenue_count_last_year
            },
            "target_revenue_amount":{
                "overall_amount": overall_revenue_target_amount,
                "amount_today": total_revenue_target_amount_today if total_revenue_target_amount_today else 0.00,
                "amount_this_week": total_revenue_target_amount_this_week if total_revenue_target_amount_this_week else 0.00, 
                "amount_this_month": total_revenue_target_amount_this_month if total_revenue_target_amount_this_month else 0.00,
                "amount_last_month": total_revenue_target_amount_last_month if total_revenue_target_amount_last_month else 0.00,
                "amount_yesterday": total_revenue_target_amount_yesterday if total_revenue_target_amount_yesterday else 0.00,
                "amount_last_week": total_revenue_target_amount_last_week if total_revenue_target_amount_last_week else 0.00,
                "amount_this_year": total_revenue_target_amount_this_year if total_revenue_target_amount_this_year else 0.00,
                "amount_last_year": total_revenue_target_amount_last_year if total_revenue_target_amount_last_year else 0.00
            },
            "target_revenue_count": {
                "total_count": overall_revenue_target_count,
                "count_today": total_revenue_target_count_today,
                "count_this_week": total_revenue_target_count_this_week, 
                "count_this_month": total_revenue_target_count_this_month,
                "count_last_month": total_revenue_target_count_last_month,
                "count_yesterday": total_revenue_target_count_yesterday,
                "count_last_week": total_revenue_target_count_last_week,
                "count_this_year": total_revenue_target_count_this_year,
                "count_last_year": total_revenue_target_count_last_year
            },
            "average_daily_amount": {
                "average_daily_revenue_amount": average_daily_revenue_amount
            }
        }
        return data

class LeaderBoard:
    def __init__(self, request):
        self.week_start = DateUtility().week_start
        self.previous_week_start = DateUtility().previous_week_start
        self.previous_week_end = DateUtility().previous_week_end        
        self.month_start  = DateUtility().month_start     
        self.previous_month_start = DateUtility().previous_month_start
        self.previous_month_end = DateUtility().previous_month_end 
        self.year_start = DateUtility().year_start
        self.previous_year_start = DateUtility().previous_year_start
        self.previous_year_end = DateUtility().previous_year_end 
        self.previous_day = DateUtility().previous_day
        self.start_of_all_transactions = DateUtility().start_of_all_transactions
        self.day_ago = DateUtility().day_ago

        self.date_filter = DateUtility().get_date_filter(request=request)

        self.retail_system_qs = RetailSystem.objects.filter(
                                ~Q(retail_account__email__in=retail_system_email_exclude_list)
                                & Q(retail_account__in=funded_retail_accounts)
                                ).filter()
        self.retail_system_users = [system.retail_account for system in self.retail_system_qs]            
        self.ledger_qs = LedgerTableModel.objects.filter(
                        Q(user__in=self.retail_system_users,
                        transaction_type__in=all_possible_transaction_list) &
                        Q(user__in=funded_retail_accounts)
                        ).order_by("-date_created")
        
        self.sessions_qs = Session.objects.all()
        self.agents_qs = User.objects.filter()
        self.successful_transactions_qs = self.ledger_qs.filter(transaction__status="SUCCESSFUL", 
                                                                transaction__is_reversed=False)
        self.cashout_qs = self.successful_transactions_qs.filter(
                        transaction_type__in=cashout_transaction_type_list)
        self.transfer_qs = self.successful_transactions_qs.filter(
                        transaction_type__in=transfer_transaction_type_list)

    def get_top20_amount_24_hours(self):
        agents_transactions = self.successful_transactions_qs.filter()

        top_agents_transctions_list = []
        for agent in self.retail_system_users:
            transactions_qs = agents_transactions.filter(user=agent, date_created__gte=self.day_ago)

            total_transaction_amount = list(transactions_qs.aggregate(Sum("amount")).values())[0]
            total_cashout_amount = list(self.cashout_qs.filter(
                                    user=agent, 
                                    date_created__gte=self.day_ago
                                    ).aggregate(Sum("amount")).values())[0]
            total_transfer_amount = list(self.transfer_qs.filter(
                                    user=agent, date_created__gte=self.day_ago
                                    ).aggregate(Sum("amount")).values())[0]

            data = {
                    "name": agent.get_full_name(), 
                    "email": agent.email, 
                    "withdrawal_value": total_cashout_amount if total_cashout_amount else 0.00, 
                    "transfer_value": total_transfer_amount if total_transfer_amount else 0.00,
                    "total_transactions": total_transaction_amount if total_transaction_amount else 0.00
                    }
            top_agents_transctions_list.append(data) 

        sorted_top_agents = sorted(top_agents_transctions_list, key = lambda d: d["total_transactions"])[::-1]
        
        data = {
            "top_agents_list": sorted_top_agents,
            "count": len(sorted_top_agents)
        }
        return data

    def get_top20_amount_date_range(self):
        agents_transactions = self.successful_transactions_qs.filter()

        top_agents_transctions_list = []
        for agent in self.retail_system_users:
            transactions_qs = agents_transactions.filter(user=agent, **self.date_filter)

            total_transaction_amount = list(transactions_qs.aggregate(Sum("amount")).values())[0]
            total_cashout_amount = list(self.cashout_qs.filter(user=agent, **self.date_filter
                                   ).aggregate(Sum("amount")).values())[0]
            total_transfer_amount = list(self.transfer_qs.filter(user=agent, **self.date_filter
                                    ).aggregate(Sum("amount")).values())[0]

            data = {
                    "name": agent.get_full_name(), 
                    "email": agent.email, 
                    "withdrawal_value": total_cashout_amount if total_cashout_amount else 0.00, 
                    "transfer_value": total_transfer_amount if total_transfer_amount else 0.00,
                    "total_transactions": total_transaction_amount if total_transaction_amount else 0.00
                    }
            top_agents_transctions_list.append(data) 

        sorted_top_agents = sorted(top_agents_transctions_list, key = lambda d: d["total_transactions"])[::-1]
        
        data = {
            "top_agents_list": sorted_top_agents,
            "count": len(sorted_top_agents)
        }
        return data
    

    def get_top20_count_24_hours(self):
        agents_transactions = self.successful_transactions_qs.filter()

        top_agents_transctions_list = []
        for agent in self.retail_system_users:
            transactions_qs = agents_transactions.filter(user=agent, date_created__gte=self.day_ago)

            total_transaction_count = list(transactions_qs.aggregate(Count("amount")).values())[0]
            total_cashout_count = list(self.cashout_qs.filter(user=agent, date_created__gte=self.day_ago
                                  ).aggregate(Count("amount")).values())[0]
            total_transfer_count = list(self.transfer_qs.filter(user=agent, date_created__gte=self.day_ago
                                   ).aggregate(Count("amount")).values())[0]

            data = {
                    "name": agent.get_full_name(), 
                    "email": agent.email, 
                    "withdrawal_count": total_cashout_count if total_cashout_count else 0, 
                    "transfer_count": total_transfer_count if total_transfer_count else 0,
                    "total_transactions": total_transaction_count if total_transaction_count else 0
                    }
            top_agents_transctions_list.append(data) 

        sorted_top_agents = sorted(top_agents_transctions_list, key = lambda d: d["total_transactions"])[::-1]

        data = {
            "top_agents_list": sorted_top_agents,
            "count": len(sorted_top_agents)
        }
        return data

    def get_top20_count_date_range(self):
        agents_transactions = self.successful_transactions_qs.filter()

        top_agents_transctions_list = []
        for agent in self.retail_system_users:
            transactions_qs = agents_transactions.filter(user=agent, **self.date_filter)

            total_transaction_count = list(transactions_qs.aggregate(Count("amount")).values())[0]
            total_cashout_count = list(self.cashout_qs.filter(user=agent, **self.date_filter
                                  ).aggregate(Count("amount")).values())[0]
            total_transfer_count = list(self.transfer_qs.filter(user=agent, **self.date_filter
                                   ).aggregate(Count("amount")).values())[0]

            data = {
                    "name": agent.get_full_name(), 
                    "email": agent.email, 
                    "withdrawal_count": total_cashout_count if total_cashout_count else 0, 
                    "transfer_count": total_transfer_count if total_transfer_count else 0,
                    "total_transactions": total_transaction_count if total_transaction_count else 0
                    }
            top_agents_transctions_list.append(data) 

        sorted_top_agents = sorted(top_agents_transctions_list, key = lambda d: d["total_transactions"])[::-1]
        
        data = {
            "top_agents_list": sorted_top_agents,
            "count": len(sorted_top_agents)
        }
        return data
    
    def get_agents_log_history(self):
        active_sessions = self.sessions_qs.filter(expire_date__gte = timezone.now())

        active_users_id = [data.get_decoded().get("_auth_user_id", "0") for data in active_sessions]
        active_agents = self.agents_qs.filter(
                    Q(id__in=active_users_id)
                    & Q(id__in=funded_retail_accounts)
                    )

        online_agents_count = active_agents.count()
        offline_agents_count = len(self.retail_system_users) - online_agents_count
        
        overview = {
            "online_agents": online_agents_count,
            "offline_agents": offline_agents_count
        }

        agents_log_table = []
        for agent in self.retail_system_users:
            agent_retail_system = RetailSystem.objects.filter(retail_account=agent).last()

            agent_log_data = {
                "agent_id": agent.id,
                "agent": agent.get_full_name(),
                "device": "POS",
                "location": agent_retail_system.location.location if agent_retail_system else "",
                "status": "online" if agent in active_agents else "offline"
                }
            agents_log_table.append(agent_log_data)
        
        data = {
            "overview": overview,
            "agents_log_table": agents_log_table,
            "count": len(agents_log_table)
        }
        return data
    

class Branch:
    def __init__(self, request):
        self.week_start = DateUtility().week_start
        self.previous_week_start = DateUtility().previous_week_start
        self.previous_week_end = DateUtility().previous_week_end        
        self.month_start  = DateUtility().month_start     
        self.previous_month_start = DateUtility().previous_month_start
        self.previous_month_end = DateUtility().previous_month_end 
        self.year_start = DateUtility().year_start
        self.previous_year_start = DateUtility().previous_year_start
        self.previous_year_end = DateUtility().previous_year_end 
        self.previous_day = DateUtility().previous_day
        self.start_of_all_transactions = DateUtility().start_of_all_transactions
        self.day_ago = DateUtility().day_ago
        self.date_filter = DateUtility().get_date_filter(request=request)

        self.location_qs = NewLocationList.objects.filter()
        self.retail_system_qs = RetailSystem.objects.filter(
                                ~Q(retail_account__email__in=retail_system_email_exclude_list)
                                & Q(retail_account__in=funded_retail_accounts)
                                ).filter()
        
        retail_system_users = [system.retail_account for system in self.retail_system_qs]            
        self.ledger_qs = LedgerTableModel.objects.filter(
                        Q(user__in=retail_system_users,
                        transaction_type__in=all_possible_transaction_list) &
                        Q(user__in=funded_retail_accounts)
                        ).order_by("-date_created")
        
        self.successful_transactions_qs = self.ledger_qs.filter(
                                            transaction__status="SUCCESSFUL", 
                                            transaction__is_reversed = False)

    def get_branch_overview(self):
        number_of_branches = self.location_qs.count()
        number_of_supervisors = self.retail_system_qs.filter(
                                supervisor__isnull=False
                                ).distinct("supervisor").count()
        number_of_agents = self.retail_system_qs.count()

        data = {
            "number_of_branches": number_of_branches,
            "number_of_supervisors": number_of_supervisors,
            "number_of_agents": number_of_agents
        }
        return data
    
    def get_branch_charts(self):
        branches = [location.location for location in self.location_qs]

        transfer_values = []
        withdrawal_values = []
        commission_values = []
        branch_activities_values = []
        revenue_values = []

        for branch in branches:
            retail_system = self.retail_system_qs.filter(location__location=branch)
            agents_list = [system.retail_account for system in retail_system]

            transactions_qs = self.successful_transactions_qs.filter(user__in = agents_list, **self.date_filter)
            revenue_qs = self.successful_transactions_qs.filter(~Q(charge__lte=0), user__in = agents_list,
                        **self.date_filter).annotate(actual_charge = F("charge") - F("liberty_commission"))

            transfer_sum = list(transactions_qs.filter(transaction_type__in=transfer_transaction_type_list
                                                       ).aggregate(Sum("amount")).values())[0]
            withdrawal_sum = list(transactions_qs.filter(transaction_type__in=cashout_transaction_type_list
                                                         ).aggregate(Sum("amount")).values())[0]
            commission_sum = list(transactions_qs.aggregate(Sum("liberty_commission_sum")).values())[0]
            revenue_sum = list(revenue_qs.aggregate(Sum("actual_charge")).values())[0]
            
            transfer_values.append(transfer_sum if transfer_sum else 0.00)
            withdrawal_values.append(withdrawal_sum if withdrawal_sum else 0.00)
            commission_values.append(commission_sum if commission_sum else 0.00)
            revenue_values.append(revenue_sum if revenue_sum else 0.00)

        data = {
            "branches": branches,
            "transfer_values": transfer_values,
            "withdrawal_values": withdrawal_values,
            "commission_values": commission_values,
            "branch_activities_values": [0.0]*len(branches),
            "branch_revenues": revenue_values
        }
        return data
    
    def get_top5_branches(self):
        branches = [location.location for location in self.location_qs]

        branch_details_list = []
        for branch in branches:
            retail_system = self.retail_system_qs.filter(location__location=branch)
            agents_list = [system.retail_account for system in retail_system]

            transactions_qs = self.successful_transactions_qs.filter(user__in=agents_list, **self.date_filter)
            transaction_amount = transactions_qs.aggregate(total_amount = Sum("amount"))

            branch_details = {"id":1, 
                              "branch": branch, 
                              "name": branch, 
                              "rating":5, 
                              "transaction_amount": transaction_amount["total_amount"] if 
                                                    transaction_amount["total_amount"] else 0.00
                              }
            branch_details_list.append(branch_details)

        sorted_branches = sorted(branch_details_list, key=lambda d: d["transaction_amount"])
        top5_performing = sorted_branches[::-1][:5]

        data = {
            "top5_performing": top5_performing,
            "top5_non_performing": sorted_branches[:5]
        }
        return data
    

    def get_branch_list(self):
        branches = [location.location for location in self.location_qs]

        branch_details_list = []
        for branch in branches:
            retail_system = self.retail_system_qs.filter(location__location=branch)
            agents_list = retail_system.filter(retail_account__isnull=False)
            supervisor_list = retail_system.filter(supervisor__isnull=False).distinct("supervisor")
            location = self.location_qs.filter(location=branch).last()
        
            branches_details = {
                                "id":location.id, 
                                "branch": branch, 
                                "number_of_supervisors": supervisor_list.count(), 
                                "number_of_agents": agents_list.count(),
                                "sub_location_num": location.sub_location_num
                                }
            branch_details_list.append(branches_details)
        data = {
            "branches": branch_details_list,
            "count": len(branch_details_list)
        }
        return data
    
    def get_performing_branch_list(self):
        branches = [location.location for location in self.location_qs]

        branch_details_list = []
        for branch in branches:
            retail_system = self.retail_system_qs.filter(location__location=branch)
            agents_list = [system.retail_account for system in retail_system]
            supervisor_list = [system.supervisor for system in retail_system]
            location = self.location_qs.filter(location=branch).last()
        
            branches_details = {
                                "id": location.id, 
                                "branch": branch, 
                                "number_of_supervisors": len(supervisor_list), 
                                "number_of_agents": len(agents_list)
                                }
            branch_details_list.append(branches_details)
        data = {
            "branches": branch_details_list,
            "count": len(branch_details_list)
        }
        return data
    
    def get_under_performing_branch_list(self):
        branches = [location.location for location in self.location_qs]

        branch_details_list = []
        for branch in branches:
            retail_system = self.retail_system_qs.filter(location__location=branch)
            agents_list = [system.retail_account for system in retail_system]
            supervisor_list = [system.supervisor for system in retail_system]
            location = self.location_qs.filter(location=branch).last()
        
            branches_details = {
                                "id": location.id, "branch": branch, 
                                "number_of_supervisors": len(supervisor_list), 
                                "number_of_agents": len(agents_list)
                                }
            branch_details_list.append(branches_details)
        data = {
            "branches": branch_details_list,
            "count": len(branch_details_list)
        }
        return data
    
class BranchDetails:
    def __init__(self, request):
        self.week_start = DateUtility().week_start
        self.previous_week_start = DateUtility().previous_week_start
        self.previous_week_end = DateUtility().previous_week_end        
        self.month_start  = DateUtility().month_start     
        self.previous_month_start = DateUtility().previous_month_start
        self.previous_month_end = DateUtility().previous_month_end 
        self.year_start = DateUtility().year_start
        self.previous_year_start = DateUtility().previous_year_start
        self.previous_year_end = DateUtility().previous_year_end 
        self.previous_day = DateUtility().previous_day
        self.start_of_all_transactions = DateUtility().start_of_all_transactions
        self.previous_year_current_month_start = DateUtility().previous_year_current_month_start
        self.months_list_names = DateUtility().months_list_names
        self.year_months_tuple_list = DateUtility().year_months_tuple_list

        self.date_filter = DateUtility().get_date_filter(request=request)
        self.request = request

        self.location_qs = NewLocationList.objects.filter()
        self.retail_system = RetailSystem.objects.filter(
                            ~Q(retail_account__email__in=retail_system_email_exclude_list)
                            & Q(retail_account__in=funded_retail_accounts)
                            ).filter()
        retail_system_users = [system.retail_account for system in self.retail_system]
        self.ledger_qs = LedgerTableModel.objects.filter(
                        Q(user__in=retail_system_users,
                        transaction_type__in=all_possible_transaction_list) &
                        Q(user__in=funded_retail_accounts)
                        ).order_by("-date_created")
        self.successful_transactions_qs = self.ledger_qs.filter(
                                        transaction__status="SUCCESSFUL",
                                        transaction__is_reversed=False
                                        )

        self.cashout_qs = self.successful_transactions_qs.filter(
                          transaction_type__in=cashout_transaction_type_list)
        
        self.transfer_qs = self.successful_transactions_qs.filter(
                           transaction_type__in=transfer_transaction_type_list)
        
        self.inflow_qs = self.successful_transactions_qs.filter(
                         transaction_type__in=inflow_transactions_list)

    def get_branch_details(self, id):
        branch = self.location_qs.get(id=id)
        retail_system = self.retail_system.filter(location__location=branch)
        agents_list = retail_system.filter(retail_account__isnull=False)
        agents_qs_list = [system.retail_account for system in agents_list]
        supervisor_list = retail_system.filter(supervisor__isnull=False).distinct("supervisor")
        transactions_qs = self.successful_transactions_qs.filter(user__in=agents_qs_list)
        revenue_qs = transactions_qs.annotate(actual_charge = F("charge") - F("liberty_commission"))

        total_transactions = list(transactions_qs.aggregate(Sum("amount")).values())[0]
        total_commission = list(transactions_qs.aggregate(Sum("liberty_commission_sum")).values())[0]
        total_transfer = list(self.transfer_qs.aggregate(Sum("amount")).values())[0]
        today_commission = list(transactions_qs.filter(date_created__date=timezone.now().date()
                            ).aggregate(Sum("liberty_commission_sum")).values())[0]
        today_transaction = list(transactions_qs.filter(date_created__date=timezone.now().date()
                            ).aggregate(Sum("amount")).values())[0]
        today_withdrawal = list(self.cashout_qs.filter(date_created__date=timezone.now().date()
                           ).aggregate(Sum("amount")).values())[0]
        total_withdrawal = list(self.cashout_qs.aggregate(Sum("amount")).values())[0]
        today_transfer = list(self.transfer_qs.filter(date_created__date=timezone.now().date()
                        ).aggregate(Sum("amount")).values())[0]
        today_transactions_count = list(transactions_qs.filter(date_created__date=timezone.now().date()
                                   ).aggregate(Count("amount")).values())[0]
        transactions_count = list(transactions_qs.aggregate(Count("amount")).values())[0]
        total_revenue_amount = list(revenue_qs.aggregate(Sum("actual_charge")).values())[0]
        today_revenue_amount = list(revenue_qs.filter(date_created__date=timezone.now().date()
                               ).aggregate(Sum("actual_charge")).values())[0]
        
        # Active Agents
        active_agents = agents_list.filter(retail_account__terminal_status="ACTIVE")
        inactive_agents = agents_list.filter(retail_account__terminal_status="INACTIVE")
        partially_inactive_agents = agents_list.filter(retail_account__terminal_status="PARTIALLY_INACTIVE")
        suspended_agents = agents_list.filter(retail_account__is_suspended = True)
            
        data = {
            "branch_name": branch.location,
            "sub_location_num": branch.sub_location_num if branch.sub_location_num else 0,
            "supervisors": supervisor_list.count(),
            "agents": agents_list.count(),
            "performing_supervisors": 0,
            "non_performing_supervisors": 0,
            "active_agents": active_agents.count(),
            "inactive_agents": inactive_agents.count(),
            "partially_inactive_agents": partially_inactive_agents.count(),
            "suspened_agents": suspended_agents.count(),
            "total_transactions": total_transactions if total_transactions else 0.00,
            "total_commission": total_commission if total_commission else 0.00,
            "total_transfer": total_transfer if total_transfer else 0.00,
            "today_commission": today_commission if today_commission else 0.00,
            "today_transaction": today_transaction if today_transaction else 0.00,
            "today_withdrawal": today_withdrawal if today_withdrawal else 0.00,
            "total_withdrawal": total_withdrawal if total_withdrawal else 0.00,
            "today_transfer": today_transfer if today_transfer else 0.00,
            "today_transactions_count": today_transactions_count if today_transactions_count else 0,
            "transactions_count": transactions_count if transactions_count else 0,
            "total_revenue_amount": total_revenue_amount if total_revenue_amount else 0.00,
            "today_revenue_amount": today_revenue_amount if today_revenue_amount else 0.00
        }
        return data
    
    def get_branch_transaction_comparatives(self, id):
        branch = self.location_qs.get(id=id)
        retail_system = self.retail_system.filter(location__location=branch)
        agents_list = [system.retail_account for system in retail_system]        

        past_year_commissions_amt_qs = self.successful_transactions_qs.filter(user__in=agents_list, 
                                    date_created__gte=self.previous_year_current_month_start,
                                    ).filter(**self.date_filter).values("date_created__year", 
                                    "date_created__month").annotate(total_amount = Sum("liberty_commission_sum")                                                           
                                ).order_by("date_created__year")
        past_year_commissions_list = [
                                        (data["date_created__year"], data["date_created__month"], 
                                         data["total_amount"]) for 
                                        data in past_year_commissions_amt_qs
                                       ]
        
        # Transactions
        past_year_commission_final_list = []
        for item in self.year_months_tuple_list:
            if item in [(tup[0], tup[1]) for tup in past_year_commissions_list]:
                for trans in past_year_commissions_list:
                    if item == (trans[0], trans[1]):
                        past_year_commission_final_list.append(trans)
                        break
            else:
                past_year_commission_final_list.append((0, 0, 0))
        commission_amount_values_list = [trans[2] for trans in past_year_commission_final_list]

        # Withdrawals Values List        
        past_year_cashout_amt_qs = self.cashout_qs.filter(
                                    user__in=agents_list, 
                                    date_created__gte=self.previous_year_current_month_start
                                ).values("date_created__year", 
                                "date_created__month").annotate(total_amount=Sum("amount")                                                              
                                ).order_by("date_created__year")
        
        past_year_cashout_list = [
                                    (data["date_created__year"], data["date_created__month"], 
                                    data["total_amount"]) for 
                                    data in past_year_cashout_amt_qs
                                       ]
        
        past_year_cashout_final_list = []
        for item in self.year_months_tuple_list:
            if item in [(tup[0], tup[1]) for tup in past_year_cashout_list]:
                for trans in past_year_cashout_list:
                    if item == (trans[0], trans[1]):
                        past_year_cashout_final_list.append(trans)
                        break
            else:
                past_year_cashout_final_list.append((0, 0, 0))
        cashout_amount_values_list = [trans[2] for trans in past_year_cashout_final_list]

        # Transfer Values List        
        past_year_transfer_amt_qs = self.transfer_qs.filter(
                                    user__in=agents_list, 
                                    date_created__gte=self.previous_year_current_month_start
                                    ).values("date_created__year", "date_created__month"
                                    ).annotate(total_amount = Sum("amount")                                                                
                                    ).order_by("date_created__year")
        
        past_year_transfer_list = [
                                        (data["date_created__year"], data["date_created__month"], 
                                         data["total_amount"]) for 
                                        data in past_year_transfer_amt_qs
                                       ]
        
        past_year_transfer_final_list = []
        for item in self.year_months_tuple_list:
            if item in [(tup[0], tup[1]) for tup in past_year_transfer_list]:
                for trans in past_year_transfer_list:
                    if item == (trans[0], trans[1]):
                        past_year_transfer_final_list.append(trans)
                        break
            else:
                past_year_transfer_final_list.append((0, 0, 0))
        transfer_amount_values_list = [trans[2] for trans in past_year_transfer_final_list]
        data = {
            "months_list": self.months_list_names,
            "transfer_values": transfer_amount_values_list,
            "withdrawal_values": cashout_amount_values_list,
            "commission_values": commission_amount_values_list
        }
        return data
    
    def get_branch_top5_supervisors(self, id):
        branch = self.location_qs.get(id=id)
        retail_system = self.retail_system.filter(location__location=branch)
        supervisor_list = [system.supervisor for system in retail_system.distinct("supervisor")]

        supervisor_details_list = []
        for supervisor in supervisor_list:
            if supervisor:
                supervisor_agents = self.retail_system.filter(supervisor=supervisor) 
                transaction_amount = list(self.successful_transactions_qs.filter(user__in = supervisor_agents,
                                          **self.date_filter).aggregate(Sum("amount")).values())[0]
                data = {"id": supervisor.id, "location": branch.location, "supervisor": supervisor.get_full_name(),
                        "agents": supervisor_agents.count(), "rating":5, 
                        "transaction_amount": transaction_amount if transaction_amount else 0.00}
                supervisor_details_list.append(data)
            else:
                pass

        top_supervisors_list = sorted(supervisor_details_list, key=lambda d: d["transaction_amount"])
        top5_performing = top_supervisors_list[::-1][:5]
        top5_non_performing = top_supervisors_list[:5]
        
        data = {
            "top5_performing": top5_performing,
            "top5_non_performing": top5_non_performing
        }
        return data
    
    def get_branch_top_supervisors_ranking(self, id):
        branch = self.location_qs.get(id=id)
        retail_system = self.retail_system.filter(location__location=branch)
        supervisor_list = [system.supervisor for system in retail_system.distinct("supervisor")]

        supervisor_details_list = []
        for supervisor in supervisor_list:
            if supervisor:
                supervisor_agents = self.retail_system.filter(supervisor=supervisor)
                supervisors_agents_list = [system.retail_account for system in supervisor_agents] 
                transaction_amount = list(self.successful_transactions_qs.filter(user__in = supervisors_agents_list,
                                    **self.date_filter).aggregate(Sum("amount")).values())[0]
                data = {"id": supervisor.id, 
                        "location": branch.location, 
                        "supervisor": supervisor.get_full_name(),
                        "agents": supervisor_agents.count(), 
                        "rating":5, 
                        "transaction_amount": transaction_amount if transaction_amount else 0.00}
                supervisor_details_list.append(data)
            else:
                pass

        top_supervisors_list = sorted(supervisor_details_list, key=lambda d: d["transaction_amount"])
        top_performing = top_supervisors_list[::-1]
        top_non_performing = top_supervisors_list
        
        data = {
            "top5_performing": top_performing,
            "top5_non_performing": top_non_performing
        }
        return data
    
    def get_branch_supervisors_list(self, id):
        branch = self.location_qs.get(id=id)
        retail_system = self.retail_system.filter(location__location=branch)
        supervisor_list = retail_system.distinct("supervisor").values("supervisor")

        supervisors_list = []
        for supervisor in supervisor_list:
            if supervisor:
                user_supervisor = retail_system.filter(supervisor__id=supervisor["supervisor"]).last()
                supervisor_agents = self.retail_system.filter(supervisor=supervisor["supervisor"])
                supervisor_agents_list = [system.retail_account for system in supervisor_agents] 
                transaction_amount = list(self.successful_transactions_qs.filter(user__in = supervisor_agents_list
                                        ).aggregate(Sum("amount")).values())[0]

                data = {"id": supervisor["supervisor"], 
                        "branch": branch.location, 
                        "supervisor": user_supervisor.supervisor.get_full_name() if user_supervisor.supervisor else "",
                        "email": user_supervisor.supervisor.email if user_supervisor.supervisor else "", 
                        "agents": supervisor_agents.count(), 
                        "rating":5, 
                        "transaction_amount": transaction_amount if transaction_amount else 0.00}
                supervisors_list.append(data)
            else:
                pass

        data = {
            "supervisors_list": supervisors_list,
            "count": len(supervisors_list)
        }
        return data
    
    def get_supervisor_user_role(self):
        active_user = self.request.user
        retail_system = self.retail_system.distinct("supervisor")
        supervisors_list = [system.supervisor for system in retail_system]

        data = {
            "role": "supervisor" if active_user in supervisors_list and \
                active_user.is_staff == False else "admin" if \
                    active_user.is_staff else ""
        }
        return data
    
class SupervisorData:
    def __init__(self) -> None:
        self.agents_qs = User.objects.filter()
        self.retail_system_qs = RetailSystem.objects.filter(
                                ~Q(retail_account__email__in=retail_system_email_exclude_list)
                                & Q(retail_account__in=funded_retail_accounts)
                                ).filter()
        retail_system_users = [system.retail_account for system in self.retail_system_qs]            
        self.ledger_qs = LedgerTableModel.objects.filter(
                        Q(user__in=retail_system_users,
                        transaction_type__in=all_possible_transaction_list) &
                        Q(user__in=funded_retail_accounts)
                        ).order_by("-date_created")

        self.cashout_qs = self.ledger_qs.filter(transaction_type__in=cashout_transaction_type_list)
        self.transfer_qs = self.ledger_qs.filter(transaction_type__in=transfer_transaction_type_list)
        self.successful_transactions_qs = self.ledger_qs.filter(
                                          transaction__status="SUCCESSFUL", 
                                          transaction__is_reversed=False)

    def get_supervisor_data_overview(self, id):
        supervisor = self.agents_qs.get(id=id)

        supervisor_agents = self.retail_system_qs.filter(supervisor=supervisor)
        agents_list = [agent.retail_account for agent in supervisor_agents]

        supervisor_transactions = self.successful_transactions_qs.filter(user__in=agents_list)
        total_transaction = list(supervisor_transactions.aggregate(Sum("amount")).values())[0]
        total_commission = list(supervisor_transactions.aggregate(Sum("liberty_commission_sum")).values())[0]
        today_transaction = list(supervisor_transactions.filter(date_created__date=timezone.now().date()
                                                                ).aggregate(Sum("amount")).values())[0]
        today_commission = list(supervisor_transactions.filter(date_created__date=timezone.now().date()
                                                               ).aggregate(Sum("liberty_commission_sum")).values())[0]
        today_transfer = list(supervisor_transactions.filter(
                         date_created__date=timezone.now().date(), 
                         transaction__transaction_type__in=transfer_transaction_type_list
                         ).aggregate(Sum("amount")).values())[0]
        
        today_withdrawal = list(supervisor_transactions.filter(
                           date_created__date=timezone.now().date(), 
                           transaction__transaction_type__in=cashout_transaction_type_list
                           ).aggregate(Sum("amount")).values())[0]
        
        transactions_count = list(supervisor_transactions.aggregate(Count("amount")).values())[0]
        today_transactions_count = list(supervisor_transactions.filter(
                                   date_created__date=timezone.now().date()
                                   ).aggregate(Count("amount")).values())[0]
        
        active_agents = supervisor_agents.filter(retail_account__terminal_status="ACTIVE").count()
        inactive_agents = supervisor_agents.filter(retail_account__terminal_status="INACTIVE").count()

        # Revenue    
        total_revenue_amount = list(supervisor_transactions.aggregate(Sum("charge")).values())[0]
        total_revenue_amount_today = list(supervisor_transactions.filter(date_created__date=timezone.now().date()
                                    ).aggregate(Sum("charge")).values())[0]        

        data = {
            "agents": supervisor_agents.count(),
            "total_transaction": total_transaction if total_transaction else 0.00,
            "total_commission": total_commission if total_commission else 0.00,
            "today_transaction": today_transaction if today_transaction else 0.00,
            "today_commission": today_commission if today_commission else 0.00, 
            "today_transfer": today_transfer if today_transfer else 0.00,
            "today_withdrawal": today_withdrawal if today_withdrawal else 0.00,
            "transactions_count": transactions_count if transactions_count else 0,
            "today_transactions_count": today_transactions_count if today_transactions_count else 0,
            "active_agent": active_agents,
            "inactive_agent": inactive_agents,
            "total_revenue_amount": total_revenue_amount if total_revenue_amount else 0.00,
            "total_revenue_amount_today": total_revenue_amount_today if total_revenue_amount_today else 0.00
        }
        return data
    
    def get_supervisor_data_agents_list(self, id):
        supervisor = self.agents_qs.get(id=id)

        supervisor_agents = self.retail_system_qs.filter(supervisor=supervisor)
        agents_list = []

        for agent in supervisor_agents:
            transaction_amount = list(self.ledger_qs.filter(user=agent.retail_account
                                ).aggregate(Sum("amount")).values())[0]
            transaction_count = list(self.ledger_qs.filter(user=agent.retail_account
                                ).aggregate(Count("amount")).values())[0]
            
            agent_details = {
                        "agent_id":agent.retail_account.id, 
                        "agent_name": agent.retail_account.get_full_name(), 
                        "email": agent.retail_account.email, 
                        "transaction_count": transaction_count if transaction_count else 0.00, 
                        "transaction_amount": transaction_amount if transaction_amount else 0}
            agents_list.append(agent_details)
        
        data = {
            "agents_list": agents_list,
            "count": len(agents_list)
        }
        return data