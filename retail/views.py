from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status, generics
from retail.services import (Maindashboard, MainDashboardCharts, 
                             Agent, AgentDetails, LeaderBoard, Branch,
                             BranchDetails, SupervisorData
                             )
from retail.models import RetailSystem, SendAgentsSmsDumpData
from retail.serializers import AssignTerminalSerializer, SendSmsListSerializer
from retail.permissions import CreateBranchPermission
from admin_dashboard.helpers.helpers import send_pos_agent_email
from main.models import NewLocationList
from main.permissions import RetailIsAuthenticated
from horizon_pay.models import TerminalSerialTable

from django.contrib.auth import get_user_model
User = get_user_model()


class MainDashboardTransactionsMetricsView(APIView):
    permission_classes = [RetailIsAuthenticated]

    def get(self, request):
        try:
            data = Maindashboard(request).get_transactions_overview_one()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardMetricsTwoView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = Maindashboard(request).get_transactions_overview_two()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardUtilitiesOverviewView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = Maindashboard(request).get_utilities_overview()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardChartsView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = MainDashboardCharts(request).get_charts()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class MainDashboardAgentsOverView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = Maindashboard(request).get_agents_overview()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class MainDashboardRevenueOverView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = Maindashboard(request).get_revenue_overview()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

class AgentOverviewView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = Agent(request).get_agent_overview()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

class TopPerformingAgentsView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = Agent(request).get_top_agents()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

class AgentsListView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = Agent(request).get_agents_table()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class ActiveAgentsListView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = Agent(request).get_active_agents_table()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
 
        
class InactiveAgentsListView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = Agent(request).get_inactive_agents_table()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class SuspendedAgentsListView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = Agent(request).get_suspended_agents_table()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

class AgentsDetailsOverView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request, id):
        try:
            data = AgentDetails(request).get_agent_detail(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AgentsWalletDetailsView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request, id):
        try:
            data = AgentDetails(request).get_agent_wallets(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AgentsTransactionsView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request, id):
        try:
            data = AgentDetails(request).get_agent_transactions(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class AgentsRevenueDetailsView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request, id):
        try:
            data = AgentDetails(request).get_agent_revenue_overview(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class TransactionsDetailView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request, id):
        try:
            data = AgentDetails(request).get_transaction_detail(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AgentsActiveSessionsView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request, id):
        try:
            data = AgentDetails(request).get_agent_active_session(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class LeaderBoardTop20Amount24HoursView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = LeaderBoard(request).get_top20_amount_24_hours()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class LeaderBoardTop20AmountRangeView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = LeaderBoard(request).get_top20_amount_date_range()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class LeaderBoardTop20Count24HoursView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = LeaderBoard(request).get_top20_count_24_hours()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class LeaderBoardTop20CountRangeView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = LeaderBoard(request).get_top20_count_date_range()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        
class AgentsLogHistoryListView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = LeaderBoard(request).get_agents_log_history()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BranchOverviewView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = Branch(request).get_branch_overview()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BranchChartsView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = Branch(request).get_branch_charts()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BranchTop5View(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = Branch(request).get_top5_branches()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BranchListView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = Branch(request).get_branch_list()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        
        
class PerformingBranchListView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = Branch(request).get_performing_branch_list()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

class UnderPerformingBranchListView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request):
        try:
            data = Branch(request).get_under_performing_branch_list()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BranchDetailsView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request, id):
        try:
            data = BranchDetails(request).get_branch_details(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BranchTransactionComparativesView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request, id):
        try:
            data = BranchDetails(request).get_branch_transaction_comparatives(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BranchTopSupervisorsView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request, id):
        try:
            data = BranchDetails(request).get_branch_top_supervisors_ranking(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class BranchTopSupervisorsRankingView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request, id):
        try:
            data = BranchDetails(request).get_branch_top_supervisors_ranking(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BranchSupervisorsListView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request, id):
        try:
            data = BranchDetails(request).get_branch_supervisors_list(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SupervisorDataOverviewView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request, id):
        try:
            data = SupervisorData().get_supervisor_data_overview(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SupervisorDataAgentsListView(APIView):
    permission_classes = [RetailIsAuthenticated]
    def get(self, request, id):
        try:
            data = SupervisorData().get_supervisor_data_agents_list(id=id)
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class CreateBranchView(generics.CreateAPIView):
    queryset = NewLocationList
    permission_classes = [RetailIsAuthenticated, CreateBranchPermission]

    def post(self, request):
        branch = request.data.get("branch_name")
        if branch:
            existing_branch = NewLocationList.objects.filter(location__icontains=branch).last()
            if existing_branch:
                return Response({"error": "Branch already exists"}, status=status.HTTP_400_BAD_REQUEST)
            else:
                bulk_create_list = [NewLocationList(location=branch, created_by=request.user)]
                NewLocationList.objects.bulk_create(bulk_create_list)
                return Response({"message": "Branch created successfully"}, status=status.HTTP_201_CREATED)
        else:
            return Response({"error": "Branch name cannot be empty"}, status=status.HTTP_400_BAD_REQUEST)


class AssignTerminalToRetailAgent(generics.CreateAPIView):
    serializer_class = AssignTerminalSerializer

    def post(self, request):
        serializer = self.serializer_class(data = request.data)

        if serializer.is_valid(raise_exception=True):
            terminal_id = serializer.validated_data["terminal_id"]
            supervisor_email = serializer.validated_data["supervisor_email"]
            agent_email = serializer.validated_data["agent_email"]

            terminal_id_in_table = TerminalSerialTable.objects.filter(terminal_id=terminal_id).last()
            terminal_already_assigned = User.objects.filter(terminal_id=terminal_id)

            
            if terminal_id_in_table and not terminal_already_assigned:
                user = User.objects.filter(email=agent_email).last()
                retail_user = RetailSystem.objects.filter(user_assigned=user).last()
                supervisor = User.objects.filter(email=supervisor_email)
                if retail_user and supervisor:
                    retail_user.user_assigned.terminal_id = terminal_id
                    retail_user.supervisor = supervisor
                    user.save()
                else:
                    return Response({"error": "Retail account does not exist"}, status = status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"error": "This Terminal ID is not available"}, status = status.HTTP_400_BAD_REQUEST)            
            return Response({"message": f"Terminal assigned to user: {agent_email}"}, status = status.HTTP_200_OK)


class SupervisorRoleView(APIView):
    permission_classes = [RetailIsAuthenticated]
    
    def get(self, request):
        try:
            data = BranchDetails(request).get_supervisor_user_role()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SendSmsToAgents(APIView):
    permission_classes = [RetailIsAuthenticated]
    
    def post(self, request):
        try:
            data = Agent(request).send_sms_to_agents()
            return Response({"data": data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

class SendAgentSmsDumpDataListView(generics.ListAPIView):
    permission_classes = [RetailIsAuthenticated]
    serializer_class = SendSmsListSerializer
    queryset = SendAgentsSmsDumpData.objects.all()


class WhatsAppCallbackView(APIView):
    # permission_classes = []

    def get(self, request):
        """
        This view is for a WhatsApp 
        webhook service
        """
        whatsapp_challenge = request.query_params.get("c")
        if whatsapp_challenge:
            challenge = whatsapp_challenge
        else:
            challenge = "30452104"

        send_pos_agent_email(
                    email = "<EMAIL>",
                    files = "",
                    template="pos_agent_email.html",
                    email_subject="WhatsApp Callback Works",
                    message="WhatsApp callback works!!!!!!!!!!!!!!!!!!!!!!!!!"
                )

        return Response(challenge)


class AjoUsersDetailsApiView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        ajo_users = get_user_model().objects.filter(type_of_user="AJO_AGENT")

        ajo_users_list = []
        try:
            for user in ajo_users:
                data = {
                    "email": user.email,
                    "type_of_user": user.type_of_user,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "customer_id": user.customer_id,
                    "terminal_id": user.terminal_id,
                    "terminal_status": user.terminal_status
                    }
                ajo_users_list.append(data)
            return Response({"data": ajo_users_list, "count": len(ajo_users_list)}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
