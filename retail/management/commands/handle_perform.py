from django.db.models import Sum
from django.conf import settings
from django.core.management.base import BaseCommand
from retail.models import LedgerTableModel, RetailSystem, RetailPerformance
from main.models import ConstantTable, User, UserFlag
from main.helper.helper_function import check_if_is_sunday

from datetime import datetime, timedelta



class Command(BaseCommand):
    help = ""

    
    def handle(self, *args, **kwargs):
        today = datetime.now().date()
        yesterday = today - timedelta(days=1)


        retail_system_email_exclude_list = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
        retail_system_qs = RetailSystem.objects.exclude(retail_account__email__in = retail_system_email_exclude_list).filter(user_assigned__isnull = False)

        for retail_system in retail_system_qs:

            RetailPerformance.calculate_daily_performance(retail_acct=retail_system)
