from django.db.models import Sum
from django.conf import settings
from django.core.management.base import BaseCommand, CommandError
from retail.models import LedgerTableModel, RetailSystem
from main.models import ConstantTable, User, UserFlag
from main.helper.helper_function import check_if_is_sunday

from datetime import datetime, timedelta



class Command(BaseCommand):
    help = ""


    def add_arguments(self, parser):
        parser.add_argument('--user', dest='arg4', type=int, help='user_id.')


    
    def handle(self, *args, **options):
        
        today = datetime.now().date()
        yesterday = today - timedelta(days=1)

        user_id = options.get('arg4')
        self.stdout.write(f'Argument 2: {user_id}')


        if user_id:

            try:
                user = User.objects.get(id=int(user_id))
                LedgerTableModel.handle_data_reconciliation(user=user)
            except Exception as err:
                raise CommandError(f'Error: {err}')
        
        else:

            try:
                if settings.ENVIRONMENT == "development":
                    main_user = User.objects.get(email="<EMAIL>")
                else:
                    main_user = User.objects.get(email="<EMAIL>")
            except:
                return

            for user in User.objects.exclude(email=main_user.email).filter(type_of_user="LIBERTY_RETAIL"):

                LedgerTableModel.handle_data_reconciliation(user=user)

        return
