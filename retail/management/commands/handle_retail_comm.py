from django.db.models import Sum, Q
from django.conf import settings
from django.core.management.base import BaseCommand
from retail.models import LedgerTableModel, RetailSystem, RetailPerformance
from main.models import ConstantTable, User, UserFlag
from main.helper.helper_function import check_if_is_sunday

from datetime import datetime, timedelta



class Command(BaseCommand):
    help = ""

    
    def handle(self, *args, **kwargs):
        today = datetime.now().date()
        yesterday = today - timedelta(days=1)
        
        main_user = RetailSystem.get_service_user()

        retail_system_email_exclude_list = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
        retail_system_qs = RetailSystem.objects.filter(~Q(retail_account__email__in=retail_system_email_exclude_list)).filter(user_assigned__isnull = False)

        # for user in User.objects.exclude(email=main_user.email).filter(type_of_user="LIBERTY_RETAIL"):
        for retail_system in retail_system_qs:

            user = retail_system.retail_account
                    
            last_ledger_inst = LedgerTableModel.get_last_object(user=user)

            # transactions_needed = LedgerTableModel.objects.filter(user=user, date_created__date=yesterday)
            # retail_system = RetailSystem.objects.filter(retail_account=user).last()

            # print(last_ledger_inst)
            # print(retail_system)

            if last_ledger_inst:
                # user.terminal_disabled = True
                # user.save()
                
                # if last_ledger_inst.charge_sum != retail_system.available_balance:
                #     user_flag = UserFlag.create_suspension_instance(
                #         user=user, reason="Charges Sum do no match retail system available balance",
                #         is_suspended=True
                #     )

                # else:

                debit_amount = last_ledger_inst.charge_sum - last_ledger_inst.liberty_commission_sum
                if debit_amount > 0:
                    RetailSystem.handle_retail_auto_debit(
                        user=user,
                        service_user=main_user,
                        amount=debit_amount,
                        retail_system=retail_system
                    )
            
            else:
                debit_amount = 0



        


            # check_for_sunday = check_if_is_sunday(yesterday)

            # if not check_if_is_sunday(yesterday):

            #     performance_transactions_qs = transactions_needed.filter(transaction_type__in=["SEND_BANK_TRANSFER", "CARD_TRANSACTION_FUND", "CARD_TRANSACTION_FUND_TRANSFER"])
            #     if performance_transactions_qs.count() < 5 or (performance_transactions_qs.exists() and performance_transactions_qs.aggregate(total_amount=Sum('amount'))['total_amount'] or 0) < 10000:
            #         user_flag_two = UserFlag.create_suspension_instance(
            #             user=user, reason="User Did Not Meet up with 16 Transaction Count of N80,000",
            #             is_suspended=True
            #         )







