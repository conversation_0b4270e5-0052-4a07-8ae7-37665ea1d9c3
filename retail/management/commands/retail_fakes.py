from django.db.models import Sum, Q
from django.conf import settings
from django.core.management.base import BaseCommand
from retail.models import LedgerTableModel, RetailSystem, RetailPerformance
from main.models import ConstantTable, User, UserFlag
from main.helper.helper_function import check_if_is_sunday
from accounts.models import DebitCreditRecordOnAccount

from datetime import datetime, timedelta

from retail.models import *




class Command(BaseCommand):
    help = ""

    
    def handle(self, *args, **kwargs):
        today = datetime.now().date()
        yesterday = today - timedelta(days=2)

        for data in UserDebt.objects.filter(unique_id__in=["c92593c8-b2f3-49fb-95fb-c78ea7701bc6", "8599fead-07f7-45a2-b092-9895177e8093"]):
            debit_credit_record = DebitCreditRecordOnAccount.objects.get(unique_reference=data.unique_id)

            deduct_balance = {
                "debit_credit_record_id": debit_credit_record.id,
                "balance_before": debit_credit_record.balance_before,
                "balance_after": debit_credit_record.balance_after
            }

            kwargs = {
                "user": data.user,
                "agent_wallet_instance": data.wallet,
                "amount": debit_credit_record.amount,
                "trans_type": "RETAIL_AUTO_DEBIT", 
                "unique_id": data.unique_id 
            }
            RetailSystem.debit_account_for_retail_comm(kwargs=kwargs, deduct_balance=deduct_balance)
            
        
        # base_instances = LedgerTableModel.objects.exclude(transaction__transaction_id__in=["d126b2ad-eaa5-483c-94f3-8d702caa9597"]).filter(date_created__gte=yesterday, transaction_type="RETAIL_AUTO_DEBIT")
        
        # distinct_inst = base_instances.values('user').distinct()

        
        # for inst in distinct_inst:
        #     user = inst['user']
        #     latest_retail_auto_debit = base_instances.filter(
        #         user=inst['user'],
        #         charge_sum__gt=0
        #     )

        #     if latest_retail_auto_debit:
        #         all_charge_sum = latest_retail_auto_debit.aggregate(Sum("charge_sum"))["charge_sum__sum"]
        #         all_liberty_commission_sum = latest_retail_auto_debit.aggregate(Sum("liberty_commission_sum"))["liberty_commission_sum__sum"]

                    
        #         get_last_object = LedgerTableModel.get_last_object(user=user)
                
        #         get_last_object.charge_sum -= all_charge_sum
        #         get_last_object.liberty_commission_sum -= all_liberty_commission_sum
        #         get_last_object.save()



        #     instances_list.append({inst['user']: {"charge": all_charge_sum, "comm": all_liberty_commission_sum}})
        # for user, instances in instances_list.items():

        # try:
        #     if settings.ENVIRONMENT == "development":
        #     else:
        #         main_user = User.objects.get(email="<EMAIL>")
        # except:
        #     return
        
        # all_retail_users = User.objects.exclude(email=main_user.email).filter(type_of_user="LIBERTY_RETAIL")


        # # Get a queryset of user IDs who have transactions with type 'RETAIL_AUTO_DEBIT'
        # users_with_retail_auto_debit = LedgerTableModel.objects.filter(transaction__transaction_type='RETAIL_AUTO_DEBIT').values('user')

        # # Get all transactions by users who don't have 'RETAIL_AUTO_DEBIT' transactions
        # users_with_other_transactions = LedgerTableModel.objects.exclude(
        #     Q(user__in=Subquery(users_with_retail_auto_debit))
        # ).values('user').distinct()

        # # Retrieve the User objects for users with other transactions
        # users = all_retail_users.filter(pk__in=Subquery(users_with_other_transactions))


        # print(users)

        # for user in all_retail_users:

        # get_users_without_retail_debit = LedgerTableModel.objects.filter()

                    

        #     transactions_needed = LedgerTableModel.objects.filter(user=user, date_created__date=yesterday)
        #     last_ledger_inst = LedgerTableModel.get_last_object(user=user)
        #     retail_system = RetailSystem.objects.filter(retail_account=user).last()

        #     # print(last_ledger_inst)
        #     # print(retail_system)

        #     if last_ledger_inst and retail_system:
        #         # user.terminal_disabled = True
        #         # user.save()
                
        #         # if last_ledger_inst.charge_sum != retail_system.available_balance:
        #         #     user_flag = UserFlag.create_suspension_instance(
        #         #         user=user, reason="Charges Sum do no match retail system available balance",
        #         #         is_suspended=True
        #         #     )

        #         # else:

        #         debit_amount = last_ledger_inst.charge_sum - last_ledger_inst.liberty_commission_sum
        #         if debit_amount > 0:
        #             RetailSystem.handle_retail_auto_debit(
        #                 user=user,
        #                 service_user=main_user,
        #                 amount=debit_amount,
        #                 retail_system=retail_system
        #             )
            
        #     else:
        #         debit_amount = 0



        


        #     check_for_sunday = check_if_is_sunday(yesterday)

        #     if not check_if_is_sunday(yesterday):

        #         performance_transactions_qs = transactions_needed.filter(transaction_type__in=["SEND_BANK_TRANSFER", "CARD_TRANSACTION_FUND", "CARD_TRANSACTION_FUND_TRANSFER"])
        #         if performance_transactions_qs.count() < 5 or (performance_transactions_qs.exists() and performance_transactions_qs.aggregate(total_amount=Sum('amount'))['total_amount'] or 0) < 10000:
        #             user_flag_two = UserFlag.create_suspension_instance(
        #                 user=user, reason="User Did Not Meet up with 16 Transaction Count of N80,000",
        #                 is_suspended=True
        #             )


        #     # RetailPerformance.calculate_daily_performance()





