from django.db.models import Sum, Q
from django.conf import settings
from django.core.management.base import BaseCommand, CommandError
from retail.models import LedgerTableModel, RetailSystem, LibertyRetailCharge
from accounts.models import Transaction
from main.models import ConstantTable, User, UserFlag
from main.helper.helper_function import check_if_is_sunday

from datetime import datetime, timedelta

from django.db.models import OuterRef, Subquery




class Command(BaseCommand):
    help = ""

    
    def add_arguments(self, parser):
        parser.add_argument('--user', dest='arg', type=int, help='user_id.')
        parser.add_argument('--fromday', dest='arg1', type=str)
        parser.add_argument('--to_day', dest='arg2', type=str)


    def handle(self, *args, **options):
        fromday = options.get('arg1')
        to_day = options.get('arg2')

        # Your custom logic goes here
        if fromday:
            try:
                fromday = datetime.strptime(fromday, "%d-%m-%Y").date()
            except ValueError:
                raise CommandError('Invalid Date Format. Use DD-MM-YYYY')
        
        if to_day:
            try:
                to_day = datetime.strptime(to_day, "%d-%m-%Y").date()
            except ValueError:
                raise CommandError('Invalid Date Format. Use DD-MM-YYYY')
        else:
            to_day = datetime.now().date()


            
        user_id = options.get('arg')

        # Your custom logic goes here
        self.stdout.write(f'Argument 1: {user_id}')
        self.stdout.write(f'Argument 2: {fromday}')
        self.stdout.write(f'Argument 3: {to_day}')

        if not user_id:
            raise CommandError('Missing User ID.')
        
        try:
            user = User.objects.get(id=int(user_id))
        except Exception as err:
            raise CommandError(f'Error: {err}')


        self.stdout.write(f'User: {user.email}')



        if user.type_of_user in ["LIBERTY_RETAIL"]:
            charge_sum = 0

            data_qs = LedgerTableModel.objects.filter(user=user, date_created__gte=fromday, date_created__lte=to_day)


            for i in data_qs:
                amount = i.amount
                transaction_instance = i.transaction

                today = transaction_instance.date_created
                liberty_commission = transaction_instance.liberty_commission
                first_trans = Transaction.objects.filter(user=user, date_created__date=today,  transaction_type__in=["SEND_BANK_TRANSFER"], transaction_leg="INTERNAL", status="SUCCESSFUL", is_reversed=False).first()

                if first_trans == transaction_instance:
                    charge = -liberty_commission
                else:
                    if settings.ENVIRONMENT == "development":
                        charge = LedgerTableModel.get_ledger_transaction_charge(transaction_instance=transaction_instance)
                        # charge = LibertyRetailCharge.calculate_pos_charges(amount, user, transaction_instance)
                    else:
                        charge = LibertyRetailCharge.calculate_pos_charges(amount, user, transaction_instance)
            



                charge_sum += charge

                i.charge = charge
                i.charge_sum = charge_sum
                i.force_input = True
                i.save()


