from django.core.management.base import BaseCommand
from django.conf import settings

from retail.tasks import run_retail_agents_performance_report
from admin_dashboard.helpers.helpers import send_agent_activity_email_task

import os
from pathlib import Path
from datetime import datetime


class Command(BaseCommand):
    def handle(self, *args, **kwargs):
        run_retail_agents_performance_report()