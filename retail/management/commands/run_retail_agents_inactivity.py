from django.core.management.base import BaseCommand
from django.conf import settings

from retail.tasks import run_retail_agent_previous_day_check
from admin_dashboard.helpers.helpers import send_agent_activity_email_task

import os
from pathlib import Path
from datetime import datetime


class Command(BaseCommand):
    def handle(self, *args, **kwargs):
        """Command to resend retail agents activities email that 
        fail to deliver"""
        # run_retail_agent_inactivity_check()
        send_retail_agents_inactivity_check_email()

        # file_path = os.path.join(settings.BASE_DIR, 'media/retail/') 
        # last_file = os.path.join(f"{file_path}Inactive Retail Agents_{datetime.now().date()}.xlsx") 
        # with open(last_file, "rb") as excel_file:
        #     agent_activities = excel_file.read()
        #     excel_file.close()        

        # if not settings.ENVIRONMENT == "development":
        #     agent_activity_email_list = [
        #                         "<EMAIL>",
        #                         "<EMAIL>",
        #                         "<EMAIL>",
        #                         "<EMAIL>",
        #                         "<EMAIL>",
        #                         "<EMAIL>",
        #                         "<EMAIL>",
        #                         "<EMAIL>",
        #                         "<EMAIL>"
        #                         ]
        # else:
        #     agent_activity_email_list = [
        #                     "<EMAIL>",
        #                     "<EMAIL>"
        #                     ]

        # for email in agent_activity_email_list:             
        #     send_agent_activity_email_task(
        #         message = "This is a list of inactive retail agents for the past day",
        #         file = agent_activities,
        #         file_name = f"Inactive Retail Agents_{datetime.now().date()}.xlsx",
        #         email_subject = "Retail Agents Inactivity List",
        #         email=email
        #         )  
        run_retail_agent_previous_day_check()
        return "DONE"