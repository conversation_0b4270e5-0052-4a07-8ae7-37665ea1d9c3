from rest_framework import permissions




class CreateBranchPermission(permissions.IsAuthenticated):
    def has_permission(self, request, view):
        user = request.user
        create_branch_group_name = "libertyretailcreateaccess"
        user_groups = [group.name for group in user.groups.all()]

        if create_branch_group_name in user_groups:
            return True
        else:
            return False