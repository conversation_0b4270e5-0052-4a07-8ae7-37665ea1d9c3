from django.db import models, transaction
from django.db.models import Sum, Case, When, F, Func
from django.utils import timezone

from django.core.validators import MinValueValidator, MaxValueValidator
from django.core.exceptions import ValidationError
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AnonymousUser


from main.models import User, OtherServiceDetail, ConstantTable, NewLocationList
from accounts.models import Transaction, WalletSystem, AccountSystem, DebitCreditRecordOnAccount, OtherServiceAccountSystem, UserDebt, BillsPaymentConstant, credit_list, debit_list
from horizon_pay.models import StartCashOut<PERSON>ran
from datetime import datetime, timedelta, date
import uuid
# Create your models here.






class LibertyRetailCharge(models.Model):

    NEPA_TRANS_TYPES = list(BillsPaymentConstant.objects.filter(bills_type="ELECTRICITY").values_list("biller", flat=True))
    BETTING_TRANS_TYPES = list(BillsPaymentConstant.objects.filter(bills_type="BETTING").values_list("biller", flat=True))
    CABLE_TRANS_TYPES = list(BillsPaymentConstant.objects.filter(bills_type="CABLE_TV").values_list("biller", flat=True))
    
    TRANS_CHOICES = [
        (item, item)
        for item in (credit_list + debit_list + NEPA_TRANS_TYPES + BETTING_TRANS_TYPES + CABLE_TRANS_TYPES)
    ]

    band = models.PositiveSmallIntegerField(default=1)
    transaction_type = models.CharField(max_length=200)
    lower_limit = models.FloatField()
    upper_limit = models.FloatField()
    location = models.ForeignKey(NewLocationList, on_delete=models.SET_NULL, blank=True, null=True)
    spare_lower_limit = models.FloatField(default=0)
    spare_upper_limit = models.FloatField(default=0)
    charge_value = models.FloatField(null=True, blank=True)
    spare_charge_value = models.FloatField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def calculate_pos_charges(cls, amount, user: User, transaction_inst: Transaction):

        if transaction_inst.transaction_type == "CARD_TRANSACTION_FUND_TRANSFER":
            new_transaction_type = "CARD_TRANSACTION_FUND"
        
        elif transaction_inst.transaction_type == "BILLS_AND_PAYMENT":
            new_transaction_type = transaction_inst.transaction_sub_type

        else:
            new_transaction_type = transaction_inst.transaction_type


        retail_system = getattr(user, 'retail_system', None)
        location_fee = cls.objects.filter(transaction_type=new_transaction_type, spare_upper_limit__gte=amount, spare_lower_limit__lte=amount, location__isnull=False, location=retail_system.location).last()
        get_fee = cls.objects.filter(transaction_type=new_transaction_type, upper_limit__gte=amount, lower_limit__lte=amount).last()
        

        # logger.info(f"{location_fee}")
        # logger.info(f"{get_fee}")


        # print(location_fee)
        # print(get_fee)

        if new_transaction_type == "CARD_PURCHASE":
            charge = -transaction_inst.liberty_commission if transaction_inst.liberty_commission > 0 else 0

        if location_fee: 
            charge = location_fee.spare_charge_value

        elif get_fee: 
            charge = get_fee.charge_value

        else:
            # Change this to reverse amount collected
            if "REVERSAL" in transaction_inst.transaction_type:
                if transaction_inst.transaction_type in ["REVERSAL_BANK_TRANSFER_IN", "REVERSAL_BANK_TRANSFER"]:
                    get_fee = cls.objects.filter(transaction_type="SEND_BANK_TRANSFER", upper_limit__gte=amount, lower_limit__lte=amount).last()
                else:
                    extracted_reversal = transaction_inst.transaction_type.replace("_REVERSAL", "")
                    get_fee = cls.objects.filter(transaction_type=extracted_reversal, upper_limit__gte=amount, lower_limit__lte=amount).last()
                
                if get_fee: 
                    charge = -get_fee.charge_value
            
                else:
                    charge = 0
            else:
                charge = 0
            
        get_other_account_det = OtherServiceAccountSystem.objects.filter(user=transaction_inst.user, account_number=transaction_inst.beneficiary_nuban, requested_by__email="<EMAIL>").first()
        
        if transaction_inst.transaction_type == "FUND_BANK_TRANSFER" and get_other_account_det:
            charge = 0


        return charge




class LedgerTableModel(models.Model):

    CHARGE_TYPE_CHOICES = [
        ("BANK", "BANK"),
        ("CASH", "CASH"),
        ("SMS_CHARGE", "SMS_CHARGE"),
        ("MANUAL", "MANUAL"),
        ("AUTO_DEBIT_CHARGE", "AUTO_DEBIT_CHARGE"),
    ]

    date_created = models.DateTimeField(auto_now_add=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True)
    transaction = models.ForeignKey(Transaction, on_delete=models.CASCADE, null=True, blank=True)
    beneficiary = models.CharField(max_length=150, null=True, blank=True)
    transaction_type = models.CharField(max_length=200, null=True, blank=True)
    amount = models.FloatField(default=0.00)
    deposit = models.BooleanField(default=False)
    withdrawal = models.BooleanField(default=False)
    charge_type = models.CharField(max_length=150, choices=CHARGE_TYPE_CHOICES, null=True, blank=True)
    charge = models.FloatField(default=0.00)
    charge_sum = models.FloatField(default=0.00)
    liberty_commission = models.FloatField(default=0.00)
    liberty_commission_sum = models.FloatField(default=0.00)
    cash_in_hand = models.FloatField(default=0.00)
    cash_at_bank = models.FloatField(default=0.00)
    cash_available_total = models.FloatField(default=0.00)
    remark = models.CharField(max_length=300, null=True, blank=True)
    pos_type = models.CharField(max_length=300, null=True, blank=True)
    cash_in = models.FloatField(null=True, blank=True)
    transfer_in = models.FloatField(null=True, blank=True)
    transfer_out = models.FloatField(null=True, blank=True)
    manual_cih_input = models.BooleanField(default=False)
    manual_cab_input = models.BooleanField(default=False)
    entered_charge = models.FloatField(default=0.00)
    mismatch = models.BooleanField(default=False)
    error_id = models.CharField(max_length=250, blank=True, null=True)
    error_msg = models.TextField(blank=True, null=True)
    force_input = models.BooleanField(default=False)
    reconciled = models.BooleanField(default=False)
    checked_for_reconciliation = models.BooleanField(default=False)
    unique_id = models.CharField(max_length=350, null=True, blank=True)
    actual_transaction_time = models.DateTimeField(null=True, blank=True)
    last_updated = models.DateTimeField(auto_now=True)


    @classmethod
    def get_unique_id(cls,):
        ref = uuid.uuid4()
        if cls.objects.filter(unique_id=ref):
            return cls.get_unique_id()
        return ref


    def clean(self):
        pass
#         if self.cash_at_bank and self.cash_in_hand:
#             raise ValidationError('You must select either manual cash in hand or manual cash at bank')
        
#         if not self.user and not self.transaction and not self.error_id and not self.manual_cab_input and not self.manual_cih_input:
#             raise ValidationError('You must enter either transation or error id or select manual cash in hand or manual cash at bank')

#         if not self.user and (self.manual_cab_input or self.manual_cih_input):
#             raise ValidationError("You must enter user if you're selecting either manual cash in hand or manual cash at bank")
        
    @classmethod
    def get_last_object(cls, user):

        user_today_qs = cls.objects.filter(user=user)
        return user_today_qs.last() if user_today_qs else None


    # Handle Cash at bank

    @classmethod
    def handle_cash_at_bank(cls, user, transaction_instance: Transaction, wallet: WalletSystem, last_object: 'LedgerTableModel', return_wallet=False):

        if last_object and last_object.mismatch and not return_wallet:
            if transaction_instance.transaction_type in debit_list:
                cash_at_bank = last_object.cash_at_bank - (transaction_instance.amount + transaction_instance.liberty_commission)
            else:
                cash_at_bank = last_object.cash_at_bank + (transaction_instance.amount - transaction_instance.liberty_commission)

        else:
            cash_at_bank = wallet.available_balance

        return cash_at_bank


    # Handle Cash in Hand
    @classmethod
    def get_charge_type(cls, transaction_instance: Transaction):
        get_start_of_trans = StartCashOutTran.objects.filter(rrn=transaction_instance.unique_reference).last()
        if get_start_of_trans:
            charge = get_start_of_trans.ledger_commission
            charge_type = get_start_of_trans.commission_type
        else:
            charge = 0
            charge_type = "CASH"

        return charge, charge_type


    # Handle Cash in Hand
    @classmethod
    def handle_cash_in_hand(cls, user, transaction_instance: Transaction, charge_collected, charge_type, last_object: 'LedgerTableModel'):
        if not charge_collected:
            charge_collected = 0

        if transaction_instance.transaction_type == "RETAIL_AUTO_DEBIT":
            cash_in_hand = last_object.cash_in_hand if last_object else 0
        else:

            if transaction_instance.transaction_type in debit_list:
                # if charge_type == "BANK":
                #     hold_amount = transaction_instance.amount
                # else:
                hold_amount = transaction_instance.amount + charge_collected

                cash_in_hand = last_object.cash_in_hand + hold_amount if last_object else hold_amount

            else:
                if transaction_instance.transaction_type in ["CARD_TRANSACTION_FUND", "CARD_TRANSACTION_FUND_TRANSFER"]:
                    if charge_type == "BANK":
                        give_amount = -abs(transaction_instance.amount - charge_collected)
                    else:
                        give_amount = -transaction_instance.amount + charge_collected
                        
                else:
                    give_amount = -abs(transaction_instance.amount - charge_collected)

                cash_in_hand = last_object.cash_in_hand - abs(give_amount) if last_object else give_amount


        return cash_in_hand




    def save(self, *args, **kwargs):

        if self.transaction:
            self.transaction_type = "SMS_CHARGE" if self.charge_type == "SMS_CHARGE" else self.transaction.transaction_type
        

        if not self.force_input and (self.manual_cih_input or self.manual_cab_input) and self.pk is None:
            last_instance = self.__class__.get_last_object(user=self.user)
            user_wallet = self.user.wallets.filter(wallet_type="COLLECTION").last()
            user_wallet_balance = user_wallet.available_balance if user_wallet else 0
            # self.cash_at_bank = user_wallet_balance


            if last_instance:
                self.cash_in_hand = last_instance.cash_in_hand
                self.cash_at_bank = last_instance.cash_at_bank
                self.charge_sum = last_instance.charge_sum
                self.liberty_commission_sum = last_instance.liberty_commission_sum
                self.pos_type = last_instance.pos_type
            

            if self.manual_cih_input:
                self.cash_in_hand += self.amount
                self.charge_type = "MANUAL"


            if self.manual_cab_input:
                self.cash_at_bank += self.amount
                self.charge_type = "MANUAL"

            self.cash_available_total = self.cash_in_hand + self.cash_at_bank
            self.pos_type = self.user.terminal_provider
            self.beneficiary = self.user.bvn_full_name if self.user.bvn_first_name else self.user.first_name

            if (self.cash_at_bank != user_wallet_balance) or (self.cash_available_total - self.cash_in_hand != self.cash_at_bank):
                self.mismatch = True
            
            if self.user.sms_subscription:
                amount_to_add = settings.WHISPER_CHARGE
                if user_wallet_balance == self.cash_at_bank - float(amount_to_add):
                    self.mismatch = False



        super(LedgerTableModel, self).save(*args, **kwargs)



    @classmethod
    def get_ledger_transaction_charge(cls, transaction_instance: Transaction):

        commission_charges = {
            "FUND_LOTTO_WALLET": 0,
            "FUND_AJO_WALLET": 0,
            "FUND_AJO_LOANS": 0,
            "FUND_COLLECTION_ACCOUNT": 0,
            "FUND_BANK_TRANSFER": 100,
            "FUND_TRANSFER_FROM_OTHER_COMMISSION": 0,
            "FUND_TRANSFER_FROM_COMMISSION": 0,
            "CARD_TRANSACTION_FUND": 100,
            "CARD_TRANSACTION_FUND_TRANSFER": 100,

            "SEND_BUDDY": 0,
            "SEND_LOTTO_WALLET": 0,
            "SEND_AJO_WALLET": 0,
            "SEND_AJO_LOANS": 0,
            "SEND_COLLECTION_ACCOUNT": 0,
            "SEND_BANK_TRANSFER": 150,
            "BILLS_AND_PAYMENT": 0,
            "AIRTIME_PIN": 0,
            "LOTTO_PLAY": 0,
            "LIBERTY_LIFE_PAYMENT": 0,
            "CREDI_LOAN": 0,
            "AUTO_REFUND": 0,
            "FUNDS_RETRIEVAL": 0,
            "SAVINGS": 0,
            "CARD_PURCHASE": 0,
            "SETTLE_CARDS_PURCHASE": 0,
            "VIRTUAL_CARD_CREATE": 0,
            "PHYSICAL_CARD_CREATE": 0,
            "SEND_REFUND_CARD_PURCHASE": 0,
            "USSD_WITHDRAW": 0
        }

        
        charge = commission_charges.get(transaction_instance.transaction_type)

        return charge if charge else 0





    @classmethod
    def create_new_ledger_object(cls, transaction_instance: Transaction, charge_type, charge=None, recon_data={}):
        
        user = transaction_instance.user
        amount = transaction_instance.amount
        wallet = WalletSystem.get_wallet(user=user, from_wallet_type="COLLECTION")
        wallet_balance = wallet.available_balance
        liberty_commission = transaction_instance.liberty_commission

        entered_charge = 0

        if user.type_of_user in ["LIBERTY_RETAIL"]:
            entered_charge = charge if charge else 0
            today = datetime.today() # date representing today's date

            # if transaction_instance.transaction_type == "SEND_BANK_TRANSFER":
            first_trans = Transaction.objects.filter(user=user, date_created__date=today,  transaction_type__in=["SEND_BANK_TRANSFER"], transaction_leg="INTERNAL", status="SUCCESSFUL", is_reversed=False).first()

            if first_trans == transaction_instance:
                charge = -liberty_commission
            else:

                if settings.ENVIRONMENT == "development":
                    charge = cls.get_ledger_transaction_charge(transaction_instance=transaction_instance)
                    # charge = LibertyRetailCharge.calculate_pos_charges(amount, user, transaction_instance)
                else:
                    charge = LibertyRetailCharge.calculate_pos_charges(amount, user, transaction_instance)
        
        else:
            charge = 0 if charge is None else charge


        if recon_data:
            return_wallet = True
            pass
        else:
            return_wallet = False


        
        previous_ledger = cls.get_last_object(user=user)
        previous_ledger_exists = True if previous_ledger is not None else False

        cash_in_hand = LedgerTableModel.handle_cash_in_hand(user, transaction_instance, charge, charge_type, previous_ledger)
        cash_at_bank = LedgerTableModel.handle_cash_at_bank(user, transaction_instance, wallet, previous_ledger, return_wallet=return_wallet)
        
        self = cls.objects.create(
            user=user,
            transaction=transaction_instance,
            amount=amount,
            charge_type=charge_type if transaction_instance.transaction_type != "RETAIL_AUTO_DEBIT" else "AUTO_DEBIT_CHARGE",
            charge=charge,
            cash_in_hand=cash_in_hand,
            cash_at_bank=cash_at_bank,
            entered_charge=entered_charge,
            liberty_commission=liberty_commission,
            mismatch=True if wallet_balance != cash_at_bank else False,
            reconciled = recon_data.get("reconciled") if recon_data else False,
            checked_for_reconciliation = recon_data.get("checked_for_reconciliation") if recon_data else False,
            actual_transaction_time = recon_data.get("actual_transaction_time")

        )
         

         # Deposit
        if transaction_instance.transaction_type in credit_list:
            self.deposit = True

        elif transaction_instance.transaction_type in debit_list:
            self.withdrawal = True



        # Sum of Bank or Cash Charge
        if transaction_instance.transaction_type == "RETAIL_AUTO_DEBIT" and not transaction_instance.unique_reference:
            self.charge_sum = 0
            self.liberty_commission_sum = 0
        
        else:

            if previous_ledger_exists:
                self.charge_sum = previous_ledger.charge_sum + self.charge
                self.liberty_commission_sum = previous_ledger.liberty_commission_sum + self.liberty_commission

            else:
                self.charge_sum = self.charge
                self.liberty_commission_sum = self.liberty_commission



        # Handle Cash Available
        self.cash_available_total = self.cash_in_hand + self.cash_at_bank


        # POS TYPE
        self.pos_type = self.user.terminal_provider

        # Beneficiary
        if self.transaction.transaction_type == "BILLS_AND_PAYMENT":
            self.beneficiary = self.transaction.transaction_sub_type

        elif self.transaction.transaction_type == "AIRTIME_PIN":
            self.beneficiary = self.transaction.transaction_type

        elif self.transaction.transaction_type == "CARD_TRANSACTION_FUND":
            self.beneficiary = "CASH OUT"

        else:
            if transaction_instance.beneficiary_account_name:
                self.beneficiary = transaction_instance.beneficiary_account_name
            else:
                self.beneficiary = transaction_instance.transaction_type

        self.save()


        return True



    

    @classmethod
    def handle_transfer_in(cls, transaction_instance: Transaction):
        
        user = transaction_instance.user
        amount = transaction_instance.amount - transaction_instance.liberty_commission

        
        cls.objects.create(
            user=user,
            transaction=transaction_instance,
            amount=amount,
            charge_type="TRANSFER_IN",
            charge=0,
            liberty_commission=transaction_instance.liberty_commission,
            transfer_in=amount,
            manual_cab_input=True
        )

        return True



    @classmethod
    def create_ledger_object(
        cls, user, charge,
        charge_type, transaction_instance,
    ):
        # ledger_bal_instance = LedgerBalance.get_legder_instance(user=user)

        # ledger_history = cls.objects.create(
        #     user=user,
        #     transaction=transaction_instance,
        #     amount=transaction_instance.amount,
        #     charge_type=charge_type,
        #     charge=charge
        # )

        return None
    


    @classmethod
    def create_sms_ledger_object(cls, debcred_instance: DebitCreditRecordOnAccount, recon_data={}):

        user = debcred_instance.user
        amount = debcred_instance.amount
        transaction_instance = Transaction.objects.filter(transaction_id=debcred_instance.transaction_instance_id).first()
        liberty_commission=amount
        charge=0
        
        if transaction_instance:
            wallet_balance = WalletSystem.get_wallet(user=user, from_wallet_type="COLLECTION").available_balance

            
            previous_ledger = cls.get_last_object(user=user)
            if previous_ledger is not None:
                cash_at_bank = (previous_ledger.cash_at_bank - debcred_instance.amount) if not recon_data else wallet_balance
                cash_in_hand = previous_ledger.cash_in_hand
                cash_available_total = previous_ledger.cash_in_hand + cash_at_bank
                charge_sum = previous_ledger.charge_sum + charge
                liberty_commission_sum = previous_ledger.liberty_commission_sum + liberty_commission
                previous_ledger_exists = True

            else:
                cash_at_bank = wallet_balance
                cash_in_hand = 0
                cash_available_total = cash_at_bank
                charge_sum = charge
                liberty_commission_sum = liberty_commission
                previous_ledger_exists = False

            
            self = cls.objects.create(
                user=user,
                transaction=transaction_instance,
                amount=amount,
                withdrawal=True,
                beneficiary="WHISPERSMS",
                charge_type="SMS_CHARGE",
                charge=0,
                cash_at_bank=cash_at_bank,
                cash_in_hand=cash_in_hand,
                cash_available_total = cash_available_total,
                liberty_commission=liberty_commission,
                charge_sum=charge_sum,
                liberty_commission_sum=liberty_commission_sum,
                pos_type = user.terminal_provider,
                mismatch=True if wallet_balance != cash_at_bank else False,
                reconciled = recon_data.get("reconciled") if recon_data else False,
                checked_for_reconciliation = recon_data.get("checked_for_reconciliation") if recon_data else False,
                actual_transaction_time = recon_data.get("actual_transaction_time")
            )



        return True



    @classmethod
    def handle_data_reconciliation(cls, user: User, daily=True, from_transaction_id=None):

        from accounts.tasks import realtime_handle_ledger_input_task


        # Fetch transactions
        # retail_trans = cls.objects.filter(user=user, checked_for_reconciliation=False)
        retail_trans = cls.objects.filter(user=user, transaction__isnull=False)
        deb_cred_transactions = DebitCreditRecordOnAccount.objects.filter(user=user)
        
        # deb_cred_transactions = DebitCreditRecordOnAccount.objects.filter(user=user) if from_transaction_id is None else DebitCreditRecordOnAccount.objects.filter(user=user, transaction_instance_id=from_transaction_id)
        

        # Extract retail transaction IDs
        retail_transaction_ids = [str(transaction.transaction.transaction_id) for transaction in retail_trans]
               
        matching_transactions = [transaction for transaction in deb_cred_transactions if transaction.transaction_instance_id in retail_transaction_ids]
        non_matching_transactions = [transaction for transaction in deb_cred_transactions if transaction.transaction_instance_id not in retail_transaction_ids]


        print("retail len", len(retail_trans))
        print("deb cred len", len(deb_cred_transactions))


        print("retail_trans_id", len(retail_transaction_ids))

        print("matching", len(matching_transactions))
        print("non matching", len(non_matching_transactions))
        
        # print(retail_transaction_ids)
        # print(matching_transactions)
        for item in non_matching_transactions:
            print(item.transaction_instance_id)

        for match in matching_transactions:
            get_match = retail_trans.filter(transaction__transaction_id=match.transaction_instance_id).first()
            get_match.actual_transaction_time = match.date_created
            get_match.checked_for_reconciliation = True
            get_match.save()

        
        for non_match in non_matching_transactions:
            recon_data = {
                "actual_transaction_time": non_match.date_created,
                "checked_for_reconciliation": True,
                "reconciled": True
            }

            if non_match.type_of_trans == "WHISPER_CHARGE" and non_match.entry == "DEBIT":
                type_of_task = "SMS_CHARGE"
            else:
                type_of_task = "LEDGER_INPUT"

            realtime_handle_ledger_input_task(instance_id=non_match.transaction_instance_id, charge_type="BANK", charge=None, type_of_task=type_of_task, recon_data=recon_data)


        return
    


    @classmethod
    def handle_pre_auto_debit(cls, user: User, amount):
        last_instance = cls.get_last_object(user=user)
        user_wallet = user.wallets.filter(wallet_type="COLLECTION").last()
        user_wallet_balance = user_wallet.available_balance if user_wallet else 0


        cash_in_hand = last_instance.cash_in_hand
        cash_at_bank = user_wallet_balance
        charge_sum = 0
        liberty_commission_sum = 0
        pos_type = last_instance.pos_type
            
        
        data = cls.objects.create(
            user=user,
            beneficiary="LIBERTY PAY",
            transaction_type="PRE_RETAIL_AUTO_DEBIT",
            amount=amount,
            withdrawal=True,
            charge_type="PRE_AUTO_DEBIT_CHARGE",
            charge=0,
            charge_sum=charge_sum,
            liberty_commission=0,
            liberty_commission_sum=liberty_commission_sum,
            cash_in_hand=cash_in_hand,
            cash_at_bank=cash_at_bank,
            cash_available_total=cash_in_hand + cash_at_bank,
            pos_type=pos_type,
            unique_id=cls.get_unique_id(),
            actual_transaction_time=datetime.now()
        )
         
        return data.unique_id




    @classmethod
    def set_pre_debit_to_true(cls, unique_id):
        for data in cls.objects.filter(unique_id=unique_id):
            data.withdrawal = True
            data.save()
        
        return True






####################################################################################################


class RetailSystem(models.Model):

    retail_account = models.OneToOneField(User, related_name="retail_system", on_delete=models.CASCADE)
    user_assigned = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True)
    location = models.ForeignKey(NewLocationList, on_delete=models.CASCADE)
    account_type = models.CharField(max_length=100, default="RETAIL")
    location_num = models.PositiveSmallIntegerField(default=1)
    supervisor = models.ForeignKey(User, related_name="retail_supervisors", on_delete=models.SET_NULL, blank=True, null=True)
    other_supervisors = models.ManyToManyField(User, related_name='otherretailsupervisors', blank=True)
    available_balance = models.FloatField(default=0.00)
    account_level = models.PositiveSmallIntegerField(default=1)
    is_active = models.BooleanField(default=True)
    date_assigned = models.DateTimeField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    consecutive_weeks_suspension_count = models.IntegerField(default=0)

    def __str__(self):
        return self.retail_account.email


    def save(self, *args, **kwargs):
        if self.user_assigned:
            if self.date_assigned:
                pass
            else:
                self.date_assigned = datetime.now()
            
        else:
            self.date_assigned = None

        super(RetailSystem, self).save(*args, **kwargs)


    @classmethod
    def create_retail_system_account(cls, retail_account: User, location: NewLocationList, account_type: str):
        get_former_locations = cls.objects.filter(location=location)

        if get_former_locations:
            location_num = get_former_locations.count() + 1
        else:
            location_num = 1


        cls.objects.create(
            retail_account=retail_account,
            location=location,
            location_num = location_num,
            account_type=account_type,
        )

        return True
    
    
    @staticmethod
    def get_service_user():
        try:
            if settings.ENVIRONMENT == "development":
                main_user = User.objects.get(email="<EMAIL>")
            else:
                main_user = User.objects.get(email="<EMAIL>")
            
            return main_user
        except:
            raise Exception("No Service User")



    @classmethod
    def debit_account_for_retail_comm(cls, kwargs, deduct_balance=None):
        # Debit Agent with amount due
        user: User = kwargs["user"]
        agent_wallet_instance: WalletSystem = kwargs["agent_wallet_instance"]
        amount = kwargs["amount"]
        trans_type = kwargs["trans_type"]
        unique_id = kwargs.get("unique_id")

        
        service_user = cls.get_service_user()

        service_user_wallet_instance = WalletSystem.get_wallet(
            user=service_user, from_wallet_type="COLLECTION"
        )

        if not deduct_balance:
            deduct_balance = WalletSystem.deduct_balance(
                user=user,
                wallet=agent_wallet_instance,
                amount=amount,
                trans_type=trans_type,
                unique_reference=unique_id
            )

        
        agent_balance_before = deduct_balance["balance_before"]

        agent_balance_after = WalletSystem.get_balance_after(
            user=user,
            balance_before=agent_balance_before,
            total_amount=amount,
            is_credit=False
        )


        print("i am herrrrrre")
        escrow_instance = WalletSystem.move_to_escrow_send_pay_buddy(
            user=user,
            balance_before=agent_balance_before,
            balance_after=agent_balance_after,
            from_wallet_id=agent_wallet_instance.wallet_id,
            to_wallet_id=service_user_wallet_instance.wallet_id,
            from_wallet_type=agent_wallet_instance.wallet_type,
            to_wallet_type=service_user_wallet_instance.wallet_type,
            transfer_type=trans_type,
            amount=amount,
            to_nuban=service_user.phone_number,
            to_account_name=service_user.bvn_full_name if service_user.bvn_first_name else service_user.full_name,
            liberty_commission=0.0,
            total_amount_charged=amount,
            narration=f"RETAIL CHRG FOR AGENT-{user.id}",
            is_beneficiary=False,
            is_recurring=False,
            debit_credit_record_id=deduct_balance.get("debit_credit_record_id"),
            ip_addr="self",
            customer_reference=unique_id
        )


        process_debit = WalletSystem.debit_credit_for_other_services(
            service_user_instance=service_user,
            agent_user_instance=user,
            service_wallet_instance=service_user_wallet_instance,
            agent_wallet_instance=agent_wallet_instance,
            total_amount=amount,
            service_comm=amount,
            agent_comm=0.00,
            unique_reference=escrow_instance.customer_reference,
            escrow_id=escrow_instance.escrow_id
        )



        if process_debit["status"] == True:
            # LedgerTableModel.handle_auto_debit_charge_sum(transaction_instance=process_debit["sender_trans_id"])
            # retail_system.available_balance -= amount
            # retail_system.save()


            user.terminal_disabled = False
            user.save()

            new_resp = True

        else:
            new_resp = False

        return new_resp     
        


    @classmethod
    def handle_retail_auto_debit(cls, user: User, service_user: User, amount: float, retail_system: 'RetailSystem'):
        service_user_check = OtherServiceDetail.objects.filter(user=service_user, service_name="RETAIL_AUTO_DEBIT").last()
        if not service_user_check:
            return {
                "status": False,
                "message": "Main Liberty User Does Not Exist"
            }


        from_wallet_type = "COLLECTION"
        trans_type = "RETAIL_AUTO_DEBIT"
        
        agent_wallet_instance = WalletSystem.get_wallet(
            user=user, from_wallet_type=from_wallet_type
        )


        deduce_user_balance = WalletSystem.check_wallet_balance(
            amount=amount,
            wallet_instance=agent_wallet_instance
        )


        if deduce_user_balance["status"] == False:
            
            with transaction.atomic():
                unique_id = LedgerTableModel.handle_pre_auto_debit(user, amount)

                UserDebt.create_user_debt(
                    user=user,
                    wallet=agent_wallet_instance,
                    amount=amount,
                    trans_type=trans_type,
                    transaction_instance_id=f"{datetime.now()}",
                    transaction_instance_new_status=None,
                    ignore_history=False,
                    unique_id=unique_id
                )

            return {
                "status": False,
                "message": "Agent does not have sufficient balance to make this transaction",
                "user_balance": agent_wallet_instance.available_balance,
            }


        kwargs = {
            "user": user,
            "agent_wallet_instance": agent_wallet_instance,
            "amount": amount,
            "trans_type": trans_type
        }
        return cls.debit_account_for_retail_comm(kwargs)








class TruncDate(Func):
    function = 'DATE_TRUNC'
    template = "%(function)s('day', %(expressions)s)"



class RetailPerformance(models.Model):

    PERFORMANCE_TYPE_CHOICES = [
        ("TOP", "TOP"),
        ("AVERAGE", "AVERAGE"),
        ("LOW", "LOW")
    ]


    retail_account = models.ForeignKey(RetailSystem, on_delete=models.CASCADE)
    agent_email = models.CharField(max_length=300, null=True)
    agent_phone = models.CharField(max_length=300, null=True)
    agent_location = models.CharField(max_length=300, null=True)
    revenue = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    total_revenue = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    average_daily_revenue = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    status = models.CharField(max_length=300, choices=PERFORMANCE_TYPE_CHOICES)
    average_daily_status = models.CharField(max_length=300, choices=PERFORMANCE_TYPE_CHOICES)
    percentage_sales = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    first_salary = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    expected_salary = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    day_of_week = models.CharField(max_length=100, null=True)
    actual_date = models.DateField(null=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


    def __str__(self):
        return self.retail_account.retail_account.email
    

    def save(self, *args, **kwargs):

        if self.actual_date:
            self.day_of_week = self.actual_date.strftime('%A')

        
        self.agent_email = self.retail_account.retail_account.email
        self.agent_phone = self.retail_account.user_assigned.phone_number
        self.agent_location = self.retail_account.location.location
    
        current_date = self.actual_date
        first_day_of_month, first_day_of_next_month = self.__class__.get_current_month_range(date=current_date)
        # current_date = datetime.now().date()

        # day_before_current = current_date - timedelta(days=1)

        print(first_day_of_month)
        print(current_date)

        # Calculate the day before the current day

        user_data = LedgerTableModel.objects.filter(user=self.retail_account.retail_account, date_created__date__range=(first_day_of_month, current_date))

        # print(user_data)
        day_in_view_data = user_data.filter(user=self.retail_account.retail_account, date_created__date=self.actual_date)
        print("-----------------------------")
        # print(day_in_view_data)

        # revenue = day_in_view_data.aggregate(Sum("charge"))["charge__sum"] or 0

        # total_revenue = user_data.aggregate(Sum("charge"))["charge__sum"] or 0
        revenue = day_in_view_data.annotate(
            filtered_charge=Case(
                When(charge__lt=0, then=0),
                default=F('charge'),
                output_field=models.DecimalField()  # Change this to the appropriate field type for 'charge'
            )
        ).aggregate(revenue=Sum('filtered_charge'))["revenue"] or 0


        total_revenue = user_data.annotate(
            filtered_charge=Case(
                When(charge__lt=0, then=0),
                default=F('charge'),
                output_field=models.DecimalField()  # Change this to the appropriate field type for 'charge'
            )
        ).aggregate(total_revenue=Sum('filtered_charge'))["total_revenue"] or 0



        if revenue >= 3000:
            self.status = "TOP"
        elif revenue >= 2500 and revenue < 3000:
            self.status = "AVERAGE"
        else:
            self.status = "LOW"



        # Update the instance with the revenue for that day
        self.revenue = revenue

        self.total_revenue = total_revenue

        # day_in_view__lte_data = user_data.filter(date_created__date__lt=day_in_view_data.latest("date_created"))
            # last_entry = .objects.filter(date_field__lt=current_date).latest('date_field')

        month_instance = self.__class__.objects.filter(retail_account=self.retail_account, actual_date__range=(first_day_of_month, current_date))

        # previous_revenues_sum = month_instance.filter(actual_date__lt=self.actual_date).aggregate(Sum("revenue"))["revenue__sum"] or 0
        data_count = month_instance.count()
        
        if data_count < 1:
            self.average_daily_revenue = 100
        else:
            self.average_daily_revenue = total_revenue / month_instance.count()



        if self.average_daily_revenue >= 3000:
            self.average_daily_status = "TOP"
        elif self.average_daily_revenue >= 2500 and self.average_daily_revenue < 3000:
            self.average_daily_status = "AVERAGE"
        else:
            self.average_daily_status = "LOW"


        target_sales = 65000
        budgeted_pay = 30000

        self.percentage_sales = (total_revenue / target_sales) * 100


        self.first_salary = (total_revenue / target_sales) * budgeted_pay
        self.expected_salary = (total_revenue / target_sales) * budgeted_pay if self.first_salary < 30000 else 30000





        # DO NOT FORGET TO CREATE FIELD FOR SUNDAY


        super(RetailPerformance, self).save(*args, **kwargs)



    @classmethod
    def get_current_month_range(cls, date: datetime):
        current_date = date
        first_day_of_month = current_date.replace(day=1)

        if current_date.month == 12:
            next_month = 1
            next_year = current_date.year + 1
        else:
            next_month = current_date.month + 1
            next_year = current_date.year

        # first_day_of_next_month = datetime(next_year, next_month, 1)
        first_day_of_next_month = timezone.make_aware(datetime(next_year, next_month, 1))

        return first_day_of_month, first_day_of_next_month



    @classmethod
    def calculate_daily_performance(cls, retail_acct: RetailSystem, run_all=False):

        # Get Current Month

        current_date = timezone.now()
        first_day_of_month, first_day_of_next_month = cls.get_current_month_range(date=current_date)



        # Loop through each day of the current month, excluding Sundays

        current_day = first_day_of_month
        while current_day < current_date:
            # if current_day.weekday() != 6:

            instance, created = cls.objects.get_or_create(actual_date=current_day, retail_account=retail_acct)

            #  # Calculate revenue for the day from LedgerTableModel data
            # day_in_view_data = LedgerTableModel.objects.filter(user=retail_acct.retail_account, date_created__date=current_day)
            # revenue = day_in_view_data.aggregate(Sum("charge"))["charge__sum"] or 0

            # # Update the instance with the revenue for that day
            # instance.revenue = revenue
            instance.save()

            current_day += timedelta(days=1)



        # user = retail_acct.retail_account

        # user_data = LedgerTableModel.objects.filter(user=user, date_created__range=(first_day_of_month, first_day_of_next_month))

        # unique_days = user_data.annotate(day=TruncDate('date_created')).values('day').distinct()


        # unique_days_list = [entry['day'] for entry in unique_days]

        # for date in unique_days_list:
        #     # print(date)
        #     day_in_view_data = user_data.filter(date_created__date=date)
        #     # print(day_in_view_data)
        #     day_in_view__lte_data = user_data.filter(date_created__date__lte=day_in_view_data.last().date_created)

        #     revenue = list(day_in_view_data.aggregate(Sum("charge")).values())[0]
        #     # print(revenue)

            
            # average_daily_revenue = list(day_in_view_data.aggregate(Sum("charge")).values())[0]



        # unique_days = LedgerTableModel.objects.filter(user=user).annotate(
        #     day=TruncDate('timestamp', output_field=DateTimeField())
        # ).values('day').distinct()


        # check_for_days = LedgerTableModel









class SendAgentsSmsDumpData(models.Model):
    date_created = models.DateTimeField(auto_now_add=True)
    recipient = models.CharField(max_length=100, blank=True, null=True)
    subject = models.CharField(max_length=200, blank=True, null=True)
    message = models.TextField(blank=True, null=True)
    status = models.CharField(max_length=100, blank=True, null=True)
    sent_by = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True)
    last_updated = models.DateTimeField(auto_now=True)


    @classmethod
    def create_send_sms_record(cls, subject, message, recipient, status, sent_by):
        cls.objects.create(
            recipient = recipient,
            subject = subject,
            message = message,
            status = status,
            sent_by = sent_by
        )
