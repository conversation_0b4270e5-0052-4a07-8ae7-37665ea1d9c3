from typing import Sequence
from django.contrib import admin
from retail.models import *

from import_export import resources, fields
from import_export.admin import ImportExportMixin, ImportExportModelAdmin


# Register your models here.


class LibertyRetailChargeResource(resources.ModelResource):
    class Meta:
        model = LibertyRetailCharge

class LedgerTableModelResource(resources.ModelResource):
    # user_email = fields.Field(attribute='user_id')
    
    
    class Meta:
        model = LedgerTableModel


    # def get_export_fields(self):
    #     fields = super().get_export_fields()
    #     fields.append('user_email')  # Add the user email field to the export fields
    #     return fields


class RetailSystemResource(resources.ModelResource):
    class Meta:
        model = RetailSystem

class RetailPerformanceResource(resources.ModelResource):
    class Meta:
        model = RetailPerformance
class SendSmsDumpDataResource(resources.ModelResource):
    class Meta:
        model = SendAgentsSmsDumpData



class LibertyRetailChargeResourceAdmin(ImportExportModelAdmin):
    resource_class = LibertyRetailChargeResource
    list_filter = (
        ('date_created', 'last_updated', 'transaction_type')
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

    
class LedgerTableModelResourceAdmin(ImportExportModelAdmin):
    # def save_model(self, request, obj, form, change):
    #     custom_request = request
    #     print(custom_request, "herrrr")
    #     obj.clean(custom_request=custom_request)

    autocomplete_fields = ["user", "transaction"]
    resource_class = LedgerTableModelResource
    search_fields = ["user__email", "transaction__transaction_id", "transaction__liberty_reference", "transaction__escrow_id"]

    list_filter = (
        ('date_created', 'manual_cih_input', 'manual_cab_input', 'transaction_type', "user__type_of_user")
    )

    date_hierarchy = 'date_created'



    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    


class RetailSystemResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["retail_account", "user_assigned", "supervisor", "other_supervisors"]
    resource_class = RetailSystemResource
    search_fields = ["retail_account__email", "user_assigned__email", "location__location"]
    # readonly_fields = ["retail_account"]

    list_filter = (
        ('date_created', 'is_active', 'location__location')
    )

    def user_phone(self, instance):
        if instance.user_assigned:
            return instance.user_assigned.phone_number
        return None

    def get_list_display(self, request):
        first_list = [field.name for field in self.model._meta.concrete_fields]
        first_list[3:3] = ["user_phone"]

        return first_list
    
class SendSmsDumpDataResourceAdmin(ImportExportModelAdmin):
    resource_class = SendSmsDumpDataResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]

# class YesterdayFilter(admin.SimpleListFilter):
#     title = 'Yesterday'
#     parameter_name = 'yesterday'

#     def lookups(self, request, model_admin):
#         return (
#             ('1', 'Yesterday'),
#         )

#     def queryset(self, request, queryset):
#         if self.value() == '1':
#             yesterday = timezone.now().date() - timedelta(days=1)
#             return queryset.filter(actual_date=yesterday)
#         return queryset
    
class RetailPerformanceResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["retail_account"]
    resource_class = RetailPerformanceResource
    search_fields = ["retail_account__retail_account__email", "retail_account__retail_account__phone_number"]

    date_hierarchy = 'actual_date'


    list_filter = (
        ('date_created', 'actual_date')
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.order_by('-actual_date')
    



admin.site.register(LibertyRetailCharge, LibertyRetailChargeResourceAdmin)
admin.site.register(LedgerTableModel, LedgerTableModelResourceAdmin)
admin.site.register(RetailSystem, RetailSystemResourceAdmin)
admin.site.register(RetailPerformance, RetailPerformanceResourceAdmin)
admin.site.register(SendAgentsSmsDumpData, SendSmsDumpDataResourceAdmin)

