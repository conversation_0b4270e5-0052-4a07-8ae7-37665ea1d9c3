from rest_framework import serializers
from django.utils import timezone
from rest_framework import status
from retail.models import SendAgentsSmsDumpData


class AssignTerminalSerializer(serializers.Serializer):
    terminal_id = serializers.CharField()
    supervisor_email = serializers.CharField()
    agent_email = serializers.EmailField()


class SendSmsSerializer(serializers.Serializer):
    recipient = serializers.ChoiceField(choices=["All agents", "In-active agents", "Active agents", "Suspended agents"])
    subject = serializers.CharField()
    message = serializers.CharField()
    send_date = serializers.DateField(required=False, default=timezone.now().date())
    send_later = serializers.BooleanField(default=False)
    

    def validate(self, attrs):
        send_date = attrs.get("send_date")
        if send_date and send_date < timezone.now().date():
            raise serializers.ValidationError({"error": "send date must not be earlier that today's date"}, code=status.HTTP_400_BAD_REQUEST)
        
        return attrs
    
class SendSmsListSerializer(serializers.ModelSerializer):
    class Meta:
        model = SendAgentsSmsDumpData
        fields = "__all__"

