from django.urls import path
from retail import views


urlpatterns = [
    path("transaction-metrics", views.MainDashboardTransactionsMetricsView.as_view()),
    path("transaction-metrics-two", views.MainDashboardMetricsTwoView.as_view()),
    path("bills-utilities", views.MainDashboardUtilitiesOverviewView.as_view()),
    path("transaction-charts", views.MainDashboardChartsView.as_view()),
    path("main-dashboard-agents-overview", views.MainDashboardAgentsOverView.as_view()),
    path("revenue-overview", views.MainDashboardRevenueOverView.as_view()),
    path("agents-overview", views.AgentOverviewView.as_view()),
    path("top-performing-agents", views.TopPerformingAgentsView.as_view()),
    path("agents-list", views.AgentsListView.as_view()),
    path("active-agents-list", views.ActiveAgentsListView.as_view()),
    path("inactive-agents-list", views.InactiveAgentsListView.as_view()),
    path("suspended-agents-list", views.SuspendedAgentsListView.as_view()),
    path("agents-details/<id>", views.AgentsDetailsOverView.as_view()),
    path("agents-wallet-details/<id>", views.AgentsWalletDetailsView.as_view()),
    path("agents-transactions/<id>", views.AgentsTransactionsView.as_view()),
    path("agents-transaction-details/<id>", views.TransactionsDetailView.as_view()),
    path("agents-revenue-details/<id>", views.AgentsRevenueDetailsView.as_view()),
    path("agents-active-sessions/<id>", views.AgentsActiveSessionsView.as_view()),
    path("leaderboard-amount-hours", views.LeaderBoardTop20Amount24HoursView.as_view()),
    path("leaderboard-amount-range", views.LeaderBoardTop20AmountRangeView.as_view()),
    path("leaderboard-count-hours", views.LeaderBoardTop20Count24HoursView.as_view()),
    path("leaderboard-count-range", views.LeaderBoardTop20CountRangeView.as_view()),
    path("agents-log-history", views.AgentsLogHistoryListView.as_view()),
    path("branch-overview", views.BranchOverviewView.as_view()),
    path("branch-charts", views.BranchChartsView.as_view()),
    path("branch-top5", views.BranchTop5View.as_view()),
    path("branch-list", views.BranchListView.as_view()),
    path("performing-branch-list", views.PerformingBranchListView.as_view()),
    path("under-performing-branch-list", views.UnderPerformingBranchListView.as_view()),
    path("branch-details/<id>", views.BranchDetailsView.as_view()),
    path("branch-details-transaction-comparatives/<id>", views.BranchTransactionComparativesView.as_view()),
    path("branch-supervisors-list/<id>", views.BranchSupervisorsListView.as_view()),
    path("supervisors-data-overview/<id>", views.SupervisorDataOverviewView.as_view()),
    path("supervisors-agents-list/<id>", views.SupervisorDataAgentsListView.as_view()),
    path("branch-top-supervisors/<id>", views.BranchTopSupervisorsView.as_view()),
    path("branch-top-supervisors-ranking/<id>", views.BranchTopSupervisorsRankingView.as_view()),
    path("add-new-branch", views.CreateBranchView.as_view()),
    path("assign-agent-terminal", views.AssignTerminalToRetailAgent.as_view()),
    path("user-supervisor-role", views.SupervisorRoleView.as_view()),
    path("send-sms-to-agents", views.SendSmsToAgents.as_view()),
    path("send-sms-to-dump-data-list", views.SendAgentSmsDumpDataListView.as_view()),
    path("whatsapp-callback-url", views.WhatsAppCallbackView.as_view()),
    path("ajo-users-details-list", views.AjoUsersDetailsApiView.as_view())
]