from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportMixin, ImportExportModelAdmin
from coral.models import *


# Register your models here.



class CoralPayResource(resources.ModelResource):
    
    class Meta:
        model = CoralPayTable

#######################################################################
# ADMINS

class CoralPayResourceAdmin(ImportExportModelAdmin):
    resource_class = CoralPayResource
    # search_fields = ['admin_user__email', 'account_affected', 'action', 'date_created']
    # list_filter = (
    #     ('date_created',)
    # )
    # date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(CoralPayTable, CoralPayResourceAdmin)