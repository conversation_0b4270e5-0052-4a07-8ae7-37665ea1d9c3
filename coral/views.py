from django.shortcuts import render

from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response

from coral.models import CoralPayTable
import json

# Create your views here.

class CoralPayAPIView(APIView):
    
    def post(self, request):
        response = request.data

        CoralPayTable.objects.create(payload = str(response))

        return Response(
            {"status":"true", "message": "response received"},
            status=status.HTTP_200_OK
        )