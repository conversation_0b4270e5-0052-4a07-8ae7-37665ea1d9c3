from django.db.models import Q
from django.conf import settings
from django.core.cache import cache
from django.contrib.auth.models import Group
from rest_framework.permissions import BasePermission

from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.authentication import BasicAuthentication
from rest_framework.authtoken.models import Token
from rest_framework.exceptions import APIException
from rest_framework import status, permissions

from admin_dashboard.models import AdminUserRole
from main.models import User, Whitelist, AvailableBalance, Blacklist, SuperAgentProfile

from accounts.models import AccountSystem, WalletSystem, ConstantTable, Transaction
from accounts.helpers.helper_func import notify_admin_on_false_float_low_balance
from accounts.helpers.vfdbank_manager import VFDBank
from accounts.authentication import CustomTokenAuthenticationWithKeyword
from accounts.helpers.helper_func import notify_admin_group

from send_money.helpers.helper_functions import get_false_float_balance_function, v2_get_false_float_balance_function

from ussd.models import BillsPayTable

from datetime import datetime
import base64

class CustomIsAuthenticated(permissions.IsAuthenticated):
    """
    Permission to use endpoints by a non fraud of suspended account.
    """

    def has_permission(self, request, view):

        ussd_session_key = request.GET.get(settings.USSD_SESSION_KEY_KEY)

        if ussd_session_key:
            get_ussd_instance = BillsPayTable.objects.filter(session_key = ussd_session_key, request_processed=False).last()
            if get_ussd_instance:
                get_ussd_instance.request_processed = True
                get_ussd_instance.save()

                get_user = User.objects.filter(phone_number=get_ussd_instance.phone_number).last()
                if get_user:
                    request.user = get_user

                    mutable_query_dict = request.GET.copy()

                    mutable_query_dict["local_channel"] = "USSD"

                    request.GET = mutable_query_dict

            else:
                print("USSD_REQUEST DOES NOT EXIST")
                return False
        # if request.user.type_of_user == "LOTTO_AGENT" and not SuperAgentProfile.objects.filter(agent=request.user, is_verified=True).exists():
        #     return False

        if not super().has_permission(request, view):
            return False

        print(request.user)
        request_user_bool = (
            not request.user.is_fraud
            and not request.user.is_suspended
            and not request.user.lotto_suspended
            # and not request.user.terminal_suspended
            # if request.user.type_of_user == "LIBERTY_RETAIL"
            # else False
        )

        print(request_user_bool)
        print(
            "FRAUD", request.user.is_fraud,
            "SUSPENDED", request.user.is_suspended,
            "LOTTO SUSPENDED", request.user.lotto_suspended,
            sep="\n\n"
        )

        return request_user_bool


class RetailIsAuthenticated(CustomIsAuthenticated):
    """
    Permission to use endpoints by admin.
    """

    def has_permission(self, request, view):
        if not super().has_permission(request, view):
            return False

        group_names = ['Agency Banking Team', 'libertyretailteam']

        for group_name in group_names:
            try:
                group = Group.objects.get(name=group_name)
                if group in request.user.groups.all():
                    return True
            except Group.DoesNotExist:
                pass

        if request.user.is_superuser:
            return True

        supervisor = request.user.retail_supervisors.all()
        other_supervisor = request.user.otherretailsupervisors.all()

        if supervisor or other_supervisor:
            return True

        return False




class CheckIPAddresses(permissions.BasePermission):
    """
    Permission to dynamically check ip addresses.
    """
    def __init__(self, service_name):
        self.service_name = service_name

    def __call__(self):
        return self

    def has_permission(self, request, view):
        # Get IP ADDRESS
        address = request.META.get('HTTP_X_FORWARDED_FOR')
        if address:
            ip_addr = address.split(',')[-1].strip()
        else:
            ip_addr = request.META.get('REMOTE_ADDR')

        service_name = self.service_name
        get_whiletist_ips = Whitelist.objects.filter(is_active=True, service_name=service_name).last()

        if settings.ENVIRONMENT == "development":
            if get_whiletist_ips:
                return True
            else:
                print("IP not Whilisted")
                raise EntryDisallowedException()

        else:

            if get_whiletist_ips and (ip_addr in get_whiletist_ips.list_of_hosts):
                return True
            elif request.user.is_authenticated and request.user.email in ["<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                return True
            else:
                print("IP not Whilisted")
                raise EntryDisallowedException()


class AdminLockPermission(permissions.BasePermission):
    """
    Permission to use endpoints by admin.
    """

    def has_permission(self, request, view):
        if ConstantTable.get_constant_table_instance().admin_sensitive_endpoint_lock == True and request.user.email in ["<EMAIL>", "<EMAIL>", "<EMAIL>"]:
            return True
        else:
            print("BLOCKED BY ADMIN")
            raise OnlyAdminsAllowed()

class TechSupportPermission(permissions.BasePermission):
    """
    Permission to use endpoints by admin.
    """

    def has_permission(self, request, view):
        if ConstantTable.get_constant_table_instance().admin_sensitive_endpoint_lock == False:
            raise OnlyAdminsAllowed()


        group_names = ['Tech Support']

        if request.user.groups.filter(name__in=group_names) != None:
            return True

        # for group_name in group_names:
        #     try:
        #         group = Group.objects.get(name=group_name)
        #         if group in request.user.groups.all():
        #             return True
        #     except Group.DoesNotExist:
        #         pass

        if request.user.email in ["<EMAIL>", "<EMAIL>", "<EMAIL>"]:
            return True

        raise OnlyAdminsAllowed()



class LottoUserPermission(permissions.BasePermission):
    """
    Permission to use endpoints by admin.
    """

    def has_permission(self, request, view):
        get_constant_table_emails = ConstantTable.get_constant_table_instance().api_create_accounts
        if get_constant_table_emails is not None:

            if request.user.email in get_constant_table_emails:
                return True
            else:
                print("BLOCKED BY ADMIN")
                raise OnlyAdminsAllowed()
        else:
            raise OnlyAdminsAllowed()

class SavingsUserPermission(permissions.BasePermission):
    """
    Permission to use endpoints by admin.
    """

    def has_permission(self, request, view):

        if request.user.email in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                return True
        else:
            print("BLOCKED BY ADMIN")
            raise OnlyAdminsAllowed()



class BuddyTransferRegulatorPermission(permissions.BasePermission):
    """
    Permission to block send money when needed.
    """

    def has_permission(self, request, view):
        if ConstantTable.get_constant_table_instance().buddy_transfer_regulator == True:
            return True
        else:
            print("BLOCKED BY ADMIN")
            raise NoSendMoneyTransactionsAllowed()

class FundLottoWalletRegulatorPermission(permissions.BasePermission):
    """
    Permission to block send money when needed.
    """

    def has_permission(self, request, view):
        if ConstantTable.get_constant_table_instance().fund_lotto_wall_regulator == True:
            return True
        else:
            print("BLOCKED BY ADMIN")
            raise NoSendMoneyTransactionsAllowed()

class SendMoneyRegulatorPermission(permissions.BasePermission):
    """
    Permission to block send money when needed.
    """

    def has_permission(self, request, view):
        if ConstantTable.get_constant_table_instance().send_money_regulator == True:
            return True
        else:
            print("BLOCKED BY ADMIN")
            raise NoSendMoneyTransactionsAllowed()

class CardWithrawRegulatorPermission(permissions.BasePermission):
    """
    Permission to block send money when needed.
    """

    def has_permission(self, request, view):
        if ConstantTable.get_constant_table_instance().card_withdraw_regulator == True:
            return True
        else:
            print("BLOCKED BY ADMIN")
            raise NoCardWithrawAllowed()


class BlockSendMoneyOnLowBalancePermission(permissions.BasePermission):
    """
    Permission to block send money when on low balance and trigger admin.
    """

    def has_permission(self, request, view):
        # amount_to_be_transfered = request.data.get("data").get("amount")
        get_float_balance = WalletSystem.get_float_wallet(from_wallet_type="FLOAT").available_balance

        amount_to_be_transfered = sum(item["amount"] for item in request.data.get("data"))

        if amount_to_be_transfered + float(10000) > get_float_balance:
            print("BLOCKED FOR LOW BALANCE")
            # raise NoSendMoneyTransactionsAllowed()
            return True
        else:
            return True

class NonLottoAgentsBlockSendMoneyOnLowBalancePermission(permissions.BasePermission):
    """
    Permission to block non lotto agents send money when on low balance and trigger admin.
    """

    def has_permission(self, request, view):
        if ConstantTable.get_constant_table_instance().false_float_low_balance_trigger == True:
            get_balance = VFDBank.get_vfd_float_balance()

            last_balance = AvailableBalance.objects.filter(is_active=True).last()

            if last_balance and get_balance is not None:
                last_balance.float_balance = get_balance
                last_balance.save()

            exclude_users_list = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]

            get_false_float_balance, original_balance = get_false_float_balance_function(get_balance=get_balance, last_balance=last_balance, exclude_users_list=exclude_users_list)
            amount_to_be_transfered = sum(item["amount"] for item in request.data.get("data"))

            print(request.user.type_of_user)
            print("amount_to_be_trans", amount_to_be_transfered)
            print("new_balance", get_false_float_balance)

            if original_balance < 0:
                if amount_to_be_transfered >= get_false_float_balance:
                    print("herrrrr")
                    print("email", request.user.email)
                    if request.user.type_of_user != "LOTTO_AGENT" and request.user.email not in exclude_users_list:
                        print("BLOCKED FOR FALSE LOW BALANCE")

                        if settings.ENVIRONMENT == "development":
                            admin_list = ["2347039516293"]
                        else:
                            admin_list = ["2347039516293", "2348167631246", "2348077469471"]


                        for num in admin_list:
                            balance_details = f"amount_to_be_transfered: {amount_to_be_transfered}\nOriginal Balance: {original_balance}\False Float Balance: {get_false_float_balance}"
                            send_trigger_for_low_balance = notify_admin_on_false_float_low_balance(phone_number=num, details=balance_details)
                        raise NoSendMoneyTransactionsAllowed()
                        # return True
                    else:
                        return True
                else:
                    return True
            else:
                return True
        else:
            return True

class V2NonLottoAgentsBlockSendMoneyOnLowBalancePermission(permissions.BasePermission):
    """
    Permission to block non lotto agents send money when on low balance and trigger admin.
    """

    def has_permission(self, request, view):
        if ConstantTable.get_constant_table_instance().false_float_low_balance_trigger == True:
            get_balance = VFDBank.get_vfd_float_balance()
            last_balance = AvailableBalance.objects.filter(is_active=True).last()

            if last_balance and get_balance is not None:
                last_balance.float_balance = get_balance
                last_balance.save()

            float_balance = get_balance if get_balance else last_balance.float_balance if last_balance else 0
            exclude_users_list = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]

            new_balance = v2_get_false_float_balance_function(float_balance=float_balance, exclude_users_list=exclude_users_list)
            amount_to_be_transfered = sum(item["amount"] for item in request.data.get("data"))

            print(request.user.type_of_user)
            print("float_balance", float_balance)
            print("amount_to_be_trans", amount_to_be_transfered)

            if amount_to_be_transfered >= new_balance:
                print("herrrrr")
                print("email", request.user.email)
                if request.user.type_of_user == "LOTTO_AGENT" or request.user.email in exclude_users_list:
                    return True
                else:
                    print("BLOCKED FOR FALSE LOW BALANCE")
                    raise NoSendMoneyTransactionsAllowed()

        return True


class BlocklistPermission(permissions.BasePermission):
    """
    Global permission check for blocked IPs.
    """

    def has_permission(self, request, view):
        ip_addr = request.META["REMOTE_ADDR"]
        # blocked = Blocklist.objects.filter(ip_addr=ip_addr).exists()
        return False


class HasKYC(permissions.BasePermission):
    """
    View based permission check KYC.
    """

    def has_permission(self, request, view):

        # if (
        #     request.user.check_kyc.is_kyc_level_one
        #     or request.user.check_kyc.is_kyc_level_two
        # ):
        from kyc_app.models import UserImage

        image_verified = UserImage.objects.filter(
                                                    user=request.user,
                                                    is_match=True
                                                ).exists()

        if request.user.can_bypass_user_image_verification or image_verified:

            if request.user.kyc_level > 0:
                return True
            else:
                raise NoKYCVerification()
        else:
            raise NoKYCVerification()


class HasKYCLevelTwo(permissions.BasePermission):
    """
    View based permission check KYC level two.
    """

    def has_permission(self, request, view):

        # if (
        #     request.user.check_kyc.is_kyc_level_one
        #     or request.user.check_kyc.is_kyc_level_two
        # ):
        if request.user.kyc_level >= 2:
            return True
        else:
            raise NoKYCLevelTwoVerification()

class HasKYCLevelThree(permissions.BasePermission):
    """
    View based permission check KYC level three.
    """

    def has_permission(self, request, view):

        if request.user.kyc_level >= 3:
            return True
        else:
            raise NoKYCLevelThreeVerification()

class LottoDynamicKYCLevelDecider(permissions.BasePermission):
    """
    View based permission check based on dynamic KYC selection.
    """

    def has_permission(self, request, view):

        lotto_choice_const = int(ConstantTable.get_constant_table_instance().lotto_kyc_level_choice)

        if request.user.type_of_user == "AGENT":
            if request.user.kyc_level >= 2:
                return True
            else:
                raise NoDynamicKYCLevelDeciderException()

        elif request.user.kyc_level >= lotto_choice_const:
            return True
        else:
            raise NoDynamicKYCLevelDeciderException()


class OTPVerified(permissions.BasePermission):
    """
    View based permission otp verified.
    """

    def has_permission(self, request, view):

        if request.user.registration_email_verified:
            return True
        else:
            raise NoRegistrationEmailVerification()


class HasTransactionPin(permissions.BasePermission):
    """
    Check if user has transaction pin else, stop transaction and ask user to create transaction pin
    """

    def has_permission(self, request, view):

        if request.user.transaction_pin and request.user.pin_retries < 6:
            return True
        elif request.user.transaction_pin and request.user.pin_retries >= 6:
            raise TransactionPinMaxRetry()
        else:
            raise NoTransactionPin()


class CanSendMoney(permissions.BasePermission):
    """
    Check if user has send money status is true
    """

    def has_permission(self, request, view):

        # if request.user.send_money_status == False or request.user.block_on_funding == True:
        if request.user.send_money_status == False:
            raise CantSendMoneyException()
        else:
            return True

# WhiteList Permission
class WhitelistPermission(permissions.BasePermission):
    """
    Permission to check for whitelisted IPs for dynamic funding call back.
    """

    def has_permission(self, request, view):

        # Get IP ADDRESS
        address = request.META.get('HTTP_X_FORWARDED_FOR')
        if address:
            ip_addr = address.split(',')[-1].strip()
        else:
            ip_addr = request.META.get('REMOTE_ADDR')


        print(ip_addr)


        whitelisted = Whitelist.objects.filter(host=ip_addr).last()
        if whitelisted:
            if whitelisted.is_active:
                return True
            else:
                print("IP WHITELISTED, BUT NOT ACTIVE")
                print("IP WHITELISTED, BUT NOT ACTIVE")
                raise EntryDisallowedException()
        else:
            print("IP NOT WHITELISTED")
            print("IP NOT WHITELISTED")
            raise EntryDisallowedException()

class OtherServiceOtherPermissions(permissions.BasePermission):
    """
    Permission to check for whitelisted IPs for dynamic funding call back.
    """

    def has_permission(self, request, view):

        auth_header = request.headers.get('Authorization', '')

        token_type, _, credentials = auth_header.partition(' ')


        expected = settings.OTHERS_SERVICES_AUTH_PERMISSION


        if token_type != 'Hook' or credentials != expected:
            raise VFD_Webhook_Whitelist_Exception()
        else:
            return True

class VFD_Webhook_Whitelist(permissions.BasePermission):
    """
    Permission to check for VFD Whitelisted Hook for callback.
    """

    def has_permission(self, request, view):

        auth_header = request.headers.get('Authorization', '')

        token_type, _, credentials = auth_header.partition(' ')

        auth_username = base64.b64encode(f"{settings.VFD_WEBHOOK_USERNAME}".encode()).decode()
        auth_password = base64.b64encode(f"{settings.VFD_WEBHOOK_PASSWORD}".encode()).decode()

        expected = base64.b64encode(f"{auth_username}:{auth_password}".encode()).decode()


        if token_type != 'Hook' or credentials != expected:
            raise VFD_Webhook_Whitelist_Exception()
        else:
            return True


class CoreBanking_Webhook_Whitelist(permissions.BasePermission):
    """
    Permission to check for Corebanking Whitelisted Hook for callback.
    """

    def has_permission(self, request, view):

        auth_header = request.headers.get('Authorization', '')

        token_type, _, credentials = auth_header.partition(' ')

        expected = settings.CORE_BANKING_WEBHOOK


        if token_type != 'Hook' or credentials != expected:
            raise VFD_Webhook_Whitelist_Exception()
        else:
            return True


class MetaMapViewWhitelist(permissions.BasePermission):
    """
    Permission to check for Metamap Webhook.
    """

    def has_permission(self, request, view):

        # print("HERE NOWWW")
        return True


class CheckWalletAvailable(permissions.BasePermission):
    """
    Permission to check if user has current Wallet.
    """

    def has_permission(self, request, view):

        user_instance = request.user

        get_all_wallets = WalletSystem.get_uncreated_wallets(user=user_instance)

        if "SPEND" not in get_all_wallets \
            and "COLLECTION" not in get_all_wallets:

            raise CheckWalletAvailableException()
        else:
            return True


class CheckAccountAvailable(permissions.BasePermission):
    """
    Permission to check if user has current Wallet.
    """

    def has_permission(self, request, view):

        user_instance = request.user
        current_provider = ConstantTable.default_account_provider()

        get_all_wallets = WalletSystem.get_uncreated_wallets(user=user_instance)

        if "SPEND" in get_all_wallets \
            and "COLLECTION" in get_all_wallets:

            # get_all_accounts = list(AccountSystem.get_uncreated_accounts(user=user_instance))

            # if get_all_accounts:
            #     needed_accounts_provs = [item.account_provider for item in get_all_accounts]
            #     needed_accounts_types = [item.account_type for item in get_all_accounts]

            #     print(needed_accounts_provs)
            #     print(needed_accounts_types)

                # if "COLLECTION" in needed_accounts_types and current_provider in needed_accounts_provs:

            # get_account_exists = AccountSystem.objects.filter(user = user_instance, account_type = "COLLECTION", account_provider = current_provider).last()

            get_all_accounts = AccountSystem.get_created_account_type(user=user_instance)

            if not any(current_provider in dct.values() for dct in get_all_accounts):
                raise CheckAccountAvailableException()

            else:
                return True

        else:
            raise CheckWalletAvailableException()

class CheckAdminPassword(permissions.BasePermission):
    """
    """

    def has_permission(self, request, view):

        auth_header = request.headers.get('Authorization', '')

        token_type, _, credentials = auth_header.partition(' ')


        expected = settings.EMEKA_ADMIN_PASSWORD


        if token_type != 'Hook' or credentials != expected:
            raise VFD_Webhook_Whitelist_Exception()
        else:
            return True


class CheckDynamicAuthentication(permissions.BasePermission):
    """
    """

    def has_permission(self, request, view):
        print("started with dynamic auth######")
        auth_header = request.headers.get('Authorization', '')
        if auth_header:
            if "Bearer" in auth_header:
                auth = JWTAuthentication()
                token = request.headers.get('Authorization').split()[1]
                user, _ = auth.authenticate(request)

            elif "Basic" in auth_header:
                auth = BasicAuthentication()
                user, _ = auth.authenticate(request)

            else:
                auth = CustomTokenAuthenticationWithKeyword()
                token = request.headers.get('Authorization').split()[1]
                user, _ = auth.authenticate_credentials(token)

            if user:
                request.user = user
                print("$$$$$$finished with dynamic auth")
                return user
            else:
                raise CheckDynamicAuthenticationException()

        else:
            raise CheckDynamicAuthenticationException()


class BasicAuthWithUsernameAsUsernameFieldAuthentication(permissions.BasePermission):
    """
    """

    def has_permission(self, request, view):
        auth_header = request.headers.get('Authorization', '')

        if auth_header and "Basic" in auth_header:
            base64_string = auth_header.split()[1]
            decoded_bytes = base64.b64decode(base64_string)
            print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::")
            print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::")
            print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::")
            decoded_string = decoded_bytes.decode('utf-8')

            get_username = decoded_string.split(":")[0]

            print(get_username)

            try:
                check_user = User.objects.get(username=get_username)
                print(check_user)
                get_token = Token.objects.filter(user=check_user).first()

                print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::")
                print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::")
                print(":::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::")

                get_user_token = get_token.key if get_token else None

            except:
                raise CheckDynamicAuthenticationException()

            auth = CustomTokenAuthenticationWithKeyword()
            user, _ = auth.authenticate_credentials(get_user_token)

            if user and user.check_password(decoded_string.split(":")[1]):
                return user
            else:
                raise CheckDynamicAuthenticationException()
        else:
            raise CheckDynamicAuthenticationException()


class AccountNumberBlacklist(permissions.BasePermission):
    """
    """

    def has_permission(self, request, view):
        request_data = request.data.get("data")
        if request_data is not None:
            for data in request_data:

                # get_account = Blacklist.objects.filter(Q(content__contains=[data.get("account_number", "KPK")]))
                # if get_account:

                get_blacklist = Blacklist.get_blacklist(list_type="ACCOUNT_NUMBERS")
                if get_blacklist and data.get("account_number", "KPK") in get_blacklist.content:
                    details = f"ALERTTTTTTTTTTT!!\n\nALERTTTTTTTTTTT!!\nUser with email: {request.user.email} sent funds to a fraudlent account number: {data.get('account_number')} and therefore has been suspended."

                    # request.user.is_suspended = True
                    # request.user.suspension_reason = details
                    # request.user.save()

                    User.suspend_user(
                        user=request.user,
                        reason=details
                    )

                    notify_admin_group(user=request.user, details = details)
                    print("ACCOUNT NUMBER HAS BEEN BLACKLISTED")
                    raise BlockBlacklistedAccountNumbers()
                else:
                    return True

        else:
            return False




class NoSendMoneyTransactionsAllowed(APIException):
    status_code = status.HTTP_405_METHOD_NOT_ALLOWED
    default_detail = {
        "error": "987",
        "message": "Sorry, cannot transfer money at this time. Please try again later"
    }
    default_code = "Not permitted"

class NoCardWithrawAllowed(APIException):
    status_code = status.HTTP_405_METHOD_NOT_ALLOWED
    default_detail = {
        "error": "987",
        "message": "Sorry, cannot perform card withdraw at this time. Please try again later"
    }
    default_code = "Not permitted"

class NoRegistrationEmailVerification(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "You need to confirm your Registration Passcode",
    }
    default_code = "Not permitted"


class NoKYCVerification(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "You Need to complete at least one level of KYC first",
        "complete_kyc_link": f"{settings.BASE_URL}/send/complete_kyc/",
    }
    default_code = "Not permitted"

class NoKYCLevelTwoVerification(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "You are not on KYC Level 2 yet. Go to your profile to complete your KYC verification",
    }
    default_code = "Not permitted"

class NoKYCLevelThreeVerification(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "You are not on KYC Level 3 yet. Go to your profile to complete your KYC verification",
    }
    default_code = "Not permitted"



class NoDynamicKYCLevelDeciderException(APIException):
    try:
        lotto_choice_const = int(ConstantTable.get_constant_table_instance().lotto_kyc_level_choice)
    except:
        lotto_choice_const = 3

    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": f"You are not on KYC Level {lotto_choice_const}. Go to your profile to complete your KYC verification",
    }
    default_code = "Not permitted"


class TransactionPinMaxRetry(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "You have exceed max retries for transaction. Please contact support to reset your pin",
    }
    default_code = "Not permitted"


class NoTransactionPin(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "You do not have any pin yet. Please create a transaction pin with link below",
        "create_transaction_pin_link": f"{settings.BASE_URL}/agency/user/create_transaction_pin/",
    }
    default_code = "Not permitted"


class CantSendMoneyException(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "You cannot send money at this time. Please complete KYC level 2 or contact customer support",
    }
    default_code = "Not permitted"

class EntryDisallowedException(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "Sorry, You cannot access this page.",
    }
    default_code = "Not permitted"


class VFD_Webhook_Whitelist_Exception(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "No Permission",
    }
    default_code = "Not permitted"

class CheckWalletAvailableException(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "No wallets Available. Create one or contact support",
    }
    default_code = "Not permitted"

class CheckAccountAvailableException(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "No Accounts Available. Create one or contact support",
    }
    default_code = "Not permitted"

class MetaMapViewWhitelistException(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "No Permission",
    }
    default_code = "Not permitted"

class OnlyAdminsAllowed(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "Only Admins are allowed to use this endpoint",
    }
    default_code = "Not permitted"

class CheckDynamicAuthenticationException(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": True,
        "detail": "invalid token",
        "message": "No authorization provided",
    }
    default_code = "Not permitted"


class BlockBlacklistedAccountNumbers(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "563",
        "detail": "error occured",
        "message": "error occured",
    }
    default_code = "Not permitted"


class UnregisteredDeviceException(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "98945",
        "detail": "error occured",
        "message": "Kindly register this device as this would provide you with an extra layer of security to help secure your account even more"

    }
    default_code = "Not permitted"


class IsAdminUserRole(BasePermission):
    def has_permission(self, request, view):
        try:
            AdminUserRole.objects.get(user=request.user)
        except AdminUserRole.DoesNotExist:
            return False
        return True


