import json
import requests
from django.conf import settings

def send_sms(phone, passcode):
    message = f"You're receiving this sms to compete your registration process. Your temporary password is {passcode}"
    url = "https://whispersms.xyz/transactional/send"
    headers = {
        "Authorization": f"Api_key {settings.WHISPER_ONBOARDING_KEY}",
        "Content-Type": "application/json",
    }
    payload = json.dumps(
        {
            "receiver": f"{phone}",
            "template": "4e08c548-54ba-453e-8cad-4876713a3f05",
            "place_holders": {"message": message},
        }
    )

    try:
        response = requests.request("POST", url, headers=headers, data=payload)
        res = response.json()

        new_resp = {
            "receiver": phone,
            "whisper_resp": res,
            "message": f"SMS SENT TO {phone}",
            "payload": payload,
            "sent": True,
        }

    except requests.exceptions.RequestException as e:
        res = "WHISPER IS DOWN"

        new_resp = {
            "receiver": phone,
            "whisper_resp": f"{e}",
            "message": res,
            "payload": payload,
            "sent": False,
        }
    return new_resp
