import requests
import json
from django.conf import settings


class ThirdPartyApis:
    bvn_verification_url = "http://main.hadada.co/identity/premium"
    verify_otp_url = "https://whispersms.xyz/2fa/verify/"
    providus_create_dynamic_account_url = (
        "http://**************:8088/appdevapi/api/PiPCreateDynamicAccountNumber"
    )
    providus_create_reserved_account_url = (
        "http://**************:8088/appdevapi/api/PiPCreateReservedAccountNumber"
    )
    haadaa_secrete_key = settings.HADADA_SECRET_KEY
    pos_auth_token = settings.POS_AUTH_TOKEN
    otp_url = "https://whispersms.xyz/2fa/message/"
    otp_app_id = settings.OTP_APP_ID
    otp_message_id = settings.OTP_MESSAGE_ID

    only_sms_otp_app_id = settings.ONLY_SMS_OTP_APP_ID
    only_sms_otp_message_id = settings.ONLY_SMS_OTP_MESSAGE_ID

    whisper_token = settings.OTP_TOKEN
    providus_signature = settings.PROVIDUS_SIGNATURE
    providus_client_id = settings.PROVIDUS_CLIENT_ID
    providus_header = {
        "Content-Type": "application/json",
        "X-Auth-Signature": providus_signature,
        "Client-Id": providus_client_id,
    }
    hadaa_header = {
        "Authorization": f"Bearer {haadaa_secrete_key}",
        "Content-Type": "application/json",
        "accept": "application/json",
    }

    whisper_header = {
        "Authorization": f"Token {whisper_token}",
        "Content-Type": "application/json",
    }

    @staticmethod
    def bvn_verification(bvn_no):
        payload = json.dumps({"ref": bvn_no, "type": "bvn", "lastname": "string"})

        response = requests.request(
            "POST",
            ThirdPartyApis.bvn_verification_url,
            headers=ThirdPartyApis.hadaa_header,
            data=payload,
        )

        return response.json()

    # ThirdPartyApis.bvn_verification(22195305414)

    @staticmethod
    def send_phone_otp(phone_no, no_callback):
        payload = json.dumps(
            {
                "applicationId": ThirdPartyApis.otp_app_id if not no_callback else ThirdPartyApis.only_sms_otp_app_id,
                "messageId": ThirdPartyApis.otp_message_id if not no_callback else ThirdPartyApis.only_sms_otp_message_id,
                "message_type": "INTERNAL-TEXT",
                "to": f"{phone_no}",
                "placeholders": {}
            }
        )

        print(payload)

        try:
            response = requests.request(
                "POST",
                ThirdPartyApis.otp_url,
                headers=ThirdPartyApis.whisper_header,
                data=payload,
                timeout=8
            )

      
        except requests.exceptions.RequestException as e:
            response = "WHISPER IS DOWN"


        return response


    @staticmethod
    def send_voice_otp(phone_no):
        payload = json.dumps(
            {
                "applicationId": ThirdPartyApis.otp_app_id,
                "messageId": ThirdPartyApis.otp_message_id,
                "message_type": "VOICE",
                "to": f"{phone_no}",
                "placeholders": {}
            }
        )

        try:
            response = requests.request(
                "POST",
                ThirdPartyApis.otp_url,
                headers=ThirdPartyApis.whisper_header,
                data=payload,
            )
        except requests.exceptions.RequestException as e:
            response = "WHISPER IS DOWN"



    @staticmethod
    def verify_otp(phone_no, otp_value):
        payload = json.dumps({"receiver": phone_no, "otp": otp_value})

        try:
            response = requests.request(
                "POST",
                ThirdPartyApis.verify_otp_url,
                headers=ThirdPartyApis.whisper_header,
                data=payload
            )
            
            resp = json.loads(response.text)

        except requests.exceptions.RequestException as e:
            resp = {
                "receiver": None,
                "verified": False,
                "otp": None
            }



        return resp

    # ThirdPartyApis.verify_otp("", "230882")

    @staticmethod
    def providus_create_new_dynamic_account(user_account_name):
        payload = json.dumps({"account_name": user_account_name})

        response = requests.request(
            "POST",
            ThirdPartyApis.providus_create_dynamic_account_url,
            headers=ThirdPartyApis.providus_header,
            data=payload,
        )

        return response.json()

    # ThirdPartyApis.providus_create_new_dynamic_account("jason derulo")
    @staticmethod
    def providus_create_reservered_account(account_name, bvn_no):
        payload = json.dumps({"account_name": account_name, "bvn": bvn_no})

        response = requests.request(
            "POST",
            ThirdPartyApis.providus_create_reserved_account_url,
            headers=ThirdPartyApis.providus_header,
            data=payload,
        )
        return response.json()


    @staticmethod
    def query_users_from_existing_form_data(phone_no):
        url = f"http://libertypay.live/api/customer/phone_number/{phone_no}"

        payload = {}
        headers = {"Authorization": f"Token {ThirdPartyApis.pos_auth_token}"}
        try:

            response = requests.request("GET", url, headers=headers, data=payload, timeout=10)
            return response.json()

        except requests.exceptions.RequestException:
            return None




    def horizon_pay_registration(**kwargs) -> dict:
        """
        Post data for pos agents to horizon pay backend
        """

        url = "http://**************/api/agent/register/"

        # payload = json.dumps(
        payload = {
                "name": kwargs["name"],
                "email": kwargs["email"],
                "customer_id": kwargs["customer_id"],
                "phone": kwargs["phone"],
                "address": kwargs["address"],
                "state": kwargs["state"],
            }


        headers = {
            "Accept": "application/json",
            "Authorization": f"Bearer {settings.HORIZON_BEARER}",
            "Content-Type": "application/json",
            "Cookie": f"XSRF-TOKEN={settings.HORIZON_COOKIE_TOKEN}",
        }

        response = requests.request("POST", url, headers=headers, json=payload, timeout=10)
        try:
            data = response.json()
            return {"error": False, "data": data}
        except Exception as e:
            return {"error": e}
