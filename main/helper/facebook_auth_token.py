import facebook as fb


def facebook_auth_token_helper(auth_token):
    fields = [
        "id",
        "name",
        "first_name",
        "short_name",
        "last_name",
        "middle_name",
        "birthday",
        "education",
        "email",
        "gender",
        "location",
        "hometown",
        "picture",
        "languages",
        "link",
        "website",
        "relationship_status",
        "accounts",
    ]
    try:
        graph = fb.GraphAPI(access_token=auth_token)
        user_profile = graph.request("/me?fields={}".format(fields))
    except Exception as e:
        return "{}".format(e)
    return user_profile


# if __name__ == '__main__':
#     access_token = 'EAANYIH05SqkBADPtb9dJoJnb18J73zZBifeDNELRI5cyZCx8W0KVLfv9MZBZBYzuZAoVtt2SDGEmZCO8QjMH7w0fxnrpdSNEpUt0YzBghnX0VHfLeHU2xlmH36nXWaTwXep9o7tfQOpbBdAcRxEtr3noXQXkB5kCuNlOx9gM5e1bAnTdsVhGo4RUeHg5XKoL1oamTLQrmcPeMuSkEqLlOl'
#     print(facebook_auth_token_helper(access_token))
