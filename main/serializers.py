from distutils.version import StrictVersion

from django.conf import settings
from django.contrib.auth import authenticate, get_user_model
from django.contrib.auth.models import update_last_login
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from djoser.conf import settings as djoser_settings
from djoser.serializers import TokenCreateSerializer, UserCreateSerializer
from rest_framework import exceptions, serializers, status
from rest_framework.exceptions import ValidationError
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.serializers import TokenRefreshSerializer
from rest_framework_simplejwt.tokens import RefreshToken

from accounts.models import AccountSystem, Transaction, WalletSystem
from accounts.tasks import create_wallets_and_accounts_task
from admin_dashboard.models import SalesRep
from horizon_pay.models import TerminalSerialTable
from kyc_app.models import BVNDetail, KYCTable, GuarantorDetail
from liberty_pay.exceptions import InvalidRequestException
from main.helper.send_emails import send_login_notify_email
from main.models import *
from main.permissions import UnregisteredDeviceException


class CustomTokenCreateSerializer(TokenObtainPairSerializer):
    def validate(self, attrs):
        password = attrs.get("password")
        params = {djoser_settings.LOGIN_FIELD: attrs.get(djoser_settings.LOGIN_FIELD).lower()}
        self.user = authenticate(**params, password=password)
        if not self.user:
            self.user = User.objects.filter(**params).first()
            if self.user and not self.user.check_password(password):
                self.fail("invalid_credentials")
        if self.user and not self.user.is_active:
            # message that the user is not active
            raise ValidationError("User is not activated. Please check your email for a confirmation email.")
        elif self.user and self.user.is_active:
            return attrs
        self.fail("invalid_credentials")


def custom_update_last_login(sender, user, device_type, **kwargs):
    """
    A signal receiver which updates the last_login date for
    the user logging in.
    """
    from django.utils import timezone

    now = timezone.now()

    if not user.last_login:
        user.first_login = now

    user.login_count += 1
    user.last_login = now
    user.passcode_retries = 0
    user.passcode_remaining_retries = 10

    if device_type == "TERMINAL":
        user.terminal_login_count += 1
        user.terminal_last_login = now

    elif device_type == "MOBILE":
        user.mobile_login_count += 1
        user.mobile_last_login = now

    elif device_type == "WEB":
        user.web_login_count += 1
        user.web_last_login = now

    user.save()


class CustomTokenObtainSerializer(TokenObtainPairSerializer):

    device_type = serializers.ChoiceField(required=True, choices=DEVICE_TYPE_CHOICES)
    serial_no = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    version = serializers.CharField(required=False)
    email = serializers.EmailField()

    def validate(self, attrs):
        device_type = attrs["device_type"]
        serial_no = attrs.get("serial_no")
        version = attrs.get("version")
        email = attrs[self.username_field].lower()
        try:
            email_validation = validate_email(email)
        except ValidationError as e:
            self.error_messages["error"] = "Enter a valid email address."
            raise exceptions.AuthenticationFailed(self.error_messages["error"])

        authenticate_kwargs = {
            self.username_field: email,
            "password": attrs["password"],
            # 'device_type': attrs['device_type'],
        }
        try:
            authenticate_kwargs["request"] = self.context["request"]
            authenticate_kwargs["email"] = authenticate_kwargs["email"].lower()
        except KeyError:
            pass

        """
        Checking if the user exists by getting the email(username field) from authentication_kwargs.
        If the user exists we check if the user account is active.
        If the user account is not active we raise the exception and pass the message.
        Thus stopping the user from getting authenticated altogether.

        And if the user does not exist at all we raise an exception with a different error message.
        Thus stopping the execution righ there.
        """
        try:
            user = User.objects.get(email=authenticate_kwargs["email"])

            # request_data = self.context['request']
            # print("request_data: ", request_data.headers)

            # get_device_token = request_data.headers.get("device_token")

            # if not RegisteredDevice.objects.filter(user=user, device_type=device_type, device_token=get_device_token).exists():
            #     raise UnregisteredDeviceException()

            # version_number_without_dots = "".join(char for char in version if char != ".")

            # if authenticate_kwargs['email'] in ["<EMAIL>", "<EMAIL>"]:

            print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
            print(attrs)
            print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")

            from horizon_pay.models import DebugTerminal

            if (
                device_type == "TERMINAL"
                and version
                and not DebugTerminal.objects.filter(serial_no=serial_no, is_active=True).exists()
            ):
                try:
                    strict_input_version = StrictVersion(version)
                except:
                    self.error_messages["error"] = f"Invalid Version Number"
                    raise exceptions.AuthenticationFailed(self.error_messages["error"])

                backend_version_number = ConstantTable.get_constant_table_instance().pos_app_latest_version
                strict_backend_version_number = StrictVersion(backend_version_number)

                print(type(strict_backend_version_number), strict_backend_version_number)
                print(type(strict_input_version), strict_input_version)

                print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
                # print(attrs)
                print(version)
                print(serial_no)
                print(strict_backend_version_number <= strict_input_version)
                print(str(serial_no) == str(user.terminal_serial))
                print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")

                if strict_input_version < strict_backend_version_number:

                    self.error_messages["error"] = (
                        f"Kindly Update Your App to the Latest Version - {backend_version_number}"
                    )
                    raise exceptions.AuthenticationFailed(self.error_messages["error"])

                # if device_type == "TERMINAL" and str(serial_no) != str(user.terminal_serial):
                #     self.error_messages['error'] = (
                #         'Cannot Sign into Unregistered Terminal. Please Contact Admin'
                #     )
                #     raise exceptions.AuthenticationFailed(
                #         self.error_messages['error']
                #     )

                if device_type == "TERMINAL" and user.terminal_id is None:
                    self.error_messages["error"] = "No Terminal ID is assigned to this account. Please Contact Admin"
                    raise exceptions.AuthenticationFailed(self.error_messages["error"])

            if user.registration_email_verified == False:
                self.error_messages["error"] = "You need to confirm your Registration Passcode"
                raise exceptions.AuthenticationFailed(self.error_messages["error"])

            if User.check_user_login_pin(user) == False:
                self.error_messages["error"] = "Please Reset Your Login Pin"
                raise exceptions.AuthenticationFailed(self.error_messages["error"])

            if user.is_suspended:
                self.error_messages["error"] = "Account Has Been Suspended. Please Contact Admin"
                raise exceptions.AuthenticationFailed(self.error_messages["error"])

            if device_type == "TERMINAL" and user.type_of_user not in list_of_allowed_terminal_users:
                self.error_messages["error"] = (
                    "Only Agents can sign in with Terminal. Please sign in via a mobile device to continue transactions"
                )
                raise exceptions.AuthenticationFailed(self.error_messages["error"])

            if device_type == "MOBILE" and user.mobile_suspended:
                self.error_messages["error"] = "This Account Has Been Suspended. Please Contact Admin"
                raise exceptions.AuthenticationFailed(self.error_messages["error"])

            if (
                device_type == "WEB"
                and user.email not in ConstantTable.get_constant_table_instance().temp_admin_dash_users
            ):
                self.error_messages["error"] = "Sorry, you cannot login"
                raise exceptions.AuthenticationFailed(self.error_messages["error"])

            if device_type == "TERMINAL" and user.terminal_suspended:
                self.error_messages["error"] = "This Terminal Has Been Suspended. Please Contact Admin"
                raise exceptions.AuthenticationFailed(self.error_messages["error"])

            if device_type == "TERMINAL" and user.terminal_disabled:
                self.error_messages["error"] = (
                    "Your terminal has been disabled. Kindly, contact your relationship officer."
                )
                raise exceptions.AuthenticationFailed(self.error_messages["error"])

        except User.DoesNotExist:
            self.error_messages["no_active_account"] = "Incorrect email or password"
            raise exceptions.AuthenticationFailed(
                self.error_messages["no_active_account"],
                # 'no_active_account',
            )

        authenticate_kwargs["email"] = user.email.lower()

        self.user = authenticate(email=user.email.lower(), password=authenticate_kwargs["password"])
        if self.user is None:
            retries = User.count_down_passcode_retries(user)
            self.error_messages["no_active_account"] = (
                f"Incorrect email or password. {retries['retry_count']} trials, {retries['remaining_retries']} remaining"
            )

            raise exceptions.AuthenticationFailed(
                self.error_messages["no_active_account"],
                # 'no_active_account',
            )

        # User.reset_passcode_retries(user)
        custom_update_last_login(None, self.user, device_type)

        user_full_name = user.bvn_full_name.upper() if user.bvn_first_name else user.full_name.upper()

        # if user.first_name is None or user.last_name is None or user.type_of_user != "PERSONAL":
        #     pass
        # else:
        #     send_login_notify_email(user_email=user.email, user_full_name=user_full_name, login_time=timezone.now())

        return super().validate(attrs)
    
class PhoneCustomTokenObtainSerializer(TokenObtainPairSerializer):
    username_field = "phone_number"

    device_type = serializers.ChoiceField(required=True, choices=DEVICE_TYPE_CHOICES)
    serial_no = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    version = serializers.CharField(required=False)
    

    def validate(self, attrs):
        device_type = attrs["device_type"]
        serial_no = attrs.get("serial_no")
        version = attrs.get("version")
        phone_number = attrs.get(self.username_field)
        # try:
        #     email_validation = validate_email(email)
        # except ValidationError as e:
        #     self.error_messages["error"] = "Enter a valid email address."
        #     raise exceptions.AuthenticationFailed(self.error_messages["error"])

        authenticate_kwargs = {
            self.username_field: phone_number,
            "password": attrs["password"],
            # 'device_type': attrs['device_type'],
        }
        try:
            authenticate_kwargs["request"] = self.context["request"]
        except KeyError:
            pass

        """
        Checking if the user exists by getting the email(username field) from authentication_kwargs.
        If the user exists we check if the user account is active.
        If the user account is not active we raise the exception and pass the message.
        Thus stopping the user from getting authenticated altogether.

        And if the user does not exist at all we raise an exception with a different error message.
        Thus stopping the execution righ there.
        """
        try:
            user = User.objects.get(phone_number=authenticate_kwargs["phone_number"])

            # request_data = self.context['request']
            # print("request_data: ", request_data.headers)

            # get_device_token = request_data.headers.get("device_token")

            # if not RegisteredDevice.objects.filter(user=user, device_type=device_type, device_token=get_device_token).exists():
            #     raise UnregisteredDeviceException()

            # version_number_without_dots = "".join(char for char in version if char != ".")

            # if authenticate_kwargs['email'] in ["<EMAIL>", "<EMAIL>"]:

            print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
            print(attrs)
            print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")

            from horizon_pay.models import DebugTerminal

            if (
                device_type == "TERMINAL"
                and version
                and not DebugTerminal.objects.filter(serial_no=serial_no, is_active=True).exists()
            ):
                try:
                    strict_input_version = StrictVersion(version)
                except:
                    self.error_messages["error"] = f"Invalid Version Number"
                    raise exceptions.AuthenticationFailed(self.error_messages["error"])

                backend_version_number = ConstantTable.get_constant_table_instance().pos_app_latest_version
                strict_backend_version_number = StrictVersion(backend_version_number)

                print(type(strict_backend_version_number), strict_backend_version_number)
                print(type(strict_input_version), strict_input_version)

                print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
                # print(attrs)
                print(version)
                print(serial_no)
                print(strict_backend_version_number <= strict_input_version)
                print(str(serial_no) == str(user.terminal_serial))
                print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")

                if strict_input_version < strict_backend_version_number:

                    self.error_messages["error"] = (
                        f"Kindly Update Your App to the Latest Version - {backend_version_number}"
                    )
                    raise exceptions.AuthenticationFailed(self.error_messages["error"])

                # if device_type == "TERMINAL" and str(serial_no) != str(user.terminal_serial):
                #     self.error_messages['error'] = (
                #         'Cannot Sign into Unregistered Terminal. Please Contact Admin'
                #     )
                #     raise exceptions.AuthenticationFailed(
                #         self.error_messages['error']
                #     )

                if device_type == "TERMINAL" and user.terminal_id is None:
                    self.error_messages["error"] = "No Terminal ID is assigned to this account. Please Contact Admin"
                    raise exceptions.AuthenticationFailed(self.error_messages["error"])

            if user.registration_email_verified == False:
                self.error_messages["error"] = "You need to confirm your Registration Passcode"
                raise exceptions.AuthenticationFailed(self.error_messages["error"])

            if User.check_user_login_pin(user) == False:
                self.error_messages["error"] = "Please Reset Your Login Pin"
                raise exceptions.AuthenticationFailed(self.error_messages["error"])

            if user.is_suspended:
                self.error_messages["error"] = "Account Has Been Suspended. Please Contact Admin"
                raise exceptions.AuthenticationFailed(self.error_messages["error"])

            if device_type == "TERMINAL" and user.type_of_user not in list_of_allowed_terminal_users:
                self.error_messages["error"] = (
                    "Only Agents can sign in with Terminal. Please sign in via a mobile device to continue transactions"
                )
                raise exceptions.AuthenticationFailed(self.error_messages["error"])

            if device_type == "MOBILE" and user.mobile_suspended:
                self.error_messages["error"] = "This Account Has Been Suspended. Please Contact Admin"
                raise exceptions.AuthenticationFailed(self.error_messages["error"])

            if (
                device_type == "WEB"
                and user.email not in ConstantTable.get_constant_table_instance().temp_admin_dash_users
            ):
                self.error_messages["error"] = "Sorry, you cannot login"
                raise exceptions.AuthenticationFailed(self.error_messages["error"])

            if device_type == "TERMINAL" and user.terminal_suspended:
                self.error_messages["error"] = "This Terminal Has Been Suspended. Please Contact Admin"
                raise exceptions.AuthenticationFailed(self.error_messages["error"])

            if device_type == "TERMINAL" and user.terminal_disabled:
                self.error_messages["error"] = (
                    "Your terminal has been disabled. Kindly, contact your relationship officer."
                )
                raise exceptions.AuthenticationFailed(self.error_messages["error"])

        except User.DoesNotExist:
            self.error_messages["no_active_account"] = "Incorrect email or password"
            raise exceptions.AuthenticationFailed(
                self.error_messages["no_active_account"],
                # 'no_active_account',
            )

        authenticate_kwargs["email"] = user.email.lower()

        self.user = authenticate(email=user.email.lower(), password=authenticate_kwargs["password"])

        if self.user is None:
            retries = User.count_down_passcode_retries(user)
            self.error_messages["no_active_account"] = (
                f"Incorrect email or password. {retries['retry_count']} trials, {retries['remaining_retries']} remaining"
            )

            raise exceptions.AuthenticationFailed(
                self.error_messages["no_active_account"],
                # 'no_active_account',
            )

        # User.reset_passcode_retries(user)
        custom_update_last_login(None, self.user, device_type)

        user_full_name = user.bvn_full_name.upper() if user.bvn_first_name else user.full_name.upper()

        # if user.first_name is None or user.last_name is None or user.type_of_user != "PERSONAL":
        #     pass
        # else:
        #     send_login_notify_email(user_email=user.email, user_full_name=user_full_name, login_time=timezone.now())
        refresh = self.get_token(self.user)
        data = {
            "refresh": str(refresh),
            "access": str(refresh.access_token),
        }
        return data

    @classmethod
    def get_token(cls, user):
        # Create a refresh token
        token = RefreshToken.for_user(user)
        # Add custom claims if necessary
        token["username"] = user.username
        return token


class CutomObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainSerializer

class PhoneCutomObtainPairView(TokenObtainPairView):
    serializer_class = PhoneCustomTokenObtainSerializer


######################################################################################
# USER
class UserSerializer(serializers.ModelSerializer):
    user_branch = serializers.SerializerMethodField(read_only=True)
    has_terminal = serializers.SerializerMethodField(read_only=True)
    bvn_number = serializers.SerializerMethodField(read_only=True)
    agent_profile = serializers.SerializerMethodField(read_only=True)

    def get_agent_profile(self, obj):
        agent = SuperAgentProfile.objects.filter(agent=obj)
        if agent.exists():
            agent_profile = agent.last()
            return {
                "is_verified": agent_profile.is_verified,
                "is_supervisor": agent_profile.is_supervisor,
                "team_lead_id": agent_profile.team_lead_id if agent_profile.team_lead else None,
                "team_lead_name": agent_profile.team_lead.full_name if agent_profile.team_lead else None,
                "team_lead_email": agent_profile.team_lead.email if agent_profile.team_lead else None,
                "team_lead_phone_number": agent_profile.team_lead.phone_number if agent_profile.team_lead else None,
                "super_agent_id": agent_profile.super_agent_id if agent_profile.super_agent else None,
                "super_agent_name": agent_profile.super_agent.full_name if agent_profile.super_agent else None,
                "super_agent_email": agent_profile.super_agent.email if agent_profile.super_agent else None,
                "super_agent_phone_number": agent_profile.super_agent.phone_number if agent_profile.super_agent else None,
                "supervisor_id": agent_profile.supervisor_id if agent_profile.supervisor else None,
                "supervisor_name": agent_profile.supervisor.full_name if agent_profile.supervisor else None,
                "supervisor_email": agent_profile.supervisor.email if agent_profile.supervisor else None,
                "supervisor_phone_number": agent_profile.supervisor.phone_number if agent_profile.supervisor else None,
            }
        return None

    def get_bvn_number(self, obj):
        bvn_number = mask_account_number(obj.bvn_number) if obj.bvn_number else ""
        return bvn_number

    def get_has_terminal(self, obj):
        term_status = False
        term_list = list()
        terminals = TerminalSerialTable.objects.filter(user=obj)
        if terminals:
            term_status = True
            term_list = [term.terminal_id for term in terminals]
        return {
            "status": term_status,
            "terminal_ids": term_list
        }

    class Meta:
        model = User
        ref_name = "User1"
        exclude = [
            "update_email",
            "created_at",
            "updated_at",
            "is_staff",
            "is_superuser",
            "email_updated",
            "groups",
            "user_permissions",
            "password",
            "transaction_pin",
            "registration_email_otp",
            "first_security_answer",
            "second_security_answer",
            "change_pass_hash",
            "change_pass_hash_time",
            "firebase_key"
        ]

    def get_user_branch(self, inst: User):
        return inst.user_branch.sub_location if inst.user_branch else None

    def get_user_referrals(self, inst: User):
        sales_rep = SalesRep.objects.filter(sales_rep=inst).last()
        if sales_rep:
            referral_code = sales_rep.sales_rep_code
        else:
            referral_code = inst.referral_code

        referrals_qs = User.objects.filter(sales_rep_upline_code=referral_code).exclude(email=inst.email)
        referrals_count = referrals_qs.count()
        referrals_emails = referrals_qs.values_list("email", flat=True)

        data = {
            "referral_code": referral_code,
            "referrals_count": referrals_count,
            "referrals_emails": referrals_emails,
        }
        return data


class UpdateUsernameSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["email", "first_name", "last_name", "username"]


class GetAccountSerializer(serializers.ModelSerializer):
    class Meta:
        model = AccountSystem
        fields = [
            "account_type",
            "true_account_type",
            "bank_name",
            "account_number",
            "account_name",
            "other_balance",
        ]


class GetWalletSerializer(serializers.ModelSerializer):

    # def get_available_balance(self, wallet: WalletSystem):
    #     return wallet.available_balance + wallet.hold_balance
    class Meta:
        model = WalletSystem
        fields = ["wallet_type", "available_balance", "hold_balance"]

    def to_representation(self, data):
        data = super(GetWalletSerializer, self).to_representation(data)
        data["available_balance"] = data.get("available_balance") + data.get("hold_balance")
        return data


class UserCreateSerializer(UserCreateSerializer):
    class Meta(UserCreateSerializer.Meta):
        model = User
        fields = "__all__"


class SendOtpSerializer(serializers.ModelSerializer):
    class Meta:
        model = Users_Otp
        fields = ["phone_no", "app_name"]

    def is_valid(self, raise_exception=False):
        if self._kwargs["data"]["phone_no"].isnumeric():
            pass
        else:
            raise serializers.ValidationError({"error_code": "14", "message": "You must supply an integer"})
        return super().is_valid(raise_exception)


class OTPVerificationSerializer(serializers.Serializer):
    phone_number = serializers.CharField(required=True)
    otp = serializers.CharField(required=True, min_length=6, max_length=6)
    app_name = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    onboarding_status = serializers.ChoiceField(required=False, allow_blank=True, allow_null=True, choices=ONBOARDING_STATUS)

    def is_valid(self, raise_exception=False):
        if not self._kwargs["data"].get("phone_number"):
            raise serializers.ValidationError({"error_code": "14", "message": "phone_number is a required field"})
        if self._kwargs["data"]["phone_number"].isnumeric():
            pass
        else:
            raise serializers.ValidationError({"error_code": "14", "message": "invalid phone_number format"})
        if not self._kwargs["data"].get("otp"):
            raise serializers.ValidationError({"error_code": "14", "message": "otp is a required field"})
        if self._kwargs["data"]["otp"].isnumeric():
            pass
        else:
            raise serializers.ValidationError({"error_code": "14", "message": "You must supply an integer for OTP"})
        return super().is_valid(raise_exception)


class PinCreateSerializer(serializers.Serializer):
    phone_number = serializers.CharField(required=True)
    pin1 = serializers.CharField(required=True, min_length=6, max_length=6)
    pin2 = serializers.CharField(required=True, min_length=6, max_length=6)
    onboarding_status = serializers.ChoiceField(required=False, allow_blank=True, allow_null=True, choices=ONBOARDING_STATUS)

    def is_valid(self, raise_exception=False):
        if not self._kwargs["data"].get("phone_number"):
            raise serializers.ValidationError({"error_code": "14", "message": "phone_number is a required field"})
        if not self._kwargs["data"].get("pin1"):
            raise serializers.ValidationError({"error_code": "14", "message": "pin1 is a required field"})
        if self._kwargs["data"]["phone_number"].isnumeric() and self._kwargs["data"]["pin1"].isnumeric():
            pass
        else:
            raise serializers.ValidationError(
                {"error_code": "14", "message": "You must supply an integer for phone number, pin1 and pin2"}
            )
        return super().is_valid(raise_exception)


class ResetPinSerializer(serializers.Serializer):
    phone_number = serializers.CharField(required=True)
    pin1 = serializers.CharField(required=True, min_length=6, max_length=6)
    pin2 = serializers.CharField(required=True, min_length=6, max_length=6)
    otp_value = serializers.CharField(required=True, min_length=6, max_length=6)

    def is_valid(self, raise_exception=False):
        if self._kwargs["data"]["pin1"].isnumeric() and self._kwargs["data"]["otp_value"].isnumeric():
            pass
        else:
            raise serializers.ValidationError(
                {"error_code": "14", "message": "You must supply an integer for pin1, pin2 and otp_value"}
            )
        return super().is_valid(raise_exception)


class EmailAndReferralCodeSerializer(serializers.Serializer):
    email = serializers.EmailField()
    referral_code = serializers.CharField()


class PersonalInfoSerializer(serializers.Serializer):
    state = serializers.CharField(max_length=250)
    lga = serializers.CharField(max_length=250)
    nearestlandmark = serializers.CharField(max_length=250)
    street = serializers.CharField(max_length=250)


class PasscodeResetSerializer(serializers.Serializer):
    phone_no = serializers.CharField(max_length=20)

    def is_valid(self, raise_exception=False):
        if self._kwargs["data"]["phone_no"].isnumeric():
            pass
        else:
            raise serializers.ValidationError(
                {"error_code": "14", "message": "entered phone number contains characters"}
            )
        return super().is_valid(raise_exception)


class PhoneSerializer(serializers.Serializer):
    # phone_number = serializers.CharField(required=True, max_length=11)
    phone_number = serializers.CharField(required=True)
    type_of_user = serializers.ChoiceField(required=True, choices=TYPE_OF_TERMINAL_USER_CHOICE)
    referral_code = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    business_local_govt = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    supervisor_id = serializers.CharField(required=False, allow_blank=True, allow_null=True)

    # def is_valid(self, raise_exception=False):
    #     if not self.initial_data.get("phone_number", "").isnumeric():
    #         raise serializers.ValidationError(
    #             {"error": "14", "message": "Phone number must contain only numeric digits."}
    #         )

    #     if not self.initial_data.get("phone_number", "") != 11:
    #         raise serializers.ValidationError({"error": "14", "message": "Ensure phone number field is 11 characters"})

    #     return super().is_valid(raise_exception=raise_exception)

    def is_valid(self, raise_exception=False):

        phone_number = self.initial_data.get("phone_number", "")
        is_phone_number_valid_status, is_phone_number_valid_msg = is_phone_number_valid(phone_number=phone_number)
        if is_phone_number_valid_status == False:
            raise serializers.ValidationError({"error_code": "14", "message": is_phone_number_valid_msg})

        return super().is_valid(raise_exception=raise_exception)


class UpdateEmailSerializer(serializers.Serializer):
    app_name = serializers.CharField(required=True)
    previous_email = serializers.EmailField(required=True)
    new_email = serializers.EmailField(required=True)


class VerifyRegistrationPasscodeSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    passcode = serializers.CharField(write_only=True)
    onboarding_status = serializers.ChoiceField(required=False, allow_blank=True, allow_null=True, choices=ONBOARDING_STATUS)

    def is_valid(self, raise_exception=False):
        if self._kwargs["data"]["passcode"].isnumeric():
            pass
        else:
            raise serializers.ValidationError({"error_code": "14", "message": "You must supply an integer"})
        return super().is_valid(raise_exception=raise_exception)
    
class VerifyPhoneRegistrationPasscodeSerializer(serializers.Serializer):
    phone_number = serializers.CharField(required=True)
    passcode = serializers.CharField(write_only=True)
    onboarding_status = serializers.ChoiceField(required=False, allow_blank=True, allow_null=True, choices=ONBOARDING_STATUS)

    def is_valid(self, raise_exception=False):
        if self._kwargs["data"]["passcode"].isnumeric():
            pass
        else:
            raise serializers.ValidationError({"error_code": "14", "message": "You must supply an integer"})
        return super().is_valid(raise_exception=raise_exception)

GENDER_CHOICES = [("Male", "MALE"), ("Female", "FEMALE")]


class CreateUserDetailInfoSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=25, required=True)
    username = serializers.CharField(max_length=25, required=True)
    first_name = serializers.CharField(max_length=50, required=True)
    last_name = serializers.CharField(max_length=50, required=True)
    email = serializers.EmailField(required=True)
    state = serializers.CharField(max_length=150, required=True)
    lga = serializers.CharField(max_length=150, required=True)
    nearest_landmark = serializers.CharField(max_length=250, required=True)
    street = serializers.CharField(max_length=150, required=True)
    type_of_user = serializers.ChoiceField(required=False, choices=TYPE_OF_TERMINAL_USER_CHOICE)
    is_paybox_merchant = serializers.BooleanField(required=False)
    referral_code = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    business_name = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    gender = serializers.ChoiceField(required=False, choices=GENDER_CHOICES)
    device_type = serializers.ChoiceField(
        required=False, choices=DEVICE_TYPE_CHOICES, allow_blank=True, allow_null=True
    )
    device_registration_code = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    source = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    onboarding_status = serializers.ChoiceField(required=False, allow_blank=True, allow_null=True, choices=ONBOARDING_STATUS)

    def is_valid(self, raise_exception=False):
        if not self._kwargs["data"].get("phone_number"):
            raise serializers.ValidationError({"error_code": "14", "message": "phone_number is a required field"})

        # if self._kwargs["data"]["phone_number"].isnumeric():
        #     pass
        # else:
        #     raise serializers.ValidationError(
        #         {"error_code": "14", "message": "You must supply an integer for phone_number"}
        #     )

        is_phone_number_valid_status, is_phone_number_valid_msg = is_phone_number_valid(
            phone_number=self._kwargs["data"].get("phone_number")
        )
        if is_phone_number_valid_status == False:
            raise serializers.ValidationError({"error_code": "14", "message": is_phone_number_valid_msg})

        return super().is_valid(raise_exception=raise_exception)
    
class CreateUserPhoneDetailInfoSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=25, required=True)
    username = serializers.CharField(max_length=25, required=True)
    first_name = serializers.CharField(max_length=50, required=True)
    last_name = serializers.CharField(max_length=50, required=True)
    state = serializers.CharField(max_length=150, required=True)
    lga = serializers.CharField(max_length=150, required=True)
    nearest_landmark = serializers.CharField(max_length=250, required=True)
    street = serializers.CharField(max_length=150, required=True)
    type_of_user = serializers.ChoiceField(required=False, choices=TYPE_OF_TERMINAL_USER_CHOICE)
    is_paybox_merchant = serializers.BooleanField(required=False)
    referral_code = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    business_name = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    gender = serializers.ChoiceField(required=False, choices=GENDER_CHOICES)
    device_type = serializers.ChoiceField(
        required=False, choices=DEVICE_TYPE_CHOICES, allow_blank=True, allow_null=True
    )
    device_registration_code = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    source = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    onboarding_status = serializers.ChoiceField(required=False, allow_blank=True, allow_null=True, choices=ONBOARDING_STATUS)

    def is_valid(self, raise_exception=False):
        if not self._kwargs["data"].get("phone_number"):
            raise serializers.ValidationError({"error_code": "14", "message": "phone_number is a required field"})

        # if self._kwargs["data"]["phone_number"].isnumeric():
        #     pass
        # else:
        #     raise serializers.ValidationError(
        #         {"error_code": "14", "message": "You must supply an integer for phone_number"}
        #     )

        is_phone_number_valid_status, is_phone_number_valid_msg = is_phone_number_valid(
            phone_number=self._kwargs["data"].get("phone_number")
        )
        if is_phone_number_valid_status == False:
            raise serializers.ValidationError({"error_code": "14", "message": is_phone_number_valid_msg})

        return super().is_valid(raise_exception=raise_exception)


# SETTINGS
class EmailAndPhoneSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    phone_number = serializers.CharField(required=True)

    def is_valid(self, raise_exception=False):
        if self._kwargs["data"]["phone_number"].isnumeric():
            pass
        else:
            raise serializers.ValidationError(
                {"error_code": "14", "message": "You must supply an integer for phone number"}
            )
        return super().is_valid(raise_exception)


# RESENDS
class ResendRegistrationPinSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)

class ResendRegistrationPhonePinSerializer(serializers.Serializer):
    phone_number = serializers.CharField(required=True)


class FirstResendPasswordSerializer(serializers.Serializer):
    entry_type = serializers.CharField(required=True)
    identifier = serializers.CharField(required=True)


#####################################################################
# Create/Get Transaction Pin Serializer


class CreateTransactionPINSerializer(serializers.Serializer):
    transaction_pin = serializers.CharField()
    transaction_pin_retry = serializers.CharField()

    def is_valid(self, raise_exception=False):
        if self._kwargs["data"]["transaction_pin"].isnumeric():
            pass
        else:
            raise serializers.ValidationError({"error_code": "14", "message": "You must supply an integer"})
        return super().is_valid(raise_exception)


class ResetPINSerializer(serializers.Serializer):
    old_pin = serializers.CharField()
    new_pin = serializers.CharField()
    new_pin_retry = serializers.CharField()


class CreateMerchantPINSerializer(serializers.Serializer):
    merchant_pin = serializers.CharField()
    merchant_pin_retry = serializers.CharField()

    def is_valid(self, raise_exception=False):
        if self._kwargs["data"]["merchant_pin"].isnumeric():
            pass
        else:
            raise serializers.ValidationError({"error_code": "14", "message": "You must supply an integer"})
        return super().is_valid(raise_exception)


class VerifyTransactionPinSerializer(serializers.Serializer):
    transaction_pin = serializers.CharField()

    def is_valid(self, raise_exception=False):
        if self._kwargs["data"]["transaction_pin"].isnumeric():
            pass
        else:
            raise serializers.ValidationError({"error_code": "14", "message": "You must supply an integer"})
        return super().is_valid(raise_exception)


class OtherServiceVerifyTransactionPinSerializer(serializers.Serializer):
    user_id = serializers.IntegerField()
    transaction_pin = serializers.CharField()

    def is_valid(self, raise_exception=False):
        if self._kwargs["data"].get("transaction_pin") is not None:
            if self._kwargs["data"].get("transaction_pin").isnumeric():
                pass
            else:
                raise serializers.ValidationError(
                    {"error_code": "14", "message": "You must supply an integer wrapped in quotes for transaction pin"}
                )
        else:
            raise serializers.ValidationError(
                {"error_code": "14", "message": "You must supply an integer wrapped in quotes for transaction pin"}
            )
        return super().is_valid(raise_exception)


#########################################################################
# Charge Band Serializer


class ChargeBandSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChargeBand
        fields = [
            "transaction_type",
            "lower_limit",
            "upper_limit",
            "transaction_charge_percent",
            "transaction_charge_value",
        ]


#########################################################################
# Constant Serializer


class ConstantSerializer(serializers.ModelSerializer):
    charge_band = ChargeBandSerializer(many=True)

    class Meta:
        model = ConstantTable
        fields = ["charge_band", "pos_app_latest_version"]


class CollectFBKSerializer(serializers.Serializer):
    fbk = serializers.CharField(required=True, allow_null=True, allow_blank=True)


#####################################################################
# Check KYC Serializer


class KYCSerializer(serializers.ModelSerializer):
    class Meta:
        model = KYCTable
        fields = ["user", "is_kyc_level_one", "is_kyc_level_two", "kyc_level"]


class CheckFirstTimerSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = [
            "email",
            "type_of_user",
            "registration_email_verified",
            "has_login_pin",
            "has_transaction_pin",
        ]


class SecurityQuestionSerializer(serializers.Serializer):
    question = serializers.CharField(required=True, max_length=600)


class SecurityAnswerSerializer(serializers.Serializer):
    first_security_question = serializers.ChoiceField(required=True, choices=settings.SECURITY_QUESTION_CHOICES)
    first_security_answer = serializers.CharField(required=True, max_length=200)
    second_security_question = serializers.ChoiceField(required=True, choices=settings.SECURITY_QUESTION_CHOICES)
    second_security_answer = serializers.CharField(required=True, max_length=200)
    onboarding_status = serializers.ChoiceField(required=False, allow_blank=True, allow_null=True, choices=ONBOARDING_STATUS)


class SecurityAnswerSerializerV2(serializers.Serializer):
    email = serializers.CharField()
    first_security_question = serializers.ChoiceField(required=True, choices=settings.SECURITY_QUESTION_CHOICES)
    first_security_answer = serializers.CharField(required=True, max_length=200)
    second_security_question = serializers.ChoiceField(required=True, choices=settings.SECURITY_QUESTION_CHOICES)
    second_security_answer = serializers.CharField(required=True, max_length=200)
    onboarding_status = serializers.ChoiceField(required=False, allow_blank=True, allow_null=True, choices=ONBOARDING_STATUS)


class FirebaseTokenSerializer(serializers.Serializer):
    firebase_key = serializers.CharField(max_length=2300, required=True)


class SMSSubscribeSerializer(serializers.Serializer):
    sms_subscription = serializers.BooleanField()


class UsersWithTransLoansSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True)
    email = serializers.EmailField(required=True)
    bvn = serializers.SerializerMethodField()
    account_number = serializers.SerializerMethodField()
    bank_code = serializers.SerializerMethodField()
    first_name = serializers.CharField(required=True)
    last_name = serializers.CharField(required=True)
    username = serializers.CharField(required=True)
    phone_number = serializers.CharField(required=True)
    street = serializers.CharField(required=True)
    lga = serializers.CharField(required=True)
    referral_code = serializers.CharField(required=True)
    nearest_landmark = serializers.CharField(required=True)
    state = serializers.CharField(required=True)
    agent_consent = serializers.BooleanField(required=True)
    date_joined = serializers.DateTimeField(required=True)
    type_of_user = serializers.CharField(required=True)
    kyc_level = serializers.IntegerField(required=True)
    date_of_birth = serializers.SerializerMethodField()
    guarantors = serializers.SerializerMethodField()
    business_name = serializers.CharField(required=True)
    acquisition_officer = serializers.SerializerMethodField()

    _account_cache = {}

    def _get_account(self, inst: User):
        if inst.id not in self._account_cache:
            self._account_cache[inst.id] = (
                AccountSystem.get_default_provider_accounts(user=inst).filter(account_type="COLLECTION").last())
        return self._account_cache[inst.id]

    def get_account_number(self, inst: User):
        account = self._get_account(inst)
        return account.account_number if account else None

    def get_bank_code(self, inst: User):
        account = self._get_account(inst)
        return account.bank_code if account else None

    def get_date_of_birth(self, obj):
        return obj.check_kyc.bvn_rel.bvn_birthdate

    def get_guarantors(self, obj):
        kyc_instance, _ = KYCTable.objects.get_or_create(user=obj)
        query = Q(kyc=kyc_instance) | Q(kyc_extra=kyc_instance)
        guarantors = GuarantorDetail.objects.filter(query, is_verified=True).distinct()
        if guarantors:
            return [{
                "name": item.guarantor_name,
                "phone_number": item.guarantor_phone_number,
                "email": item.guarantor_email,
                "occupation": item.guarantor_occupation,
                "address": item.guarantor_address,
                "id_number": item.guarantor_id_number,
                "is_verified": item.guarantor_id_is_verified,
                "next_of_kin_name": item.next_of_kin_name,
                "next_of_kin_relationship": item.next_of_kin_relationship,
                "next_of_kin_phone_number": item.next_of_kin_phone_number,
                "next_of_kin_address": item.next_of_kin_address,
            } for item in guarantors]
        return None

    def get_bvn(self, inst: User):
        kyc_instance, created = KYCTable.objects.get_or_create(user=inst)
        bvn_rel, created = BVNDetail.objects.get_or_create(kyc=kyc_instance)
        return bvn_rel.bvn_number

    def get_acquisition_officer(self, obj):
        acquisition = MerchantAcquisitionOfficer.objects.filter(merchants__in=[obj])
        acquisition_officer_data = dict()
        if acquisition.exists():
            officer = acquisition.last()
            acquisition_officer_data['first_name'] = officer.user.first_name
            acquisition_officer_data['last_name'] = officer.user.last_name
            acquisition_officer_data['phone_number'] = officer.user.phone_number
        return acquisition_officer_data

    # def get_account_number(self, inst: User):
    #     account_det = AccountSystem.get_default_provider_accounts(user=inst).filter(account_type="COLLECTION").last()
    #     if account_det:
    #         return account_det.account_number
    #     else:
    #         return None
    #
    # def get_bank_code(self, inst: User):
    #     account_det = AccountSystem.get_default_provider_accounts(user=inst).filter(account_type="COLLECTION").last()
    #     if account_det:
    #         return account_det.bank_code
    #     else:
    #         return None


class LoansTransactionHistorySerializer(serializers.ModelSerializer):
    fee = serializers.FloatField(read_only=True, source="liberty_commission")
    email = serializers.CharField(source="user.email")
    phone_number = serializers.CharField(source="user.phone_number")
    full_name = serializers.CharField(source="user.get_full_name")
    has_terminal = serializers.SerializerMethodField(read_only=True)

    def get_has_terminal(self, obj):
        term_status = False
        term_list = list()
        terminals = TerminalSerialTable.objects.filter(user=obj.user)
        if terminals:
            term_status = True
            term_list = [term.terminal_id for term in terminals]
        return {
            "status": term_status,
            "terminal_ids": term_list
        }


    class Meta:
        model = Transaction
        fields = [
            "user_id",
            "full_name",
            "phone_number",
            "email",
            "type_of_user",
            "transaction_type",
            "amount",
            "fee",
            "transaction_id",
            "wallet_type",
            "narration",
            "date_created",
            "has_terminal"
        ]


class UserSerializerCardTransactionReport(serializers.ModelSerializer):
    UserEmail = serializers.CharField(source="email")
    Category = serializers.CharField(source="type_of_user")

    class Meta:
        model = User
        fields = ["UserEmail", "Category"]


class AgentGiveConsentSerializer(serializers.Serializer):
    consent = serializers.BooleanField()


class MerchantPinVerifyOTPSerializer(serializers.Serializer):
    otp_value = serializers.CharField(required=True, min_length=6, max_length=6)


class CreateMerchantPinSerializer(serializers.Serializer):
    pin1 = serializers.CharField(required=True, min_length=4, max_length=4)
    pin2 = serializers.CharField(required=True, min_length=4, max_length=4)
    otp_value = serializers.CharField(required=True, min_length=6, max_length=6)


class RemoveMerchantPinSerializer(serializers.Serializer):
    toggle = serializers.BooleanField()


class AddMultipleToWhatsappGroupSerializer(serializers.Serializer):
    group_id = serializers.CharField(required=True)
    num_list = serializers.ListField(required=True)


class UserPhoneNumberSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["phone_number"]


class LottoGetUserTypeOfUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["type_of_user"]


class SavingsGetUserPasswordSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["id", "email", "password", "type_of_user"]


class ChildSerializerForLottoSalesRepSuperAgent(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["id", "email", "first_name", "last_name", "phone_number", "unique_id", "customer_id"]


class DeliveryAddressDataSerializer(serializers.ModelSerializer):

    full_address = serializers.SerializerMethodField()

    def get_full_address(self, address: DeliveryAddressData):
        return address.full_address

    class Meta:
        model = DeliveryAddressData
        fields = "__all__"


class PostPutDeliveryAddressDataSerializer(serializers.Serializer):
    state = serializers.CharField(required=True, max_length=100)
    lga = serializers.CharField(required=True, max_length=100)
    nearest_landmark = serializers.CharField(required=True, max_length=100)
    street = serializers.CharField(required=True, max_length=100)


class UserOthersSuspendSerializer(serializers.Serializer):
    user_id = serializers.IntegerField(required=True)
    suspend = serializers.BooleanField(required=True)
    suspend_or_unsuspend_reason = serializers.CharField(required=True)


class OnboardCorporateAccountSerializer(serializers.Serializer):

    user_email = serializers.EmailField(required=True)
    entity_type = serializers.CharField(required=True)
    rc_number = serializers.CharField(required=True)
    company_name = serializers.CharField(required=True)
    incorp_date = serializers.DateField(required=True)


class ChildCreateOtherAccountSerializer(serializers.Serializer):
    ACCOUNT_TYPE_CHOICES = [
        ("CORPORATE", "CORPORATE"),
        ("PERSONAL", "PERSONAL"),
        ("CORPORATE_LIBERTY_RETAIL", "CORPORATE_LIBERTY_RETAIL"),
        ("SEEDS_CORPORATE", "SEEDS_CORPORATE"),
    ]

    account_type = serializers.ChoiceField(required=True, choices=ACCOUNT_TYPE_CHOICES)
    corporate_id = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    phone_number = serializers.CharField(required=True, allow_blank=True)
    suffix_or_location_id = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    password = serializers.CharField(required=False, min_length=6, max_length=6, allow_null=True, allow_blank=True)
    company_name = serializers.CharField(required=True, allow_blank=True, allow_null=True)
    num_of_accounts = serializers.IntegerField(required=True, allow_null=True)
    user_for_creation = serializers.PrimaryKeyRelatedField(queryset=User.objects.all(), required=True, allow_null=True)

    def is_valid(self, raise_exception=False):
        if self._kwargs["data"].get("password"):
            if self._kwargs["data"]["password"].isnumeric():
                pass
            else:
                raise serializers.ValidationError(
                    {"error_code": "14", "message": "You must supply an integer for password"}
                )
        return super().is_valid(raise_exception)


class CreateOtherAccountListSerializer(serializers.Serializer):
    data = serializers.ListSerializer(child=ChildCreateOtherAccountSerializer())


class RegisterDeviceSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    device_token = serializers.CharField(required=True)
    security_answer_one = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    security_answer_two = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    device_registration_code = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    transaction_pin = serializers.CharField(required=True)


class AnswerSecurityQuestionSerializer(serializers.Serializer):
    CHECK_TYPE_CHOICES = ("RESET_TRANS_PIN", "RESET_TRANS_PIN", "REGISTER_DEVICE", "REGISTER_DEVICE")

    SEND_OTP_CHOICES = ("EMAIL", "EMAIL", "SMS", "SMS" "ALL", "ALL" "NONE", "NONE")

    security_question_number = serializers.IntegerField(required=True)
    security_answer = serializers.CharField(required=True)
    check_type = serializers.ChoiceField(required=True, choices=CHECK_TYPE_CHOICES)
    send_otp = serializers.ChoiceField(required=True, choices=SEND_OTP_CHOICES)


class ForgotTransPinSerializer(serializers.Serializer):
    gen_token = serializers.CharField(required=True)
    otp = serializers.CharField(required=True)
    pin1 = serializers.CharField(required=True, min_length=4, max_length=4)
    pin2 = serializers.CharField(required=True, min_length=4, max_length=4)


class DisplayBannerSerializer(serializers.ModelSerializer):
    image_url = serializers.SerializerMethodField()

    def get_image_url(self, obj):
        request = self.context.get("request")
        absolute_url = obj.image.url
        return request.build_absolute_uri(absolute_url)

    class Meta:
        model = DisplayBanner
        fields = "__all__"


class UserDataDeletionRequestSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserDataDeletionRequest
        fields = "__all__"


class AjoAgentFormSerializer(serializers.ModelSerializer):
    # first_name = serializers.CharField()
    # last_name = serializers.CharField()
    # phone_number = serializers.CharField()
    # email = serializers.EmailField()
    # gender = serializers.ChoiceField(choices=AjoAgentForm.gender_choices)
    # above_eighteen = serializers.BooleanField()
    # building_no = serializers.CharField()
    # street = serializers.CharField()
    # nearest_landmark = serializers.CharField()
    # city = serializers.CharField()
    # lga = serializers.CharField()
    # state = serializers.CharField()
    # bvn = serializers.CharField(allow_null=True)
    # nin = serializers.CharField(allow_null=True)
    # otp = serializers.CharField()

    # otp = serializers.CharField()

    class Meta:
        model = AjoAgentForm
        fields = "__all__"


class UserProfilePictureSerializer(serializers.ModelSerializer):
    profile_picture = serializers.ImageField(required=True)

    class Meta:
        model = User
        fields = ["profile_picture"]


class PingDeviceSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    title = serializers.CharField(required=True)
    body = serializers.CharField(required=True)


class CheckUserEmailSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)

class OnboardingStatusEmailSerializer(serializers.Serializer):
    email = serializers.EmailField(required=False, allow_blank=True, allow_null=True)
    phone_number = serializers.CharField(required=False, allow_blank=True, allow_null=True)

    def validate(self, attrs):
        email = attrs.get("email")
        phone_number = attrs.get("phone_number")

        if email is None and phone_number is None:
            raise serializers.ValidationError(
                    {"error_code": "14", "message": "email or phone number is required"}
                )
        if phone_number:
            phone = User.format_number_from_back_add_234(phone_number)
            if phone is None:
                raise serializers.ValidationError(
                    {"error_code": "14", "message": "invalid phone number"}
                )
            attrs["phone_number"] = phone
        return attrs

class OnboardingStatusPhoneSerializer(serializers.Serializer):
    phone_number = serializers.CharField(required=True)

class UpdatePreferenceSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["news_and_update", "tips_and_tutorial", "user_research", "transaction_notification", "reminder_update"]


class UpdatePersonalInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["first_name", "last_name", "email", "phone_number", "state"]
        
    def validate(self, attrs):
        phone_number = attrs.get("phone_number")
        email = attrs.get("email")

        # Email validation regex pattern
        email_pattern = re.compile(r"^[\w\.-]+@[\w\.-]+\.\w+$")
        if not email or not email_pattern.match(email):
            raise serializers.ValidationError(
                {"message": "provide a valid email address"}
            )
        phone = User.format_number_from_back_add_234(phone_number)
        if phone is None:
            raise serializers.ValidationError(
                {"message": "invalid phone number"}
            )
        attrs["phone_number"] = phone
        return attrs

class UpdateUserPasswordSerializer(serializers.Serializer):
    old_passcode = serializers.CharField()
    passcode1 = serializers.CharField(required=True, min_length=6, max_length=6)
    passcode2 = serializers.CharField(required=True, min_length=6, max_length=6)

    def validate(self, attrs):
        old_passcode = attrs.get("old_passcode")
        passcode1 = attrs.get("passcode1")
        passcode2 = attrs.get("passcode2")
        if not old_passcode:
            raise serializers.ValidationError({"error_code": "14", "message": "old_passcode is a required field"})
        if not passcode1:
            raise serializers.ValidationError({"error_code": "14", "message": "passcode1 is a required field"})
        if not passcode2:
            raise serializers.ValidationError({"error_code": "14", "message": "passcode2 is a required field"})
        if passcode1 != passcode2:
            raise serializers.ValidationError({"error_code": "14", "message": "passcode1 and passcode2 does not match"})
        if passcode1.isnumeric() and passcode2.isnumeric():
            pass
        else:
            raise serializers.ValidationError(
                {"error_code": "14", "message": "You must supply an integer for phone number, passcode1 and passcode2"}
            )
        return attrs


class LocationToRegionSerializerIn(serializers.Serializer):
    region_id = serializers.IntegerField()
    new_location_list = serializers.ListField(child=serializers.IntegerField())

    def validate(self, attrs):
        region_id = attrs.get("region_id")
        new_location_list = attrs.get("new_location_list")
        try:
            Region.objects.get(id=region_id)
        except Region.DoesNotExist:
            raise InvalidRequestException({"message": f"Region with provided ID ({region_id}) is not found"})
        for location_id in new_location_list:
            if not NewLocationList.objects.filter(id=location_id).exists():
                raise InvalidRequestException({"message": f"NewLocationList with provided ID ({region_id}) is not found"})
        return attrs


class RegionalHeadToRegionSerializerIn(serializers.Serializer):
    region_id = serializers.IntegerField()
    user_id = serializers.IntegerField()

    def validate(self, attrs):
        region_id = attrs.get("region_id")
        user_id = attrs.get("user_id")
        try:
            Region.objects.get(id=region_id)
        except Region.DoesNotExist:
            raise InvalidRequestException({"message": f"Region with provided ID ({region_id}) is not found"})
        try:
            User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise InvalidRequestException({"message": f"User with provided ID ({user_id}) is not found"})
        return attrs


class BranchToLocationSerializerIn(serializers.Serializer):
    new_location_id = serializers.IntegerField()
    branch_teams = serializers.ListField(child=serializers.IntegerField())

    def validate(self, attrs):
        new_location_id = attrs.get("new_location_id")
        branch_teams = attrs.get("branch_teams")
        try:
            NewLocationList.objects.get(id=new_location_id)
        except NewLocationList.DoesNotExist:
            raise InvalidRequestException({"message": f"NewLocationList with provided ID ({new_location_id}) is not found"})
        for branch_id in branch_teams:
            if not BranchTeam.objects.filter(id=branch_id).exists():
                raise InvalidRequestException({"message": f"BranchTeam with provided ID ({branch_id}) is not found"})
        return attrs


class AgentToBranchSerializerIn(serializers.Serializer):
    user_team_id = serializers.IntegerField()  # BranchTeam ID
    user_emails = serializers.ListField(child=serializers.EmailField())

    def validate(self, attrs):
        user_team_id = attrs.get("user_team_id")
        user_emails = attrs.get("user_emails")
        try:
            BranchTeam.objects.get(id=user_team_id)
        except BranchTeam.DoesNotExist:
            raise InvalidRequestException({"message": f"BranchTeam with provided ID ({user_team_id}) is not found"})
        for email in user_emails:
            if not User.objects.filter(email__iexact=email).exists():
                raise InvalidRequestException({"message": f"User with provided email ({email}) is not found"})
        return attrs


class RemapLoanAgentToLocationToRegionIn(serializers.Serializer):
    REMAP_CHOICES = (
        ("LOCATION_TO_REGION", "Location to Region"),
        ("REGIONAL_HEAD_TO_REGION", "Regional Head to Region"),
        ("BRANCH_TO_LOCATION", "Branch to Location"),
        ("AGENT_TO_BRANCH", "Agent to Branch/Team")
    )
    remap_type = serializers.ChoiceField(choices=REMAP_CHOICES)
    location_to_region = serializers.ListField(child=LocationToRegionSerializerIn(), required=False)
    regional_head_to_region = serializers.ListField(child=RegionalHeadToRegionSerializerIn(), required=False)
    branch_to_location = serializers.ListField(child=BranchToLocationSerializerIn(), required=False)
    agent_to_branch = serializers.ListField(child=AgentToBranchSerializerIn(), required=False)

    def validate(self, attrs):
        remapping_type = attrs.get("remap_type")
        location_to_region = attrs.get("location_to_region")
        branch_to_location = attrs.get("branch_to_location")
        regional_head_to_region = attrs.get("regional_head_to_region")
        agent_to_branch = attrs.get("agent_to_branch")
        if remapping_type == "LOCATION_TO_REGION" and not location_to_region:
            raise InvalidRequestException({"message": f'Key: "location_to_region" is required for selected remap type'})
        if remapping_type == "BRANCH_TO_LOCATION" and not branch_to_location:
            raise InvalidRequestException({"message": f'Key: "branch_to_location" is required for selected remap type'})
        if remapping_type == "REGIONAL_HEAD_TO_REGION" and not regional_head_to_region:
            raise InvalidRequestException({"message": f'Key: "regional_head_to_region" is required for selected remap type'})
        if remapping_type == "AGENT_TO_BRANCH" and not agent_to_branch:
            raise InvalidRequestException({"message": f'Key: "agent_to_branch" is required for selected remap type'})

        return attrs

    def create(self, validated_data):
        location_to_region = validated_data.get("location_to_region")
        branch_to_location = validated_data.get("branch_to_location")
        agent_to_branch = validated_data.get("agent_to_branch")

        if location_to_region:
            for item in location_to_region:
                region_id = item.get("region_id")
                location_ids = item.get("new_location_list")
                NewLocationList.objects.filter(id__in=location_ids).update(region_id=region_id)

        if branch_to_location:
            for item in branch_to_location:
                location_id = item.get("new_location_id")
                branch_teams_ids = item.get("branch_teams")
                BranchTeam.objects.filter(id__in=branch_teams_ids).update(branch_id=location_id)

        if agent_to_branch:
            for item in agent_to_branch:
                # user_team_id = item.get("user_team_id")
                user_emails = item.get("user_emails")
                branch_team_id = item.get("user_team_id")
                # user_team = UserTeam.objects.get(id=user_team_id)
                branch_team = BranchTeam.objects.get(id=branch_team_id)
                user_team = branch_team.userteam
                for email in user_emails:
                    e_mail = str(email).lower()
                    u = User.objects.filter(email__iexact=e_mail).last()
                    user_team.users.add(u)

        return {"message": "Update Completed"}


class ProfileUpdateSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    address = serializers.CharField(required=False, allow_blank=False, allow_null=False)
    landmark = serializers.CharField(required=False, allow_blank=False, allow_null=False)
    local_govt = serializers.CharField(required=False, allow_blank=False, allow_null=False)
    dob = serializers.CharField(required=False, allow_blank=False, allow_null=False)
    marital_status = serializers.ChoiceField(choices=User.MARITAL_STATUS_CHOICES)
    middle_name = serializers.CharField(required=False, allow_blank=False, allow_null=False)
    state = serializers.CharField(required=False, allow_blank=False, allow_null=False)
    city = serializers.CharField(required=False, allow_blank=False, allow_null=False)
    gender = serializers.ChoiceField(choices=User.GENDER_CHOICES)

    def create(self, validated_data):
        user = validated_data.get("user")
        address = validated_data.get("address")
        dob = validated_data.get("dob")
        middle_name = validated_data.get("middle_name")
        bvn_info = user.check_kyc.bvn_rel
        if not bvn_info.bvn_residential_address and address:
            bvn_info.bvn_residential_address = address
        if not bvn_info.bvn_birthdate and dob:
            bvn_info.bvn_birthdate = dob
        if not bvn_info.bvn_middle_name and middle_name:
            bvn_info.bvn_middle_name = middle_name
        bvn_info.save()
        user.street = bvn_info.bvn_residential_address
        user.nearest_landmark = validated_data.get("landmark", user.nearest_landmark)
        user.lga = validated_data.get("local_govt", user.lga)
        user.marital_status = validated_data.get("marital_status", user.marital_status)
        user.state = validated_data.get("state", user.state)
        user.city = validated_data.get("city", user.city)
        user.gender = validated_data.get("gender", user.gender)
        user.save()

        try:
            create_wallets_and_accounts_task(bvn_info.id)
        except Exception as err:
            print(f"ERROR ON PROFILE UPDATE: {err}")
        return {"detail": "Profile updated successfully"}


class MerchantAcquisitionOfficerSerializerOut(serializers.ModelSerializer):
    agent = serializers.SerializerMethodField()
    merchants = serializers.SerializerMethodField()

    def get_agent(self, obj):
        user = obj.user
        return {
            "user_id": user.id,
            "full_name": user.full_name,
            "email": user.email,
            "phone_number": user.phone_number
        }

    def get_merchants(self, obj):
        return [
            {
                "user_id": merchant.id,
                "full_name": merchant.full_name,
                "email": merchant.email,
                "phone_number": merchant.phone_number
            } for merchant in obj.merchants.all()
        ]

    class Meta:
        model = MerchantAcquisitionOfficer
        exclude = ["user"]


class ApplicationBannerSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = ApplicationBanner
        exclude = []


class CustomTokenRefreshSerializer(TokenRefreshSerializer):
    def validate(self, attrs):
        try:
            old_refresh_token = RefreshToken(attrs["refresh"])
            user_id = old_refresh_token.payload.get('user_id')
            user = User.objects.get(id=user_id)

            new_refresh_token = RefreshToken.for_user(user)

            old_refresh_token.blacklist()
            return {
                'access': str(new_refresh_token.access_token),
                'refresh': str(new_refresh_token),
            }
        except Exception:
            raise InvalidRequestException({"message": "Invalid refresh token"})


class CustomTokenRefreshView(TokenRefreshView):
    serializer_class = CustomTokenRefreshSerializer