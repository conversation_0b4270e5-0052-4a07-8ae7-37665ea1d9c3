import ast
import secrets
import logging

import pytz
from django.db import transaction
from django_filters.rest_framework import DjangoFilterBackend

from liberty_pay.exceptions import raise_serializer_error_msg
from main.helper.sms_helpers import send_sms
from rest_framework import generics, status
from rest_framework.decorators import api_view
from rest_framework.filters import <PERSON><PERSON>ilter
from rest_framework.pagination import PageNumberPagination
from rest_framework.parsers import FormParser, MultiPartParser
from rest_framework.response import Response
from rest_framework.views import APIView

from accounts.authentication import CustomTokenAuthentication
from accounts.filters import TransactionDateFilter, UserDataFilter
from accounts.helpers.general_account_manager import create_temp_trans_access_token
from accounts.models import (
    OtherCommissionsRecord,
    OtherServiceAccountSystem,
    UserOwnAccount,
)
from accounts.serializers import UserOwnAccountSerializer
from kyc_app.models import UserImage
from liberty_pay.settings import notify_messaging
from main.helper.username_recommendation import generate_usernames
from main.helper.utils import generate_ussd_otp
from main.permissions import (
    Ad<PERSON><PERSON>ockPermission,
    CheckAccountAvailable,
    CheckDynamicAuthentication,
    CheckIPAddresses,
    CheckWalletAvailable,
    CustomIsAuthenticated,
    HasKYC,
    HasTransactionPin,
    LottoDynamicKYCLevelDecider,
    LottoUserPermission,
    OTPVerified,
    SavingsUserPermission,
)
from main.serializers import *
from main.tasks import send_whisper_otp_to_email_task
from retail.models import RetailSystem
from singlelogin.models import BlackListedJWT

from main.models import Users_Otp, User

logger = logging.getLogger(__name__)


# Paginate
class CustomPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = "page_size"
    max_page_size = 500
    page_query_param = "page"


class CustomPaginationV2(PageNumberPagination):
    page_size = 200
    page_size_query_param = "page_size"
    max_page_size = 20
    page_query_param = "page"


class GetTypeOfUser(APIView):

    def get(self, request):

        response = {
            "status": "success",
            "user_types": [x[0] for x in TYPE_OF_TERMINAL_USER_CHOICE if x[0] not in ["STAFF_AGENT"]],
        }
        return Response(response, status=status.HTTP_200_OK)


class CheckPhoneNumberAPIView(APIView):
    permission_classes = [CheckDynamicAuthentication, CheckIPAddresses(service_name="SAVINGS")]

    def post(self, request):

        serializer = PhoneSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        phone_number = serializer.validated_data["phone_number"]
        type_of_user = serializer.validated_data["type_of_user"]

        phone_number = User.format_number_from_back_add_234(phone_number)

        if not phone_number or len(phone_number) != 13:
            response = {
                "status": "success",
                "search_code": "119",
            }
        else:
            try:
                user = User.objects.get(phone_number=phone_number)
                response = {
                    "status": "success",
                    "search_code": "100",
                    "user_id": user.id,
                }
            except User.DoesNotExist:
                response = {
                    "status": "success",
                    "search_code": "129",
                }

        return Response(response, status=status.HTTP_200_OK)


class GetUserWithPhoneNumber(APIView):
    user_queryset = User.objects.all()
    unregistered_number_queryset = UnregisteredPhoneNumber.objects.all()

    def post(self, request):
        RegistrationData.objects.create(data=json.dumps(request.data))

        serializer = PhoneSerializer(data=request.data)
        if serializer.is_valid():

            phone_number = User.format_number_from_back_add_234(serializer.validated_data["phone_number"])
            type_of_user = serializer.validated_data["type_of_user"]
            referral_code = serializer.validated_data.get("referral_code", None)
            device_type = serializer.validated_data.get("device_type")
            device_registration_code = serializer.validated_data.get("device_registration_code")

            # if not phone_number or len(phone_number) != 13:
            #     response = {"error": "174", "message": "entered phone number is not equal to 13 numbers"}
            #     return Response(response, status=status.HTTP_400_BAD_REQUEST)

            # validate referral code
            if referral_code != None and referral_code != "":
                if self.user_queryset.filter(referral_code=referral_code).exists():
                    response = {"error": "175", "message": "Invalid referral code"}
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if self.user_queryset.filter(phone_number=phone_number).exists():
                user_check = self.user_queryset.filter(phone_number=phone_number).first()

                user_serializer = CheckFirstTimerSerializer(user_check)
                response = {"message": "user found", "data": user_serializer.data}
                return Response(response, status=status.HTTP_200_OK)
            else:
                # CHECK EXTERNAL

                # Check if phone number exists in Marketing Automation backend
                get_agent_profile_from_USSD = ThirdPartyApis.query_users_from_existing_form_data(phone_no=phone_number)
                get_data_key_from_response = (
                    get_agent_profile_from_USSD.get("data", None) if get_agent_profile_from_USSD else None
                )

                if get_data_key_from_response != None:
                    get_data_key_from_response["sales_rep_code"] = referral_code

                    user_instance = User.create_agent_profile_and_user(
                        get_data_key_from_response, type_of_user=type_of_user
                    )
                    user_serializer = CheckFirstTimerSerializer(user_instance)
                    response = {
                        "message": "new user created",
                        "data": user_serializer.data,
                    }

                    # RegisteredDevice.objects.create(
                    #     user=user_instance,
                    #     device_token=device_registration_code,
                    #     device_status="PRIMARY",
                    #     device_type=device_type
                    # )

                    return Response(response, status=status.HTTP_200_OK)

                else:
                    if self.unregistered_number_queryset.filter(phone_number=phone_number).exists():
                        pass
                    else:
                        UnregisteredPhoneNumber.objects.create(phone_number=phone_number)

                    response = {"message": "user not found", "data": []}
                    return Response(response, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CreateUserDetailAPIView(APIView):
    def post(self, request):
        RegistrationData.objects.create(data=json.dumps(request.data))
        serializer = CreateUserDetailInfoSerializer(data=request.data)
        if serializer.is_valid():
            phone_number = User.format_number_from_back_add_234(serializer.validated_data["phone_number"])
            username = serializer.validated_data["username"]
            first_name = serializer.validated_data["first_name"]
            last_name = serializer.validated_data["last_name"]
            email = serializer.validated_data["email"]
            state = serializer.validated_data["state"]
            lga = serializer.validated_data["lga"]
            nearest_landmark = serializer.validated_data["nearest_landmark"]
            street = serializer.validated_data["street"]
            type_of_user = (
                serializer.validated_data.get("type_of_user")
                if serializer.validated_data.get("type_of_user")
                else "AGENT"
            )

            len_of_phone_number = len(serializer.validated_data["phone_number"])
            raw_phone_number = serializer.validated_data["phone_number"]

            referal_code = serializer.validated_data.get("referral_code")
            business_name = serializer.validated_data.get("business_name")
            gender = serializer.validated_data.get("gender")
            device_type = serializer.validated_data.get("device_type")
            device_registration_code = serializer.validated_data.get("device_registration_code")
            source = serializer.validated_data.get("source")
            is_paybox_merchant = serializer.validated_data.get("is_paybox_merchant", False)
            onboarding_status = serializer.validated_data.get("onboarding_status", None)

            if not first_name:
                response = {
                    "status": "error",
                    "error_code": "172",
                    "message": "invalid first name. first name contains special characters",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if not last_name:
                response = {
                    "status": "error",
                    "error_code": "173",
                    "message": "invalid last name. last name contains special characters",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            # if len_of_phone_number == 13 and not raw_phone_number.startswith("234"):
            #     response = {
            #         "status": "error",
            #         "error_code": "174",
            #         "message": f"entered phone number is incorrect. it is {len_of_phone_number} numbers."
            #     }
            #     return Response(response, status=status.HTTP_400_BAD_REQUEST)

            # if len_of_phone_number != 11 and len_of_phone_number != 13:
            #     response = {
            #         "status": "error",
            #         "error_code": "177",
            #         "message": f"entered phone number is incorrect. it is {len_of_phone_number} numbers."
            #     }
            #     return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if not state:
                response = {
                    "status": "error",
                    "error_code": "178",
                    "message": "invalid state. state contains special characters",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            # get_email_exists = User.objects.filter(
            #     Q(email=email) | Q(username=username) | Q(phone_number=phone_number)
            # )

            get_email_exists = User.objects.filter(email=email).first()
            get_username_exists = User.objects.filter(username=username).first()
            get_phone_exists = User.objects.filter(phone_number=phone_number).first()

            if get_email_exists:
                response = {
                    "status": "error",
                    "error_code": "09",
                    "message": "Sorry, a user with this email already exists",
                    "user_id": get_email_exists.id,
                }
                return Response(response, status=status.HTTP_403_FORBIDDEN)

            if get_username_exists:
                response = {
                    "status": "error",
                    "error_code": "10",
                    "message": "Sorry, a user with this username already exists",
                    "user_id": get_username_exists.id,
                }
                return Response(response, status=status.HTTP_403_FORBIDDEN)

            if get_phone_exists:
                response = {
                    "status": "error",
                    "error_code": "11",
                    "message": "Sorry, a user with this phone number already exists",
                    "user_id": get_phone_exists.id,
                }
                return Response(response, status=status.HTTP_403_FORBIDDEN)

            upper_gender = gender.upper() if gender else None

            user_instance = User.objects.create(
                username=username,
                first_name=first_name,
                last_name=last_name,
                phone_number=phone_number,
                email=email,
                state=state,
                lga=lga,
                nearest_landmark=nearest_landmark,
                street=street,
                type_of_user=type_of_user,
                is_paybox_merchant=is_paybox_merchant,
                sales_rep_upline_code=referal_code,
                business_name=business_name,
                gender=upper_gender,
                source=source,
                registration_email_verified=True if type_of_user == "MICRO_SAVER" else False,
            )

            if onboarding_status:
                user_instance.onboarding_status = onboarding_status
                user_instance.save()

            # RegisteredDevice.objects.create(
            #     user=user_instance,
            #     device_token=device_registration_code,
            #     device_status="PRIMARY",
            #     device_type=device_type
            # )

            passcode = str(passcode_generator())
            User.create_registration_email_otp(user=user_instance, passcode=passcode)

            send_email(email=email, passcode=passcode)
            access_token = User.generate_micro_saver_access_token(user_instance.id)

            response = {
                "status": "success",
                "message": "User successfully created and registration passcode sent to email",
                "user_id": user_instance.id,
                "access": access_token,
            }
            if settings.ENVIRONMENT == "development":
                response["passcode"] = passcode
            return Response(response, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
class CreateUserPhoneDetailAPIView(APIView):
    def post(self, request):
        RegistrationData.objects.create(data=json.dumps(request.data))
        serializer = CreateUserPhoneDetailInfoSerializer(data=request.data)
        if serializer.is_valid():
            phone_number = User.format_number_from_back_add_234(serializer.validated_data["phone_number"])
            username = serializer.validated_data["username"]
            first_name = serializer.validated_data["first_name"]
            last_name = serializer.validated_data["last_name"]
            email = f"{phone_number}@paybox360.com"
            state = serializer.validated_data["state"]
            lga = serializer.validated_data["lga"]
            nearest_landmark = serializer.validated_data["nearest_landmark"]
            street = serializer.validated_data["street"]
            type_of_user = (
                serializer.validated_data.get("type_of_user")
                if serializer.validated_data.get("type_of_user")
                else "AGENT"
            )

            len_of_phone_number = len(serializer.validated_data["phone_number"])
            raw_phone_number = serializer.validated_data["phone_number"]

            referal_code = serializer.validated_data.get("referral_code")
            business_name = serializer.validated_data.get("business_name")
            gender = serializer.validated_data.get("gender")
            device_type = serializer.validated_data.get("device_type")
            device_registration_code = serializer.validated_data.get("device_registration_code")
            source = serializer.validated_data.get("source")
            is_paybox_merchant = serializer.validated_data.get("is_paybox_merchant", False)
            onboarding_status = serializer.validated_data.get("onboarding_status", None)

            if not first_name:
                response = {
                    "status": "error",
                    "error_code": "172",
                    "message": "invalid first name. first name contains special characters",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if not last_name:
                response = {
                    "status": "error",
                    "error_code": "173",
                    "message": "invalid last name. last name contains special characters",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            # if len_of_phone_number == 13 and not raw_phone_number.startswith("234"):
            #     response = {
            #         "status": "error",
            #         "error_code": "174",
            #         "message": f"entered phone number is incorrect. it is {len_of_phone_number} numbers."
            #     }
            #     return Response(response, status=status.HTTP_400_BAD_REQUEST)

            # if len_of_phone_number != 11 and len_of_phone_number != 13:
            #     response = {
            #         "status": "error",
            #         "error_code": "177",
            #         "message": f"entered phone number is incorrect. it is {len_of_phone_number} numbers."
            #     }
            #     return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if not state:
                response = {
                    "status": "error",
                    "error_code": "178",
                    "message": "invalid state. state contains special characters",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            # get_email_exists = User.objects.filter(
            #     Q(email=email) | Q(username=username) | Q(phone_number=phone_number)
            # )

            get_username_exists = User.objects.filter(username=username).first()
            get_phone_exists = User.objects.filter(phone_number=phone_number).first()
            get_email_exists = User.objects.filter(email=email).first()

            if get_email_exists:
                response = {
                    "status": "error",
                    "error_code": "09",
                    "message": "Sorry, a user with this phone number already exists",
                    "user_id": get_email_exists.id,
                }
                return Response(response, status=status.HTTP_403_FORBIDDEN)

            if get_username_exists:
                response = {
                    "status": "error",
                    "error_code": "10",
                    "message": "Sorry, a user with this username already exists",
                    "user_id": get_username_exists.id,
                }
                return Response(response, status=status.HTTP_403_FORBIDDEN)

            if get_phone_exists:
                response = {
                    "status": "error",
                    "error_code": "11",
                    "message": "Sorry, a user with this phone number already exists",
                    "user_id": get_phone_exists.id,
                }
                return Response(response, status=status.HTTP_403_FORBIDDEN)

            upper_gender = gender.upper() if gender else None

            user_instance = User.objects.create(
                username=username,
                first_name=first_name,
                last_name=last_name,
                phone_number=phone_number,
                email=email,
                state=state,
                lga=lga,
                nearest_landmark=nearest_landmark,
                street=street,
                type_of_user=type_of_user,
                is_paybox_merchant=is_paybox_merchant,
                sales_rep_upline_code=referal_code,
                business_name=business_name,
                gender=upper_gender,
                source=source,
                fake_email=True,
                registration_email_verified=True if type_of_user == "MICRO_SAVER" else False,
            )

            if onboarding_status:
                user_instance.onboarding_status = onboarding_status
                user_instance.save()

            # RegisteredDevice.objects.create(
            #     user=user_instance,
            #     device_token=device_registration_code,
            #     device_status="PRIMARY",
            #     device_type=device_type
            # )

            passcode = str(passcode_generator())
            User.create_registration_email_otp(user=user_instance, passcode=passcode)

            send_sms(phone=phone_number, passcode=passcode)
            access_token = User.generate_micro_saver_access_token(user_instance.id)

            response = {
                "status": "success",
                "message": "User successfully created and registration passcode sent to phone number",
                "user_id": user_instance.id,
                "access": access_token,
            }
            if settings.ENVIRONMENT == "development":
                response["passcode"] = passcode
            return Response(response, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


#####################################################################################
# SECURITY QUESTIONS


class SecurityQuestionsAPIView(APIView):
    get_serializer_class = SecurityQuestionSerializer
    post_serializer_class = SecurityAnswerSerializer
    permission_classes = [CustomIsAuthenticated, OTPVerified]

    def get(self, request):
        queryset = dict(settings.SECURITY_QUESTION_CHOICES)

        questions_qs = []

        for key, value in queryset.items():
            questions_qs.append(value)

        response = {"questions": questions_qs}
        return Response(response, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = self.post_serializer_class(data=request.data)
        request_user = request.user

        if serializer.is_valid():
            # DATA/PAYLOAD
            first_security_question = serializer.validated_data["first_security_question"]
            first_security_answer = serializer.validated_data["first_security_answer"]
            second_security_question = serializer.validated_data["second_security_question"]
            second_security_answer = serializer.validated_data["second_security_answer"]
            onboarding_status = serializer.validated_data.get("onboarding_status", None)

            # LOGIC
            if first_security_question == second_security_question:
                response = {"error": "94", "message": "First and Second security questions cannot be the same."}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            elif not request_user.first_security_answer and not request_user.second_security_answer:
                request_user.first_security_question = first_security_question
                request_user.first_security_answer = first_security_answer
                request_user.second_security_question = second_security_question
                request_user.second_security_answer = second_security_answer
                if onboarding_status:
                    request_user.onboarding_status = onboarding_status
                request_user.save()

                response = {"message": "Security questions and answers saved successfully"}
                return Response(response, status=status.HTTP_200_OK)
            else:
                response = {"error": "96", "message": "User already has security questions saved"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class SecurityQuestionsAPIViewV2(APIView):
    get_serializer_class = SecurityQuestionSerializer
    post_serializer_class = SecurityAnswerSerializerV2

    def get(self, request):
        queryset = dict(settings.SECURITY_QUESTION_CHOICES)

        questions_qs = []

        for key, value in queryset.items():
            questions_qs.append(value)

        response = {"questions": questions_qs}
        return Response(response, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = self.post_serializer_class(data=request.data)

        if serializer.is_valid():
            # DATA/PAYLOAD

            email = serializer.validated_data["email"]
            first_security_question = serializer.validated_data["first_security_question"]
            first_security_answer = serializer.validated_data["first_security_answer"]
            second_security_question = serializer.validated_data["second_security_question"]
            second_security_answer = serializer.validated_data["second_security_answer"]
            onboarding_status = serializer.validated_data.get("onboarding_status", None)

            # LOGIC
            get_user = User.objects.filter(email=email).last()
            if not get_user:
                response = {
                    "error_code": "79",
                    "message": "user with email does not exist",
                }
                return Response(response, status=status.HTTP_403_FORBIDDEN)

            request_user = get_user

            if first_security_question == second_security_question:
                response = {"error": "94", "message": "First and Second security questions cannot be the same."}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            elif not request_user.first_security_answer and not request_user.second_security_answer:
                request_user.first_security_question = first_security_question
                request_user.first_security_answer = first_security_answer
                request_user.second_security_question = second_security_question
                request_user.second_security_answer = second_security_answer
                if onboarding_status:
                    request_user.onboarding_status = onboarding_status
                request_user.save()

                response = {"message": "Security questions and answers saved successfully"}
                return Response(response, status=status.HTTP_200_OK)
            else:
                response = {"error": "96", "message": "User already has security questions saved"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


#####################################################################################
# Confirm Email Pin On Registration


class ConfirmEmailPinOnRegistration(APIView):
    def post(self, request):
        serializer = VerifyRegistrationPasscodeSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data["email"]
            passcode = serializer.validated_data["passcode"]
            onboarding_status = serializer.validated_data.get("onboarding_status", None)
            try:
                user = User.objects.get(email=email)
                if not user.registration_email_verified:
                    if User.check_registration_email_otp(user=user, passcode=passcode):
                        user.registration_email_verified = True
                        if onboarding_status:
                            user.onboarding_status = onboarding_status
                        user.save()
                        response = {"status": "success", "message": "pin correct. account verified"}
                        return Response(response, status=status.HTTP_200_OK)
                    else:
                        response = {"status": "error", "error_code": "17", "error": "17", "message": "invalid pin"}
                        return Response(response, status=status.HTTP_401_UNAUTHORIZED)
                else:
                    response = {"status": "error", "message": "user pin already verified"}
                    return Response(response, status=status.HTTP_403_FORBIDDEN)

            except User.DoesNotExist:
                response = {"status": "error", "message": "user does not exist"}
                return Response(response, status=status.HTTP_404_NOT_FOUND)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)\
        
class ConfirmPhonePinOnRegistration(APIView):
    def post(self, request):
        serializer = VerifyPhoneRegistrationPasscodeSerializer(data=request.data)
        if serializer.is_valid():
            phone_number = User.format_number_from_back_add_234(serializer.validated_data.get("phone_number"))
            passcode = serializer.validated_data.get("passcode")
            onboarding_status = serializer.validated_data.get("onboarding_status", None)
            try:
                user = User.objects.get(phone_number=phone_number)
                if not user.registration_email_verified:
                    if User.check_registration_email_otp(user=user, passcode=passcode):
                        user.registration_email_verified = True
                        if onboarding_status:
                            user.onboarding_status = onboarding_status
                        user.save()
                        response = {"status": "success", "message": "pin correct. account verified"}
                        return Response(response, status=status.HTTP_200_OK)
                    else:
                        response = {"status": "error", "error_code": "17", "error": "17", "message": "invalid pin"}
                        return Response(response, status=status.HTTP_401_UNAUTHORIZED)
                else:
                    response = {"status": "error", "message": "user pin already verified"}
                    return Response(response, status=status.HTTP_403_FORBIDDEN)

            except User.DoesNotExist:
                response = {"status": "error", "message": "user does not exist"}
                return Response(response, status=status.HTTP_404_NOT_FOUND)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


################################################################################
# Update Email


class UpdateEmailAPIView(APIView):
    def post(self, request):

        serializer = UpdateEmailSerializer(data=request.data)
        if serializer.is_valid():
            # app_name = serializer.validated_data["app_name"]
            app_name = "Liberty paiy"
            previous_email = serializer.validated_data["previous_email"]
            new_email = serializer.validated_data["new_email"]

            try:
                user = User.objects.get(email=previous_email)
                phone_number = user.phone_number
                check_existing_email = User.objects.filter(email=new_email).first()

                if user.has_login_pin:
                    response = {
                        "status": "error",
                        "error_code": "12",
                        "message": "Cannot Update Email for Active Account",
                    }
                    return Response(response, status=status.HTTP_405_METHOD_NOT_ALLOWED)

                elif check_existing_email:
                    response = {
                        "status": "error",
                        "error_code": "02",
                        "message": f"Previous Email and new Email cannot be the same",
                    }
                    return Response(response, status=status.HTTP_405_METHOD_NOT_ALLOWED)

                else:
                    send_whisper_otp = Users_Otp.send_new_otp(phone_number, app_name, no_callback=True)

                    user.update_email = new_email
                    user.save()

                    response = {
                        "status": "success",
                        "message": f"Passcode sent to {phone_number}",
                    }
                    return Response(response, status=status.HTTP_201_CREATED)

            except User.DoesNotExist:
                response = {"status": "error", "error_code": "01", "message": "user does not exist"}
                return Response(response, status=status.HTTP_404_NOT_FOUND)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# Verify OTP For Update Email
class VerifyOTPUpdateEmail(APIView):
    def post(self, request):
        serializer = OTPVerificationSerializer(data=request.data)
        if serializer.is_valid():
            phone_number = User.format_number_from_back_add_234(serializer.validated_data["phone_number"])
            otp_value = serializer.validated_data["otp"]
            # app_name = serializer.validated_data["app_name"]
            onboarding_status = serializer.validated_data.get("onboarding_status", None)
            app_name = "Liberty paiy"

            if not phone_number:
                response = {"status": "error", "error_code": "18", "message": f"Incorrect phone_number format"}
                return Response(response, status=status.HTTP_404_NOT_FOUND)

            if len(phone_number) == 13:
                verify_otp = Users_Otp.verify_new_otp(phone_number, otp_value, app_name)
                if verify_otp.get("verified") is True:
                    try:
                        user_instance = User.objects.get(phone_number=phone_number)

                        old_email_dump, created = OldEmailDump.objects.get_or_create(
                            user=user_instance, last_email=user_instance.email
                        )
                        user_instance.email_updated = True
                        user_instance.email = user_instance.update_email
                        if onboarding_status:
                            user_instance.onboarding_status = onboarding_status
                        user_instance.save()
                        response = {"status": "success", "message": "OTP Verified"}
                        return Response(response, status=status.HTTP_202_ACCEPTED)

                    except User.DoesNotExist:
                        response = {
                            "status": "error",
                            "error_code": "01",
                            "message": "user does not exist",
                        }
                        return Response(response, status=status.HTTP_404_NOT_FOUND)

                else:
                    response = {"status": "error", "error_code": "18", "message": "Invalid OTP"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            else:
                response = {
                    "status": "error",
                    "error_code": "18",
                    "message": f"Incorrect phone number format: {phone_number}",
                }
                return Response(response, status=status.HTTP_404_NOT_FOUND)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


##############################################################################################
#########################################################
# Transaction PIN


class CreateTransactionPINAPIView(APIView):
    serializer_class = CreateTransactionPINSerializer
    permission_classes = [CustomIsAuthenticated, OTPVerified]

    def post(self, request):
        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid():
            pincode = serializer.validated_data["transaction_pin"]
            pincode_retry = serializer.validated_data["transaction_pin_retry"]

            if request.user.transaction_pin:
                response = {"status": "error", "message": "User Transaction Pin Exists"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            elif len(pincode) != 4 or len(pincode_retry) != 4:

                response = {
                    "status": "error",
                    "message": "Transaction Pin must be four digits",
                }
                return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

            else:
                if pincode == pincode_retry:
                    pin_created = User.create_transaction_pin(user=request.user, pincode=pincode)
                    if pin_created:
                        user = request.user
                        user.has_transaction_pin = True
                        user.save()
                        response = {
                            "status": "success",
                            "message": "Transaction Pin Created",
                        }
                        return Response(response, status=status.HTTP_201_CREATED)
                    else:
                        response = {
                            "status": "error",
                            "message": "Transaction Pin Could Not Be Created At This Time. Please Try Again",
                        }
                        return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)
                else:
                    response = {
                        "status": "error",
                        "message": "Transaction Pins Entered Do Not Match",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CreateMerchantPINAPIView(APIView):
    serializer_class = CreateTransactionPINSerializer
    permission_classes = [CustomIsAuthenticated, OTPVerified]

    def post(self, request):
        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid():
            pincode = serializer.validated_data["merchant_pin"]
            pincode_retry = serializer.validated_data["merchant_pin_retry"]

            if request.user.merchant_pin:
                response = {"status": "error", "message": "Merchant Pin Exists"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            elif len(pincode) != 4 or len(pincode_retry) != 4:

                response = {
                    "status": "error",
                    "message": "Merchant Pin must be four digits",
                }
                return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

            else:
                if pincode == pincode_retry:
                    pin_created = User.create_merchant_pin(user=request.user, pincode=pincode)
                    if pin_created:
                        user = request.user
                        user.has_merchant_pin = True
                        user.save()
                        response = {
                            "status": "success",
                            "message": "Merchant Pin Created",
                        }
                        return Response(response, status=status.HTTP_201_CREATED)
                    else:
                        response = {
                            "status": "error",
                            "message": "Merchant Pin Could Not Be Created At This Time. Please Try Again",
                        }
                        return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)
                else:
                    response = {
                        "status": "error",
                        "message": "Merchant Pins Entered Do Not Match",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class SimpleCheckTransactionPinAPIView(APIView):
    serializer_class = VerifyTransactionPinSerializer
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin]

    def post(self, request):
        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid():
            pincode = serializer.validated_data["transaction_pin"]
            check_pin = User.check_sender_transaction_pin(user=request.user, pincode=pincode)
            if check_pin:
                response = {"message": "Pin Correct"}
                return Response(response, status=status.HTTP_200_OK)
            else:
                if request.user.type_of_user == "MERCHANT":
                    check_merchant_pin = User.check_merchant_pin(user=request.user, pincode=pincode)
                    if check_merchant_pin:
                        response = {"message": "Pin Correct"}
                        return Response(response, status=status.HTTP_200_OK)
                    else:
                        response = {"message": "Incorrect Pin"}
                else:
                    response = {"message": "Incorrect Pin"}
                return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


##############################################################################################
# SETTINGS API


class ResetTransactionPinAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin]
    serializer_classes = ResetPINSerializer

    def post(self, request):
        serializer = self.serializer_classes(data=request.data)
        if serializer.is_valid():
            old_transaction_pin = serializer.validated_data["old_pin"]
            new_transaction_pin = serializer.validated_data["new_pin"]
            new_transaction_pin_retry = serializer.validated_data["new_pin_retry"]

            # Get User
            request_user = request.user

            # Check Old Pin
            check_pin = User.check_sender_transaction_pin(user=request_user, pincode=old_transaction_pin)
            if not check_pin:
                response = {"error": "584", "message": "old pin is incorrect"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if not old_transaction_pin.isnumeric():
                response = {"error_code": "14", "message": "You must supply an integer for old transaction pin"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if not new_transaction_pin.isnumeric():
                response = {"error_code": "14", "message": "You must supply an integer for new transaction pin"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if new_transaction_pin != new_transaction_pin_retry:
                response = {"error_code": "14", "message": "New Entered Pins do not match"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if len(old_transaction_pin) != 4 or len(new_transaction_pin) != 4 or len(new_transaction_pin_retry) != 4:

                response = {
                    "error": "995",
                    "message": "New and Old Transaction Pins must be four digits",
                }
                return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

            if old_transaction_pin == new_transaction_pin:
                response = {
                    "error": "958",
                    "message": "cannot change transaction pin to old pin",
                }
                return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

            if new_transaction_pin == new_transaction_pin_retry:
                hashed_pin = make_password(new_transaction_pin)
                request_user.transaction_pin = hashed_pin
                request_user.save()
                response = {
                    "status": "success",
                    "message": "Transaction Pin Changed",
                }
                return Response(response, status=status.HTTP_201_CREATED)

            else:
                response = {
                    "status": "error",
                    "message": "Transaction Pins Entered Do Not Match",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


#####################################################
# FORGOT TRANSACTION PIN


class ForgotTransactionPinAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin]
    serializer_classes = ForgotTransPinSerializer

    def post(self, request):
        serializer = self.serializer_classes(data=request.data)
        if serializer.is_valid():
            gen_token = serializer.validated_data["gen_token"]
            otp = serializer.validated_data["otp"]
            new_transaction_pin = serializer.validated_data["pin1"]
            new_transaction_pin_retry = serializer.validated_data["pin2"]

            # Get User
            user = request.user

            # verify generated token
            try:
                decoded_token = jwt.decode(gen_token, settings.SECRET_KEY, algorithms=["HS256"])
                user_customer_id = decoded_token.get("jti")
            except:
                response = {
                    "error_code": "141",
                    "message": "An error occured, please try again.",
                    "extra_message": "Entered Exception",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if user_customer_id != str(user.customer_id):
                response = {
                    "error_code": "141",
                    "message": "An error occured, please try again.",
                    "extra_message": "User IDs do not match",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            get_unexpired_otp = UserTempToken.objects.filter(
                user=user, token=otp, pin_type="RESET_TRANS_PIN", exp__gt=datetime.now()
            ).last()
            if not get_unexpired_otp:
                response = {"error_code": "178", "message": "OTP Expired"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if not new_transaction_pin.isnumeric():
                response = {"error_code": "14", "message": "You must supply an integer for new transaction pin"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if new_transaction_pin != new_transaction_pin_retry:
                response = {"error_code": "14", "message": "New Entered Pins do not match"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if len(new_transaction_pin) != 4 or len(new_transaction_pin_retry) != 4:

                response = {
                    "error": "995",
                    "message": "New and Old Transaction Pins must be four digits",
                }
                return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

            if new_transaction_pin == new_transaction_pin_retry:

                if get_unexpired_otp.last_success_req is not None:
                    tz = pytz.timezone(settings.TIME_ZONE)

                    change_hour_interval = 60
                    if datetime.now(tz) < get_unexpired_otp.last_success_req + timedelta(minutes=change_hour_interval):
                        response = {
                            "error": "959",
                            "message": f"cannot change transaction pin more than once with {int(change_hour_interval/60)} hour",
                        }
                        return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

                old_transaction_pin = user.transaction_pin

                new_hashed_pin = make_password(new_transaction_pin)

                if old_transaction_pin == new_hashed_pin:
                    response = {
                        "error": "958",
                        "message": "cannot change transaction pin to old pin",
                    }
                    return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

                user.transaction_pin = new_hashed_pin
                user.save()

                get_unexpired_otp.last_success_req = datetime.now()
                get_unexpired_otp.save()

                response = {
                    "status": "success",
                    "message": "Transaction Pin Changed",
                }
                return Response(response, status=status.HTTP_201_CREATED)

            else:
                response = {
                    "status": "error",
                    "message": "Transaction Pins Entered Do Not Match",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


#####################################################


class FirstResetPasswordAPIView(APIView):
    # permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin]

    def post(self, request):
        serializer = FirstResendPasswordSerializer(data=request.data)
        if serializer.is_valid():
            entry_type = serializer.validated_data["entry_type"]

            if entry_type == "email":
                identifier = serializer.validated_data["identifier"]
            else:
                identifier = User.format_number_from_back_add_234(serializer.validated_data["identifier"])

            check_types = ["email", "phone_number"]

            if entry_type in check_types:
                if entry_type == "phone_number":

                    if identifier.isnumeric() == False and len(User.format_number_from_back_add_234(identifier)) == 13:
                        response = {"error": "57", "message": "You must supply a valid phone number"}
                        return Response(response, status=status.HTTP_403_FORBIDDEN)

                elif entry_type == "email":
                    if not validate_email_function(email=identifier) == True:

                        response = {"error": "59", "message": "You must supply an a valid email address"}
                        return Response(response, status=status.HTTP_403_FORBIDDEN)

                get_user = User.objects.filter(Q(email=identifier) | Q(phone_number=identifier)).last()
                if not get_user:

                    response = {
                        "error": "79",
                        "message": "Invalid credentials",
                    }
                    return Response(response, status=status.HTTP_403_FORBIDDEN)

                else:
                    if get_user.registration_email_verified == False:
                        response = {
                            "error": "79",
                            "message": "You need to confirm your Registration Passcode",
                            "email": get_user.email,
                        }
                        return Response(response, status=status.HTTP_403_FORBIDDEN)

                    if entry_type == "phone_number":
                        send_whisper_otp = Users_Otp.send_new_otp(
                            phone_number=get_user.phone_number, app_name="Liberty Pay", no_callback=True
                        )

                        masked_phone_number = mask_phone_number(get_user.phone_number)

                        response = {
                            "message": f"OTP sent to phone number ({masked_phone_number}) to reset login pin",
                            "phone_number": get_user.phone_number,
                        }
                        return Response(response, status=status.HTTP_200_OK)

                    elif entry_type == "email":
                        masked_email = mask_email(get_user.email)

                        new_change_pass = str(passcode_generator())
                        User.create_change_passcode_hash(user=get_user, passcode=new_change_pass)

                        send_email(email=get_user.email, passcode=new_change_pass)
                        response = {
                            "message": f"OTP sent to email ({masked_email}) to reset login pin",
                            "phone_number": get_user.phone_number,
                        }
                        return Response(response, status=status.HTTP_200_OK)

            else:
                response = {"error_code": "52", "message": "You must supply a valid type (email or phone_number)"}

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ConfirmLoginOTPResetPinAPIView(APIView):
    # permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin]

    def post(self, request):
        serializer = OTPVerificationSerializer(data=request.data)
        if serializer.is_valid():
            phone_number = User.format_number_from_back_add_234(serializer.validated_data["phone_number"])
            app_name = "Liberty paiy"
            otp_value = serializer.validated_data["otp"]
            user_instance = User.objects.filter(phone_number=phone_number).last()

            if user_instance:

                if len(phone_number) == 13:
                    verify_otp = Users_Otp.verify_new_otp(user_instance.phone_number, otp_value, app_name)
                    if verify_otp["verified"] is True:
                        ResetPinStorage.objects.create(
                            user=user_instance,
                            type_of_reset="LOGIN_PIN_RESET",
                            hashed_pin=User.custom_hash_values(otp_value),
                        )
                        response = {"message": "OTP Verified", "phone_number": user_instance.phone_number}
                        return Response(response, status=status.HTTP_202_ACCEPTED)

                    else:
                        if User.check_change_passcode_otp(user=user_instance, passcode=otp_value):
                            ResetPinStorage.objects.create(
                                user=user_instance,
                                type_of_reset="LOGIN_PIN_RESET",
                                hashed_pin=User.custom_hash_values(otp_value),
                            )

                            user_instance.change_pass_hash = None
                            user_instance.change_pass_hash_time = None
                            user_instance.save()

                            response = {"message": "OTP Verified", "phone_number": user_instance.phone_number}
                            return Response(response, status=status.HTTP_202_ACCEPTED)

                        else:

                            response = {"error": "18", "message": "Invalid OTP"}
                            return Response(response, status=status.HTTP_400_BAD_REQUEST)

                else:
                    response = {"message": f"Incorrect phone number format: {phone_number}"}
                    return Response(response, status=status.HTTP_404_NOT_FOUND)

            else:
                response = {"error_code": "01", "message": "user does not exist"}
                return Response(response, status=status.HTTP_404_NOT_FOUND)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ResetLoginPasscodeAPIView(APIView):
    def post(self, request):
        serializer = ResetPinSerializer(data=request.data)
        if serializer.is_valid():
            phone_number = User.format_number_from_back_add_234(serializer.validated_data["phone_number"])
            pin1 = serializer.validated_data["pin1"]
            pin2 = serializer.validated_data["pin2"]
            otp_value = serializer.validated_data["otp_value"]

            try:
                user = User.objects.get(phone_number=phone_number)

                get_hashed_code = ResetPinStorage.objects.filter(user=user, type_of_reset="LOGIN_PIN_RESET").last()
                if get_hashed_code:
                    check_hash_auth = User.custom_unhash_values(
                        passcode=otp_value, hashed_counterpart=get_hashed_code.hashed_pin
                    )
                else:
                    check_hash_auth = None

                if pin1 != pin2:

                    response = {
                        "error": "48",
                        "message": "Pins Entered Do Not Match",
                    }
                    return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

                elif check_hash_auth is None:
                    response = {
                        "error": "76",
                        "message": "You have not requested for a reset password recently",
                    }
                    return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

                elif check_hash_auth is False:
                    response = {
                        "error": "77",
                        "message": "Entered OTP is invalid, please retry the resend pin endpoint",
                    }
                    return Response(response, status=status.HTTP_403_FORBIDDEN)

                elif check_hash_auth is True:

                    user.has_login_pin = True
                    user.set_password(pin1)
                    user.passcode_retries = 0
                    user.passcode_remaining_retries = 10
                    user.save()
                    get_hashed_code.delete()

                    response = {"message": "Pin successfully reset"}
                    return Response(response, status=status.HTTP_201_CREATED)

                else:
                    response = {"error": "84", "message": "Unexpected error occured. Please try again"}
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

            except User.DoesNotExist:
                response = {"error_code": "01", "message": "user does not exist"}
                return Response(response, status=status.HTTP_404_NOT_FOUND)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class InAppResetLoginPasscodeInAppAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin]
    serializer_classes = ResetPINSerializer

    def post(self, request):
        serializer = self.serializer_classes(data=request.data)
        if serializer.is_valid():
            old_login_pin = serializer.validated_data["old_pin"]
            new_login_pin = serializer.validated_data["new_pin"]
            new_login_pin_retry = serializer.validated_data["new_pin_retry"]

            # Get User
            request_user = request.user

            # Check Old Pin
            check_pin = User.check_if_user_login_pin_match(user=request_user, pincode=old_login_pin)
            if not check_pin:
                response = {"error": "584", "message": "old password is incorrect"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if not old_login_pin.isnumeric():
                response = {"error_code": "14", "message": "You must supply an integer for old password"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if not new_login_pin.isnumeric():
                response = {"error_code": "14", "message": "You must supply an integer for new password"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if new_login_pin != new_login_pin_retry:
                response = {"error_code": "14", "message": "New Entered Pins do not match"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if len(old_login_pin) != 6 or len(new_login_pin) != 6 or len(new_login_pin_retry) != 6:

                response = {
                    "error": "995",
                    "message": "New and Old Passwords Pins must be six digits",
                }
                return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

            if old_login_pin == new_login_pin:
                response = {
                    "error": "958",
                    "message": "cannot change password to old password",
                }
                return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

            if new_login_pin == new_login_pin_retry:
                request_user.set_password(new_login_pin)
                request_user.save()
                response = {
                    "status": "success",
                    "message": "Password Changed",
                }
                return Response(response, status=status.HTTP_201_CREATED)

            else:
                response = {
                    "status": "error",
                    "message": "Passwords Entered Do Not Match",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class WhisperOTPCallBack(APIView):
    def post(self, request):
        response = request.data
        print("((((((((((((((((((((((((")
        print(response)
        otp = response["otp"]
        print(otp)
        print("((((((((((((((((((((((((")
        phone_number = User.format_number_from_back_add_234(response["receiver"])

        user = User.objects.filter(phone_number=phone_number).first()
        if user:
            # send_email(email=user.email, passcode=otp)
            send_whisper_otp_to_email_task(user_id=user.id, otp=otp)
        else:
            pass
        return Response({"otp": "sent"})


# RESENDS


class ResendRegistrationPinAPIView(APIView):
    def post(self, request):
        serializer = ResendRegistrationPinSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data["email"]
            try:
                user = User.objects.get(email=email)
                passcode = str(passcode_generator())

                print("-------------------------")
                print("-------------------------")
                print(passcode)
                print("-------------------------")
                print("-------------------------")
                if User.create_registration_email_otp(user=user, passcode=passcode):
                    send_email(email=email, passcode=passcode)

                    response = {"message": "registration pin has been sent to email"}
                    return Response(response, status=status.HTTP_200_OK)
                else:
                    response = {
                        "error_code": "09",
                        "message": "Sorry, this user has already verified email passcode",
                    }
                    return Response(response, status=status.HTTP_403_FORBIDDEN)

                # else:
                #     response = {
                #     "error_code": "13",
                #     "message": "User already active. Cannot reset pin"
                # }
                # return Response(response, status=status.HTTP_404_NOT_FOUND)

            except User.DoesNotExist:
                response = {"error_code": "01", "message": "user does not exist"}
                return Response(response, status=status.HTTP_404_NOT_FOUND)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
class ResendRegistrationPhonePinAPIView(APIView):
    def post(self, request):
        serializer = ResendRegistrationPhonePinSerializer(data=request.data)
        if serializer.is_valid():
            phone_number = User.format_number_from_back_add_234(serializer.validated_data.get("phone_number"))
            try:
                user = User.objects.get(phone_number=phone_number)
                passcode = str(passcode_generator())

                print("-------------------------")
                print("-------------------------")
                print(passcode)
                print("-------------------------")
                print("-------------------------")
                if User.create_registration_email_otp(user=user, passcode=passcode):
                    send_sms(phone=phone_number, passcode=passcode)

                    response = {"message": "registration pin has been sent to phone number"}
                    return Response(response, status=status.HTTP_200_OK)
                else:
                    response = {
                        "error_code": "09",
                        "message": "Sorry, this user has already verified phone number passcode",
                    }
                    return Response(response, status=status.HTTP_403_FORBIDDEN)

                # else:
                #     response = {
                #     "error_code": "13",
                #     "message": "User already active. Cannot reset pin"
                # }
                # return Response(response, status=status.HTTP_404_NOT_FOUND)

            except User.DoesNotExist:
                response = {"error_code": "01", "message": "user does not exist"}
                return Response(response, status=status.HTTP_404_NOT_FOUND)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


###################################################################################################
# CREATE PASSCODE


class CreateLoginPasscodeAPIView(APIView):
    def post(self, request):
        serializer = PinCreateSerializer(data=request.data)
        if serializer.is_valid():
            phone_number = User.format_number_from_back_add_234(serializer.validated_data["phone_number"])
            pin1 = serializer.validated_data["pin1"]
            pin2 = serializer.validated_data["pin2"]
            onboarding_status = serializer.validated_data.get("onboarding_status", None)

            user = User.objects.filter(phone_number=phone_number).last()
            if user:
                check_if_user_created_pin = User.check_user_login_pin(user)

                if pin1 != pin2:

                    response = {
                        "status": "error",
                        "message": "Pins Entered Do Not Match",
                    }
                    return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

                elif check_if_user_created_pin:
                    response = {
                        "status": "error",
                        "message": "Login Pin already created",
                    }
                    return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

                elif not user.registration_email_verified:
                    response = {
                        "status": "error",
                        "message": "user email has not been verified",
                    }
                    return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)
                else:
                    # with transaction.atomic():
                    user.has_login_pin = True
                    user.set_password(pin1)
                    if onboarding_status:
                        user.onboarding_status = onboarding_status
                    user.save()
                    # toDO send with celery
                    # send_data_to_horizon_pay_task.delay(user_id=user.id)
                    # send_data_to_horizon_pay_task.apply_async(
                    #         queue="horizon_queue",
                    #         kwargs={
                    #             "user_id": user.id
                    #         }
                    #     )

                    response = {"message": "Pin successfully created"}
                    return Response(response, status=status.HTTP_201_CREATED)

            else:
                response = {"error_code": "01", "message": "user does not exist"}
                return Response(response, status=status.HTTP_404_NOT_FOUND)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CreateLoginPasscodeAndActivateEmailAPIView(APIView):
    def post(self, request):
        serializer = PinCreateSerializer(data=request.data)
        if serializer.is_valid():
            phone_number = User.format_number_from_back_add_234(serializer.validated_data["phone_number"])
            pin1 = serializer.validated_data["pin1"]
            pin2 = serializer.validated_data["pin2"]
            onboarding_status = serializer.validated_data.get("onboarding_status")

            user = User.objects.filter(phone_number=phone_number).last()
            if user:
                check_if_user_created_pin = User.check_user_login_pin(user)

                if pin1 != pin2:

                    response = {
                        "status": "error",
                        "message": "Pins Entered Do Not Match",
                    }
                    return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

                elif check_if_user_created_pin:
                    response = {
                        "status": "error",
                        "message": "Login Pin already created",
                    }
                    return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)



                user.has_login_pin = True
                user.registration_email_verified = True
                if onboarding_status:
                    user.onboarding_status = onboarding_status
                user.set_password(pin1)
                user.save()

                response = {"message": "Pin successfully created"}
                return Response(response, status=status.HTTP_201_CREATED)

            else:
                response = {"error_code": "01", "message": "user does not exist"}
                return Response(response, status=status.HTTP_404_NOT_FOUND)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


################################################################################
# User Viewsss


class UserDetailAPIView(APIView):
    user_serializer_class = UserSerializer
    accounts_serializer_class = GetAccountSerializer
    wallets_serializer_class = GetWalletSerializer
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin]

    def get(self, request):

        admin_password = self.request.query_params.get("admin_password")
        user_by_admin = self.request.query_params.get("user_by_admin")

        if admin_password and not user_by_admin or user_by_admin and not admin_password:
            user_instance = request.user

        if admin_password and user_by_admin:
            check_user_as_admin = User.objects.filter(email=user_by_admin).last()

            if admin_password != f"{settings.EMEKA_ADMIN_PASSWORD}" or not check_user_as_admin:
                user_instance = request.user

            else:
                user_instance = check_user_as_admin
        else:

            user_instance = request.user

        #################################################################################################################

        # user_instance = request.user

        account_instance = AccountSystem.get_default_provider_accounts(user=user_instance)
        wallet_instance = WalletSystem.fetch_wallets(user=user_instance)
        verification_status = UserImage.objects.filter(user=user_instance, is_match=True).exists()

        accounts_serializer = self.accounts_serializer_class(account_instance, many=True)

        wallets_serializer = self.wallets_serializer_class(wallet_instance, many=True)

        user_serializer = self.user_serializer_class(user_instance)

        account_data = accounts_serializer.data

        if user_instance.kyc_level < 1:
            account_data = []

        referral_details = user_serializer.get_user_referrals(user_instance)

        response = {
            "message": "user found",
            "user_data": user_serializer.data,
            "accounts_data": account_data,
            "wallets_data": wallets_serializer.data,
            "img_verification_status": verification_status,
            "referral_details": referral_details,
        }

        return Response(response, status=status.HTTP_200_OK)


##########################################################################
# DARE ADDED THIS

SUCCESS_MSG = {"message": "user updated successfully", "status": status.HTTP_200_OK}


@api_view(["POST"])
def add_user_personal_email_and_referral_code(request, phone):
    phone_no = format_phone_no(phone)
    try:
        user = User.objects.get(phone_number=phone_no)
    except User.DoesNotExist:
        data = {
            "status": status.HTTP_401_UNAUTHORIZED,
            "message": f"invalid user with phone number {phone_no}",
        }
        return Response(data, status=status.HTTP_401_UNAUTHORIZED)
    serializer = EmailAndReferralCodeSerializer(data=request.data)
    if serializer.is_valid():
        if user.email:
            email = serializer.validated_data["email"]
            # user = User.objects.filter(email=email)
            if user.email == email:
                data = {
                    "status": status.HTTP_400_BAD_REQUEST,
                    "message": "user already exists",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            referral_code = serializer.validated_data["referral_code"]
            user.email, user.referral_code = email, referral_code
            user.email_updated = True
            user.save()
            return Response(SUCCESS_MSG, status=status.HTTP_200_OK)
        else:
            data = {"message": "account already exist", "status": status.HTTP_302_FOUND}
            return Response(data, status=status.HTTP_302_FOUND)
    data = {
        "status": status.HTTP_400_BAD_REQUEST,
        "message": "field error",
        "type": serializer.errors,
    }
    return Response(data, status=status.HTTP_400_BAD_REQUEST)


@api_view(["POST"])
def update_user_personal_info(request, phone):
    phone_no = format_phone_no(phone)
    try:
        user = User.objects.get(phone_number=phone_no)
    except User.DoesNotExist:
        data = {
            "status": status.HTTP_401_UNAUTHORIZED,
            "message": f"invalid user with phone number {phone_no}",
        }
        return Response(data, status=status.HTTP_401_UNAUTHORIZED)
    serializer = PersonalInfoSerializer(data=request.data)
    if serializer.is_valid():
        if not user.state or user.lga or user.street or user.nearestlandmark:
            state = serializer.validated_data["state"]
            lga = serializer.validated_data["lga"]
            nearestlandmark = serializer.validated_data["nearestlandmark"]
            street = serializer.validated_data["street"]
            user.state = state
            user.lga = lga
            user.nearestlandmark = nearestlandmark
            user.street = street
            user.save()
            return Response(SUCCESS_MSG, status=status.HTTP_200_OK)
        else:
            data = {"message": "account already exist", "status": status.HTTP_302_FOUND}
    data = {
        "status": status.HTTP_400_BAD_REQUEST,
        "message": "field error",
        "type": serializer.errors,
    }
    return Response(data, status=status.HTTP_400_BAD_REQUEST)


@api_view(["POST"])
def send_otp(request):
    if request.method == "POST":
        serializer = SendOtpSerializer(data=request.data)
        if serializer.is_valid():
            phone_no = serializer.validated_data["phone_no"]
            app_name = serializer.validated_data["app_name"]
            if len(phone_no) == 11:
                phone_no = format_phone_no(phone_no)
                response = Users_Otp.send_new_otp(phone_no, app_name)
            elif len(phone_no) == 13:
                response = Users_Otp.send_new_otp(phone_no, app_name)
            else:
                response = {"message": f"Bad Phone Number Format: {phone_no}"}
            return Response({"data": response}, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(["GET"])
def validate_recovery_phone_no_to_passcode_reset(request):
    serializer = PasscodeResetSerializer(data=request.data)
    if serializer.is_valid():
        phone = serializer.validated_data["phone_no"]
        phone_no = format_phone_no(phone)
        try:
            User.objects.get(phone_number=phone_no)
        except User.DoesNotExist:
            data = {
                "status": status.HTTP_401_UNAUTHORIZED,
                "message": f"invalid user with phone number {phone_no}",
            }
            return Response(data, status=status.HTTP_401_UNAUTHORIZED)
        data = {"status": status.HTTP_200_OK, "message": "user found"}
        return Response(data, status=status.HTTP_200_OK)
    data = {
        "status": status.HTTP_400_BAD_REQUEST,
        "message": "field error",
        "type": serializer.errors,
    }
    return Response(data, status=status.HTTP_400_BAD_REQUEST)


####################################################################################
# Constants


class ConstantsAPIView(APIView):
    get_serializer_class = ConstantSerializer
    post_serializer_class = CollectFBKSerializer
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin]

    def post(self, request):
        post_serializer = self.post_serializer_class(data=request.data)
        if post_serializer.is_valid():
            user_FBK = post_serializer.validated_data["fbk"]
            request.user.firebase_key = user_FBK
            request.user.save()

            # cosntant_instance = ConstantTable.objects.filter(is_active=True)
            cosntant_instance = ConstantTable.get_constant_table_instance()
            if cosntant_instance:
                serializer = self.get_serializer_class(cosntant_instance)
                response = {
                    "status": "true",
                    "message": "found constants",
                    "data": serializer.data,
                }
            else:
                response = {"status": "false", "message": "no constants found", "data": []}
            return Response(response, status=status.HTTP_200_OK)
        return Response(post_serializer.errors, status=status.HTTP_400_BAD_REQUEST)


#################################################################################################
# Check KYC


class CheckKYCLevelView(APIView):
    serializer_class = KYCSerializer
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin]

    def get(self, request):
        user_instance = KYCTable.objects.filter(user=request.user).first()
        serializer = self.serializer_class(user_instance)
        return Response(serializer.data, status=status.HTTP_200_OK)


############################################################################################


class CloudNotification(APIView):
    def get(self, request):
        # response = cloud_messaging.send_broadcast(
        #     token="CppBmjwa0bnyRtycQhrEQ8_baBu-1paJni6yzkUYtZoomC3LZRDF5VWNR56cMNkQ--gnKXFf4HlEWbUb",
        response = notify_messaging.send_broadcast(
            token="hlHEAMGqQ-qUOURZ3shadb3ghD-aydVPp0KP4fMv6f_Ppa-si3r4LqoatUKSq3MLqGanHFgOpZkkQ0rqG553G3i",
            title="Hello, Efe",
            body="Where are you?",
            data={"happy": "yes"},
        )
        return Response(response, status=status.HTTP_200_OK)


class GetUserTokenFirebase(APIView):
    permission_classes = [CustomIsAuthenticated]

    def post(self, request):
        serializer = FirebaseTokenSerializer(data=request.data)
        if serializer.is_valid():
            firebase_key = serializer.validated_data["firebase_key"]
            if firebase_key:
                try:
                    user = User.objects.get(email=request.user.email)
                    user.firebase_key = firebase_key
                    user.save()
                    details = {
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "phone_number": user.phone_number,
                        "email": user.email,
                        "customer_id": user.customer_id,
                        "firebase_key": user.firebase_key,
                    }

                    return Response(details, status=status.HTTP_200_OK)
                except User.DoesNotExist:
                    response = {
                        "status": "error",
                        "message": "user does not exist",
                    }
                    return Response(status=status.HTTP_404_NOT_FOUND)
            else:
                response = {"status": "error", "message": "error sending request"}
                return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class SetNotifyAppTokenFBKAPIView(APIView):
    authentication_classes = [CustomTokenAuthentication]
    permission_classes = [CustomIsAuthenticated]

    def post(self, request):
        serializer = FirebaseTokenSerializer(data=request.data)
        if serializer.is_valid():
            firebase_key = serializer.validated_data["firebase_key"]
            if firebase_key:
                try:
                    user = User.objects.get(email=request.user.email)
                    user.notify_app_token = firebase_key
                    user.save()
                    response = {"status": "success", "message": "posted successfully"}

                    return Response(response, status=status.HTTP_200_OK)
                except User.DoesNotExist:
                    response = {
                        "status": "error",
                        "message": "user does not exist",
                    }
                    return Response(status=status.HTTP_404_NOT_FOUND)
            else:
                response = {"status": "error", "message": "error sending request"}
                return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


################################################################################################################
################################################################################################################


class UnsubscribeSMS(APIView):
    permission_classes = [CustomIsAuthenticated]
    serializer_class = SMSSubscribeSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid():
            sms_subscription = serializer.validated_data["sms_subscription"]

            if sms_subscription == True:
                sms_status = "subscribed"
            else:
                sms_status = "unsubscribed"

            if request.user.sms_subscription == sms_subscription:
                response = {"error": "47", "message": f"User already {sms_status} for SMS Notification"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
            else:
                if sms_subscription == True:
                    request.user.sms_subscription = True
                else:
                    request.user.sms_subscription = False

                request.user.save()

                response = {"message": f"You have successfully {sms_status} for SMS Notification"}
                return Response(response, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


########################################################################################################
# LOANS VIEWS


class GetAgentsWithTransactionsAPIView(generics.ListAPIView):
    authentication_classes = [CustomTokenAuthentication]
    permission_classes = [CustomIsAuthenticated]
    serializer_class = UsersWithTransLoansSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_class = UserDataFilter

    def get_queryset(self):

        active_users_trans = (
            Transaction.objects.exclude(status="FAILED").distinct("user").values_list("user", flat=True)
        )
        active_users = User.objects.filter(Q(id__in=active_users_trans) & ~Q(email=settings.FLOAT_USER_EMAIL))

        return active_users

    # def get(self, request):

    #     active_users_trans = Transaction.objects.exclude(status = "FAILED").distinct("user").values_list("user", flat=True)
    #     active_users = User.objects.filter(Q(id__in=active_users_trans) & ~Q(email=settings.FLOAT_USER_EMAIL))
    #     serializer_data = self.get_serializer_class(active_users, many=True)

    #     return Response(serializer_data.data, status=status.HTTP_200_OK)


class GetLoansTransactionHistoryAPIView(generics.ListAPIView):
    authentication_classes = [CustomTokenAuthentication]
    permission_classes = [CustomIsAuthenticated]
    serializer_class = LoansTransactionHistorySerializer
    # pagination_class = CustomPagination
    # search_fields = ['liberty_reference']
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_class = TransactionDateFilter

    def get_queryset(self):

        transactions_needed = [
            "SEND_BUDDY",
            "SEND_BANK_TRANSFER",
            "FUND_BUDDY",
            "FUND_BANK_TRANSFER",
            "FUND_PAYSTACK",
            "CARD_TRANSACTION_FUND",
            "CARD_TRANSACTION_FUND_TRANSFER",
            "BILLS_AND_PAYMENT",
            "AIRTIME_PIN",
            "CARD_PURCHASE",
        ]

        active_users_trans = (
            Transaction.objects.exclude(
                Q(user__email=settings.FLOAT_USER_EMAIL) | Q(user_id=None) | Q(transaction_leg="INTERNAL")
            )
            .filter(status="SUCCESSFUL", transaction_type__in=transactions_needed)
            .order_by("-date_created")
        )

        return active_users_trans

        # serializer_data = self.transaction_serializer_class(active_users_trans, many=True)

        # return Response(serializer_data.data, status=status.HTTP_200_OK)

        # return Transaction.objects.filter(
        #     Q(user=request_user) & ~Q(narration="QLP_IN_HOUSE") \
        #         & ~Q(transaction_type="SEND_BACK_TO_FLOAT_TRANSFER") \
        #         & ~Q(transaction_type="SEND_LIBERTY_COMMISSION")
        # ).order_by("-date_created")

    # authentication_classes = [CustomTokenAuthentication]
    # permission_classes = [CustomIsAuthenticated]
    # transaction_serializer_class = LoansTransactionHistorySerializer
    # # filter_backends = [DjangoFilterBackend, SearchFilter]
    # # filterset_class = TransactionDateFilter

    # def get(self, request):
    #     user_id = request.query_params.get('user_id')
    #     if user_id is None:
    #         response = {
    #             "status": "error",
    #             "message": "No user id attached"
    #         }
    #         return Response(response, status=status.HTTP_400_BAD_REQUEST)

    #     transactions_needed = [
    #         "SEND_BUDDY", "SEND_BANK_TRANSFER", "FUND_BUDDY", "FUND_BANK_TRANSFER", "FUND_PAYSTACK",
    #         "CARD_TRANSACTION_FUND", "CARD_TRANSACTION_FUND_TRANSFER", "BILLS_AND_PAYMENT", "AIRTIME_PIN"
    #     ]

    #     active_users_trans = Transaction.objects.exclude(Q(user__email=settings.FLOAT_USER_EMAIL) | Q(user_id=None) | Q(transaction_leg="INTERNAL")) \
    #         .filter(status = "SUCCESSFUL", transaction_type__in=transactions_needed)

    #     serializer_data = self.transaction_serializer_class(active_users_trans, many=True)

    #     return Response(serializer_data.data, status=status.HTTP_200_OK)


########################################
# update user transaction pin
########################################


class TransactionPinUpdateView(APIView):
    permission_classes = [CustomIsAuthenticated]

    def put(self, request):
        body = request.data
        user = request.user
        if not user.has_transaction_pin:
            return Response(
                {"message": "User does not have a transaction PIN please create a new pin!"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        check_pin = User.check_sender_transaction_pin(user=user, pincode=body["oldPin"])
        if not check_pin:
            return Response({"message": "User old pin is incorrect!"}, status=status.HTTP_400_BAD_REQUEST)
        if body["pin"] != body["repeatPin"]:
            return Response({"message": "Transaction Pin's must be equal!"}, status=status.HTTP_400_BAD_REQUEST)
        try:
            with transaction.atomic():
                # remove pin
                user.transaction_pin = None
                user.save()
                update_user_pin = User.create_transaction_pin(user, body["pin"])
                serializer = UserSerializer(user)
                return Response(serializer.data, status=status.HTTP_200_OK)
        except:
            return Response({"message": "Something went wrong, please try again!"}, status=status.HTTP_200_OK)


####################################
# update usernames
####################################


class UpdateUsernameView(APIView):
    """
    new usernames must be unique so upon failure of check,
    suggest 3 new usernames.
    Write a recommendation algorithm
    """

    permission_classes = [CustomIsAuthenticated]

    def put(self, request):
        user = request.user
        username = request.data["username"]
        if user.username == username:
            resp = {"message": "Username already taken by you!"}
            return Response(resp, status=status.HTTP_400_BAD_REQUEST)
        try:
            thisUser = User.objects.get(username=username)
            recommended_usernames = generate_usernames(user, username)
            resp = {"message": "Username already taken, please pick of these three", "data": recommended_usernames}
            return Response(resp, status=status.HTTP_400_BAD_REQUEST)
        except User.DoesNotExist:
            updatedUser = User.objects.filter(email=user.email).update(username=username)
            user = User.objects.get(username=username)
            serializer = UpdateUsernameSerializer(user)
            return Response(serializer.data, status=status.HTTP_200_OK)


class AgentGiveConsentAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]
    serializer_class = AgentGiveConsentSerializer

    def post(self, request):
        response = request.data
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            consent = serializer.validated_data["consent"]

            if request.user.agent_consent and consent is False:
                response = {"error": "576", "message": "consent cannot be ungiven"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            elif consent is False:
                response = {"error": "577", "message": "consent not given"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            elif request.user.agent_consent:
                response = {"error": "578", "message": "consent already ungiven"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            elif consent is True:
                request.user.agent_consent = True
                request.user.date_of_consent = datetime.now()
                request.user.save()

                response = {"message": "consent successfully given"}
                return Response(response, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# Verify Rep Code
class VerifyRepReferalCode(APIView):
    def get(self, request):
        rep_code = request.query_params.get("code")

        if rep_code is None:
            response = {"error": "549", "message": "No Relationship Officer Code Entered"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)

        sales_rep = SalesRep.objects.filter(sales_rep_code=rep_code).last()
        if sales_rep is None:
            response = {"error": "550", "message": "Relationship Officer With Code Does Not Exist"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)
        else:
            response = {
                "message": "Relationship Officer Successfully Retrieved",
                "data": {"name": sales_rep.sales_rep.bvn_full_name},
            }

            return Response(response, status=status.HTTP_200_OK)


# class FirstResetPasswordAPIView(APIView):
#     # permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin]

#     def post(self, request):
#         serializer = FirstResendPasswordSerializer(data=request.data)
#         if serializer.is_valid():
#             entry_type = serializer.validated_data["entry_type"]

#             if entry_type == "email":
#                 identifier = serializer.validated_data["identifier"]
#             else:
#                 identifier = User.format_number_from_back_add_234(serializer.validated_data["identifier"])


#             check_types = ["email", "phone_number"]

#             if entry_type in check_types:
#                 if entry_type == "phone_number":

#                     if identifier.isnumeric() == False and len(User.format_number_from_back_add_234(identifier)) == 13:
#                         response = {
#                             "error_code": "57",
#                             "message": "You must supply a valid phone number"
#                         }
#                         return Response(response, status=status.HTTP_403_FORBIDDEN)

#                 elif entry_type == "email":
#                     if not validate_email_function(email=identifier) == True:

#                         response = {
#                             "error_code": "59",
#                             "message": "You must supply an a valid email address"
#                         }
#                         return Response(response, status=status.HTTP_403_FORBIDDEN)


#                 get_user = User.objects.filter(Q(email=identifier) | Q(phone_number=identifier)).last()
#                 if not get_user:

#                     response = {
#                         "error_code": "79",
#                         "message": "No user detail found in our database",
#                     }
#                     return Response(response, status=status.HTTP_403_FORBIDDEN)

#                 else:
#                     send_whisper_otp = Users_Otp.send_new_otp(
#                         phone_number = get_user.phone_number, app_name = "Liberty Pay"
#                     )

#                     masked_phone_number = mask_phone_number(get_user.phone_number)
#                     masked_email = mask_email(get_user.email)

#                     response = {
#                         "message": f"OTP sent to email ({masked_email}) and phone number ({masked_phone_number}) to reset login pin",
#                         "phone_number": get_user.phone_number
#                     }
#                     return Response(response, status=status.HTTP_200_OK)
#             else:
#                 response =  {
#                     "error_code": "52",
#                     "message": "You must supply a valid type (email or phone_number)"
#                 }


#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class MerchantPinVerifyOTPAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin]

    def post(self, request):
        TrackUserClick.track_clicks_accross(user=request.user, view="MERCHANT_PIN_CREATE_OR_CHANGE")

        phone_number = request.user.phone_number
        user_email = request.user.email

        send_whisper_otp = Users_Otp.send_new_otp(phone_number=phone_number, app_name="Liberty Pay")

        masked_phone_number = mask_phone_number(phone_number)
        masked_email = mask_email(user_email)

        response = {
            "message": f"OTP sent to email ({masked_email}) and phone number ({masked_phone_number}) to reset login pin",
            "phone_number": phone_number,
        }
        return Response(response, status=status.HTTP_200_OK)


class MerchantPinConfirmOTPAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin]

    def post(self, request):
        TrackUserClick.track_clicks_accross(user=request.user, view="MERCHANT_PIN_CONFIRM_OTP")

        serializer = MerchantPinVerifyOTPSerializer(data=request.data)

        if serializer.is_valid():

            otp_value = serializer.validated_data["otp_value"]
            phone_number = request.user.phone_number
            app_name = "Liberty paiy"

            user_instance = User.objects.filter(phone_number=phone_number).last()

            if not otp_value.isnumeric():
                response = {"error": "19", "message": "You must supply an integer for otp_value"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            verify_otp = Users_Otp.verify_new_otp(user_instance.phone_number, otp_value, app_name)

            if verify_otp["verified"] is True:
                ResetPinStorage.objects.create(
                    user=user_instance,
                    type_of_reset="CREATE_MERCHANT_PIN",
                    hashed_pin=User.custom_hash_values(otp_value),
                )
                response = {"message": "OTP Verified", "phone_number": user_instance.phone_number}
                return Response(response, status=status.HTTP_202_ACCEPTED)

            else:
                response = {"error": "18", "message": "Invalid OTP"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class MerchantPinCreateAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin]

    def post(self, request):
        serializer = CreateMerchantPinSerializer(data=request.data)
        if serializer.is_valid():
            phone_number = request.user.phone_number

            pin1 = serializer.validated_data["pin1"]
            pin2 = serializer.validated_data["pin2"]
            otp_value = serializer.validated_data["otp_value"]

            user = request.user
            if not pin1.isnumeric() or not pin2.isnumeric():
                response = {"error": "19", "message": "You must supply an integer for pin"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            get_hashed_code = ResetPinStorage.objects.filter(user=user, type_of_reset="CREATE_MERCHANT_PIN").last()
            if not get_hashed_code:
                response = {
                    "error": "76",
                    "message": "You have not requested for a merchant pin create or reset recently",
                }
                return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

            else:
                check_hash_auth = User.custom_unhash_values(
                    passcode=otp_value, hashed_counterpart=get_hashed_code.hashed_pin
                )
                if pin1 != pin2:

                    response = {
                        "error": "48",
                        "message": "Pins Entered Do Not Match",
                    }
                    return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

                elif check_hash_auth is False:
                    response = {
                        "error": "77",
                        "message": "Entered OTP is invalid, please retry the resend pin endpoint",
                    }
                    return Response(response, status=status.HTTP_403_FORBIDDEN)

                elif check_hash_auth is True:
                    get_hashed_code.delete()

                    pin_created = User.create_merchant_pin(user=request.user, pincode=pin1)
                    if pin_created:
                        response = {
                            "status": "success",
                            "message": "Merchant Pin Created",
                        }
                        return Response(response, status=status.HTTP_201_CREATED)
                    else:
                        response = {
                            "status": "error",
                            "message": "Merchant Pin Could Not Be Created At This Time. Please Try Again",
                        }
                        return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

                else:
                    response = {"error": "84", "message": "Unexpected error occured. Please try again"}
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class RemoveMerchantPin(APIView):
    permission_classes = [CustomIsAuthenticated]
    serializer_class = RemoveMerchantPinSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid():
            toggle = serializer.validated_data["toggle"]

            if toggle == True:
                pin_status = "On"
            else:
                pin_status = "Off"

            if request.user.has_merchant_pin == toggle:
                response = {"error": "47", "message": f"User Merchant Pin is already {pin_status}"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
            else:
                if toggle == False:
                    request.user.merchant_pin = None
                    request.user.has_merchant_pin = False
                    request.user.save()

                    response = {"message": f"You have successfully turned {pin_status} Merchant Pin"}
                    return Response(response, status=status.HTTP_200_OK)
                else:
                    response = {
                        "error": "847",
                        "message": "Cannot Turn On Merchant Pin Without Crating a New One. Please call the create endpoints.",
                    }
                    return Response(response, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ToggleLottoWinAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]
    serializer_class = RemoveMerchantPinSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)

        permitted_users = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]

        if serializer.is_valid():
            toggle = serializer.validated_data["toggle"]

            if toggle == True:
                toggle_status = "On"
            else:
                toggle_status = "Off"

            if request.user.lotto_win_toggle == toggle:
                response = {"error": "47", "message": f"Lotto Win Toggle is already {toggle_status}"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
            else:
                if toggle == True:
                    user_id = request.query_params.get("user_id")
                    try:
                        int_user_id = int(user_id)
                    except:
                        int_user_id = None

                    if int_user_id is None:
                        response = {"error": "848", "message": "Invalid User ID. user_id must be integer"}
                        return Response(response, status=status.HTTP_400_BAD_REQUEST)
                    else:
                        get_user = User.objects.filter(id=int_user_id).last()
                        if not get_user:
                            response = {"error": "849", "message": "User with User ID does not exist"}
                            return Response(response, status=status.HTTP_400_BAD_REQUEST)
                        else:

                            if request.user.email in permitted_users:
                                get_user.lotto_win_toggle = toggle
                                get_user.save()

                                response = {
                                    "message": f"You have successfully turned {toggle_status} Lotto Win Toggle"
                                }
                                return Response(response, status=status.HTTP_200_OK)

                            else:
                                response = {
                                    "error": "847",
                                    "message": "Sorry, you do not have permission to turn this toggle on.",
                                }
                                return Response(response, status=status.HTTP_200_OK)

                else:
                    request.user.lotto_win_toggle = toggle
                    request.user.save()

                    response = {"message": f"You have successfully turned {toggle_status} Lotto Win Toggle"}
                    return Response(response, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LottoCheckAvailabilityAPIView(APIView):

    permission_classes = [
        CustomIsAuthenticated,
        OTPVerified,
        HasTransactionPin,
        LottoDynamicKYCLevelDecider,
        CheckWalletAvailable,
        CheckAccountAvailable,
    ]

    def get(self, request):

        if ConstantTable.get_constant_table_instance().lotto_play_regulator is False:
            response = {"error": "743", "message": "Service Unavailable"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        else:
            response = {"message": "Success! Please proceed"}
            return Response(response, status=status.HTTP_200_OK)


class AddMultipleToWhatsappGroup(APIView):
    permission_classes = [CustomIsAuthenticated, AdminLockPermission]
    serializer_class = AddMultipleToWhatsappGroupSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid():
            group_id = serializer.validated_data["group_id"]
            num_list = serializer.validated_data["num_list"]

            sucess_dict = []
            error_dict = []

            for data in num_list:
                phone = User.format_number_from_back_add_234(data)
                if phone is None:
                    error_det = {"num": phone, "error": "Bad Phone Number"}

                    error_dict.append(error_det)

                else:
                    add_data = add_agents_whatsapp_group_func(phone_number=data)
                    if isinstance(add_data, dict):
                        if add_data.get("status") == 100:

                            success_det = {
                                "num": phone,
                            }

                            sucess_dict.append(success_det)
                        else:

                            error_det = {"num": phone, "error": add_data}
                            error_dict.append(error_det)

                    else:
                        error_det = {"num": phone, "error": add_data}
                        error_dict.append(error_det)

            response = {"sucess_dict": sucess_dict, "error_dict": error_dict}

            return Response(response, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class GetAllPhoneNumbersAPIView(APIView):

    authentication_classes = [CustomTokenAuthentication]
    permission_classes = [CustomIsAuthenticated]
    serializer_class = UserPhoneNumberSerializer

    def get(self, request):

        # all_numbers = User.objects.filter(phone_number__isnull=False, type_of_user__in=["LOTTO_AGENT", "AGENT"])
        all_numbers = User.objects.filter(phone_number__isnull=False)
        if all_numbers:
            serializer = self.serializer_class(all_numbers, many=True)

            response = {
                "status": "success",
                "message": "phone numbers retreived successfully",
                "data": serializer.data,
            }
            return Response(response, status=status.HTTP_200_OK)

        else:
            response = {"status": "error", "message": "no phone numbers found"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)


class LottoGetUserTypeOfUserAPIView(APIView):

    permission_classes = [CustomIsAuthenticated, LottoUserPermission]
    serializer_class = LottoGetUserTypeOfUserSerializer

    def get(self, request):
        user_id = request.query_params.get("user_id")
        if user_id is None:
            response = {"status": "error", "message": "No user id attached"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        try:
            user_instance = User.objects.get(id=user_id)
        except:
            response = {"status": "error", "message": "Invalid user id attached"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        if user_instance:
            response = {
                "status": "success",
                "type_of_user": user_instance.type_of_user,
                "terminal_id": user_instance.terminal_id,
            }
            return Response(response, status=status.HTTP_200_OK)

        else:
            response = {"status": "error", "message": "user with ID does not exist"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)


class SavingsGetUserHashedPass(APIView):
    authentication_classes = [CustomTokenAuthentication]
    permission_classes = [CustomIsAuthenticated, SavingsUserPermission]
    serializer_class = SavingsGetUserPasswordSerializer

    def get(self, request):
        user_id = request.query_params.get("user_id")
        if user_id is None:
            response = {"status": "error", "message": "No user id attached"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        try:
            user_instance = User.objects.get(id=user_id)
        except:
            response = {"status": "error", "message": "Invalid user id attached"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        if user_instance:
            serializer = self.serializer_class(user_instance)
            response = {"status": "success", "data": serializer.data}
            return Response(response, status=status.HTTP_200_OK)

        else:
            response = {"status": "error", "message": "user with ID does not exist"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)


class OtherServiceVerifyPinAPIView(APIView):
    serializer_class = OtherServiceVerifyTransactionPinSerializer
    permission_classes = [CheckDynamicAuthentication]

    def post(self, request):
        custom_permission_user = [*self.permission_classes][0]().has_permission(request, self.as_view())

        request_user = custom_permission_user

        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid():
            user_id = serializer.validated_data["user_id"]
            pincode = serializer.validated_data["transaction_pin"]

            try:
                user_instance = User.objects.get(id=user_id)
            except:
                response = {"status": "error", "message": "Invalid user id attached"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if user_instance:
                check_pin = User.check_sender_transaction_pin(user=user_instance, pincode=pincode)
                if check_pin:
                    response = {"message": "Pin Correct"}
                    return Response(response, status=status.HTTP_200_OK)
                else:
                    response = {"message": "Incorrect Pin"}
                    return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

            else:
                response = {"status": "error", "message": "user with ID does not exist"}
                return Response(response, status=status.HTTP_404_NOT_FOUND)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LottoGetUserSuperAgentDetailsAPIView(APIView):
    permission_classes = [CheckDynamicAuthentication]
    serializer_class = ChildSerializerForLottoSalesRepSuperAgent

    def get(self, request):
        user_id = request.query_params.get("user_id")
        if user_id is None:
            response = {"status": "error", "message": "No user id attached"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        try:
            get_int_id = int(user_id)
        except:
            response = {"error": "545", "message": "must supply integer for user_id"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)

        get_user = User.objects.filter(id=get_int_id).last()
        user_own_accounts = UserOwnAccount.objects.filter(user=get_user)

        if get_user:
            sales_rep_qs = OtherCommissionsRecord.check_if_sales_rep_exists(downline_user=get_user)
            super_agent_qs = SuperAgentProfile.objects.filter(agent=get_user).last()

            sales_rep_serializer = self.serializer_class(sales_rep_qs.sales_rep) if sales_rep_qs else None
            super_agent_serializer = (
                self.serializer_class(super_agent_qs.super_agent)
                if super_agent_qs and super_agent_qs.super_agent
                else None
            )
            supervisor_serializer = (
                self.serializer_class(super_agent_qs.supervisor)
                if super_agent_qs and super_agent_qs.supervisor
                else None
            )
            own_accounts_serializer = (
                UserOwnAccountSerializer(user_own_accounts, many=True) if user_own_accounts else None
            )

            response = {
                "status": "success",
                "data": {
                    "agent": get_user.id,
                    "phone_number": get_user.phone_number,
                    "sales_rep": sales_rep_serializer.data if sales_rep_serializer else None,
                    "super_agent": super_agent_serializer.data if super_agent_serializer else None,
                    "supervisor": supervisor_serializer.data if supervisor_serializer else None,
                    "user_own_accounts": own_accounts_serializer.data if own_accounts_serializer else None,
                },
            }

            return Response(response, status=status.HTTP_200_OK)

        else:
            response = {"error": "521", "message": "user with ID does not exist"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)


class LottoServiceSuspendUserAPIView(APIView):
    permission_classes = [CheckDynamicAuthentication, LottoUserPermission]
    serializer_class = UserOthersSuspendSerializer

    def post(self, request):

        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            user_id = serializer.data["user_id"]
            suspend = serializer.data["suspend"]
            suspend_or_unsuspend_reason = serializer.data["suspend_or_unsuspend_reason"]

            try:
                user = User.objects.get(id=int(user_id))
                if user.type_of_user != "LOTTO_AGENT":
                    response = {"error": "470", "message": "User is not a lotto agent"}
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                else:
                    if suspend is True and (user.lotto_suspended is True or user.is_suspended is True):
                        # Mark lotto_suspended and is_suspended as true
                        user.lotto_suspended = user.is_suspended = True
                        user.save()
                        response = {"error": "478", "message": "User currently suspended. Try again later"}
                        return Response(response, status=status.HTTP_400_BAD_REQUEST)

                    elif suspend is False and (user.lotto_suspended is False or user.is_suspended is False):
                        # Mark lotto_suspended and is_suspended as false
                        user.lotto_suspended = user.is_suspended = False
                        user.save()
                        response = {"error": "471", "message": "User currently not suspended"}
                        return Response(response, status=status.HTTP_400_BAD_REQUEST)

                    else:
                        user.lotto_suspended = user.is_suspended = suspend
                        user.save()

                        UserFlag.objects.create(
                            user=user,
                            suspended_or_unsuspended_by=request.user,
                            alert_message=suspend_or_unsuspend_reason,
                            lotto_suspended=suspend,
                            lotto_unsuspended=not suspend,
                        )

                        response = {"status": "success", "data": f"User suspension status updated to {suspend}"}
                        return Response(response, status=status.HTTP_200_OK)

            except Exception as err:

                response = {"error": "521", "message": f"Error processing request: {err}"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AllDeliveryAddressAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]
    get_serializer_class = DeliveryAddressDataSerializer
    post_put_serializer_class = PostPutDeliveryAddressDataSerializer

    def get(self, request):
        request_user = request.user

        delivery_ad_qs = DeliveryAddressData.objects.filter(user=request_user, is_active=True)

        if not delivery_ad_qs:
            DeliveryAddressData.objects.create(
                user=request_user,
                state=request_user.state,
                lga=request_user.lga,
                nearest_landmark=request_user.nearest_landmark,
                street=request_user.street,
                is_primary=True,
            )
        else:
            get_is_locked = delivery_ad_qs.filter(is_primary=True).last()
            if not get_is_locked:
                DeliveryAddressData.objects.create(
                    user=request_user,
                    state=request_user.state,
                    lga=request_user.lga,
                    nearest_landmark=request_user.nearest_landmark,
                    street=request_user.street,
                    is_primary=True,
                )

                for data in delivery_ad_qs:
                    data.is_primary = False
                    data.save()

            else:
                if (
                    request_user.state == get_is_locked.state
                    and request_user.lga == get_is_locked.lga
                    and request_user.street == get_is_locked.street
                ):
                    pass
                else:
                    get_is_locked.is_active = False
                    get_is_locked.is_primary = False
                    get_is_locked.save()

                    DeliveryAddressData.objects.create(
                        user=request_user,
                        state=request_user.state,
                        lga=request_user.lga,
                        nearest_landmark=request_user.nearest_landmark,
                        street=request_user.street,
                        is_primary=True,
                    )

        delivery_ad_qs = DeliveryAddressData.objects.filter(user=request_user, is_active=True)
        serializer = self.get_serializer_class(delivery_ad_qs, many=True)

        response = {
            "status": "success",
            "message": "delivery addresses retreived successfully",
            "data": serializer.data,
        }
        return Response(response, status=status.HTTP_200_OK)

    def post(self, request):
        request_user = request.user
        serializer = self.post_put_serializer_class(data=request.data)

        if serializer.is_valid():
            state = serializer.validated_data.get("state")
            lga = serializer.validated_data.get("lga")
            nearest_landmark = serializer.validated_data.get("nearest_landmark")
            street = serializer.validated_data.get("street")

            get_addresses = DeliveryAddressData.objects.filter(user=request_user, is_active=True)
            if get_addresses.count() >= 4:

                response = {"error": "546", "message": "You cannot add more than 4 addresses"}
                return Response(response, status=status.HTTP_404_NOT_FOUND)

            if not get_addresses.filter(is_primary=True).exists():
                response = {"error": "546", "message": "please use the get request first"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if get_addresses.filter(state=state, lga=lga, street=street).exists():
                response = {"error": "546", "message": "address already exists"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            DeliveryAddressData.objects.create(
                user=request_user, state=state, lga=lga, nearest_landmark=nearest_landmark, street=street
            )

            response = {"status": "success", "message": "delivery address added successfully"}
            return Response(response, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request):
        request_user = request.user

        address_id = request.query_params.get("address_id")
        serializer = self.post_put_serializer_class(data=request.data)
        if serializer.is_valid():
            if address_id is None:
                response = {"status": "error", "message": "No address_id attached"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            try:
                get_int_id = int(address_id)
            except:
                response = {"error": "545", "message": "must supply integer for address_id"}
                return Response(response, status=status.HTTP_404_NOT_FOUND)

            get_address = DeliveryAddressData.objects.filter(user=request_user, id=get_int_id, is_active=True).last()
            if not get_address:
                response = {"error": "546", "message": "Delivery Address Not Found"}
                return Response(response, status=status.HTTP_404_NOT_FOUND)

            else:
                if get_address.is_primary == True:
                    response = {"error": "546", "message": "Cannot edit primary address"}
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                state = serializer.validated_data.get("state")
                lga = serializer.validated_data.get("lga")
                nearest_landmark = serializer.validated_data.get("nearest_landmark")
                street = serializer.validated_data.get("street")

                get_address.state = state
                get_address.lga = lga
                get_address.nearest_landmark = nearest_landmark
                get_address.street = street
                get_address.save()

                response = {
                    "status": "success",
                    "message": "delivery address edited",
                    "data": self.get_serializer_class(get_address).data,
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request):
        request_user = request.user

        address_id = request.query_params.get("address_id")

        if address_id is None:
            response = {"status": "error", "message": "No address id attached"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        try:
            get_int_id = int(address_id)
        except:
            response = {"error": "545", "message": "must supply integer for address_id"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)

        get_address = DeliveryAddressData.objects.filter(user=request_user, id=get_int_id, is_active=True).last()
        if not get_address:
            response = {"error": "546", "message": "Delivery Address Not Found"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)

        else:
            if get_address.is_primary == True:
                response = {"error": "546", "message": "Cannot delete primary address"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            get_address.is_active = False
            get_address.save()

            response = {"status": "success", "message": "delivery address successfully deleted"}
            return Response(response, status=status.HTTP_204_NO_CONTENT)


class OnboardCorporateDetailAPIView(APIView):
    permission_classes = [CheckDynamicAuthentication]
    serializer_class = OnboardCorporateAccountSerializer

    def post(self, request):
        custom_permission_user = [*self.permission_classes][0]().has_permission(request, self.as_view())

        request_user = custom_permission_user

        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():

            user_email = serializer.validated_data["user_email"]
            entity_type = serializer.validated_data["entity_type"]
            rc_number = serializer.validated_data["rc_number"]
            company_name = serializer.validated_data.get("company_name")
            incorp_date = serializer.validated_data.get("incorp_date")

            get_bvn = BVNDetail.objects.filter(
                kyc__user__email=user_email, bvn_number__isnull=False, is_verified=True
            ).first()

            if not get_bvn:
                response = {
                    "error": "652",
                    "message": "User BVN Not Verified",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            user = get_bvn.kyc.user
            bvn = get_bvn.bvn_number

            date_string = incorp_date.strftime("%d %B %Y")

            # check_rc_start = starts_with_prefix(string=rc_number)

            if entity_type not in ["RC", "BN", "IT", "LP", "LPP"]:
                response = {"error": "653", "message": "Entity Type must be RC,BN,IT,LP or LPP"}
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            create_corporate = CorporateAccount.objects.create(
                onboarded_by=request_user,
                user=user,
                entity_type=entity_type,
                rc_number=rc_number,
                company_name=company_name,
                raw_incorp_date=incorp_date,
                incorp_date=date_string,
                bvn=bvn,
            )

            response = {
                "status": "success",
                "message": "Corporate Account Created",
                "data": {"corporate_id": create_corporate.corporate_id},
            }
            return Response(response, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CreateOtherAccountAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, HasKYC]
    serializer_class = ChildCreateOtherAccountSerializer

    def post(self, request):

        admin_password = self.request.query_params.get("admin_password")
        user_by_admin = self.request.query_params.get("user_by_admin")
        print(user_by_admin)

        if admin_password and not user_by_admin or user_by_admin and not admin_password:
            request_user = self.request.user

        if admin_password and user_by_admin:
            try:
                check_user_as_admin = User.objects.get(email=user_by_admin)
            except User.DoesNotExist:
                response = {
                    "error": "515",
                    "message": "User by Admin Does not Exist",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if admin_password == f"{settings.EMEKA_ADMIN_PASSWORD}" or User.check_if_user_login_pin_match(
                request.user, admin_password
            ):
                request_user = check_user_as_admin
            else:
                response = {
                    "error": "515",
                    "message": "Invalid Password",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

        else:

            request_user = self.request.user

        user = request_user

        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            phone_number = (
                User.format_number_from_back_add_234(serializer.validated_data.get("phone_number"))
                if serializer.validated_data.get("phone_number")
                else None
            )

            account_type = serializer.validated_data["account_type"]
            corporate_id = serializer.validated_data.get("corporate_id")
            suffix_or_location_id = serializer.validated_data.get("suffix_or_location_id")
            password = serializer.validated_data.get("password")
            company_name = serializer.validated_data.get("company_name")
            num_of_accounts = serializer.validated_data.get("num_of_accounts")
            user_for_creation = serializer.validated_data.get("user_for_creation")

            if account_type in ["CORPORATE", "CORPORATE_LIBERTY_RETAIL", "SEEDS_CORPORATE"]:
                if corporate_id is None:
                    response = {
                        "error": "545",
                        "message": "Must Pass Corporate ID if account type is corporate",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                get_corporate = CorporateAccount.objects.filter(
                    Q(user=user, corporate_id=corporate_id) | Q(other_users=user)
                ).last()
                if not get_corporate:
                    response = {
                        "error": "117",
                        "message": "Incorrect Corporate ID",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

            get_location = None

            if account_type in ["CORPORATE_LIBERTY_RETAIL", "SEEDS_CORPORATE"]:
                if suffix_or_location_id is None:
                    response = {
                        "error": "549",
                        "message": "Must Pass suffix, eg. location of agent if account type is CORPORATE_LIBERTY_RETAIL",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                try:
                    get_location_id = int(suffix_or_location_id)
                except:
                    response = {
                        "error": "550",
                        "message": "Suffix for CORPORATE_LIBERTY_RETAIL must be a location ID and must be Integer",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                get_location = NewLocationList.objects.filter(id=get_location_id).last()
                if not get_location:
                    response = {
                        "error": "551",
                        "message": "Location Does Not Exists",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

            start_num = (
                "2351"
                if user.type_of_user == "AJO_AGENT"
                else "2341" if user.type_of_user == "LIBERTY_RETAIL" else "2342"
            )

            total_acct_num = int(num_of_accounts) if num_of_accounts else 1
            created_accounts_list = []

            for acct_num in range(total_acct_num):
                user_to_use = user_for_creation if user_for_creation else user

                create_other_account, new_user_bvn_rel, new_user = UserOtherAccount.create_other_account(
                    user=user_to_use, phone_number=phone_number, password=password, start_num=start_num
                )

                if create_other_account["status"] == False:
                    response = {
                        "error": "848",
                        "message": f"{create_other_account['message']}",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                else:
                    bvn_rel = new_user_bvn_rel

                    if bvn_rel.is_verified == True:
                        if account_type in ["CORPORATE", "CORPORATE_LIBERTY_RETAIL", "SEEDS_CORPORATE"]:
                            if account_type == "CORPORATE_LIBERTY_RETAIL":
                                create_retail_agent = RetailSystem.create_retail_system_account(
                                    retail_account=new_user, location=get_location
                                )

                                create_corporate = OtherServiceAccountSystem.arrange_corporate_detail(
                                    user=user,
                                    new_user=new_user,
                                    corporate_id=corporate_id,
                                    suffix=suffix_or_location_id,
                                    get_location=get_location,
                                )

                                create_corporate_two = OtherServiceAccountSystem.arrange_corporate_detail(
                                    user=user,
                                    new_user=new_user,
                                    corporate_id=corporate_id,
                                    suffix="COLLECTION",
                                    get_location=get_location,
                                )

                            elif account_type == "SEEDS_CORPORATE":
                                create_retail_agent = RetailSystem.create_retail_system_account(
                                    retail_account=new_user, account_type=account_type, location=get_location
                                )

                                create_corporate = OtherServiceAccountSystem.arrange_corporate_detail(
                                    user=user,
                                    new_user=new_user,
                                    corporate_id=corporate_id,
                                    suffix=suffix_or_location_id,
                                    get_location=get_location,
                                    company_name_account_type=account_type,
                                )

                            else:
                                create_corporate = OtherServiceAccountSystem.arrange_corporate_detail(
                                    user=user,
                                    new_user=new_user,
                                    corporate_id=corporate_id,
                                    suffix=suffix_or_location_id,
                                    get_location=get_location,
                                    company_name=company_name,
                                )

                        else:
                            create_wallets = create_wallets_and_accounts_task(bvn_instance_id=bvn_rel.id)

                created_accounts_list.append({"message": "Account successfully created", "data": create_other_account})

            response = created_accounts_list
            return Response(response, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# class VerifyEmailGetSecurityQuestionsAPIView(APIView):

#     def get(self, request):
#         get_email = request.query_params.get("email")

#         try:
#             user = User.objects.get(email=get_email)
#             response = {
#                 "message": "User Exists,
#                 "data": {
#                     "email": emai
#                 }
#             }
#             return Response(response, status=status.HTTP_200_OK)


class RegisterDeviceAPIView(APIView):
    serializer_class = RegisterDeviceSerializer

    def post(self, request):

        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data["email"]
            transaction_pin = serializer.validated_data["transaction_pin"]
            device_token = serializer.validated_data["device_token"]
            security_answer_one = serializer.validated_data.get("security_answer_one")
            security_answer_two = serializer.validated_data.get("security_answer_one")
            device_registration_code = serializer.validated_data.get("device_registration_code")

            if not transaction_pin.isnumeric():
                response = {"error": "14", "message": "You must supply an integer for transaction pin"}

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)

            try:
                request_user = User.objects.get(email=email)
            except:
                response = {"error": "187", "message": "Invalid Email"}

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)

            chcek_pin = User.check_sender_transaction_pin(user=request_user, pincode=transaction_pin)
            if chcek_pin == False:
                retries = User.count_down_transaction_pin_retries(request_user)

                response = {
                    "error": "error",
                    "message": "Incorrect Pin",
                    "retry_count": retries["retry_count"],
                    "remaining_retries": retries["remaining_retries"],
                }

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)

            else:
                User.reset_transaction_pin_retries(request_user)


class GetAndAnswerSecurityQuestionsAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin]
    serializer_classes = AnswerSecurityQuestionSerializer

    def get(self, request):
        user = request.user

        response = [
            {
                "security_question_number": 1,
                "security_question_question": user.first_security_question,
            },
            {
                "security_question_number": 2,
                "security_question_question": user.second_security_question,
            },
        ]

        return Response(response, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = self.serializer_classes(data=request.data)
        if serializer.is_valid():
            security_question_number = serializer.validated_data["security_question_number"]
            security_answer = serializer.validated_data["security_answer"]
            check_type = serializer.validated_data["check_type"]
            send_otp = serializer.validated_data["send_otp"]

            # Get User
            user = request.user

            if security_question_number not in [1, 2]:
                response = {"error": "657", "message": "invalid security question number"}

                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            check_answer = check_security_questions(
                user=user, number=security_question_number, security_answer=security_answer
            )
            if check_answer == True:

                User.reset_security_question_retries(user)

                generate_token = create_temp_trans_access_token(str(user.customer_id), 0, added_time=300)

                response = {"status": "success", "message": "security question answered", "token": generate_token}

                if send_otp in ["EMAIL", "ALL"]:
                    passcode = str(passcode_generator())
                    UserTempToken.objects.create(
                        user=user, pin_type=check_type, token=passcode, exp=datetime.now() + timedelta(minutes=5)
                    )

                    send_email(email=user.email, passcode=passcode)

                    if send_otp == "EMAIL":
                        response["other_message"] = f"An OTP has been sent to your email: {user.email}"

                    elif send_otp == "ALL":
                        response["other_message"] = (
                            f"An OTP has been sent to your email: {user.email} and phone number {user.phone_number}"
                        )

                    if settings.ENVIRONMENT == "development":
                        response["test_otp"] = passcode

                return Response(response, status=status.HTTP_200_OK)

            else:
                retries = User.count_down_sec_ques_pin_retries(user)

                response = {
                    "error": "545",
                    "message": "Incorrect Security Answer",
                    "retry_count": retries["retry_count"],
                    "remaining_retries": retries["remaining_retries"],
                }

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CreateSuperTokenAPIView(APIView):
    permission_classes = [CheckDynamicAuthentication]

    def get(self, request):
        custom_permission_user = [*self.permission_classes][0]().has_permission(request, self.as_view())

        request_user = custom_permission_user

        # Check Service User
        service_user_check = OtherServiceDetail.objects.filter(user=request_user, can_gen_super_token=True).last()
        if not service_user_check:
            response = {
                "error": "890",
                "message": "Incorrect Service User on Agency Banking List or Cannot Generate Super Token",
            }

            return Response(response, status=status.HTTP_401_UNAUTHORIZED)

        # Get IP ADDRESS
        address = request.META.get("HTTP_X_FORWARDED_FOR")
        if address:
            ip_addr = address.split(",")[-1].strip()
        else:
            ip_addr = request.META.get("REMOTE_ADDR")

        if ip_addr not in service_user_check.ip_addresses:
            response = {"error": "891", "message": "IP not whitelisted"}

            return Response(response, status=status.HTTP_401_UNAUTHORIZED)

        create_super_token = generate_super_token(service_name=service_user_check.service_name)

        response = {"status": "success", "super_token": str(create_super_token)}

        return Response(response, status=status.HTTP_200_OK)


class VerifyPassword(APIView):
    permission_classes = [CustomIsAuthenticated]
    serializer_class = MerchantPinVerifyOTPSerializer

    def post(self, request):

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        password = serializer.validated_data.get("otp_value")

        user = authenticate(username=request.user.email.lower(), password=password)
        if user is not None:
            return Response({"valid": True}, status=status.HTTP_200_OK)

        else:
            return Response({"valid": False}, status=status.HTTP_400_BAD_REQUEST)


class CreateDisplayBannerView(generics.CreateAPIView):
    permission_classes = [CustomIsAuthenticated]
    serializer_class = DisplayBannerSerializer


class DisplayBannerListView(APIView):
    permission_classes = [CustomIsAuthenticated]
    serializer_class = DisplayBannerSerializer

    def get(self, request):
        user = request.user
        display_banners_qs = DisplayBanner.objects.filter(start_date__lte=timezone.now(), end_date__gte=timezone.now())
        banners_to_be_viewed_ids = []  # A list of banners a user has not seen and should see

        for banner in display_banners_qs:
            banner_viewers = banner.seen_by if banner.seen_by else []

            banner_viewers_list = [(view.get("user"), view.get("view_count")) for view in banner_viewers]
            banner_users = [view[0] for view in banner_viewers_list]  # A list of users that have seen this banner

            if str(user) in banner_users:
                user_index = banner_users.index(str(user))
                view_count = banner_viewers_list[user_index][1]

                if int(view_count) < banner.display_count:
                    banners_to_be_viewed_ids.append(banner.id)
                else:
                    pass

            else:
                banners_to_be_viewed_ids.append(banner.id)

        banners_to_show = display_banners_qs.filter(id__in=banners_to_be_viewed_ids)
        view_serializer = self.serializer_class(banners_to_show, many=True, context={"request": request})

        return Response({"data": view_serializer.data, "count": len(view_serializer.data)}, status=status.HTTP_200_OK)


class ViewedDisplayBannersView(APIView):
    permission_classes = [CustomIsAuthenticated]
    serializer_class = DisplayBannerSerializer

    def get(self, request, banner_id):
        # banner_id = request.data.get("banner_id")
        user = request.user

        try:
            banner = DisplayBanner.objects.get(id=banner_id)
            banner_viewers = banner.seen_by if banner.seen_by else []

            banner_viewers_list = [(view.get("user"), view.get("view_count")) for view in banner_viewers]
            banner_users = [view[0] for view in banner_viewers_list]  # A list of users that have seen this banner

            if str(user) in banner_users:
                user_index = banner_users.index(str(user))
                view_count = banner_viewers_list[user_index][1]

                if int(view_count) < banner.display_count:
                    view_data = {"user": str(user), "view_count": int(view_count) + 1}
                    banner_viewers_list[user_index] = (view_data.get("user"), view_data.get("view_count"))
                else:
                    pass

            else:
                view_data = {"user": str(user), "view_count": 1}
                banner_viewers_list.append((view_data["user"], view_data["view_count"]))

            banner.seen_by = [{"user": str(data[0]), "view_count": data[1]} for data in banner_viewers_list]
            banner.save()

            serializer = self.serializer_class(banner, context={"request": request})
            return Response({"data": serializer.data, "message": "Banner viewed"}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class UserDataDeletionRequestView(APIView):
    # permission_classes = [CustomIsAuthenticated]
    serializer_class = UserDataDeletionRequestSerializer

    def get(self, request):
        user_email = request.query_params.get("user_email")
        data = {"user_email": user_email}
        serializer = self.serializer_class(data=data)
        if serializer.is_valid():
            serializer.save()
            return Response({"message": "Your account is being deleted"}, status=status.HTTP_200_OK)
        else:
            return Response({"error": "Enter a valid email address"}, status=status.HTTP_400_BAD_REQUEST)


class GetUSSDOTPAPIView(APIView):
    # authentication_classes = [CustomTokenAuthentication]
    # permission_classes = [CustomIsAuthenticated, CheckIPAddresses(service_name="USSD_BACKEND")]

    def get(self, request):
        phone = request.query_params.get("phone")
        if phone is None:
            response = {"error": "549", "message": "No phone attached"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)

        get_otp = generate_ussd_otp(phone_number=phone)

        response = {"status": "success", "message": "otp generated successfully", "otp": get_otp}

        return Response(response, status=status.HTTP_200_OK)


class SendAjoFormAPIView(APIView):
    # permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin]
    serializer_classes = AjoAgentFormSerializer

    def post(self, request):
        serializer = self.serializer_classes(data=request.data)
        serializer.is_valid(raise_exception=True)

        phone_number = serializer.validated_data.get("phone_number")
        bvn = serializer.validated_data.get("bvn")
        nin = serializer.validated_data.get("nin")

        if AjoAgentForm.objects.filter(phone_number=phone_number).exists():
            response = {"status": "error", "message": "phone number exists"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        # if not bvn and not nin:
        #     response = {
        #         "status": "error",
        #         "message": "no bvn or nin attached"
        #     }
        #     return Response(response, status=status.HTTP_400_BAD_REQUEST)

        # verify_otp = verify_ussd_otp(otp=serializer.validated_data.get("otp"), phone_number=phone_number)
        # if not verify_otp:
        #     response = {
        #         "status": "error",
        #         "message": "invalid otp"
        #     }
        #     return Response(response, status=status.HTTP_400_BAD_REQUEST)

        # if nin:
        #     verify_id = verify_nin_with_uverify(nin_number=nin)
        #     id_key = "NIN"
        # else:
        #     verify_id = verify_bvn_with_uverify(bvn_number=bvn)
        #     id_key = "BVN"

        # if verify_id["success"] == False:
        #     response = {
        #         "status": "error",
        #         "message": f"could not verify {id_key}"
        #     }
        #     return Response(response, status=status.HTTP_400_BAD_REQUEST)

        if serializer.validated_data.get("otp"):
            del serializer.validated_data["otp"]
        # serializer.save(id_payload=verify_id)
        serializer.save()
        response = {
            "status": "success",
            "message": "form submitted successfully",
        }

        return Response(response, status=status.HTTP_200_OK)


class UpdateUserProfilePictureAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]
    parser_classes = (MultiPartParser, FormParser)

    def post(self, request, format=None):
        serializer = UserProfilePictureSerializer(data=request.data)
        user: User = self.request.user

        if serializer.is_valid():
            user.profile_picture = serializer.validated_data["profile_picture"]
            user.save()

            # base_url = request.build_absolute_uri('/')

            response = {
                "status": True,
                "message": "profile picture updated successfully",
                "profile_picture": user.profile_picture.url,
                # "profile_picture": urljoin(base_url, user.profile_picture.url)
            }

            return Response(response, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class GetUserSupervisorsandBranchAPIView(APIView):
    authentication_classes = [CustomTokenAuthentication]
    permission_classes = [CustomIsAuthenticated, SavingsUserPermission]
    serializer_class = SavingsGetUserPasswordSerializer

    def get(self, request):
        user_id = request.query_params.get("user_id")
        if user_id is None:
            response = {"status": "error", "message": "No user id attached"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(id=user_id)
        except:
            response = {"status": "error", "message": "Invalid user id attached"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        serializer = ChildSerializerForLottoSalesRepSuperAgent

        try:
            user_team = UserTeam.objects.get(users=user)
            user_team.save()

            team_lead = user_team.team.supervisor
        except UserTeam.DoesNotExist:
            user_team = None
            team_lead = None

        if user:
            max_attempts = 5  # Set the maximum number of attempts
            attempts = 0  # Initialize the attempts counter

            """The below is because when branch teams are added by importing a sheet into the admin, they are supposed to create user teams which are to update the user_branch field of the users in turn, but most times, this doesn't happen, so I do a .save() above when getting the user_team. However, while saving a M2M field, django's ORM buffers the changes and executes them in a separate database operation. So, I do a while loop until the changes are persisted to the db or it runs out of attempts.

            For the refresh from dbs you see, I was just using all my powers, abido-shaker and rest...lol"""

            while (
                (not user.user_branch or (user_team and user.user_branch != user_team.team.branch))
                and user_team
                and attempts < max_attempts
            ):
                user_team.refresh_from_db()
                user.refresh_from_db()
                attempts += 1

            user.user_branch
            # branch = getattr(user.user_branch, 'location', None)
            branch = user.user_branch.location if user.user_branch else None
            if branch and user.user_branch.supervisor:
                head_supervisor = user.user_branch.supervisor
            else:
                head_supervisor = None

            data = {
                "branch": branch,
                "head_supervisor": serializer(head_supervisor).data if head_supervisor else None,
                "team_lead": serializer(team_lead).data if team_lead else None,
            }

            response = {"status": "success", "data": data}
            return Response(response, status=status.HTTP_200_OK)

        elif not user.user_branch:
            response = {"status": "error", "message": "user branch does not exist"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)

        else:
            response = {"status": "error", "message": "user with ID does not exist"}
            return Response(response, status=status.HTTP_404_NOT_FOUND)


class GetSavingsUserDetailsView(APIView):
    """
    View to return the login_count, branch and
    supervisor details for an agency banking user
    in savings.
    """

    permission_classes = [CheckDynamicAuthentication]

    def get(self, request):
        user_id = request.query_params.get("user_id")
        start_date = request.query_params.get("start_date")

        if user_id is None:
            data = {"error": True, "message": "User does not exist"}
            return Response(data, status.HTTP_400_BAD_REQUEST)

        user_instance = User.objects.get(id=user_id)
        liberty_user = None

        if "@libertyng.com" in user_instance.email:
            retail_system = RetailSystem.objects.filter(retail_account=user_instance).last()

            if retail_system:
                liberty_user = retail_system.user_assigned

        login_qs = BlackListedJWT.objects.filter(user_id=user_instance.id)

        try:
            if liberty_user:
                user_bvn_details = liberty_user.check_kyc.bvn_rel
            else:
                user_bvn_details = user_instance.check_kyc.bvn_rel

            verification_payload = ast.literal_eval(user_bvn_details.payload)

            if user_bvn_details.verified_by in ["youverify", "hadada", "WHISPER_OTP", "dojah"]:
                image_base64_string_photo = verification_payload.get("data", {}).get("data", {}).get("photo")
                image_base64_string_image = verification_payload.get("data", {}).get("data", {}).get("image")
                image_base64_string_photo_2 = verification_payload.get("data", {}).get("photo")
                image_base64_string_image_2 = verification_payload.get("data", {}).get("entity", {}).get("image")

                if image_base64_string_photo:
                    image_base64_string = image_base64_string_photo
                elif image_base64_string_photo_2:
                    image_base64_string = image_base64_string_photo_2
                elif image_base64_string_image_2:
                    image_base64_string = image_base64_string_image_2
                else:
                    image_base64_string = image_base64_string_image
            else:
                image_base64_string = ""

        except BVNDetail.DoesNotExist:
            image_base64_string = ""

        user_team = UserTeam.objects.filter(users=user_instance).last()
        if user_team:
            supervisor = (
                user_team.team.supervisor.get_full_name() if user_team.team and user_team.team.supervisor else ""
            )
            branch = (
                user_team.team.branch.location
                if user_team.team and user_team.team.branch and user_team.team.branch.location
                else ""
            )
            supervisor_email = user_team.team.supervisor.email if user_team.team and user_team.team.supervisor else ""
        else:
            supervisor = ""
            branch = ""
            supervisor_email = ""

        if start_date:
            date_format = datetime.strptime(start_date, "%Y-%m-%d")
            login_count = login_qs.filter(date_created__gte=date_format).count()
        else:
            login_count = login_qs.count()

        data = {
            "success": True,
            "login_count": login_count,
            "supervisor": supervisor,
            "branch": branch,
            "user_bvn_image_base64_string": image_base64_string,
            "supervisor_email": supervisor_email,
            "user_email": user_instance.email,
        }
        return Response(data, status.HTTP_200_OK)


class GetSavingSupervisorDetails(APIView):
    permission_classes = [CheckDynamicAuthentication]

    def get(self, request):
        supervisors_list = []
        user_teams = UserTeam.objects.all()

        for team in user_teams:
            team: UserTeam
            if team.team and team.team.supervisor:
                data = {
                    "supervisor": team.team.supervisor.get_full_name(),
                    "supervisor_email": team.team.supervisor.email,
                    "supervisor_user_id": team.team.supervisor.id,
                    "users_list": [user.id for user in team.users.all()],
                    "branch": (
                        team.team.branch.location
                        if team.team and team.team.branch and team.team.branch.location
                        else ""
                    ),
                }
                supervisors_list.append(data)
            else:
                pass

        data = {
            "success": True,
            "supervisors_teams_list": supervisors_list,
        }
        return Response(data, status.HTTP_200_OK)


class SetupPingDevicesAPIView(APIView):
    def post(self, request):
        serializer = PingDeviceSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        email = serializer.validated_data.get("email")
        title = serializer.validated_data.get("title")
        body = serializer.validated_data.get("body")
        try:
            user = User.objects.get(email=email)
            token = user.notify_app_token
        except User.DoesNotExist:
            response = {
                "status": "error",
                "message": "user does not exist",
            }
            return Response(status=status.HTTP_404_NOT_FOUND)
        if not token:
            response = {
                "status": "error",
                "message": "user does not have token",
            }
        response = notify_messaging.send_broadcast(
            token=token, title=title, body=body, data={"status": "success", "message": "device pinged successfully"}
        )
        return Response(response, status=status.HTTP_200_OK)


class CheckUserEmailAPIView(APIView):
    def get(self, request):
        email = request.query_params.get("email")

        email_pattern = re.compile(r"^[\w\.-]+@[\w\.-]+\.\w+$")

        if not email or not email_pattern.match(email):
            response = {
                "success": False,
                "message": "invalid email address",
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        email = email.lower()
        try:
            user_instance = User.objects.get(email=email)
        except User.DoesNotExist:
            response = {
                "success": True,
                "message": "next step",
            }
            return Response(response, status=status.HTTP_200_OK)

        response = {
            "success": True,
            "message": "user already exist",
        }
        return Response(response, status=status.HTTP_201_CREATED)
    
class CheckUserPhoneAPIView(APIView):
    def get(self, request):
        phone_number = request.query_params.get("phone_number")
        
        phone_number = User.format_number_from_back_add_234(phone_number)
        if phone_number is None:
            return Response(
                {
                    "success": False,
                    "message":"invalid phone number"
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            user_instance = User.objects.get(phone_number=phone_number)
        except User.DoesNotExist:
            response = {
                "success": True,
                "message": "next step",
            }
            return Response(response, status=status.HTTP_200_OK)

        response = {
            "success": True,
            "message": "user already exist",
        }
        return Response(response, status=status.HTTP_201_CREATED)

class FetchUserByEmailAPIView(APIView):
    def get(self, request):
        email = request.query_params.get("email")
        email_pattern = re.compile(r"^[\w\.-]+@[\w\.-]+\.\w+$")

        if not email or not email_pattern.match(email):
            response = {
                "success": False,
                "message": "invalid email address",
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        email = email.lower()
        try:
            user_instance = User.objects.get(email=email)
        except User.DoesNotExist:
            response = {
                "success": False,
                "message": "user not found",
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        response = {
            "success": True,
            "message": "user found",
            "user": {
                "email": email,
                "first_name": user_instance.first_name,
                "last_name": user_instance.last_name,
                "phone_number": user_instance.phone_number,
                "username": user_instance.username,
                "state": user_instance.state,
                "lga": user_instance.lga,
                "nearest_landmark": user_instance.nearest_landmark,
                "street": user_instance.street,
                "gender": user_instance.gender,
            },
        }
        return Response(response, status=status.HTTP_200_OK)
    
class FetchUserByPhoneAPIView(APIView):
    def get(self, request):
        phone_number = request.query_params.get("phone_number")

        phone_number = User.format_number_from_back_add_234(phone_number)
        if phone_number is None:
            return Response(
                {
                    "success": False,
                    "message":"invalid phone number"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            user_instance = User.objects.get(phone_number=phone_number)
        except User.DoesNotExist:
            response = {
                "success": False,
                "message": "user not found",
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        response = {
            "success": True,
            "message": "user found",
            "user": {
                "email": user_instance.email,
                "first_name": user_instance.first_name,
                "last_name": user_instance.last_name,
                "phone_number": phone_number,
                "username": user_instance.username,
                "state": user_instance.state,
                "lga": user_instance.lga,
                "nearest_landmark": user_instance.nearest_landmark,
                "street": user_instance.street,
                "gender": user_instance.gender,
            },
        }
        return Response(response, status=status.HTTP_200_OK)
    
class GetOnboardingStatusAPIView(APIView):
    def post(self, request):
        serializer = OnboardingStatusEmailSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        email = serializer.validated_data.get("email")
        phone_number = serializer.validated_data.get("phone_number")
        if email:
            email = email.lower()
        try:
            user_instance = User.objects.get(Q(email=email) | Q(phone_number=phone_number))
        except User.DoesNotExist:
            response = {
                "success": False,
                "message": "user not found",
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
   
        completed_status = []
        if user_instance.registration_email_verified:
            completed_status.append("EMAIL_VERIFICATION")
        if user_instance.has_login_pin:
            completed_status.append("CREATE_PASSCODE")
        try:
            if user_instance.check_kyc.bvn_rel.is_verified:
                completed_status.append("BVN_VERIFICATION")
        except Exception as e:
            pass
        if user_instance.first_security_answer and user_instance.second_security_answer:
            completed_status.append("SECURITY_QUESTION")


        response = {
            "success": True,
            "onboaring_type": "PHONE_NUMBER" if user_instance.fake_email is True else "EMAIL",
            "message": "user found",
            "email": user_instance.email,
            "phone_number": user_instance.phone_number,
            "onboarding_status": user_instance.onboarding_status,
            "completed_status": completed_status
        }
        return Response(response, status=status.HTTP_200_OK)
    
class UpdatePreferenceAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]

    def get(self, request):
        user = request.user
        serializer = UpdatePreferenceSerializer(user)
        response = {
            "success": True,
            "data": serializer.data,
        }
        return Response(response, status=status.HTTP_200_OK)

    def post(self, request):
        user = request.user
        serializer = UpdatePreferenceSerializer(
                user, data=request.data, partial=True
            )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        response = {
            "success": True,
            "message": "preference updated successfully",
        }
        return Response(response, status=status.HTTP_200_OK)
    
class UpdatePersonalInfoAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]

    def get(self, request):
        user = request.user
        serializer = UpdatePersonalInfoSerializer(user)
        response = {
            "success": True,
            "data": serializer.data,
        }
        return Response(response, status=status.HTTP_200_OK)

    def post(self, request):
        user = request.user
        serializer = UpdatePersonalInfoSerializer(
                user, data=request.data, partial=True
            )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        response = {
            "success": True,
            "message": "personal infomation updated successfully",
        }
        return Response(response, status=status.HTTP_200_OK)

class UpdateUserPasswordAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]

    def post(self, request):
        user: User = request.user
        serializer = UpdateUserPasswordSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        old_passcode = serializer.validated_data.get("old_passcode")
        passcode1 = serializer.validated_data.get("passcode1")
        passcode2 = serializer.validated_data.get("passcode2")
        check_if_user_created_pin = User.check_user_login_pin(user)

        if not check_if_user_created_pin:
            response = {
                "status": "error",
                "message": "Login Pin has not been created",
            }
            return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)
    
        verified_login = User.check_user_passcode_pin(user=user, passcode=old_passcode)
        if not verified_login:
            response = {
                "status": "error",
                "message": "user old password is incorrect",
            }
            return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)
        if not user.registration_email_verified:
            response = {
                "status": "error",
                "message": "user email has not been verified",
            }
            return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)
        user.has_login_pin = True
        user.set_password(passcode1)
        user.save()

        response = {"message": "Login Pin successfully updated"}
        return Response(response, status=status.HTTP_201_CREATED)


class RemapRegionBranchAgentAPIView(APIView):
    permission_classes = []

    def post(self, request):
        serializer = RemapLoanAgentToLocationToRegionIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class OTPCheckAPIView(APIView):
    """
    API endpoint to check if a phone number belongs to a valid user
    and generate an OTP if it does.
    """
    def post(self, request):
        try:
            # Get phone number from request
            phone_number = request.data.get('phone_number')
            
            if not phone_number:
                return Response({
                    'status': 'error',
                    'message': 'Phone number is required',
                    'has_bvn': False
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Format the phone number
            if phone_number.startswith('+'):
                phone_number = phone_number[1:]
            
            if not phone_number.startswith('234') and len(phone_number) == 11:
                phone_number = '234' + phone_number[1:]
            
            # Check if user exists with this phone number
            user = User.objects.filter(phone_number=phone_number).first()
            
            if user:
                # Generate 6-digit OTP
                otp = ''.join(secrets.choice('0123456789') for i in range(6))
                
                # Save OTP to database - using app_name 'LIBERTY_PAY_USSD'
                otp_record = Users_Otp.objects.create(
                    otp_id=f"bvn_ussd_{secrets.token_hex(8)}",
                    otp_type="USSD",
                    route="SMS",
                    phone_number=phone_number,
                    app_name="LIBERTY_PAY_USSD",
                    receiver=phone_number,
                    otp_sent_data=otp,
                    hotp_count=0,
                    notified="YES",
                    utilized="NO",
                    template_id="LIBERTY_PAY_USSD_OTP",
                    application_id="LIBERTY_PAY_USSD",
                    isOtpdelivered=True,
                    isOtpVerified=False,
                    otp=otp
                )
                
                # Return success response with the OTP
                return Response({
                    'status': 'success',
                    'message': 'User found and OTP generated',
                    'otp': otp,
                    'has_bvn': True,
                    'user_name': f"{user.first_name} {user.last_name}"
                }, status=status.HTTP_200_OK)
            else:
                message = 'No user found with this phone number'
                return Response({
                    'status': 'success',
                    'message': message,
                }, status=status.HTTP_200_OK)
                
        except Exception as e:
            logger.error(f"Error in BVN OTP Check: {str(e)}")
            return Response({
                'status': 'error',
                'message': 'An error occurred while processing your request',
                'has_bvn': False
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UpdateUserProfileAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin]

    def post(self, request):
        serializer = ProfileUpdateSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class GetMerchantAcquisitionOfficersAPIView(APIView, CustomPagination):
    permission_classes = [CheckDynamicAuthentication]

    def get(self, request):
        agent_id = request.GET.get("agent_id")
        if agent_id:
            try:
                serializer = MerchantAcquisitionOfficerSerializerOut(MerchantAcquisitionOfficer.objects.get(user_id=agent_id)).data
                return Response({"message": "Acquisition Officer retrieved", "data": serializer})
            except MerchantAcquisitionOfficer.DoesNotExist:
                return Response({"message": "Acquisition Officer not found", "data": {}}, status=status.HTTP_404_NOT_FOUND)

        queryset = self.paginate_queryset(MerchantAcquisitionOfficer.objects.all(), request)
        serializer = MerchantAcquisitionOfficerSerializerOut(queryset, many=True).data
        response = self.get_paginated_response(serializer).data
        return Response({"message": "Data retrieved", "data": response})


class ApplicationBannerListAPIView(generics.ListAPIView):
    permission_classes = [CustomIsAuthenticated]
    queryset = ApplicationBanner.objects.all().order_by("-date_created")
    serializer_class = ApplicationBannerSerializerOut


