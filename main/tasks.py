from django_celery_results.models import TaskResult
from django.utils import timezone
from celery import shared_task
from main.helper.send_emails import send_email
from main.models import User, Users_Otp, ConstantTable
from main.helper.helper_function import freshworks_register_contact, add_agents_whatsapp_group_func
from accounts.models import Transaction
from kyc_app.models import KYCTable, BVNDetail, DocumentFaceMatchKYC2Detail
from singlelogin.models import BlackListedJWT




@shared_task
def confirm_passcode_mail(email, passcode):
    send_email(email, passcode)
    return f"confirm email passcode sent to {email}"



@shared_task
def create_kyc_object_on_user_create_task(user_id):
    user = User.objects.filter(id=user_id).first()
    user_kyc, created = KYCTable.objects.get_or_create(user=user)
    bvn_rel, created = BVNDetail.objects.get_or_create(kyc=user_kyc)
    docs_rel, created = DocumentFaceMatchKYC2Detail.objects.get_or_create(kyc=user_kyc)

    # kyc_instance = KYCTable.objects.create(user=user)
    # .objects.create(kyc=kyc)
    # .objects.create(kyc=kyc)
    return f"KYC Object has been created for user with id {user_id}"


@shared_task
def send_user_data_to_freshworks_task(user_id):
    instance = User.objects.filter(id=user_id).last()

    register_contact_task = freshworks_register_contact(instance)

    return f"Data sent to freshworks"


# @shared_task
def send_whisper_otp_to_email_task(user_id, otp):
    user = User.objects.filter(id=user_id).first()
    send_email(email=user.email, passcode=otp)
    return f"otp sent to email {user.email}"


@shared_task
def send_bvn_otp_task(formatted_bvn_phone_number, app_name):
    Users_Otp.send_new_otp(phone_number=formatted_bvn_phone_number, app_name=app_name)
    return f"otp sent to bvn number {formatted_bvn_phone_number}"



@shared_task
def daily_terminal_login_count_reset():
    User.objects.all().update(daily_terminal_login_count=0)

@shared_task
def weekly_terminal_login_count_reset_and_activity_check():
    pass
    # users = User.objects.filter(terminal_id__isnull=False)
    # if len(users) > 0:
    #     for user in users:
    #         if user.weekly_terminal_login_count < 4:
    #             # suspend terminal
    #             user.terminal_suspended = True
    #             stock = Stock.objects.get(terminal_id=user.terminal_id)
    #             stock.status = 'INACTIVE'
    #             stock.save()
    #         else:
    #             d_now = datetime.now()
    #             d_prev = d_now - timedelta(days=7)
    #             now = self.timezone.localize(d_now)
    #             prev = self.timezone.localize(d_prev)

    #             transaction_count = Transaction.objects.filter(terminal_id=user.terminal_id).filter(date_created__lte=now).filter(
    #                 date_created__gte=prev).count()
    #             if transaction_count < 13:
    #                 user.terminal_suspended = True
    #                 stock = Stock.objects.get(terminal_id=user.terminal_id)
    #                 stock.status = 'INACTIVE'
    #                 stock.save()

    # User.objects.filter(terminal_id__isnull=False).update(weekly_terminal_login_count=0)


@shared_task
def monthly_terminal_login_count_reset():
    User.objects.all().update(monthly_terminal_login_count=0)


# celery.conf.beat_schedule = {
#     'daily terminal login count reset': {
#         'task': 'main.tasks.daily_terminal_login_count_reset',
#         'schedule': crontab(hour=0, minute=0),
#     },
# }

# celery.conf.beat_schedule = {
#     'weekly terminal login count reset and activity check': {
#         'task': 'main.tasks.weekly_terminal_login_count_reset_and_activity_check',
#         'schedule': crontab(hour=7, minute=30, day_of_week='sunday'),
#     },
# }

# celery.conf.beat_schedule = {
#     'monthly terminal login count reset': {
#         'task': 'main.tasks.monthly_terminal_login_count_reset',
#         'schedule': crontab(0, 0, day_of_month='3'),
#     },
# }


@shared_task
def add_agents_whatsapp_group_task(phone_number):
    
    add_agents_whatsapp_group_func(phone_number)
    return "DONE"


@shared_task
def delete_old_celery_tasks():

    cutoff_date = timezone.now() - timezone.timedelta(days=100)

    old_instances = TaskResult.objects.filter(date_done__lt=cutoff_date).order_by("id")[:3500]
    
    for old_instance in old_instances:
        old_instance.delete()

    return "DONE"


@shared_task
def delete_old_logins():
    
    cutoff_date = timezone.now() - timezone.timedelta(days=100)

    old_instances = BlackListedJWT.objects.filter(date_created__lt=cutoff_date).order_by("id")[:500]
    
    for old_instance in old_instances:
        old_instance.delete()

    return "DONE"


# @shared_task
# def start_trans_band_regulator(status):
#     try:
#         stat_num = int(status)
#     except:
#         stat_num = None

#     if stat_num == 1:
#         const = ConstantTable.get_constant_table_instance()
#         const.trans_band_regulator = True
#         const.save()

#         for user in User.objects.all():
#             user.save()

#     elif stat_num == 0:
#         for user in User.objects.all():
#             user.trans_band = 1
#             user.save()

#     return "DONE"