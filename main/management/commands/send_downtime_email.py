from django.core.management.base import BaseCommand

from main.models import User
from main.helper.send_emails import send_bulk_uptime_and_downtime_email


class Command(BaseCommand):

    def add_arguments(self, parser):
        parser.add_argument('--send_type', dest='send_type', type=str, help="Type of email (Uptime or Downtime")

    def handle(self, *args, **kwargs):
        send_type = kwargs.get('send_type')

        for user in User.objects.filter(registration_email_verified=True, kyc_level__gte=1).exclude(email__icontains="+"):
            if user.email:
                try:
                    # send_bulk_uptime_and_downtime_email.delay(str(user.email), send_type)
                    send_bulk_uptime_and_downtime_email.apply_async(
                        queue="service_downtime_email",
                        kwargs={
                            "email": str(user.email),
                            "send_type": send_type
                        }
                    )
                except Exception as err:
                    print(err)


