from django.core.management.base import BaseCommand
from django.conf import settings
from datetime import datetime

import jwt

class Command(BaseCommand):
    help = "THIS COMMAND GENERATES TOKEN"

    def handle(self, *args, **kwargs):
        
        token = jwt.encode({'time': str(datetime.now())}, f'{settings.CARD_LOAD_TOKONE}', algorithm='HS256')
        # token = jwt.encode({'exp': datetime.now(), 'iat': 1665259799, 'pld': {'amount': '100.00'}}, f'{settings.CARD_LOAD_TOKONE}', algorithm='HS256')

        return token