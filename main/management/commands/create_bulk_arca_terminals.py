from django.core.management.base import BaseCommand
from horizon_pay.models import ArcaPayTerminalTable


class Command(BaseCommand):

    def add_arguments(self, parser):
        parser.add_argument('--count', dest='number', type=int, help="Numbers of terminals to be created")

    def handle(self, *args, **kwargs):
        number = kwargs.get('number')

        created_instances = list()
        for i in range(0, number):
            created_tid = ArcaPayTerminalTable.generate_arca_tids(num_add=0)
            instance = ArcaPayTerminalTable.objects.get(arca_terminal_id=created_tid)
            instance.assigned = True
            instance.save()
            created_instances.append(created_tid)
            self.stdout.write(self.style.SUCCESS('Successfully created for "%s"' % created_tid))

        ArcaPayTerminalTable.objects.filter(arca_terminal_id__in=created_instances).update(assigned=False)


