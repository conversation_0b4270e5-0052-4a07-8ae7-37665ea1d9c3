from django.core.management import BaseCommand
from main.models import User
from kyc_app.models import KYCTable, BVNDetail

class Command(BaseCommand):
    help = ''
    def handle(self, *args, **options):

        for user in User.objects.all():

            kyc_instance = KYCTable.objects.filter(user=user).last()
            bvn_rel, created = BVNDetail.objects.get_or_create(kyc=kyc_instance)

            if bvn_rel.is_verified:
                bvn_rel.verification_status = "SUCCESSFUL"
                user.kyc_one_progress = "SUCCESSFUL"
                bvn_rel.save()
                user.save()