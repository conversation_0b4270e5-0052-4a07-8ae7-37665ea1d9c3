from django.core.management.base import BaseCommand
from datetime import timedelta
from django.utils import timezone
from main.models import ResetPinStorage


class Command(BaseCommand):
    help = "Remove Hashed Values"

    def handle(self, *args, **kwargs):

        for i in ResetPinStorage.objects.all():
            if timezone.now() - i.date_added > timedelta(minutes=10):
                i.delete()

