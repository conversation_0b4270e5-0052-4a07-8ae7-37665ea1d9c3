from django.core.management import BaseCommand
import random
import string

from accounts.models import Transaction
from kyc_app.models import KYCTable

from main.models import User

from datetime import datetime, timedelta
import requests
import json
from time import sleep





def random_char(y):
    return ''.join(random.choice(string.ascii_letters) for x in range(y))


def create_user_helper_test(digit, num, email, user_name):

    import requests
    import json

    url = "https://428a-105-112-180-221.eu.ngrok.io/agency/user/create_user_detail/"
    
    phone_number = f"0{num}0895598{digit}{num}"

    if "000" in phone_number:
        phone_number = phone_number.replace("000", "070")

    payload = json.dumps({
        "phone_number": phone_number,
        "username": f"{user_name}",
        "first_name": "Ini",
        "last_name": "Soye",
        "email": f"{email}",
        "state": "lagos",
        "lga": "shomolu",
        "nearest_landmark": "Ozone",
        "street": "12 alara"
    })

    print(payload)
    
    headers = {
        'Content-Type': 'application/json'
    }

    sleep(5)

    response = requests.request("POST", url, headers=headers, data=payload)

    print(response.text)


class Command(BaseCommand):
    help = ''
    def handle(self, *args, **options):
        
        # create random user
        # num = 00

        # for i in range(0, 100):

        #     name = random_char(5)
        #     email = f"{name[:5]}@gmail.com"
        #     n = random.randint(0, 9)
        #     create_user_helper_test(num, n, email, name)

        #     num += 1



        # # update some users created_date to pass
        # users = User.objects.all()
        
        
        # if users:
        #     num = int(users.count() / 2)
        #     if num > 20:
        #         num - 5
            
            
        #     for i in range(0, int(num)):
        #         n = random.randint(1,num)
                

        #         _date = datetime.now() - timedelta(days=50)

        #         User.objects.filter(id=n).update(created_at=_date)
                

        
        # # create transaction
        # users = User.objects.all()
        # if users:
        #     num = users.count() / 2
        #     if num > 20:
        #         num - 5
            
        #     _user = User.objects.all()[:int(num)]

        #     for i in _user:
        #         Transaction.objects.create(
        #             user=i,
        #             wallet_id="F123456789",
        #             wallet_type="wallet",
        #             transaction_type="SEND_BUDDY",
        #             amount=random.randint(1,100),
        #             liberty_reference="F123456789",
        #             liberty_commission=random.randint(1,100),
        #             total_amount_charged=random.randint(1,100),
        #             total_amount_sent_out=random.randint(1,100),
        #             beneficiary_wallet_id="F123456789",
        #             beneficiary_wallet_type="wallet",
        #             escrow_id="F123456789",
        #             narration="test",
        #             status="SUCCESSFUL",
        #             transaction_leg="EXTERNAL",
        #         )


        #         Transaction.objects.create(
        #             user=i,
        #             wallet_id="F123456789",
        #             wallet_type="wallet",
        #             transaction_type="SEND_BUDDY",
        #             amount=random.randint(1,100),
        #             liberty_reference="F123456789",
        #             liberty_commission=random.randint(1,100),
        #             total_amount_charged=random.randint(1,100),
        #             total_amount_sent_out=random.randint(1,100),
        #             beneficiary_wallet_id="F123456789",
        #             beneficiary_wallet_type="wallet",
        #             escrow_id="F123456789",
        #             narration="test",
        #             status="SUCCESSFUL",
        #             transaction_leg="EXTERNAL",
        #         )

        # create kyc

        # users = User.objects.all()
        # for i in users:

        #     KYCTable.objects.create(
        #         user=i
        #     )


        # create transaction
        user = User.objects.filter(email = "<EMAIL>").last()

        for i in range(0, 50):

            transaction_type_choices = random.choices(["SEND_BUDDY", "SEND_BANK_TRANSFER", "SEND_LIBERTY_COMMISSION", "FUND_BUDDY", "FUND_BANK_TRANSFER", "CARD_TRANSACTION_FUND", "SEND_BACK_TO_FLOAT_TRANSFER"])[0]

            transaction_status= random.choices(["SUCCESSFUL", "FAILED", "PENDING", "IN_PROGRESS"])[0]

            transaction_leg = random.choices(["EXTERNAL", "INTERNAL", "COMMISSIONS"])[0]

            transaction_mode = random.choices(["SEND_MONEY_ONLINE", "SEND_MONEY_OFFLINE", "CARD_WITHDRAW_ONLINE", "CARD_WITHDRAW_OFFLINE", "CARD_WITHDRAW_SEND_MONEY_ONLINE", "CARD_WITHDRAW_SEND_MONEY_OFFLINE"])[0]

            wallet_type = random.choices(["SPEND", "COLLECTION", "SAVINGS", "COMMISSIONS"])[0]

            Transaction.objects.create(
                user=user,
                wallet_type=wallet_type,
                transaction_type=transaction_type_choices,
                amount=random.randint(1,100),
                liberty_reference="F123456789",
                liberty_commission=random.randint(1,100),
                total_amount_charged=random.randint(1,100),
                total_amount_sent_out=random.randint(1,100),
                beneficiary_wallet_id="F123456789",
                beneficiary_wallet_type="wallet",
                escrow_id="F123456789",
                narration="test",
                status=transaction_status,
                transaction_leg=transaction_leg,
                transaction_mode = transaction_mode,
            )