from django.db import models
from main.models import User, BaseModel
from main.helper.helper_function import *


class Facebook(BaseModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    facebook_id = models.CharField(max_length=16, null=True, blank=True)
    name = models.CharField(max_length=50, null=True, blank=True)
    first_name = models.Char<PERSON>ield(max_length=50, null=True, blank=True)
    short_name = models.Char<PERSON>ield(max_length=50, null=True, blank=True)
    last_name = models.Char<PERSON>ield(max_length=50, null=True, blank=True)
    middle_name = models.Char<PERSON>ield(max_length=50, null=True, blank=True)
    birthday = models.CharField(max_length=15, null=True, blank=True)
    education = models.CharField(max_length=250, null=True, blank=True)
    email = models.CharField(max_length=50, null=True, blank=True)
    gender = models.Char<PERSON>ield(max_length=10, null=True, blank=True)
    location = models.TextField(null=True, blank=True)
    hometown = models.CharField(max_length=200, null=True, blank=True)
    picture_url = models.TextField(null=True, blank=True)
    languages = models.CharField(max_length=50, null=True, blank=True)
    link = models.TextField(null=True, blank=True)
    website = models.TextField(null=True, blank=True)
    relationship_status = models.CharField(max_length=50, null=True, blank=True)
    accounts = models.TextField(null=True, blank=True)
    account_category = models.CharField(max_length=250, null=True, blank=True)

    class Meta:
        db_table = "Facebook"
        verbose_name_plural = "Facebook"

    def __str__(self):
        return f"{self.name}"

    @staticmethod
    def create_facebook_user(
        user_data,
        phone,
    ):
        try:
            user_data["id"]
        except TypeError:
            response = {"message": "error", "data": user_data}
        else:
            try:
                user = User.objects.get(phone_number=phone)
            except User.DoesNotExist:
                response = {"message": "unsuccessful", "data": "invalid user id"}
            facbook = Facebook.objects.filter(name=user_data["name"])
            if facbook.exists():
                response = {"message": "found", "data": "user already exist"}
            else:
                Facebook.objects.create(
                    user=user,
                    facebook_id=user_data.get("id"),
                    name=user_data.get("name"),
                    first_name=user_data.get("first_name"),
                    short_name=user_data.get("short_name"),
                    last_name=user_data.get("last_name"),
                    middle_name=user_data.get("middle_name"),
                    birthday=user_data.get("birthday"),
                    education=user_data.get("education"),
                    email=user_data.get("email"),
                    gender=user_data.get("gender"),
                    location=user_data.get("location"),
                    hometown=user_data.get("hometown")["name"],
                    picture_url=user_data.get("picture")["data"]["url"],
                    languages=user_data.get("languages"),
                    link=user_data.get("user_data"),
                    website=user_data.get("website"),
                    relationship_status=user_data.get("relationship_status"),
                    accounts=user_data.get("accounts", None)["data"][0]["name"],
                    account_category=user_data.get("accounts")["data"][0]["category"],
                )
            response = {"message": "successful", "data": "user added successfully"}
        return response
