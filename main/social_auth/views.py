from rest_framework import status
from rest_framework.decorators import api_view
from rest_framework.response import Response

from main.helper.facebook_auth_token import facebook_auth_token_helper
from main.social_auth.serializers import FacebookAuthTokenSerializer
from main.social_auth.models import *
from main.models import *


@api_view(["POST"])
def facebook_sign_in(request):
    if request.method == "POST":
        serializer = FacebookAuthTokenSerializer(data=request.data)
        if serializer.is_valid(raise_exception=True):
            auth_token = serializer.validated_data["fb_auth_token"]
            phone = serializer.validated_data["phone"]
            user_data = facebook_auth_token_helper(auth_token)
            response = Facebook.create_facebook_user(user_data, phone)
            if response["message"] == "error" or response["message"] == "unsuccessfull":
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
            elif response["message"] == "found":
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(response, status=status.HTTP_302_FOUND)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
