from faker import Faker
import factory.django

from main.models import User, NewLocationList

fake = Faker()


class NewLocationListFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = NewLocationList
    

class UserFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = User
    

    username = factory.Faker("user_name")
    phone_number = factory.Sequence(
        lambda n: f"234902{fake.random_int(1000000, 9999999)}"
    )
    street = factory.Faker("address")
    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")
    user_branch = factory.SubFactory(NewLocationListFactory)
