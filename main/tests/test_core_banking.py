import pytest
from unittest.mock import patch, MagicMock
from main.helper.core_banking import LibertyCoreBankingAPI


@pytest.mark.django_db
class TestLibertyCoreBankingAPI:
    
    @patch('main.helper.core_banking.requests.post')
    @patch('main.helper.core_banking.LibertyCoreBankingAPI.get_access_token')
    def test_send_money_success(self, mock_get_access_token, mock_post):
        # Mock the access token
        mock_get_access_token.return_value = "test_token"
        
        # Mock the response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "status": "success",
            "data": {
                "reference": "ce0d28ee-7735-499c-b383-0df9f9d4921b",
                "account_number": "************",
                "account_name": "<PERSON>",
                "amount": 15.76
            }
        }
        mock_post.return_value = mock_response
        
        # Call the method
        result = LibertyCoreBankingAPI.send_money(
            first_name="<PERSON>",
            last_name="<PERSON><PERSON>",
            email="<EMAIL>",
            phone_number="**********",
            transaction_reference="TXN123456789",
            amount=1500.75,
            beneficiary_account_number="************",
            beneficiary_account_name="<PERSON> Doe",
            beneficiary_bank_code="BANK123",
            narration="Payment for invoice #987654",
            source_account="**********",
            provider="FIDELITY"
        )
        
        # Assert the result
        assert result["status"] == "success"
        assert result["reference"] == "ce0d28ee-7735-499c-b383-0df9f9d4921b"
        assert result["account_number"] == "************"
        assert result["account_name"] == "John Doe"
        assert result["amount"] == 15.76
        
        # Assert the request was made correctly
        mock_post.assert_called_once()
        
    @patch('main.helper.core_banking.requests.post')
    @patch('main.helper.core_banking.LibertyCoreBankingAPI.get_access_token')
    def test_send_money_request_exception(self, mock_get_access_token, mock_post):
        # Mock the access token
        mock_get_access_token.return_value = "test_token"
        
        # Mock the exception
        mock_post.side_effect = Exception("Connection error")
        
        # Call the method
        result = LibertyCoreBankingAPI.send_money(
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            phone_number="**********",
            transaction_reference="TXN123456789",
            amount=1500.75,
            beneficiary_account_number="************",
            beneficiary_account_name="John Doe",
            beneficiary_bank_code="BANK123",
            narration="Payment for invoice #987654",
            source_account="**********",
            provider="FIDELITY"
        )
        
        # Assert the result
        assert result["status"] == "error"
        assert "Connection error" in result["message"]
        assert result["reference"] is None
        assert result["account_number"] is None
        assert result["account_name"] is None
        assert result["amount"] is None
