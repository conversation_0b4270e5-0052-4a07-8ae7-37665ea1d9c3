import pytest
from unittest.mock import patch, MagicMock
from main.helper.core_banking import LibertyCoreBankingAPI


@pytest.mark.django_db
class TestLibertyCoreBankingAPINewMethods:
    
    @patch('main.helper.core_banking.requests.get')
    @patch('main.helper.core_banking.LibertyCoreBankingAPI.get_access_token')
    def test_verify_transaction_success(self, mock_get_access_token, mock_get):
        # Mock the access token
        mock_get_access_token.return_value = "test_token"
        
        # Mock the response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "status": "success",
            "message": "Transaction verified successfully",
            "data": {
                "reference": "7a690172-c3f3-4ace-a956-9cfaaf8228d5",
                "amount": 1000.00,
                "status": "completed",
                "transaction_date": "2023-05-15T10:30:45Z"
            }
        }
        mock_get.return_value = mock_response
        
        # Call the method
        result = LibertyCoreBankingAPI.verify_transaction(
            reference="7a690172-c3f3-4ace-a956-9cfaaf8228d5"
        )
        
        # Assert the result
        assert result["status"] == "success"
        assert result["message"] == "Transaction verified successfully"
        assert result["data"]["reference"] == "7a690172-c3f3-4ace-a956-9cfaaf8228d5"
        assert result["data"]["amount"] == 1000.00
        assert result["data"]["status"] == "completed"
        
        # Assert the request was made correctly
        mock_get.assert_called_once()
        
    @patch('main.helper.core_banking.requests.get')
    @patch('main.helper.core_banking.LibertyCoreBankingAPI.get_access_token')
    def test_verify_transaction_request_exception(self, mock_get_access_token, mock_get):
        # Mock the access token
        mock_get_access_token.return_value = "test_token"
        
        # Mock the exception
        mock_get.side_effect = Exception("Connection error")
        
        # Call the method
        result = LibertyCoreBankingAPI.verify_transaction(
            reference="7a690172-c3f3-4ace-a956-9cfaaf8228d5"
        )
        
        # Assert the result
        assert result["status"] == "error"
        assert "Connection error" in result["message"]
        assert result["data"] is None
        
    @patch('main.helper.core_banking.requests.get')
    @patch('main.helper.core_banking.LibertyCoreBankingAPI.get_access_token')
    def test_get_account_details_success(self, mock_get_access_token, mock_get):
        # Mock the access token
        mock_get_access_token.return_value = "test_token"
        
        # Mock the response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "status": "success",
            "message": "Account details retrieved successfully",
            "data": {
                "account_number": "**********",
                "account_name": "Liberty Float Account",
                "balance": 50000.00,
                "currency": "NGN",
                "provider": "WEMA_BANK"
            }
        }
        mock_get.return_value = mock_response
        
        # Call the method
        result = LibertyCoreBankingAPI.get_account_details(
            provider="WEMA_BANK"
        )
        
        # Assert the result
        assert result["status"] == "success"
        assert result["message"] == "Account details retrieved successfully"
        assert result["data"]["account_number"] == "**********"
        assert result["data"]["account_name"] == "Liberty Float Account"
        assert result["data"]["balance"] == 50000.00
        
        # Assert the request was made correctly
        mock_get.assert_called_once()
        
    @patch('main.helper.core_banking.requests.get')
    @patch('main.helper.core_banking.LibertyCoreBankingAPI.get_access_token')
    def test_get_account_details_request_exception(self, mock_get_access_token, mock_get):
        # Mock the access token
        mock_get_access_token.return_value = "test_token"
        
        # Mock the exception
        mock_get.side_effect = Exception("Connection error")
        
        # Call the method
        result = LibertyCoreBankingAPI.get_account_details(
            provider="WEMA_BANK"
        )
        
        # Assert the result
        assert result["status"] == "error"
        assert "Connection error" in result["message"]
        assert result["data"] is None
        
    @patch('main.helper.core_banking.requests.get')
    @patch('main.helper.core_banking.LibertyCoreBankingAPI.get_access_token')
    def test_account_inquiry_success(self, mock_get_access_token, mock_get):
        # Mock the access token
        mock_get_access_token.return_value = "test_token"
        
        # Mock the response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "status": "success",
            "message": "Account inquiry successful",
            "data": {
                "account_number": "**********",
                "account_name": "JOHN DOE",
                "bank_code": "011",
                "bank_name": "First Bank of Nigeria"
            }
        }
        mock_get.return_value = mock_response
        
        # Call the method
        result = LibertyCoreBankingAPI.account_inquiry(
            account_number="**********",
            bank_code="011"
        )
        
        # Assert the result
        assert result["status"] == "success"
        assert result["message"] == "Account inquiry successful"
        assert result["data"]["account_number"] == "**********"
        assert result["data"]["account_name"] == "JOHN DOE"
        assert result["data"]["bank_code"] == "011"
        
        # Assert the request was made correctly
        mock_get.assert_called_once()
        
    @patch('main.helper.core_banking.requests.get')
    @patch('main.helper.core_banking.LibertyCoreBankingAPI.get_access_token')
    def test_account_inquiry_request_exception(self, mock_get_access_token, mock_get):
        # Mock the access token
        mock_get_access_token.return_value = "test_token"
        
        # Mock the exception
        mock_get.side_effect = Exception("Connection error")
        
        # Call the method
        result = LibertyCoreBankingAPI.account_inquiry(
            account_number="**********",
            bank_code="011"
        )
        
        # Assert the result
        assert result["status"] == "error"
        assert "Connection error" in result["message"]
        assert result["data"] is None
