from django.contrib import admin, messages
from django_celery_results.models import TaskResult

from import_export import resources
from import_export.admin import ImportExportMixin, ImportExportModelAdmin

from main.models import *
from django.contrib.admin.models import LogEntry, DELETION
from django.utils.html import escape
from django.urls import reverse
from django.utils.safestring import mark_safe
from rest_framework.authtoken.models import Token
from .models import IssueLog

class TaskResultAdmin(admin.ModelAdmin):
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        search_kwargs = request.GET.get('task_kwargs', None)
        if search_kwargs:
            queryset = queryset.filter(task_kwargs__contains=search_kwargs)
        return queryset

# admin.site.register(TaskResult, TaskResultAdmin)

@admin.register(LogEntry)
class LogEntryAdmin(admin.ModelAdmin):
    date_hierarchy = 'action_time'

    list_filter = [
        # 'user',
        'content_type',
        'action_flag'
    ]

    search_fields = [
        'user__email',
        'object_repr',
        'change_message'
    ]

    list_display = [
        'action_time',
        'user',
        'change_message',
        'content_type',
        # 'object_link',
        'action_flag',
    ]


    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def has_view_permission(self, request, obj=None):
        return request.user.is_superuser

    # def object_link(self, obj):
    #     if obj.action_flag == DELETION:
    #         link = escape(obj.object_repr)
    #     else:
    #         # ct = obj.content_type
    #         # link = '<a href="%s">%s</a>' % (
    #         #     reverse('admin:%s_%s_change' % (ct.app_label, ct.model), args=[obj.object_id]),
    #         #     escape(obj.object_repr),
    #         # )
    #         pass
    #     return mark_safe(link)
    # object_link.admin_order_field = "object_repr"
    # object_link.short_description = "object"



###############################################################################
# RESOURCES

class RegistrationDataResource(resources.ModelResource):
    class Meta:
        model = RegistrationData

class UserResource(resources.ModelResource):
    class Meta:
        model = User

class ConstantResource(resources.ModelResource):
    class Meta:
        model = ConstantTable


class ChargeBandResource(resources.ModelResource):
    class Meta:
        model = ChargeBand


class AgentProfileResource(resources.ModelResource):
    class Meta:
        model = AgentProfile


class UnregisteredPhoneNumbersResource(resources.ModelResource):
    class Meta:
        model = UnregisteredPhoneNumber

class WhitelistResource(resources.ModelResource):
    class Meta:
        model = Whitelist

class ResetPinStorageResource(resources.ModelResource):
    class Meta:
        model = ResetPinStorage

class SMSRecordResource(resources.ModelResource):
    class Meta:
        model = SMSRecord

class UnsentSMSResource(resources.ModelResource):
    class Meta:
        model = UnsentSMS

class OtherServiceDetailResource(resources.ModelResource):
    class Meta:
        model = OtherServiceDetail

class TrackUserClickResource(resources.ModelResource):
    class Meta:
        model = TrackUserClick

class CallbackSystemResource(resources.ModelResource):
    class Meta:
        model = CallbackSystem

class SuperAgentProfileResource(resources.ModelResource):
    class Meta:
        model = SuperAgentProfile

class UserFormattedResource(resources.ModelResource):
    class Meta:
        model = UserFormatted

class AvailableBalanceResource(resources.ModelResource):
    class Meta:
        model = AvailableBalance

class DeliveryAddressDataResource(resources.ModelResource):
    class Meta:
        model = DeliveryAddressData

class UserFlagResource(resources.ModelResource):
    class Meta:
        model = UserFlag

class BlacklistResource(resources.ModelResource):
    class Meta:
        model = Blacklist

class UserWhitelistResource(resources.ModelResource):
    class Meta:
        model = UserWhitelist

class AgentSupervisorResource(resources.ModelResource):
    class Meta:
        model = AgentSupervisor

class UserOtherAccountResource(resources.ModelResource):
    class Meta:
        model = UserOtherAccount

class CorporateAccountResource(resources.ModelResource):
    class Meta:
        model = CorporateAccount

class UserTempTokenResource(resources.ModelResource):
    class Meta:
        model = UserTempToken

class DisplayBannerResource(resources.ModelResource):
    class Meta:
        model = DisplayBanner


class DataDeletionResource(resources.ModelResource):
    class Meta:
        model = UserDataDeletionRequest

class AjoAgentFormResource(resources.ModelResource):
    class Meta:
        model = AjoAgentForm

class TerminalRetrievalRequestResource(resources.ModelResource):
    class Meta:
        model = TerminalRetrievalRequest



class NewLocationListResource(resources.ModelResource):
    class Meta:
        model = NewLocationList

class BranchTeamResource(resources.ModelResource):
    class Meta:
        model = BranchTeam

class UserTeamResource(resources.ModelResource):
    class Meta:
        model = UserTeam

#######################################################################
# ADMINS



class RegistrationDataResourceAdmin(ImportExportModelAdmin):
    resource_class = RegistrationDataResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class UserAdmin(ImportExportModelAdmin):

    def suspend_terminals(self, request, queryset):
        for user in queryset:
            User.suspend_user(
                user=user,
                reason="Suspended By Super Admin. Do not unsuspend without instruction"
            )


        self.message_user(request, "Successfully Suspended Terminals")

    suspend_terminals.short_description = "Suspend terminals"


    def save_model(self, request, obj, form, change):
        custom_request = request
        obj.save(custom_request=custom_request)



    def get_readonly_fields(self, request, obj=None):
        if obj and obj.is_fraud: # editing an existing object
            return self.readonly_fields + ['is_fraud', 'send_money_status', 'is_suspended', 'terminal_suspended', 'mobile_suspended', 'mobile_disabled', 'terminal_disabled']
        return self.readonly_fields

    readonly_fields = [
        "terminal_provider", "unique_id", "customer_id", "password", "lotto_win_toggle",
        "registration_email_otp", "date_of_consent", "date_assigned", "sales_rep_full_name", "sales_rep_comm_balance_daily",
        "sales_rep_comm_balance", "bills_pay_comm_balance_daily", "bills_pay_comm_balance", "other_comm_balance_daily",
        "other_comm_balance", "kyc_level", "kyc_one_progress", "kyc_two_progress", "wallet_balance"
    ]
    exclude = ('registration_email_otp', 'password')

    resource_class = UserResource
    search_fields = ['email', 'phone_number', 'terminal_id', "first_name", "last_name", "bvn_number", "sales_rep_upline_code", "username"]
    list_filter = (
        ('date_joined', "type_of_user", "kyc_two_progress", "send_money_status", "sms_subscription", "kyc_level", "has_transaction_pin", "terminal_suspended", "is_suspended", "is_fraud", "reactivation_request")
    )
    date_hierarchy = 'date_joined'
    actions = [suspend_terminals]


    def get_list_display(self, request):
        return [field.name if field.name not in ["bvn_number"] else "phone_number" for field in self.model._meta.concrete_fields]


    # def save_model(self, request, obj, form, change):
    #     if change:
    #         obj.save(update_fields=form.changed_data)
    #     else:
    #         super().save_model(request, obj, form, change)


class ConstantResourceAdmin(ImportExportModelAdmin):
    resource_class = ConstantResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ChargeBandResourceAdmin(ImportExportModelAdmin):
    resource_class = ChargeBandResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AgentProfileResourceAdmin(ImportExportModelAdmin):
    search_fields = ['email', 'phone_number']
    resource_class = AgentProfileResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UnregisteredPhoneNumbersResourceAdmin(ImportExportModelAdmin):
    resource_class = UnregisteredPhoneNumbersResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class WhitelistResourceAdmin(ImportExportModelAdmin):
    resource_class = WhitelistResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class ResetPinStorageResourceAdmin(ImportExportModelAdmin):
    resource_class = ResetPinStorageResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class SMSRecordResourceAdmin(ImportExportModelAdmin):
    resource_class = SMSRecordResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class UnsentSMSResourceAdmin(ImportExportModelAdmin):
    resource_class = UnsentSMSResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class OtherServiceDetailResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user']
    resource_class = OtherServiceDetailResource
    search_fields = ['user__email', 'service_name', 'wallet_id']


    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class TrackUserClickResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user']
    resource_class = TrackUserClickResource
    search_fields = ['user__email', 'view_or_screen']
    list_filter = (
        ('date_added', "view_or_screen")
    )


    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class CallbackSystemResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user']
    resource_class = CallbackSystemResource
    search_fields = ['user__email', 'url']
    list_filter = (
        ('date_added', "transaction_type", "other_transaction_type")
    )


    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class SuperAgentProfileResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['agent', 'super_agent', 'supervisor']
    resource_class = SuperAgentProfileResource
    actions = ["resave_super_agent_profile"]

    # search_fields = ['user__email', 'url']
    # list_filter = (
    #     ('date_added', "transaction_type", "other_transaction_type")
    # )

    def resave_super_agent_profile(self, request, queryset):
        for agent in queryset:
            agent.save()
            self.message_user(request, f"SuperAgentProfile saved {agent.id}", level=messages.SUCCESS)

    resave_super_agent_profile.short_description = "RE-SAVE SuperAgentProfile FOR SELECTED ITEM(S)"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UserFormattedResourceAdmin(ImportExportModelAdmin):
    readonly_fields = ["type_of_user"]
    resource_class = UserResource
    search_fields = ['email', 'phone_number', "first_name", "last_name"]
    list_filter = (
        ('date_joined', "type_of_user", "kyc_level")
    )
    date_hierarchy = 'date_joined'


    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AvailableBalanceResourceAdmin(ImportExportModelAdmin):
    resource_class = AvailableBalanceResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class DeliveryAddressDataResourceAdmin(ImportExportModelAdmin):
    resource_class = DeliveryAddressDataResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class UserFlagResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user']
    resource_class = UserFlagResource
    search_fields = ['user__email']

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


    def get_search_results(self, request, queryset, search_term):
        search_list = [term.strip() for term in search_term.split(",")]

        if len(search_list) == 1 and not search_list[0].strip():
            return queryset, True
        filtered_qs = queryset.filter(
                        user__email__in=search_list,
                        suspended=True,
                        ).distinct("user__email")
        print(len(search_list))

        return filtered_qs, True


    def mass_unsuspend_terminals(self, request, queryset):
        for query in queryset:
            UserFlag.unsuspend_user(
                user=query.user,
                reason="Unsuspended by admin",
                request=request
            )
        self.message_user(request, "Suspension successfully removed from terminals")

    mass_unsuspend_terminals.add_description = "Manually unsuspend multiple terminals"
    actions = [mass_unsuspend_terminals]


class BlacklistResourceAdmin(ImportExportModelAdmin):
    resource_class = BlacklistResource
    search_fields = ['content']

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class UserWhitelistResourceAdmin(ImportExportModelAdmin):
    resource_class = UserWhitelistResource
    search_fields = ['content']

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class AgentSupervisorResourceAdmin(ImportExportModelAdmin):
    resource_class = AgentSupervisorResource
    autocomplete_fields = ['supervisor', 'agents']
    search_fields = ['supervisor__email', 'agents__email']
    list_filter = (
        ('date_created',)
    )
    date_hierarchy = 'date_created'


    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class UserOtherAccountResourceAdmin(ImportExportModelAdmin):
    resource_class = UserOtherAccountResource
    autocomplete_fields = ['owner', 'other_account']
    search_fields = ['owner__email', 'other_account__email']
    list_filter = (
        ('date_created',)
    )
    date_hierarchy = 'date_created'


    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CorporateAccountResourceAdmin(ImportExportModelAdmin):
    resource_class = CorporateAccountResource
    autocomplete_fields = ['user', 'other_users']
    search_fields = ['user__email', 'rc_number', 'corporate_id']
    list_filter = (
        ('date_created', 'raw_incorp_date')
    )
    date_hierarchy = 'date_created'
    readonly_fields = ["corporate_id"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class TokenAdmin(admin.ModelAdmin):
    autocomplete_fields = ["user"]
    search_fields = ('user__email', 'key')

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class UserTempTokenResourceAdmin(admin.ModelAdmin):
    autocomplete_fields = ["user"]
    search_fields = ('token',)
    list_filter = (
        ('date_created', 'exp', 'pin_type')
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class DisplayBannerResourceAdmin(ImportExportModelAdmin):
    resource_class = DisplayBannerResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UserDataDeletionRequestResourceAdmin(ImportExportModelAdmin):
    resource_class = DisplayBannerResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class AjoAgentFormResourceAdmin(ImportExportModelAdmin):
    search_fields = ["phone_number"]
    resource_class = AjoAgentFormResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class TerminalRetrievalRequestResourceAdmin(ImportExportModelAdmin):
    search_fields = ["phone_number", "user__email"]
    resource_class = TerminalRetrievalRequestResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class NewLocationListResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["supervisor"]
    resource_class = NewLocationListResource
    search_fields = ["location", "sub_location"]

    list_filter = (
        ('date_created', 'location')
    )
    readonly_fields = ["created_by"]

    def save_model(self, request, obj, form, change):
        custom_request = request
        obj.save(custom_request=custom_request)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class BranchTeamResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["supervisor", "branch"]
    search_fields = ["branch__location", "supervisor__email"]
    resource_class = BranchTeamResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class UserTeamResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["team", "users"]
    search_fields = ["team__branch__location", "users__email", "team__supervisor__email"]
    resource_class = UserTeamResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class IssueLogAdmin(admin.ModelAdmin):
    list_display = ('user', 'description', 'date_created', 'date_updated')
    list_filter = ('date_created', 'date_updated')
    search_fields = ('user__email', 'user__username', 'description')
    readonly_fields = ('date_created', 'date_updated')
    raw_id_fields = ('user',)

    fieldsets = (
        ('User Information', {
            'fields': ('user',)
        }),
        ('Issue Details', {
            'fields': ('description',)
        }),
        ('Timestamps', {
            'fields': ('date_created', 'date_updated'),
            'classes': ('collapse',)
        })
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')

admin.site.register(RegistrationData, RegistrationDataResourceAdmin)
admin.site.register(User, UserAdmin)
admin.site.register(IssueLog, IssueLogAdmin)
admin.site.register(ConstantTable, ConstantResourceAdmin)
admin.site.register(ChargeBand, ChargeBandResourceAdmin)
admin.site.register(AgentProfile, AgentProfileResourceAdmin)
admin.site.register(UnregisteredPhoneNumber, UnregisteredPhoneNumbersResourceAdmin)
admin.site.register(Whitelist, WhitelistResourceAdmin)
admin.site.register(ResetPinStorage, ResetPinStorageResourceAdmin)
admin.site.register(SMSRecord, SMSRecordResourceAdmin)
admin.site.register(UnsentSMS, UnsentSMSResourceAdmin)
admin.site.register(OtherServiceDetail, OtherServiceDetailResourceAdmin)
admin.site.register(TrackUserClick, TrackUserClickResourceAdmin)
admin.site.register(CallbackSystem, CallbackSystemResourceAdmin)
admin.site.register(SuperAgentProfile, SuperAgentProfileResourceAdmin)
admin.site.register(UserFormatted, UserFormattedResourceAdmin)
admin.site.register(AvailableBalance, AvailableBalanceResourceAdmin)
admin.site.register(DeliveryAddressData, DeliveryAddressDataResourceAdmin)
admin.site.register(UserFlag, UserFlagResourceAdmin)
admin.site.register(Blacklist, BlacklistResourceAdmin)
admin.site.register(UserWhitelist, UserWhitelistResourceAdmin)
admin.site.register(AgentSupervisor, AgentSupervisorResourceAdmin)
admin.site.register(UserOtherAccount, UserOtherAccountResourceAdmin)
admin.site.register(CorporateAccount, CorporateAccountResourceAdmin)
admin.site.register(Token, TokenAdmin)
admin.site.register(UserTempToken, UserTempTokenResourceAdmin)
admin.site.register(DisplayBanner, DisplayBannerResourceAdmin)
admin.site.register(UserDataDeletionRequest, UserDataDeletionRequestResourceAdmin)
admin.site.register(AjoAgentForm, AjoAgentFormResourceAdmin)
admin.site.register(TerminalRetrievalRequest, TerminalRetrievalRequestResourceAdmin)
admin.site.register(NewLocationList, NewLocationListResourceAdmin)
admin.site.register(BranchTeam, BranchTeamResourceAdmin)
admin.site.register(UserTeam, UserTeamResourceAdmin)
admin.site.register(MerchantAcquisitionOfficer)
admin.site.register(ApplicationBanner)


