import os
import debug_toolbar
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from main.serializers import CutomObtainPairView, PhoneCutomObtainPairView, CustomTokenRefreshView
from rest_framework_simplejwt.views import TokenRefreshView

from pathlib import Path
from django.http import FileResponse



admin.site.site_header = "Liberty Pay POS"

schema_view = get_schema_view(
    openapi.Info(
        title="Liberty Pay API",
        default_version="v1",
        description="Test API",
        terms_of_service="",
        contact=openapi.Contact(email=""),
        license=openapi.License(name="MIT License"),
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
)

def trigger_error(request):
    division_by_zero = 1 / 0


def android_liberty_deep_link(request):
    BASE_DIR_SET = Path(__file__).resolve().parent.parent
    file_path = os.path.join(BASE_DIR_SET, 'media')
    return FileResponse(open(f"{file_path}/deeplinks/assetlinks.json", 'rb'), as_attachment=True)

def ios_liberty_deep_link(request):
    BASE_DIR_SET = Path(__file__).resolve().parent.parent
    file_path = os.path.join(BASE_DIR_SET, 'media')
    return FileResponse(open(f"{file_path}/deeplinks/apple-app-site-association.unknown", 'rb'), as_attachment=True)


urlpatterns = [
    path('__debug__/', include(debug_toolbar.urls)),
    path('sentry-debug/', trigger_error),
    path("data-browser/", include("data_browser.urls")),
    # path('activate/', activate_email, name='activate'),
    # path("auth_token/", include("djoser.urls.jwt")),
    # path("auth/", include("djoser.urls")),
    path("admin/", admin.site.urls),
    path(".well-known/assetlinks.json", android_liberty_deep_link),
    path(".well-known/apple-app-site-association", ios_liberty_deep_link),
    path("user/login/create/", CutomObtainPairView.as_view(), name='customtoken'),
    path("user/login/refresh/", CustomTokenRefreshView.as_view(), name='refresh-token'),
    path("user/phone_login/create/", PhoneCutomObtainPairView.as_view(), name='phonecustomtoken'),
    path("agency/", include("main.urls")),
    path("kyc/", include("kyc_app.urls")),
    path("accounts/", include("accounts.urls")),
    path("cards/", include("cards.urls")),
    path("", include("main.social_auth.urls")),
    path("onboarding/", include("pos_onboarding.urls")),
    path("coral/", include("coral.urls")),
    path("horizon/", include("horizon_pay.urls")),
    path("loans/", include("loan_DB.urls")),
    path("send/", include("send_money.urls", namespace="send_money")),
    path("ussd/", include("ussd.urls")),
    path("", schema_view.with_ui("swagger", cache_timeout=0), name="schema-swagger-ui"),
    path("api/admin/", include("admin_dashboard.urls")),
    path("api/agents/", include("agents_dashboard.urls")),
    path("api/retail/admin/", include("retail.urls")),

]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL_2, document_root=settings.MEDIA_ROOT_2)

# if settings.ENVIRONMENT == "development":
#     urlpatterns += [path('silk/', include('silk.urls', namespace='silk'))]
