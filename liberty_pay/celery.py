from celery import Celery
from celery.schedules import crontab
from django.conf import settings
import os


os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'liberty_pay.settings')
celery = Celery('liberty_pay')

celery.config_from_object('django.conf:settings', namespace='CELERY')
celery.autodiscover_tasks()

# Celery task to clear log file every two days
@celery.task(bind=True)
def clear_log_file(self):
    # log_file_path = os.path.join(settings.BASE_DIR)
    # open(f"{settings.BASE_DIR}/general.log", "w").close()
    pass

# from accounts import tasks

# celery.conf.beat_schedule = {
#     'set_next_charge_time': {
#         'task': 'set_next_charge_time',
#         'schedule': crontab(minute='*/1'),
#         'options': {
#             'queue': 'horizon_queue'
#         }
#     },
# }


###########################################################33
#
#
#
# hourly_sweeps = []
# daily_sweeps = []
# weekly_sweeps = []
# monthly_sweeps = []
#
#
#
# @celery.on_after_finalize.connect
# def setup_periodic_tasks(sender, **kwargs):
#     # Executes every day at 00:00
#     sender.add_periodic_task(
#         crontab(hour=0, minute=0),
#         sweep_merchants.s(),
#     )
#
#     # Executes every hour
#     for ds in hourly_sweeps:
#         sender.add_periodic_task(
#             crontab(hour=0, minute=0),
#             send_money.s(ds),
#         )
#
#     # Executes every day at 00:00
#     for ds in daily_sweeps:
#         sender.add_periodic_task(
#             crontab(hour=0, minute=0),
#             send_money.s(ds),
#         )
#
#     # Executes every week
#     for ds in weekly_sweeps:
#         sender.add_periodic_task(
#             crontab(0, 0, day_of_week='monday'),
#             send_money.s(ds)
#         )
#
#     # Executes every second day of the month
#     for ds in monthly_sweeps:
#         sender.add_periodic_task(
#             crontab(0, 0, day_of_month='2'),
#             send_money(ds)
#         )
#
# @celery.task(name="tasks.send_monday", bind=True)
# def send_money(self):
#     print("ds")

#
# @celery.task(name="tasks.sweep_merchants")
# def sweep_merchants():
#     wallets = WalletSystem.objects.filter(available_balance__gt=1000.00)
#     get_user = lambda id : User.objects.get(id=id)
#     merchants = [get_user(i.user.id) for i in wallets if get_user(i.user.id).type_of_user == "MERCHANT"]
#     for merchant in merchants:
#         disbursement_schedule = merchant.disbursement
#         if disbursement_schedule.status != 'GROUPED':
#             if disbursement_schedule.frequency == "INSTANT":
#                 # send money to merchant now
#                 pass
#             elif disbursement_schedule.frequency == "CUSTOM":
#                 pass
#             elif disbursement_schedule.frequency == 'HOURLY':
#                 hourly_sweeps.append(disbursement_schedule)
#             elif disbursement_schedule.frequency == 'DAILY':
#                 daily_sweeps.append(disbursement_schedule)
#             elif disbursement_schedule.frequency == 'WEEKLY':
#                 weekly_sweeps.append(disbursement_schedule)
#             elif disbursement_schedule.frequency == 'MONTHLY':
#                 monthly_sweeps.append(disbursement_schedule)
#
#
