
from django.contrib.auth import get_user_model
from django.conf import settings
from django.http import HttpResponseForbidden

from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.exceptions import AuthenticationFailed

import json

class AuthLowerCaseMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        # One-time configuration and initialization.

    def __call__(self, request):


        if settings.APP_LOGIN_URL in request.build_absolute_uri():
            body = json.loads(request.body)
            
            # if body["email"] == "<EMAIL>":
            #     print("$$$$$$$$$$$$$$$$$$$")
            #     print(body)
            #     print("$$$$$$$$$$$$$$$$$$$")


            body["email"] = body["email"].lower()
            request._body = bytes(json.dumps(body), encoding='utf-8')

            

        # Code to be executed for each request before
        # the view (and later middleware) are called.

        response = self.get_response(request)

        # Code to be executed for each request/response after
        # the view is called.

        return response
    



class OneLoginSessionMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        user = request.user
        if user.is_authenticated:
            UserModel = get_user_model()
            try:
                user = UserModel.objects.get(pk=user.pk)
                session_id = request.data.get('session_id', None)
                if not session_id or session_id != user.session_id:
                    raise AuthenticationFailed('Invalid session ID')
            except UserModel.DoesNotExist:
                pass

        response = self.get_response(request)
        return response





class CustomSilkAuthorizationMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Check if the user is authenticated and authorized to view Silk


        if request.path.startswith('/silk/'):
            if request.user and request.user.is_superuser:
                return self.get_response(request)
            else:
                return HttpResponseForbidden("You are not authorized to view this page.")
        else:
            return self.get_response(request)
