import os
import logging
from datetime import datetime, timedelta
from pathlib import Path

import sentry_sdk
from decouple import Csv, config
from firebase_admin import initialize_app
from sentry_sdk.integrations.django import DjangoIntegration

from helpers.cloud import CloudMessaging, NotifyMessaging

# Initialise environment variables

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

cloud_messaging = CloudMessaging(BASE_DIR)
notify_messaging = NotifyMessaging()

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config("SECRET_KEY")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = int(config("DEBUG", default=False))

ALLOWED_HOSTS = config("ALLOWED_HOSTS", cast=Csv())

ENVIRONMENT = config("ENVIRONMENT")
# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.humanize",
    # RestFrameWork
    "rest_framework",
    "rest_framework.authtoken",
    "rest_framework_simplejwt.token_blacklist",
    # Import Export
    "import_export",
    # Apps
    "main",
    "pos_onboarding",
    "cards",
    "send_money",
    "loan_DB",
    "coral",
    "horizon_pay",
    "accounts",
    "kyc_app",
    "ussd",
    "admin_dashboard",
    "singlelogin",
    "retail",
    "apiCalls",
    # Others
    "drf_yasg",
    "djoser",
    "django_filters",
    # "django_cron",
    # Celery
    "django_celery_results",
    "django_celery_beat",
    "corsheaders",
    "agents_dashboard",
    "redisboard",
    "data_browser",
    "django_extensions",
    "rangefilter",
]

# CRON_CLASSES = [
#     "accounts.cron.PreviousDayTransactionCron",
#     "horizon_pay.cron.update.BankPerformanceCron",
#     "horizon_pay.cron.reset.BankPerformanceResetCron"
# ]

# CRONJOBS = [
#     ('0 0 * * *', 'accounts.cron.previousDayTransactionCron')
# ]

CORS_ALLOW_ALL_ORIGINS = True

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "singlelogin.middlewares.CheckBlackListJWTMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "singlelogin.middlewares.SingleLoginCaseMiddleware",
    "liberty_pay.middlewares.AuthLowerCaseMiddleware",
    # 'apiCalls.middleware.RequestTimingMiddleware',
    # "apiCalls.middleware.HandleAPIHitsMiddleware",
    # "liberty_pay.middlewares.CustomSilkAuthorizationMiddleware",
    # "silk.middleware.SilkyMiddleware",
    # "django_user_agents.middleware.UserAgentMiddleware",
]


if ENVIRONMENT == "development":
    INSTALLED_APPS.extend(["debug_toolbar"])

    # INSTALLED_APPS.extend(['silk'])

    # # Add the Debug Toolbar middleware to the MIDDLEWARE list
    MIDDLEWARE.insert(0, "debug_toolbar.middleware.DebugToolbarMiddleware")

    # silk_middlewares = [
    #     "liberty_pay.middlewares.CustomSilkAuthorizationMiddleware",
    #     "silk.middleware.SilkyMiddleware"
    # ]

    # MIDDLEWARE.extend(silk_middlewares)

    # Add your development machine's IP address to INTERNAL_IPS
    INTERNAL_IPS = [
        "127.0.0.1",
    ]

    # # SILK
    # SILKY_PYTHON_PROFILER = True
    # SILKY_AUTHENTICATION = True  # User must login
    # SILKY_AUTHORISATION = True  # User must have permissions
    # SILKY_PERMISSIONS = lambda user: user.is_superuser

    # SILKY_MAX_REQUEST_BODY_SIZE = -1  # Silk takes anything <0 as no limit
    # SILKY_MAX_RESPONSE_BODY_SIZE = 1024  # If response body>1024 bytes, ignore
    # SILKY_META = True
    # SILKY_INTERCEPT_PERCENT = 50 # log only 50% of requests


ROOT_URLCONF = "liberty_pay.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / "templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "liberty_pay.wsgi.application"

# Database
# https://docs.djangoproject.com/en/4.0/ref/settings/#databases


EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"

if DEBUG:
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.postgresql_psycopg2",
            "NAME": config("DATABASE_NAME"),
            "USER": config("DATABASE_USER"),
            "PASSWORD": config("DATABASE_PASSWORD"),
            "HOST": config("DATABASE_HOST"),
            "PORT": config("DATABASE_PORT"),
        }
    }
else:
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.postgresql_psycopg2",
            "NAME": config("DATABASE_NAME"),
            "USER": config("DATABASE_USER"),
            "PASSWORD": config("DATABASE_PASSWORD"),
            "HOST": config("DATABASE_HOST"),
            "PORT": config("DATABASE_PORT"),
        },
        # "slaveone": {
        #     "ENGINE": "django.db.backends.postgresql_psycopg2",
        #     "NAME": config("DATABASE_NAME"),
        #     "USER": config("DATABASE_USER"),
        #     "PASSWORD": config("DATABASE_PASSWORD"),
        #     "HOST": config("SLAVE_DATABASE_HOST"),
        #     "PORT": config("SLAVE_DATABASE_PORT"),
        #     "OPTIONS": {
        #         'options': '-c default_transaction_read_only=on',
        #     },
        # }
    }

    # DATABASE_ROUTERS=[
    # "liberty_pay.dbrouter.ReadWriteRouter"
    # ]


# Password validation
# https://docs.djangoproject.com/en/4.0/ref/settings/#auth-password-validators
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
        "OPTIONS": {
            "min_length": 6,
        },
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.0/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "Africa/Lagos"

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.0/howto/static-files/

STATIC_URL = "/static/"
STATIC_ROOT = os.path.join(BASE_DIR, "static")

# Base url to serve media files
# MEDIA_URL = "/media/"

# Path where media is stored
# MEDIA_ROOT = os.path.join(BASE_DIR, "media")
MEDIA_ROOT_2 = os.path.join(BASE_DIR, "media", "media")
MEDIA_URL_2 = "/media/media/"


# Default primary key field type
# https://docs.djangoproject.com/en/4.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

AUTH_USER_MODEL = "main.User"

LOGIN_URL = "/admin/login/"
APP_LOGIN_URL = "/login/create/"


if ENVIRONMENT == "production":
    sentry_sdk.init(
        dsn="https://<EMAIL>/6356604",
        # dsn="https://<EMAIL>/4506988717735936",
        integrations=[
            DjangoIntegration(),
        ],
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for performance monitoring.
        # We recommend adjusting this value in production.
        traces_sample_rate=0.001,
        # If you wish to associate users to errors (assuming you are using
        # django.contrib.auth) you may enable sending PII data.
        send_default_pii=True,
    )


DJOSER = {
    "LOGIN_FIELD": "email",
    "USER_CREATE_PASSWORD_RETYPE": True,
    "USERNAME_CHANGED_EMAIL_CONFIRMATION": True,
    "PASSWORD_CHANGED_EMAIL_CONFIRMATION": True,
    "SEND_ACTIVATION_EMAIL": True,
    "SEND_CONFIRMATION_EMAIL": True,
    "PASSWORD_RESET_SHOW_EMAIL_NOT_FOUND": True,
    "PASSWORD_RESET_CONFIRM_URL": "password/reset/confirm/?uid={uid}&token={token}",
    "USERNAME_RESET_CONFIRM_URL": "password/reset/confirm/{uid}/{token}",
    "ACTIVATION_URL": "activate/?uid={uid}&token={token}",
    "TOKEN_MODEL": "rest_framework.authtoken.models.Token",
    "SERIALIZERS": {
        "user_create": "main.serializers.UserCreateSerializer",
        "user": "main.serializers.UserCreateSerializer",
        "user_delete": "djoser.serializers.UserDeleteSerializer",
        # "token_create": "main.serializers.CustomTokenCreateSerializer",  # path to serializer
    },
}

IMPORT_EXPORT_USE_TRANSACTIONS = True

REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework_simplejwt.authentication.JWTAuthentication",
        "accounts.authentication.CustomTokenAuthentication",
    ),
}

SIMPLE_JWT = {
    # 'ACCESS_TOKEN_LIFETIME': timedelta(seconds=10),
    # 'ACCESS_TOKEN_LIFETIME': timedelta(minutes=50),
    # "ACCESS_TOKEN_LIFETIME": timedelta(days=5) if ENVIRONMENT == "production" else timedelta(days=100),
    # "REFRESH_TOKEN_LIFETIME": timedelta(days=5),
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=30) if ENVIRONMENT == "production" else timedelta(days=10),
    "REFRESH_TOKEN_LIFETIME": timedelta(minutes=60) if ENVIRONMENT == "production" else timedelta(days=15),
    "BLACKLIST_AFTER_ROTATION": True,
    "ROTATE_REFRESH_TOKENS": True,
}


APP_NAME = (
    ("Liberty Pay", "Liberty Pay"),
    ("Remita ussd", "Remita ussd"),
    ("Liberty paiy", "Liberty paiy"),
    ("Liberty Agency BVN OTP", "Liberty Agency BVN OTP"),
)

SECURITY_QUESTION_CHOICES = (
    ("What is your Mother's maiden name?", "What is your Mother's maiden name?"),
    ("What is the name of your first pet?", "What is the name of your first pet?"),
    ("What is your favourite meal?", "What is your favourite meal?"),
    ("What is your birth month?", "What is your birth month?"),
    ("What is the name of your favourite movie?", "What is the name of your favourite movie?"),
    ("What is the name of your favourite music?", "What is the name of your favourite music?"),
    ("What is the name of your crush?", "What is the name of your crush?"),
)


TRANSACTION_TYPE_CHOICES = (
    ("SEND_BUDDY", "SEND_BUDDY"),
    ("SEND_LOTTO_WALLET", "SEND_LOTTO_WALLET"),
    ("SEND_AJO_WALLET", "SEND_AJO_WALLET"),
    ("SEND_AJO_LOANS", "SEND_AJO_LOANS"),
    ("SEND_BANK_TRANSFER", "SEND_BANK_TRANSFER"),
    ("CARD_PURCHASE", "CARD_PURCHASE"),
    ("REFUND_CARD_PURCHASE", "REFUND_CARD_PURCHASE"),
    ("SEND_LIBERTY_COMMISSION", "SEND_LIBERTY_COMMISSION"),
    ("REVERSAL_BUDDY", "REVERSAL_BUDDY"),
    ("REVERSAL_BANK_TRANSFER", "REVERSAL_BANK_TRANSFER"),
    ("FUND_BUDDY", "FUND_BUDDY"),
    ("FUND_LOTTO_WALLET", "FUND_LOTTO_WALLET"),
    ("FUND_AJO_WALLET", "FUND_AJO_WALLET"),
    ("FUND_AJO_LOANS", "FUND_AJO_LOANS"),
    ("FUND_COLLECTION_ACCOUNT", "FUND_COLLECTION_ACCOUNT"),
    ("FUND_BANK_TRANSFER", "FUND_BANK_TRANSFER"),
    ("FUND_PAYSTACK", "FUND_PAYSTACK"),
    ("FUND_BY_USSD", "FUND_BY_USSD"),
    ("FUND_QRCODE", "FUND_QRCODE"),
    ("FUND_TRANSFER_FROM_COMMISSION", "FUND_TRANSFER_FROM_COMMISSION"),
    ("FUND_TRANSFER_FROM_OTHER_COMMISSION", "FUND_TRANSFER_FROM_OTHER_COMMISSION"),
    ("CARD_TRANSACTION_FUND", "CARD_TRANSACTION_FUND"),
    ("CARD_TRANSACTION_FUND_TRANSFER", "CARD_TRANSACTION_FUND_TRANSFER"),
    ("USSD_WITHDRAW", "USSD_WITHDRAW"),
    ("BILLS_AND_PAYMENT", "BILLS_AND_PAYMENT"),
    ("AIRTIME_PIN", "AIRTIME_PIN"),
    ("SEND_BACK_TO_FLOAT_TRANSFER", "SEND_BACK_TO_FLOAT_TRANSFER"),
    ("LOTTO_PLAY", "LOTTO_PLAY"),
    ("LIBERTY_LIFE_PAYMENT", "LIBERTY_LIFE_PAYMENT"),
    ("CREDI_LOAN", "CREDI_LOAN"),
    ("LOTTO_WINNING_CREDIT", "LOTTO_WINNING_CREDIT"),
    ("LOTTO_WINNING_DEBIT", "LOTTO_WINNING_DEBIT"),
)


DJANGO_SETTINGS_MODULE = config("DJANGO_SETTINGS_MODULE")
# DEBUG_PROPAGATE_EXCEPTIONS = True


USSD_SESSION_KEY_KEY = config("USSD_SESSION_KEY_KEY")


# TOGGLE BASE_URL
BASE_URL = config("BASE_URL")

# IN-HOUSE
FLOAT_USER_EMAIL = config("FLOAT_USER_EMAIL")
CARDS_USER_EMAIL = config("CARDS_USER_EMAIL")
LIBERTY_ASSURED_USER = config("LIBERTY_ASSURED_USER")
TERMINAL_PAYMENT_USER = config("TERMINAL_PAYMENT_USER")
CARD_WITHDRAW_AGENT_CHARGE = config("CARD_WITHDRAW_AGENT_CHARGE")
CARD_WITHDRAW_MERCHANT_CHARGE = config("CARD_WITHDRAW_MERCHANT_CHARGE")

PRINT_AIRTIME_BASE_URL = config("PRINT_AIRTIME_BASE_URL")
CORAL_PAY_BASE_URL = config("CORAL_PAY_BASE_URL")

LIBERTY_VAS_AUTH_USERNAME = config("LIBERTY_VAS_AUTH_USERNAME")
LIBERTY_VAS_AUTH_PASSWORD = config("LIBERTY_VAS_AUTH_PASSWORD")

EMEKA_ADMIN_PASSWORD = config("EMEKA_ADMIN_PASSWORD")

MARKETING_PAYSTACK_URL = config("MARKETING_PAYSTACK_URL")

# WHISPER
OTP_APP_ID = config("OTP_APP_ID")
OTP_MESSAGE_ID = config("OTP_MESSAGE_ID")

ONLY_SMS_OTP_APP_ID = config("ONLY_SMS_OTP_APP_ID")
ONLY_SMS_OTP_MESSAGE_ID = config("ONLY_SMS_OTP_MESSAGE_ID")

OTP_TOKEN = config("OTP_TOKEN")
WHISPER_KEY = config("WHISPER_KEY")
WHISPER_CHARGE = float(config("WHISPER_CHARGE"))
WHISPER_URL = config("WHISPER_URL")
LOTTO_GUARANTOR_VERF_URL = config("LOTTO_GUARANTOR_VERF_URL")

LIBERTY_AGENCY_RESET_PINS = config("LIBERTY_AGENCY_RESET_PINS")
LIBERTY_AGENCY_CREDIT_DEBIT_ALERT = config("LIBERTY_AGENCY_CREDIT_DEBIT_ALERT")
LIBERTY_PAY_ACCOUNT_NUMBER_CREATED_SMS = config("LIBERTY_PAY_ACCOUNT_NUMBER_CREATED_SMS")
REFUNDED_CLAIMS_SMS_TEMPLATE = config("REFUNDED_CLAIMS_SMS_TEMPLATE")

GUARANTOR_FORM_LINK = config("GUARANTOR_FORM_LINK")
GUARANTOR_FORM_LINK_AGENT = config("GUARANTOR_FORM_LINK_AGENT")
GUARANTOR_TEST_FIRST_NAME = config("GUARANTOR_TEST_FIRST_NAME")
GUARANTOR_TEST_LAST_NAME = config("GUARANTOR_TEST_LAST_NAME")
GUARANTOR_FORM_ADMIN_VERIFICATION_EMAIL = config("GUARANTOR_FORM_ADMIN_VERIFICATION_EMAIL")
GUARANTOR_CANCEL_LINK = config("GUARANTOR_CANCEL_LINK")
GUARANTOR_SMS_TEMPLATE = config("GUARANTOR_SMS_TEMPLATE")


# HADADA
HADADA_SECRET_KEY = config("HADADA_SECRET_KEY")

# PROVIDUS
PROVIDUS_SIGNATURE = config("PROVIDUS_SIGNATURE")
PROVIDUS_CLIENT_ID = config("PROVIDUS_CLIENT_ID")

# WOVEN
WOVEN_API_KEY = config("WOVEN_API_KEY_LIVE")
WOVEN_PAYOUT_PIN = config("WOVEN_PAYOUT_PIN")

# VFDBank
VFD_WEBHOOK_USERNAME = config("VFD_WEBHOOK_USERNAME")
VFD_WEBHOOK_PASSWORD = config("VFD_WEBHOOK_PASSWORD")
VFD_WEBHOOK_AUTH = config("VFD_WEBHOOK_AUTH")

if ENVIRONMENT == "production":
    VFD_BASE_URL = config("VFD_BASE_URL_LIVE")
    VFD_ACCOUNT_CREATION_BASE_URL_LIVE = config("VFD_ACCOUNT_CREATION_BASE_URL_LIVE")
    VFD_ACCESS_TOKEN = config("VFD_ACCESS_TOKEN_LIVE")
    VFD_WALLET_CREDENTIALS = config("VFD_WALLET_CREDENTIALS_LIVE")

else:
    VFD_BASE_URL = config("VFD_BASE_URL_TEST")
    VFD_ACCOUNT_CREATION_BASE_URL_LIVE = config("VFD_ACCOUNT_CREATION_BASE_URL_LIVE_TEST")
    VFD_ACCESS_TOKEN = config("VFD_ACCESS_TOKEN_TEST")
    VFD_WALLET_CREDENTIALS = config("VFD_WALLET_CREDENTIALS_TEST")


# PARALEX BANK
PARALLEX_BASE_URL = config("PARALLEX_BASE_URL")
PARALLEX_KEY = config("PARALLEX_KEY")
PARALLEX_IV = config("PARALLEX_IV")
PARALLEX_USERNAME = config("PARALLEX_USERNAME")
PARALLEX_PASSWORD = config("PARALLEX_PASSWORD")
PARALLEX_AGENT_ID = config("PARALLEX_AGENT_ID")

PARALLEX_NOTIFY_KEY = config("PARALLEX_NOTIFY_KEY")
PARALLEX_NOTIFY_IV = config("PARALLEX_NOTIFY_IV")


# HERITAGE BANK
HERITAGE_API_KEY = config("HERITAGE_API_KEY")
HERITAGE_SECRET_KEY = config("HERITAGE_SECRET_KEY")

# MetaMap
METAMAP_CLIENT_ID = config("METAMAP_CLIENT_ID")
METAMAP_CLIENT_SECRET = config("METAMAP_CLIENT_SECRET")
METAMAP_WEBHOOK_SECRET = config("METAMAP_WEBHOOK_SECRET")
METAMAP_WEBHOOK_SECRET_HASH = config("METAMAP_WEBHOOK_SECRET_HASH")


# LIBERTY MARKETING USSD
POS_AUTH_TOKEN = config("POS_AUTH_TOKEN")
TRANSACTION_VERIFICATION_BACEKEND_URL = config("TRANSACTION_VERIFICATION_BACEKEND_URL")
VERIFY_TRANSFER_LOCALLY = config("VERIFY_TRANSFER_LOCALLY")
OTHERS_SERVICES_AUTH_PERMISSION = config("OTHERS_SERVICES_AUTH_PERMISSION")
OTHERS_SERVICES_SUPERTOKEN = config("OTHERS_SERVICES_SUPERTOKEN")

# SENDGRID
SENDGRID_API_KEY = config("SENDGRID_API_KEY")

# HORIZON
HORIZON_COOKIE_TOKEN = config("HORIZON_COOKIE_TOKEN")
HORIZON_BEARER = config("HORIZON_BEARER")
HORIZON_PAY_URL = config("HORIZON_PAY_URL")

# PAYSTACK
PAYSTACK_API_SECRET = config("PAYSTACK_API_SECRET")

LOANDISK_PUBLIC_KEY = config("LOANDISK_PUBLIC_KEY")
LOANDISK_AUTH_TOKEN = config("LOANDISK_AUTH_TOKEN")

# LIBERTY CREDI LOANS
LIBERTY_LOANS_TOKEN = config("LIBERTY_LOANS_TOKEN")

CARD_LOAD_TOK = config("CARD_LOAD_TOK")
CARD_LOAD_TOKONE = config("CARD_LOAD_TOKONE")

# UVERIFY
UVERIFY_TOKEN = config("UVERIFY_TOKEN")

# WEMA BANK
WEMA_BANK_ACCOUNT_PASSWORD = config("WEMA_BANK_ACCOUNT_PASSWORD")
WEMA_PASSWORD = config("WEMA_PASSWORD")
WEMA_BANK_AES_KEY = config("WEMA_BANK_AES_KEY")
WEMA_BANK_AES_IV = config("WEMA_BANK_AES_IV")

# FRESHWORKS
FRESHWORKS_API_KEY = config("FRESHWORKS_API_KEY")

# SAVINGS
AJO_BASE_URL = config("AJO_BASE_URL")
AJO_MULTI_AUTH = config("AJO_MULTI_AUTH")

# FRESHWORKS
MAILGUN_API_KEY = config("MAILGUN_API_KEY")

# PICKY ASSIST
PICKY_ASSIST_TOKEN = config("PICKY_ASSIST_TOKEN")

# CELERY
CELERY_BROKER_URL = "redis://127.0.0.1:6379"
CELERY_ACCEPT_CONTENT = ["application/json"]
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_SERIALIZER = "json"
CELERY_TIMEZONE = TIME_ZONE
CELERY_RESULT_BACKEND = "django-db"
# CELERY_RESULT_BACKEND = "redis://localhost:6379"

# CELERY BEAT
CELERY_BEAT_SCHEDULER = "django_celery_beat.schedulers:DatabaseScheduler"

# LIBERTY POS MARKETING BASE URL
LIBERTY_MARKETING_URL = config("LIBERTY_MARKETING_URL")


UVERIFY_TOKEN = config("UVERIFY_TOKEN")
MARKETING_USSD_CODE = config("MARKETING_USSD_CODE")

# Digital Ocean Spaces
# if ENVIRONMENT == "development":
#     AWS_S3_ACCESS_KEY_ID = config("TEST_AWS_S3_ACCESS_KEY_ID")
#     AWS_S3_SECRET_ACCESS_KEY = config("TEST_AWS_S3_SECRET_ACCESS_KEY")
#     AWS_STORAGE_BUCKET_NAME = config("TEST_AWS_STORAGE_BUCKET_NAME")
#     AWS_S3_ENDPOINT_URL = config("TEST_AWS_S3_ENDPOINT_URL")
# else:

AWS_ACCESS_KEY_ID = config("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = config("AWS_SECRET_ACCESS_KEY")
AWS_REGION = config("AWS_REGION")


AWS_S3_ACCESS_KEY_ID = config("AWS_S3_ACCESS_KEY_ID")
AWS_S3_SECRET_ACCESS_KEY = config("AWS_S3_SECRET_ACCESS_KEY")
AWS_STORAGE_BUCKET_NAME = config("AWS_STORAGE_BUCKET_NAME")
AWS_S3_ENDPOINT_URL = config("AWS_S3_ENDPOINT_URL")

# AWS_S3_CUSTOM_DOMAIN = config("AWS_S3_CUSTOM_DOMAIN")
# I enabled the CDN, so you get a custom domain. Use the end point in the AWS_S3_CUSTOM_DOMAIN setting.
AWS_S3_OBJECT_PARAMETERS = {
    "CacheControl": "max-age=86400",
}
# AWS_DEFAULT_ACL = 'public-read'
DEFAULT_FILE_STORAGE = "custom_storages.MediaStorage"

# Use AWS_S3_ENDPOINT_URL here if you haven't enabled the CDN and got a custom domain.
MEDIA_URL = "{}/{}/".format(AWS_S3_ENDPOINT_URL, "media")
MEDIA_ROOT = MEDIA_URL
# MEDIA_URL = f'{AWS_S3_ENDPOINT_URL}/'


#### Rewards
TRANSACTION_REWARD_PERCENTAGE = config("TRANSACTION_REWARD_PERCENTAGE")
WEEKLY_REWARD_TARGET_COIN = config("WEEKLY_REWARD_TARGET_COIN")
DAILY_REWARD_TARGET_COIN = config("DAILY_REWARD_TARGET_COIN")

# CYBER PAY
if ENVIRONMENT == "development":
    CYBER_PAY_INTEGRATION_KEY = config("CYBER_PAY_INTEGRATION_KEY_TEST")
    CYBERPAY_RETURN_URL = config("CYBERPAY_RETURN_URL_TEST")
    CYBERPAY_WEBHOOK_URL = config("CYBERPAY_WEBHOOK_URL_TEST")
    CYBERPAY_BASE_URL = config("CYBERPAY_BASE_URL_TEST")
else:
    CYBER_PAY_INTEGRATION_KEY = config("CYBER_PAY_INTEGRATION_KEY")
    CYBERPAY_RETURN_URL = config("CYBERPAY_RETURN_URL")
    CYBERPAY_WEBHOOK_URL = config("CYBERPAY_WEBHOOK_URL")
    CYBERPAY_BASE_URL = config("CYBERPAY_BASE_URL")


# Template for Pos Agents Inactivity Notification
AGENT_ACTIVITIES_SMS_TEMPLATE = config("AGENT_ACTIVITIES_SMS_TEMPLATE")

# NOTIFY APP FBK
NOTIFY_APP_FBK_SERVER_TOKEN = config("NOTIFY_APP_FBK_SERVER_TOKEN")

if ENVIRONMENT == "development":
    SUDO_API_KEY = config("SUDO_API_KEY_TEST")
else:
    SUDO_API_KEY = config("SUDO_API_KEY")

SUDO_FUNDING_SOURCE_ID = config("SUDO_FUNDING_SOURCE_ID")
SUDO_DEBIT_ACCOUNT_ID = config("SUDO_DEBIT_ACCOUNT_ID")
SUDO_LIVE_BASE_URL = config("SUDO_LIVE_BASE_URL")
SUDO_AUTH_HEADER = config("SUDO_AUTH_HEADER")
VGS_USERNAME = config("VGS_USERNAME")
VGS_PASSWORD = config("VGS_PASSWORD")

IDENTITYPASS_TEST_BASE_URL = config("IDENTITYPASS_TEST_BASE_URL")
IDENTITYPASS_TEST_SECRET_KEY = config("IDENTITYPASS_TEST_SECRET_KEY")
IDENTITYPASS_LIVE_BASE_URL = config("IDENTITYPASS_LIVE_BASE_URL")
IDENTITYPASS_LIVE_SECRET_KEY = config("IDENTITYPASS_LIVE_SECRET_KEY")
IDENTITYPASS_APP_ID = config("IDENTITYPASS_APP_ID")

# CORAL USSD WITHDRAWAL
CORALPAY_WITHDRAWAL_USSD_MERCHANT_ID = config("CORALPAY_WITHDRAWAL_USSD_MERCHANT_ID")
CORALPAY_WITHDRAWAL_USSD_TERMINAL_ID = config("CORALPAY_WITHDRAWAL_USSD_TERMINAL_ID")
CORALPAY_WITHDRAWAL_USSD_TOKEN = config("CORALPAY_WITHDRAWAL_USSD_TOKEN")
CORALPAY_WITHDRAWAL_USSD_USERNAME = config("CORALPAY_WITHDRAWAL_USSD_USERNAME")
CORALPAY_WITHDRAWAL_USSD_PASSWORD = config("CORALPAY_WITHDRAWAL_USSD_PASSWORD")
CORALPAY_WITHDRAWAL_USSD_KEY = config("CORALPAY_WITHDRAWAL_USSD_KEY")


# COREBANKING
if ENVIRONMENT == "production":
    CORE_BANKING_BASE_URL = config("CORE_BANKING_BASE_URL")
else:
    CORE_BANKING_BASE_URL = config("CORE_BANKING_BASE_URL_TEST")

CORE_BANKING_EMAIL = config("CORE_BANKING_EMAIL")
CORE_BANKING_PASSWORD = config("CORE_BANKING_PASSWORD")


if ENVIRONMENT == "development":
    CARDS_APP_BASE_URL = config("CARDS_APP_BASE_URL_DEV")
else:
    CARDS_APP_BASE_URL = config("CARDS_APP_BASE_URL_PROD")

CARDS_APP_LOGIN_USERNAME = config("CARDS_APP_LOGIN_USERNAME")
CARDS_APP_LOGIN_PASSWORD = config("CARDS_APP_LOGIN_PASSWORD")


# Google API Credentials
GOOGLE_SHEET_CREDENTIALS = config("GOOGLE_SHEET_CREDENTIALS")
GOOGLE_SHEET_LINK = config("GOOGLE_SHEET_LINK")

LIB_PAY_API_KEY = config("LIB_PAY_API_KEY")

# Get Response
GETRESPONSE_API_KEY = config("GETRESPONSE_API_KEY")

BILLS_USER_EMAIL = config("BILLS_USER_EMAIL")
BILLS_USER_PASSWORD = config("BILLS_USER_PASSWORD")
BILLS_USER_PIN = config("BILLS_USER_PIN")

# onboarding only
WHISPER_ONBOARDING_KEY = config("WHISPER_ONBOARDING_KEY")

# CACHE
CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.redis.RedisCache",
        "LOCATION": [
            "redis://127.0.0.1:6379",  # leader
            # "redis://127.0.0.1:6378",  # read-replica 1
            # "redis://127.0.0.1:6377",  # read-replica 2
        ],
    }
}

# DOJAH API
DOJAH_APP_ID = config("DOJAH_APP_ID")
DOJAH_SECRET_KEY = config("DOJAH_SECRET_KEY")

# LIBERTY DRAW
LIBERTY_DRAW_BASE_URL = config("LIBERTY_DRAW_BASE_URL")

# Always use IPython for shell_plus
SHELL_PLUS = "ipython"
SHELL_PLUS_PRINT_SQL = False

# Truncate sql queries to this number of characters (this is the default)
SHELL_PLUS_PRINT_SQL_TRUNCATE = 200

# Specify sqlparse configuration options when printing sql queries to the console
SHELL_PLUS_SQLPARSE_FORMAT_KWARGS = dict(
  reindent_aligned=True,
  truncate_strings=500,
)

SHELL_PLUS_MODEL_IMPORTS_RESOLVER = 'django_extensions.collision_resolvers.AppLabelPrefixCR'


# LOG_DIR = os.path.join('logs')
# os.makedirs(LOG_DIR, exist_ok=True)
# logging.basicConfig(
#     filename=os.path.join(LOG_DIR, 'libertypay.log'),
#     filemode='a',
#     level=logging.DEBUG,
#     format='[{asctime}] {levelname} {module} {thread:d} - {message}',
#     datefmt='%d-%m-%Y %H:%M:%S',
#     style='{',
# )
#
# # Logging
# LOGGING = {
#     'version': 1,
#     'disable_existing_loggers': False,
#     'formatters': {
#         'verbose': {
#             'format': '[{asctime}] {levelname} {module} {thread:d} - {message}',
#             'style': '{',
#             'datefmt': '%d-%m-%Y %H:%M:%S'
#         },
#     },
#     'handlers': {
#         'file': {
#             'level': 'INFO',
#             'class': 'logging.handlers.TimedRotatingFileHandler',
#             'filename': os.path.join(LOG_DIR, 'libertypay.log'),
#             'when': 'midnight',
#             'interval': 1,  # daily
#             'backupCount': 7,  # Keep logs for 7 days
#             'formatter': 'verbose',
#         }
#     },
#     'root': {
#         'handlers': ['file'],
#         'level': 'INFO',
#     },
#     'loggers': {
#         'django': {
#             'handlers': ['file'],
#             'level': 'INFO',
#             'propagate': True,
#         },
#         'django.server': {
#             'handlers': ['file'],
#             'level': 'INFO',
#             'propagate': True,
#         },
#         'django.request': {
#             'handlers': ['file'],
#             'level': 'INFO',
#             'propagate': True,
#         },
#     },
# }



