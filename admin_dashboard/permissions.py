from rest_framework.exceptions import APIException
from rest_framework import status, permissions
from main.models import User
from admin_dashboard.role.models import Role
import ast


######################################
# Permissions
######################################

class DashboardViewPermission(permissions.BasePermission):
    
    def has_permission(self, request, view):
        if request.user.role:
            try:
                role = Role.objects.get(unique_id=request.user.role)
                permissions = ast.literal_eval(role.permissions)
                if 'DASHBOARD' in permissions:
                    return True
            except Role.DoesNotExist:
                raise AdminException()
        raise AdminException()


class CustomerViewPermission(permissions.BasePermission):
    
    def has_permission(self, request, view):
        if request.user.role:
            try:
                role = Role.objects.get(unique_id=request.user.role)
                permissions = ast.literal_eval(role.permissions)
                if 'CUSTOMERS' in permissions:
                    return True
            except Role.DoesNotExist:
                raise AdminException()
        raise AdminException()

class AgentPermission(permissions.BasePermission):

    def has_permission(self, request, view):
        if request.user.role:
            try:
                role = Role.objects.get(unique_id=request.user.role)
                permissions = ast.literal_eval(role.permissions)
                if 'AGENTS' in permissions:
                    return True
            except Role.DoesNotExist:
                raise AdminException()
        raise AdminException()


class MerchantViewPermission(permissions.BasePermission):

    def has_permission(self, request, view):
        if request.user.role:
            try:
                role = Role.objects.get(unique_id=request.user.role)
                permissions = ast.literal_eval(role.permissions)
                if 'MERCHANTS' in permissions:
                    return True
            except Role.DoesNotExist:
                raise AdminException()
        raise AdminException()

class TransactionViewPermission(permissions.BasePermission):

    def has_permission(self, request, view):
        if request.user.role:
            try:
                role = Role.objects.get(unique_id=request.user.role)
                permissions = ast.literal_eval(role.permissions)
                if 'TRANSACTIONS' in permissions:
                    return True
            except Role.DoesNotExist:
                raise AdminException()
        raise AdminException()


class TerminalViewPermission(permissions.BasePermission):

    def has_permission(self, request, view):
        if request.user.role:
            try:
                role = Role.objects.get(unique_id=request.user.role)
                permissions = ast.literal_eval(role.permissions)
                if 'TERMINALS' in permissions:
                    return True
            except Role.DoesNotExist:
                raise AdminException()
        raise AdminException()


class SalesViewPermission(permissions.BasePermission):

    def has_permission(self, request, view):
        if request.user.role:
            try:
                role = Role.objects.get(unique_id=request.user.role)
                permissions = ast.literal_eval(role.permissions)
                if 'SALES' in permissions:
                    return True
            except Role.DoesNotExist:
                raise AdminException()
        raise AdminException()


class DisputeViewPermission(permissions.BasePermission):

    def has_permission(self, request, view):
        if request.user.role:
            try:
                role = Role.objects.get(unique_id=request.user.role)
                permissions = ast.literal_eval(role.permissions)
                if 'DISPUTES' in permissions:
                    return True
            except Role.DoesNotExist:
                raise AdminException()
        raise AdminException()


class RoleViewPermission(permissions.BasePermission):
    
    def has_permission(self, request, view):
        if request.user.role:
            try:
                role = Role.objects.get(unique_id=request.user.role)
                permissions = ast.literal_eval(role.permissions)
                if 'ROLES' in permissions:
                    return True
            except Role.DoesNotExist:
                raise AdminException()
        raise AdminException()


class LogViewPermission(permissions.BasePermission):

    def has_permission(self, request, view):
        if request.user.role:
            try:
                role = Role.objects.get(unique_id=request.user.role)
                permissions = ast.literal_eval(role.permissions)
                if 'LOG' in permissions:
                    return True
            except Role.DoesNotExist:
                raise AdminException()
        raise AdminException()


class SettingsViewPermission(permissions.BasePermission):

    def has_permission(self, request, view):
        if request.user.role:
            try:
                role = Role.objects.get(unique_id=request.user.role)
                permissions = ast.literal_eval(role.permissions)
                if 'SETTINGS' in permissions:
                    return True
            except Role.DoesNotExist:
                raise AdminException()
        raise AdminException()


class ApproveRequisitionPermission(permissions.BasePermission):
    def has_permission(self, request, view):
        if request.user.role:
            try:
                role = Role.objects.get(unique_id=request.user.role)
                permissions = ast.literal_eval(role.permissions)
                if 'APPROVE_REQUISITION' in permissions:
                    return True
            except Role.DoesNotExist:
                raise AdminException()
        raise AdminException()


######################################
# Exceptions
######################################

class AdminException(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "You are not permitted to access resource",
    }
    default_code = "Not permitted"