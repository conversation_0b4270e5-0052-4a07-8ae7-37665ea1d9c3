from django.urls import path
from main import views

from admin_dashboard import views
from admin_dashboard.stock import views as stock_view

stock_url = [
    path("create_stock/", stock_view.CreateStockView.as_view()),
    path("stock_count/", stock_view.AllStocksCountView.as_view()),
    path("all_stocks/", stock_view.StocksView.as_view()),
    path("stocks_history/", stock_view.StockHistoryView.as_view()),
    path("stock_request_details/", stock_view.StockRequestDetailsView.as_view()),
    path("stock_aprroval/", stock_view.StockRequestApprovalView.as_view())
]



customer_urls = [
    path("customer_count/", views.CustomersCountView.as_view()),
    path("all_customers/", views.AllCustomersView.as_view()),
    path("active_customers/", views.ActiveCustomersView.as_view()),
    path("inactive_customers/", views.InActiveCustomersView.as_view()),
    path("new_customers/", views.NewCustomersView.as_view()),
    path("churn_customers/", views.ChurnCustomersView.as_view()),
    path("suspended_customers/", views.SuspendedCustomersView.as_view()),
    path("customer_details/<id>", views.CustomerDetailsView.as_view()),
    path("customer_wallet_balance/<id>", views.CustomerWalletsBalanceView.as_view()),
    path("customer_transactions/<id>", views.CustomerTransactionsView.as_view()),
    path("get_customer_transaction_details/", views.GetCustomerTransactionDetails.as_view()),
    path("sales_rep_onboarding/", views.SalesRepOnboardingView.as_view()),
    path("incomplete_kyc_customers", views.IncompleteKycView.as_view()),
    path("incomplete_customers_registration", views.IncompleteCustomerRegistrationsView.as_view()),
    path("suspend_customer/<id>", views.SuspendCustomerView.as_view()),
]
   

dashboard_urls = [
    path("dashboard_data/", views.DashboardAPIView.as_view()),
    path("dashboard_transaction_comparatives", views.DashboardTransactionComparatives.as_view()),
    path("dashboard_cashout_comparatives", views.DashboardCashoutComparatives.as_view()),
    path("dashboard_transfer_comparatives", views.DashboardTransfersComparatives.as_view()),
    path("dashboard_top_transactions_comparatives", views.DashboardTopTransactionsComparatives.as_view()),
    path("dashboard_transactions_comparatives_charts", views.DashboardTopTransactionsComparativesChart.as_view()),
    path("dashboard_chart_terminals", views.DashboardChartTerminalsView.as_view()),
    path("dashboard_commissions", views.DashboardAgentCommissionView.as_view()),
    path("dashboard_chart_users", views.DashboardUsersChartView.as_view()),
    path("dashboard_chart_transactions_amount", views.DashboardChartsTransactionAmountView.as_view()),
    path("dashboard_chart_transactions_count", views.DashboardChartsTransactionCountView.as_view()),
    path("dashboard_chart_commissions", views.DashboardChartsCommissionView.as_view()),
    path("dashboard_chart_average_commissions", views.DashboardChartsAverageCommissionView.as_view()),
    path("dashboard_wallet_transactions_details", views.DashboardWalletTransactionsDetails.as_view()),
    path("dashboard_float_accounts", views.DashboardFloatAccountsView.as_view()),
    path("dashboard_two_data", views.DashboardTwoView.as_view()),
    path("dashboard_transaction_activities", views.DashboardTransactionActivitesView.as_view()),
    path("dashboard_service_quality", views.ServiceQualityView.as_view()),
    path("customer_bank_statement", views.CustomerAccountStatementView.as_view())
    ]


transaction_urls = [
    path("transactions_lists", views.TransactionListView.as_view()),
    # path("transactions_amounts", views.TransactionsAmountOverView.as_view()),
    # path("transactions_counts", views.TransactionsCountsOverview.as_view()),
    path("transaction/<pk>", views.TransactionDetailView.as_view()),
    path("other_commissions", views.OtherCommissionView.as_view()),
    # path("transaction_activities_report", views.TransactionActivitesReport.as_view()),
    # path("agent_activities_report", views.ManualSendAgentActivitiesView.as_view()),
]

terminal_status_urls = [
    path("terminal_status_overview", views.TerminalStatusOverView.as_view()),
    #path("terminal_status_table", views.TerminalStatusTable.as_view()),
    path("terminal_agent_details", views.TerminalAgentDetailsView.as_view()),
    path("terminal_performance", views.TerminalPerformance.as_view()),
    path("active_terminal_status", views.ActiveTerminalStatus.as_view()),
    path("inactive_terminal_status", views.InactiveTerminalStatus.as_view()),
    path("suspended_terminal_status", views.SuspendedTerminalsView.as_view()),
    path("dormant_terminal_status", views.DormantTerminalsView.as_view()),
    path("terminal_status_details/<id>", views.TerminalDetailsView.as_view()),
    path("terminal_status_suspend_terminal/<id>", views.SuspendTerminalView.as_view()),
]

pos_agents_urls = [
    path("agent_total_transactions", views.TransactionAmountDaily.as_view()),
    path("pos_agents_details", views.PosAgentDetailsView.as_view()),
    path("pos_agent_wallet/<id>", views.PosAgentWalletView.as_view()),
    path("pos_agent_transactions_list/<id>", views.PosAgentTransactionsView.as_view()),
    path("pos_agent_active_sessions/<id>", views.PosAgentActiveSessionsView.as_view()),
    path("pos_agent_disputes_list/<id>", views.PosAgentDisputesListView.as_view()),
    path("pos_agent_disputes_detail/<id>", views.PosAgentDisputeDetailView.as_view()),
    path("pos_agent_details_form/<id>", views.PosAgentFormDetailsView.as_view()),
    path("pos_agent_send_email/<id>", views.SendPosAgentEmail.as_view()),
    path("pos_agent_send_sms/<id>", views.SendPosAgentSMS.as_view()),
    # path("send_agent_activities_email", views.ManualSendAgentActivitiesView.as_view()),
    # path("prospective-agents", views.Pros)
]

dispute_urls = [
    path("disputes_list", views.DisputeListView.as_view()),
    path("customer_disputes_list", views.CustomerDisputeListView.as_view()),
    path("dispute/create_dispute", views.DisputeCreateView.as_view()),
    path("disputes_overview", views.DisputesOverview.as_view()),
    path("dispute/detail/<pk>", views.DisputeDetailView.as_view()),
    path("dispute/resolve/<id>", views.DisputeResolveView.as_view()),
    path("dispute/export", views.DisputeResourceExport.as_view()),
    path("dispute/import", views.DisputeImportResource.as_view()),
    path("dispute_batch_upload", views.AgentDisputeBatchUpload.as_view()),
    path("dispute_batch_upload_list", views.BatchDisputesUploadList.as_view()),
    path("dispute_batch_upload_details/<id>/<stan>", views.BatchDisputesUploadDetails.as_view()),
    path("admin_create_bulk_disputes", views.AdminCreateBulkDisputes.as_view())
]


log_history_urls = [
    path("log_history_list", views.LogHistoryListView.as_view()),
    path("loghistory_detail/<id>", views.LogHistoryDetailView.as_view()),
    path("audit_trail", views.AuditTrailView.as_view())
]

dashboard_sales_urls = [
        #path("sales_reps_count", "")
]

stock_urls = [
    path("branch/import/<str:filename>/", views.StockUploadView.as_view()),
    path("input_terminal_details", views.InputTerminalDetails.as_view()),
    path("stock_providers", views.StockProvidersView.as_view()),
    path("assign_single_terminal", views.AssignSingleTerminalView.as_view()),
    path("assign_multiple_terminals", views.AssignMultipleTerminalView.as_view()),
    path("stock_head_office_zones", views.StockHeadOfficeZonesView.as_view()),
    path("stock_head_office_data", views.StockHeadOfficeDashboardDataView.as_view()),
    path("device_stock_details/<int:id>", views.DeviceStockDetailsView.as_view()),
    path("download_sample_stock_upload", views.DownloadSampleExcelFile.as_view()),
    path("zone_stock_details/<int:id>", views.ZoneStockDetails.as_view()),
    path("zone_branch_details/<int:id>", views.ZoneBranchDetails.as_view()),
    path("reassign_single_terminal", views.ReAssignTerminal.as_view()),
    path("update_terminal_location/<int:id>", views.TerminalStatusAction.as_view()),
    path("state_terminal_details/<int:id>", views.StateTerminalsDetails.as_view()),
    path("state_all_terminal/<int:id>", views.StateAllTerminalsView.as_view()),
    path("delayed_terminals", views.DelayedTerminalsView.as_view()),
    path("all_terminal_trails", views.AllTerminalTrailsView.as_view()),
    path("branch_terminal_trails/<int:id>", views.TerminalTrailView.as_view()),
    path("zone_all_terminals_trail/<int:id>", views.ZoneTerminalTrailView.as_view()),
    path("terminal_trail_detail/<int:id>", views.TerminalTrailDetailsView.as_view()),
    path("individual_terminal_trail_detail/<int:id>", views.IndividualTerminalTrailView.as_view()),
    path("zone_head_branches/<int:id>", views.ZoneHeadBranchesView.as_view()),
    path("zone_head_terminal_trail_details/<int:id>", views.ZoneHeadTerminalTrailDetails.as_view()),
    path("set_stock_in_transit", views.SetStockInTransit.as_view()),
    path("stock_request_approval/<id>", views.StockRequestApprovalView.as_view()),
    path("stock_request_disapproval/<id>", views.StockRequestDisApprovalView.as_view()),
]

terminal_management_urls = [
    path("zone_delivery_request/<id>", views.ZoneDeliveryRequestViewDetails.as_view()),
    # path("zone_stockin_request/<id>", views.ZoneStockInRequestDetails.as_view()),
    path("zones_delivery_requests", views.ZonesDeliveryRequestView.as_view()),
    path("zones_stockin_requests", views.ZonesStockInRequestView.as_view()),
    path("zones_stockin_request_details/<id>", views.ZoneStockInRequestDetails.as_view()),
    path("branch_stockin_requests_details/<branch_id>", views.BranchStockInRequestDetails.as_view()),
    path("zones_list", views.ZonesListView.as_view()),
    path("branches_list/<id>", views.BranchListView.as_view()),
]

roles_urls = [
    path("roles/", views.RoleView.as_view()),
    path("roles/tag/<roleId>", views.RoleTagUpdateView.as_view()),
    path("roles/permissions/add/<roleId>", views.RolePermissionsAddView.as_view()),
    path("roles/permissions/remove/<roleId>", views.RolePermissionsRemoveView.as_view()),
    path("user/", views.UserRolesView.as_view()),
    path("roles/user_details/<id>", views.UserRoleDetails.as_view()),
    path("roles/role_details/<id>", views.RoleDetailsView.as_view()),
    path("roles/user_details", views.AuthUserRoleDetails.as_view()),
]


stock_history_urls = [
    path("stock_history_details", views.StockHistoryDetailsView.as_view()),
    path("stock_provider_history_details/<id>", views.StockHistoryDetailView.as_view()),
    path("branch_stock_details/<id>", views.BranchStockDetailsView.as_view()),
    path("zones_stock_request_history", views.ZoneStockRequestHistory.as_view()),
    path("zones_stock_request_history_details/<id>", views.ZoneStockRequestHistoryDetail.as_view()),
    path("stock_history_history", views.StockHistoryView.as_view()),
    path("stock_history_detail/<id>", views.StockHistoryDetailMoreView.as_view()),
]

extra_urls = [
    path("reports/card_transactions", views.CardTransactionReportView.as_view())
]

sales_dashboard_urls = [
    path("sales_table", views.SalesTableView.as_view()),
    path("sales_table_overview", views.SalesViewOverview.as_view()),
    # path('monthly_pos_sales', views.MonthlyPosSalesView.as_view()),
    path('terminal_sold', views.TerminalsSoldView.as_view()),
    path('transaction_count', views.TransactionCountView.as_view()),
    path('transaction_amount', views.TransactionAmountView.as_view()),
    path('representative_data', views.RepresentativeDataView.as_view())
]

sales_rep_n_stock_urls = [
    path("sales_rep/total_sold_bar_chart/", views.SalesRepBarView.as_view()),
    path("sales_rep/", views.SalesRepDetailView.as_view()),
    path("sales_rep/agents_details/", views.AgentDetails.as_view()),
    path("sales_rep/<sales_rep_id>/sales_representatives", views.SalesRepresentativeView.as_view()),
    path('sales_rep/prospective_agents', views.ProspectiveAgentsView.as_view()),
    path('sales_rep/terminals', views.SalesRepTerminalsView.as_view()),
    path('sales_rep/request_history', views.RequestHistoryView.as_view()),
    path('sales_rep/wallet_history', views.WalletHistoryView.as_view()),
    path('sales_rep/disputes', views.SalesRepDisputesView.as_view()),
    path('sales_rep/profile', views.SalesRepFullProfileView.as_view()),
    path('sales_rep/profile/<id>', views.SalesRepFullProfileDetailView.as_view()),
    path("sales_rep/withdraw/", views.SalesRepWithdraw.as_view()),
    path("stock/suspend/<str:terminal_id>/", views.SuspendStock.as_view()),
    path("stock/remove_suspension/<str:terminal_id>/", views.RemoveStockSuspension.as_view()),
    path("stock/recover/<str:terminal_id>/", views.RecoverStock.as_view()),
    path("stock/restore/<str:terminal_id>/", views.RestoreStock.as_view()),
    path("stock_request/", views.InitStockRequest.as_view()),
    path("stock_request/<int:stock_request_id>/", views.StockRequestView.as_view()),
    path("sales/sales_rep_zones", views.SalesRepZonesView.as_view()),
    path("sales/sales_rep_zones_overview", views.SalesRepZonesOverview.as_view()),
    path("sales/sales_rep_zones_details/<id>", views.SalesZoneDetailsView.as_view()),
    # path("stock_request/", views.SalesRepStockRequest.as_view()),
    # path("stock_request_history/", views.SalesRepStockRequestCount.as_view()),
    # path("assigned_terminals/", views.AssignedTerminalView.as_view()),
    # path("sales_overview", views.SalesOverview.as_view())
    path("suspended_terminals_list", views.SuspendedTerminalsList.as_view()),
    path("recovered_stocks_list", views.RecoveredTerminalsList.as_view()),
    path("branch_head_salesreps", views.SuperAgentSalesReps.as_view())
]

merchant_urls = [
    path("merchants", views.MerchantsTopTransactions.as_view()),
    path("merchants/details", views.MerchantsDetailsView.as_view())
]

webhook_urls = [
    path("lotto-agents-suspension-webhook", views.LottoAgentsSuspensionView.as_view())
]

urlpatterns = [
    *stock_url,
    *customer_urls,
    *dashboard_urls,
    *sales_rep_n_stock_urls,
    *transaction_urls,
    *terminal_status_urls,
    *dispute_urls,
    *dashboard_sales_urls,
    *log_history_urls,
    *extra_urls,
    *sales_dashboard_urls,
    *pos_agents_urls,
    *stock_urls,
    *roles_urls,
    *stock_history_urls,
    *terminal_management_urls,
    *merchant_urls,
    *webhook_urls,

    path("all_prospective_agent/", views.AllProspectiveAgentView.as_view()),
    path("get_one_prospective_agent/<id>", views.GetOneProspectiveAgentView.as_view()),
    path("prospective_agent/send_sms/<id>", views.SendProspectiveAgentSMS.as_view()),
    path("prospective_agent/send_email/<id>", views.SendProspectiveAgentEmail.as_view()),
    path("add_agent/", views.AddAgentView.as_view()),
    path("add_agent_with_id", views.AddAgentWithAgentId.as_view()),
    path("add_agent/<str:phone_number>/bvn_verify/", views.VerifyAgentBVNView.as_view()), 
    path("add_agent/<str:phone_number>/bvn_verify/available/", views.AvailableTerminalView.as_view()),
    path("add_agent/<str:phone_number>/verify/bvn_verify/available/terminal_assign/", views.AssignTerminalView.as_view()),
    path("add_agent/<str:phone_number>/verify/bvn_verify/available/terminal_assign/assign/", views.AgentAssignTerminalView.as_view()),
    path("settings/profile/", views.SalesRepProfileView.as_view()),
    path("user/get_referer_code/", views.GetReferalCode.as_view()),
    path("add_agent_guarantor_details/<str:phone_number>", views.ProspectiveAgentGuarantorFormView.as_view()),
    path("get-user-kyc-details", views.GetUserKycDetailsView.as_view()),


]
