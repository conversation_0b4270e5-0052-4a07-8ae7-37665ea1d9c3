from import_export import resources
from accounts.models import Transaction
from .models import Branch, Dispute, DevelopersEmail

class DisputeResource(resources.ModelResource):
    class Meta:
        model = Dispute
        #fields = "__all__"

class BranchResource(resources.ModelResource):
    class Meta:
        model = Branch

class DevelopersEmailResource(resources.ModelResource):
    class Meta:
        model = DevelopersEmail