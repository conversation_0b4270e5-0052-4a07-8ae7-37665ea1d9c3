from main.models import User
from datetime import datetime

from accounts.models import Transaction, WalletSystem, OtherCommissionsRecord
from admin_dashboard.models import (Dispute, ProspectiveAgent, SalesRep, Stock, 
                                    AssignedTerminal,  StockRequest, StockHistory, 
                                    StockRecovery, WithdrawalBankDetails, DisputeBatchUpload, 
                                    StockInTransit, Zone, Branch, AdminDashboardLogHistory,
                                    ProspectiveAgentsVerifiedBvnTable)

from admin_dashboard.role.models import Role

from datetime import datetime
from rest_framework import status, serializers


class CustomerDetailsSerializer(serializers.Serializer):
    email = serializers.EmailField()
    reason = serializers.CharField()


class CustomerWalletSerializer(serializers.ModelSerializer):
    class Meta:
        model = WalletSystem
        fields = '__all__'
        

class CustomerTransactionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        fields = ["id", "amount", "user_full_name", "transaction_mode", "transaction_type", "liberty_reference",
        "transaction_id", "date_created", "beneficiary_nuban", "status"]

class CustomerTransactionDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        fields = "__all__"

class OtherCommissionsRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = OtherCommissionsRecord
        fields = "__all__"


class GetCustomerTransactiondDetailSerializer(serializers.Serializer):
    trans_id = serializers.UUIDField()
    email = serializers.EmailField()


    
class ProspectiveAgentSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProspectiveAgent
        fields = "__all__"
        # exclude = [
        #     "update_email",
        #     "created_at",
        #     "updated_at",
        #     "is_staff",
        #     "is_superuser",
        #     "email_updated",
        #     "groups",
        #     "user_permissions",
        #     "password",
        #     "transaction_pin",
        #     "registration_email_otp",
        # ]

class GetOneProspectiveAgentSerializer(serializers.Serializer):
    id = serializers.IntegerField()

class AddAgentSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProspectiveAgent
        fields = (
            "first_name",
            "last_name",
            "phone_number",
            "email",
            "first_address",
            "second_address",
            "city",
            "state",
            "lga",
            "postal_zipcode",
            "pick_up_option",
            "profile_picture",
            "next_of_kin_name",
            "next_of_kin_phone_number",
            "next_of_kin_relationship",
            "next_of_kin_address",
            "source_account",
            "sales_rep",
            "bvn_number",
            "device",
            "house_number",
            "landmark",
            "street",
            "is_above_18",
            "gender",
            "signature",
            "agent_type"
            )   
        extra_kwargs = {
            "postal_zipcode": {"required":True, "allow_null":True}, 
            "second_address": {"required":True, "allow_null":True}, 
            "sales_rep": {"required":False, "allow_null":True}, 
            "agent_type": {"required":True, "allow_null":True} 
                       
        }

    # def validate(self, attrs):
    #     existing_successful_verification = ProspectiveAgentsVerifiedBvnTable.objects.filter(
    #     phone_number = attrs.get("phone_number"),
    #     bvn_number = attrs.get("bvn_number"),
    #     verification_successful = True
    #     ).last()

    #     if existing_successful_verification is None:
    #         raise serializers.ValidationError({"error":"BVN is not Verified"}, status.HTTP_400_BAD_REQUEST)
    #     return attrs


class AddAgentWithIdSerializer(serializers.Serializer):
    agent_id = serializers.CharField(max_length=255)   

    def validate(self, attrs):
        agent_id = attrs.get("agent_id")
        if not User.objects.filter(unique_id=agent_id).exists():
            raise serializers.ValidationError({"error": "No user with this ID"}, status.HTTP_400_BAD_REQUEST)

        agent_data = User.objects.get(unique_id=agent_id)
        agent_phone_number = agent_data.phone_number

        if ProspectiveAgent.objects.filter(phone_number=agent_phone_number).exists():
            raise serializers.ValidationError({"error": "This agent already exists"}, status.HTTP_400_BAD_REQUEST)
        return attrs
    
class ProspectiveAgentGuarantorSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProspectiveAgent
        fields = [
                "guarantor_one_first_name",
                "guarantor_one_last_name",
                "guarantor_one_email",
                "guarantor_one_phone_number",
                "guarantor_one_street_address",
                "guarantor_one_state",
                "guarantor_one_city",
                "guarantor_one_postal_zipcode",
                "guarantor_one_bvn_number",
                "guarantor_one_profile_picture",
                "guarantor_one_signature",   
                "guarantor_two_first_name",
                "guarantor_two_last_name",
                "guarantor_two_email",
                "guarantor_two_phone_number",
                "guarantor_two_street_address",
                "guarantor_two_state",
                "guarantor_two_city",
                "guarantor_two_postal_zipcode",
                "guarantor_two_bvn_number",
                "guarantor_two_profile_picture",
                "guarantor_two_signature"
            ]
        
    def validate(self, attrs):
            if len(attrs) == 0:
                raise serializers.ValidationError({"message":"Invalid form data"}, status.HTTP_400_BAD_REQUEST)
            else:
                return attrs

        
class VerifyBVNSerializer(serializers.Serializer):
    bvn_number = serializers.CharField(max_length=255, allow_blank=False)
    last_name = serializers.CharField(max_length=255, allow_blank=False)
    

class AssignTerminalSerializer(serializers.Serializer):
    terminal_id = serializers.CharField(max_length=255, allow_blank=False) 
    

class RequestStockOutSerializer(serializers.Serializer):
    requested_payload = serializers.JSONField()

class StockRequestSerializer(serializers.ModelSerializer):
    number_of_terminals = serializers.SerializerMethodField(read_only=True)
    total_amount = serializers.SerializerMethodField(read_only=True)
    depth = 1
    class Meta:
        model = StockRequest
        fields = ["id", "request_id", "liberty_pay_plus", "hoirzon_pay", "request_type",
        "nine_naira_point", "npsb", "paydia", "request_msg", "date_created", "date_updated", "request_type", "number_of_terminals",
        "stock_request_location", "zone", "payment_made", "total_amount"]

        extra_kwargs = {
            "request_id": {"required": False, "allow_null":True},
            "sales_rep": {"required": False, "allow_null":True},
            "request_type": {"required": True, "allow_null": False},
            "liberty_pay_plus": {"required": True, "allow_null":False},
            "hoirzon_pay": {"required": True, "allow_null":False},
            "nine_naira_point": {"required": True, "allow_null":False},
            "npsb": {"required": True, "allow_null":False},
            "paydia": {"required": True, "allow_null":False},
            "date_created": {"required": False, 'allow_null': True},
        }

    def to_representation(self, instance):
        serialized_data = super(StockRequestSerializer, self).to_representation(instance)
        serialized_data["request_id"] = instance.request_id
        serialized_data["account_number"] = "***********"
        serialized_data["bank_name"] = "VFD"
        serialized_data["account_name"] = "LibertyPay"
        serialized_data["requested_by"] = instance.sales_rep.sales_rep.get_full_name()
        serialized_data["branch"] = instance.sales_rep.branch

        return serialized_data

    def get_number_of_terminals(self, obj):
        liberty_pay_plus = obj.liberty_pay_plus
        hoirzon_pay = obj.hoirzon_pay
        nine_naira_point = obj.nine_naira_point 
        npsb = obj.npsb
        paydia = obj.paydia

        return liberty_pay_plus + hoirzon_pay + npsb + paydia + nine_naira_point
    
    def get_total_amount(self, obj):
        total_count = self.get_number_of_terminals(obj)
        return total_count * 25000

class ApproveStockRequestSerializer(StockRequestSerializer):
    class Meta:
        model = StockRequest
        fields = ["liberty_pay_plus", "hoirzon_pay", "npsb", "paydia", "nine_naira_point", "disapproval_note"]
class UpdateStockRequestLocationSerializer(serializers.ModelSerializer):
    class Meta:
        model = StockRequest
        fields = ["zone", "branch", "stock_request_location", "stock_location_evidence"]

class ZoneSerializer(serializers.ModelSerializer):
    class Meta:
        model = Zone
        fields = "__all__"

class BranchSerializer(serializers.ModelSerializer):
    class Meta:
        model = Branch
        fields = "__all__"

class StockRequestCountSerializer(serializers.ModelSerializer):
    depth = 1
    class Meta:
        model = StockRequest
        fields = "__all__"

    def to_representation(self, instance):
        serialized_data = super(StockRequestCountSerializer, self).to_representation(instance)
        _model_keys = list(serialized_data.keys())
        for i in _model_keys:
            serialized_data.pop(i)
        
        

        
        assigned_terminals = AssignedTerminal.objects.filter(request_id = instance.request_id)


        serialized_data["sales_rep"] = instance.sales_rep.user.first_name + " " + instance.sales_rep.user.last_name
        serialized_data["requested_stock_count"] = instance.liberty_pay_plus + instance.hoirzon_pay + instance.nine_naira_point + instance.npsb + instance.paydia
        serialized_data["assigned_stock_count"] = assigned_terminals.count()
        serialized_data["unassigned_stock_count"] = serialized_data["requested_stock_count"] - serialized_data["assigned_stock_count"]
        serialized_data["approved_by"] = assigned_terminals.last().assigned_by.email if assigned_terminals.last() else None
        serialized_data["approved_date"] = assigned_terminals.last().date_created if assigned_terminals.last() else None
        serialized_data["request_id"] = instance.request_id

        return serialized_data

class StockSerializer(serializers.ModelSerializer):
    class Meta:
        model = Stock
        fields = [
                    "id", "device_name", "serial_number", "model_number", "terminal_id", "stock_location", 
                    "payment_status", "is_assigned", "zone", "branch",
                    "comment"
                    ]

    def update(self, instance, validated_data):
        return super().update(instance, validated_data)

class StockInTransitSerializer(serializers.ModelSerializer):
    class Meta:
        model = StockInTransit
        fields = ["id", "zone", "branch", "stocks", "comment", "stock_location", 
                    "stock_location_evidence", "date_started"]


class AssignStockSerializer(serializers.ModelSerializer):
    class Meta:
        model = Stock
        fields = ["branch", "zone", "terminal_id"]

    def validate_terminal_id(self, value):
        if not Stock.objects.filter(terminal_id=value).exists():
            raise serializers.ValidationError({"message": "This terminal does not exist"}, status.HTTP_400_BAD_REQUEST)
        return value

    def update(self, instance, validated_data):
        instance.terminal_id = self.validated_data.get("terminal_id", self.terminal_id)
        instance.branch = self.validated_data.get("branch", self.branch)
        instance.zone = self.validated_data.get("zone", self.zone)
        instance.save()
        return instance
          
class StockHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = StockHistory
        fields = "__all__"

class TerminalTrailSerializer(serializers.Serializer):
    terminals = serializers.ListField(
        child = serializers.CharField()
    )
        
    # def validate_terminals(self, value):
    #     for terminal in value:
    #         if not Stock.objects.filter(terminal_id=terminal).exists():
    #             raise serializers.ValidationError(
    #                 f"{value}The selection contains terminal IDs that do not exist"
    #             )
    #     return value

class AssignedTerminalSerializer(serializers.ModelSerializer):
    depth = 1
    class Meta:
        model = AssignedTerminal
        fields = "__all__"

    def to_representation(self, instance):
        serialized_data = super(AssignedTerminalSerializer, self).to_representation(instance)

        serialized_data["serial_number"] = instance.stock.serial_number
        serialized_data["model_number"] = instance.stock.model_number
        serialized_data["terminal_id"] = instance.stock.terminal_id

        del serialized_data["stock"]

        return serialized_data

class StockRequestApprovalSerializer(serializers.Serializer):
    STATUS_CHOICES =( 
        ("APPROVE", "APPROVE"), 
        ("DISAPPROVE", "DISAPPROVE")
    )
 

    request_id = serializers.CharField()
    liberty_pay_plus = serializers.IntegerField()
    hoirzon_pay = serializers.IntegerField()
    nine_naira_point = serializers.IntegerField()
    npsb = serializers.IntegerField()
    paydia = serializers.IntegerField()
    status = serializers.CharField()


class AgentAssignTerminalSerializer(serializers.Serializer):
    terminal_id = serializers.CharField(max_length=300, required=True, allow_blank=False)



class DisputeSerializer(serializers.ModelSerializer):
    pAN = serializers.SerializerMethodField()
    class Meta:
        model = Dispute
        # fields = "__all__"
        fields = [
                    "id", "pAN", "narration", "dispute_type", "card_first_six_digits", "card_last_four_digits",
                    "receiver_account_number", "customer_mobile_number", "customer_account_number",
                    "customer_name", "transaction_rrn", "transaction_date", "issue", "stan", "requested_amount",
                    "dispensed_amount", "terminal_id", "respond_time", "resolved_by", "status", "support_id", 
                    "date_created"
                    ]
        extra_kwargs = {
            "terminal_id": {"required": False}, "transaction_rrn": {"required": False},
            "transaction_date": {"required": True}, "pAN": {"required": True}, "narration": {"required": True},
            "card_first_six_digits": {"required": True}, "card_last_four_digits": {"required": True},
            "dispute_type": {"required": True}, "receiver_account_number": {"required": True}, "customer_mobile_number":{"required": True},
            "customer_account_number": {"required": True}, "customer_name": {"required": True}, "issue": {"required": True}, "stan": {"required": False},
            "requested_amount": {"required": True}
            }

    def get_pAN(self, obj):
        pan = f"{obj.card_first_six_digits}******{obj.card_last_four_digits}"
        return pan

    def validate(self, attrs):
        first_6_card_digits = attrs.get("card_first_six_digits")
        last_4_digits = attrs.get("card_last_four_digits")
        customer_mobile = attrs.get("customer_mobile_number")
        cus_account_num = attrs.get("customer_account_number")
        rec_account_num = attrs.get("receiver_account_number")
        transaction_rrn = attrs.get("transaction_rrn")
        stan = attrs.get("stan")

        # if not Transaction.objects.filter(unique_reference=transaction_rrn).exists():
        #     raise serializers.ValidationError({"transaction_rrn":"This transaction does not exist. Check the rrn"}, status.HTTP_400_BAD_REQUEST)

        # if not HorizonPayTable.objects.filter(stan=stan).exists():
        #     raise serializers.ValidationError({"stan":"This transaction does not exist. Check the stan"}, status.HTTP_400_BAD_REQUEST)

        if len(first_6_card_digits) < 6 or len(first_6_card_digits) > 6:
            raise serializers.ValidationError({"card_first_six_digits":"This field requires 6 digits"}, status.HTTP_400_BAD_REQUEST)

        if len(last_4_digits) < 4 or len(last_4_digits) > 4:
            raise serializers.ValidationError({"card_last_four_digits":"This field requires 6 digits"}, status.HTTP_400_BAD_REQUEST)

        if not first_6_card_digits.isdigit() or not last_4_digits.isdigit():
            raise serializers.ValidationError({"card_first_six_digits":"This field requires only digits"}, status.HTTP_400_BAD_REQUEST)
        
        if not customer_mobile.isdigit() or not cus_account_num.isdigit() \
            or not rec_account_num.isdigit() or len(customer_mobile) != 11 or len(cus_account_num) != 10:
            raise serializers.ValidationError({"account_details":"Incomplete account digits"}, status.HTTP_400_BAD_REQUEST)
        
        for k,v in attrs.items():
            if v == "":
                raise serializers.ValidationError("This field cannot be empty", code=f"{k}")
        return attrs

class DisputeBatchUploadSerializer(serializers.ModelSerializer):
    class Meta:
        model = DisputeBatchUpload
        fields = ["id", "disputes", "date_uploaded"]

class SalesRepSerializer(serializers.ModelSerializer):
    class Meta:
        model = SalesRep
        fields = "__all__"

class AdminDashboardLogHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = AdminDashboardLogHistory
        fields = "__all__"

class RoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields  = ["id"]

    def to_representation(self, obj):
        serialized_data = super(RoleSerializer, self).to_representation(obj)
        serialized_data["role_id"] = obj.unique_id 
        serialized_data["role"] = obj.name
        serialized_data["role_description"] = obj.description
        serialized_data["number_of_users"] = User.objects.filter(role=obj.name).count()
        serialized_data["status"] = "ACTIVE"

        return serialized_data

class AdminDashboardUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'first_name', 'last_name', 'username', 'email', 'phone_number','type_of_user', 'role']

    def to_representation(self, obj):
        serialized_data = super(AdminDashboardUserSerializer, self).to_representation(obj)
        serialized_data["user_id"] = obj.unique_id
        serialized_data["name"] = obj.get_full_name()
        serialized_data["log_in"] = obj.email
        serialized_data["last_session"] = obj.last_login
        serialized_data["status"] = obj.tag if obj.tag else ""
        serialized_data["role"] = obj.role if obj.role else ""

        return serialized_data

class AssignRoleSerializer(serializers.Serializer):
    # ROLE_CHOICES = [(f"{role.name.upper()}", f"{role.name.upper()}") for role in Role.objects.all()]
    email = serializers.EmailField()
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    role = serializers.CharField()


class StockRecoverySerializer(serializers.ModelSerializer):
    class Meta:
        model = StockRecovery
        fields = "__all__"


class WithdrawalBankDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = WithdrawalBankDetails
        fields = "__all__"


class StockRequestUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = StockRequest
        fields = ["id", "liberty_pay_plus", "hoirzon_pay", "request_type",
        "nine_naira_point", "npsb", "paydia", "request_msg", "date_created", "request_type"]
        extra_kwargs = {
            "request_id": {"required": False, "allow_null":True},
            "sales_rep": {"required": False, "allow_null":True},
            "request_type": {"required": False, "allow_null": True},
            "liberty_pay_plus": {"required": False, "allow_null":True},
            "hoirzon_pay": {"required": False, "allow_null":True},
            "nine_naira_point": {"required": False, "allow_null":True},
            "npsb": {"required": False, "allow_null":True},
            "paydia": {"required": False, "allow_null":True},
            "date_created": {"required": False, 'allow_null': True},
        }


#############################################################################333
# SEND POS AGENT EMAILS

class SendEmailMessageSerializer(serializers.Serializer):
    message = serializers.CharField()
    title = serializers.CharField()
    schedule = serializers.DateTimeField(default=datetime.now())
    # attach = serializers.FileField(max_length=None, allow_empty_file=True, use_url=True)


# Account Statement Serializer
class AccountStatementSerializer(serializers.Serializer):
    customer_email = serializers.EmailField()
    start_date = serializers.DateField()
    end_date = serializers.DateField()