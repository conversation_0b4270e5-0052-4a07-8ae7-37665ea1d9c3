import random
from django.db import models
import ast, uuid


def generate_role_id():
    gen_uuid = str(uuid.uuid4()).replace('-', '').upper()
    generated_id = ''.join(random.choice(gen_uuid) for i in range(6))
    return f"LT-{generated_id}"


class Role(models.Model):
    TYPES_OF_PERMISSIONS = [
        "DASHBOARD", "CUSTOMERS", "AGENTS", "PROSPECTIVE_AGENT", "ONBOARD_AGENT",
        "TRANSACTIONS", "TERMINALS", "SALES", "CREATE_REQUISITION", "TERMINAL_STATUS",
        "DISPUTES", "ROLES", "LOG", "SETTINGS", "MERCHANTS",
        "APPROVE_REQUISITION", "MAKE_REQUISITION_PAYMENT"
    ]
    TYPES_OF_TAGS = [
        ("ACTIVE", "ACTIVE"),
        ("INACTIVE", "INACTIVE"),
        ("DISABLED", "DISABLED")
    ]
    name = models.CharField(max_length=360)
    description = models.CharField(max_length=1000, null=True, blank=True)
    permissions = models.TextField(default="[]", null=True, blank=True)
    unique_id = models.CharField(max_length=300, default=generate_role_id)
    tag = models.CharField(max_length=200, blank=True, default="INACTIVE", choices=TYPES_OF_TAGS)

    def __str__(self):
        return self.name

    def add_single_permission(self, permission):
        if permission not in self.TYPES_OF_PERMISSIONS:
            raise Exception("Permission is not valid!")
        permissions = ast.literal_eval(self.permissions)
        if permission not in permissions:
            permissions.append(permission)
            self.permissions = str(permissions)

    # def add_multiple_access(self, m_access):
    #     found = []
    #     not_found = []
    #     has_access_to = ast.literal_eval(self.has_access_to)
    #     for access in m_access:
    #         if access not in self.TYPES_OF_ACCESS:
    #             not_found.append(access)
    #         else:
    #             has_access_to.append(access)
    #             found.append(access)
    #     self.has_access_to = str(has_access_to)
        
    def remove_permission(self, permission):
        permissions = ast.literal_eval(self.permissions)
        if permission not in permissions:
            raise Exception("Permission is not present in this role!")
        permissions.remove(permission)
        self.permissions = str(permissions)


