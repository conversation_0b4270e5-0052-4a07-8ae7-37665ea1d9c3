from admin_dashboard.signals import action_performed_signal

class ObjectChangeMixin:
    def dispatch(self, request, *args, **kwargs):
        try:
            instance = self.get_object(request)
        except self.model.DoesNotExist:
            instance = None
        if instance:
            action_performed_signal.send(instance.__class__, instance=instance, request=request)
        return super(ObjectChangeMixin, self).dispatch(request, *args, **kwargs)