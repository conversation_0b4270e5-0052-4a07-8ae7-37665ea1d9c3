import os
import requests
from django.core.paginator import <PERSON>gin<PERSON> as django_core_paginator


def send_simple_message(sender_email):
	return requests.post(
		"https://api.mailgun.net/v3/mg.whispersms.com/messages",
		auth=("api", os.environ.get("MAILGUN_SECRET")),
		data={"from": "Mailgun <<EMAIL>>",
			"to": "<PERSON> <<EMAIL>>",
			"subject": "New Dispute Alert",
			"html": """<!DOCTYPE html>
            <html>
            <head>
            <title>Dispute Notification</title>
            </head>
            <body>

            <h1>Opened Dispute Notification</h1>
            <p><#= sender_email #> opened a dispute. Please resolve soonest</p>

            </body>
            </html>"""
            })


class Paginator:

    @staticmethod
    def paginate(request, queryset):

        request_get_data = request.GET
    
        paginator = django_core_paginator(queryset.order_by("-id"), int(request_get_data.get('size', 100)))
        requested_page = int(request_get_data.get('page', 1))

        verified_page = requested_page if requested_page < paginator.num_pages else paginator.num_pages

        page = paginator.page(verified_page)

        return page

