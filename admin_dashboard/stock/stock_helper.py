from admin_dashboard.models import AssignedTerminal, SalesRep, Stock, StockHistory, StockRequest


class StockModelsHelper:

    @staticmethod
    def get_all_stocks():
        device_names = Stock.objects.all().distinct("device_name")
        device_name = [e.device_name for e in device_names]
        stocks = {}
        for name in device_name:
            stocks[name] = {
                "number_of_stocks": Stock.objects.filter(device_name=name).count(),
                "available_stocks": Stock.objects.filter(device_name=name, is_assigned=False).count(),
                "date": Stock.objects.filter(device_name=name).first().date_added
            }

        all_stocks = Stock.objects.all()
        stocks["all_stocks"] = {
            "all_stocks": all_stocks.count(),
            "last_date_added": all_stocks.first().date_added
        }
        return stocks

    def create_stock(self, user, data):
        for key in data:
            _data = dict(*[key.items()])
            stock_instance = Stock.objects.create(**_data)

            self.create_stock_history(user, stock_instance, "UN_ASSIGNED", "STOCK_IN")




    def create_stock_history(self, user, stock_instance, action, status, sales_rep = None):
        _stock_history_instnace = StockHistory.objects.create(
            stock=stock_instance,
            action = action,
            admin = user,
            batch_date_created = stock_instance.date_added,
            status = status
        )

        if sales_rep is not None:
            _stock_history_instnace.sales_repr = sales_rep
            _stock_history_instnace.save()

    def approve_stock_request(self, admin_instance, sales_rep_instance, serialized_data):

        stocks = Stock.objects.filter(is_active = True, is_assigned = False)

        

        request_id = serialized_data.get("request_id")
        liberty_pay_plus = serialized_data.get("liberty_pay_plus")
        hoirzon_pay = serialized_data.get("hoirzon_pay")
        nine_naira_point = serialized_data.get("nine_naira_point")
        npsb = serialized_data.get("npsb")
        paydia = serialized_data.get("paydia")
        status = serialized_data.get("status").upper()

        if status == "APPROVE":
            if liberty_pay_plus > 0:
                for _ in range(0, liberty_pay_plus):
                    stock = Stock.objects.filter(is_active = True, is_assigned = False, device_name = "LIBERTYPAYPLUS").last()
                    if stock:
                        AssignedTerminal.objects.create(
                            assigned_by = admin_instance,
                            sales_rep = sales_rep_instance,
                            stock = stock,
                            status = "ASSIGNED",
                            request_id = request_id
                        )

                        self.create_stock_history(admin_instance, stock,  "STOCK_OUT", "ASSIGNED",  sales_rep = sales_rep_instance)

                        stock.is_assigned = True
                        stock.save()
                            


            if hoirzon_pay > 0:
                for _ in range(0, hoirzon_pay):
                    stock = Stock.objects.filter(is_active = True, is_assigned = False, device_name = "HOIRZONPAY").last()
                    if stock:
                        
                        AssignedTerminal.objects.create(
                            assigned_by = admin_instance,
                            sales_rep = sales_rep_instance,
                            stock = stock,
                            status = "ASSIGNED",
                            request_id = request_id
                        )

                        self.create_stock_history(admin_instance, stock,  "STOCK_OUT", "ASSIGNED",  sales_rep = sales_rep_instance)

                        stock.is_assigned = True
                        stock.save()

            if nine_naira_point > 0:
                for _ in range(0, nine_naira_point):
                    stock = Stock.objects.filter(is_active = True, is_assigned = False, device_name = "9NAIRAPOINT").last()
                    if stock:
                        AssignedTerminal.objects.create(
                            assigned_by = admin_instance,
                            sales_rep = sales_rep_instance,
                            stock = stock,
                            status = "ASSIGNED",
                            request_id = request_id
                        )

                        self.create_stock_history(admin_instance, stock,  "STOCK_OUT", "ASSIGNED",  sales_rep = sales_rep_instance)

                        stock.is_assigned = True
                        stock.save()

            
            if npsb > 0:
                for _ in range(0, npsb):
                    stock = Stock.objects.filter(is_active = True, is_assigned = False, device_name = "NPSB").last()
                    if stock:
                        AssignedTerminal.objects.create(
                            assigned_by = admin_instance,
                            sales_rep = sales_rep_instance,
                            stock = stock,
                            status = "ASSIGNED",
                            request_id = request_id
                        )

                        self.create_stock_history(admin_instance, stock,  "STOCK_OUT", "ASSIGNED",  sales_rep = sales_rep_instance)

                        stock.is_assigned = True
                        stock.save()

            if paydia > 0:
                for _ in range(0, paydia):
                    stock = Stock.objects.filter(is_active = True, is_assigned = False, device_name = "PAYDIA").last()
                    if stock:
                        AssignedTerminal.objects.create(
                            assigned_by = admin_instance,
                            sales_rep = sales_rep_instance,
                            stock = stock,
                            status = "ASSIGNED",
                            request_id = request_id
                        )

                        self.create_stock_history(admin_instance, stock,  "STOCK_OUT", "ASSIGNED",  sales_rep = sales_rep_instance)

                        stock.is_assigned = True
                        stock.save()

            StockRequest.objects.filter(request_id = request_id).update(pending_approval = False, is_approved = True)

        else:
            StockRequest.objects.filter(request_id = request_id).update(pending_approval = False, is_approved = False)

                    

                        