from rest_framework import serializers

from admin_dashboard.models import Stock, StockHistory


class StockSerializerV1(serializers.ModelSerializer):
    class Meta:
        model = Stock
        fields = ["id", "device_name", "serial_number", "model_number", "terminal_id"]


class StockHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = StockHistory
        fields = ["id", "action", "admin", "sales_repr", "stock", "batch_id", "batch_date_created", "status", "date_created", "approved_date", "pick_up_date"]