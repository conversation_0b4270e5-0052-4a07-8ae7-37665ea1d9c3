from rest_framework.views import APIView
from rest_framework import status
from rest_framework.response import Response
from rest_framework import generics
from rest_framework import filters

from admin_dashboard.serializers import StockRequestApprovalSerializer, StockRequestSerializer
from admin_dashboard.stock.serializers import StockHistorySerializer, StockSerializerV1
from admin_dashboard.stock.stock_helper import StockModelsHelper
from admin_dashboard.models import SalesRep, Stock, StockHistory, StockRequest

from main.views import CustomPagination
from main.permissions import CustomIsAuthenticated



class CreateStockView(APIView):
    permission_classes = [CustomIsAuthenticated]

    serilizer_class = StockSerializerV1
    def post(self, request):
        serializer = StockSerializerV1(data=request.data, many = True)
        serializer.is_valid(raise_exception=True)
        StockModelsHelper().create_stock(request.user, serializer.data)
        return Response(status=status.HTTP_201_CREATED)


class AllStocksCountView(APIView):
    permission_classes = [CustomIsAuthenticated]

    def get(self, request):
        stocks = StockModelsHelper.get_all_stocks()
        return Response(data = stocks, status = status.HTTP_200_OK)

class StocksView(generics.ListAPIView):
    search_fields = ['device_name', "serial_number", "terminal_id"]
    permission_classes = [CustomIsAuthenticated]

    search_fields = search_fields
    filter_backends = (filters.SearchFilter,)
    queryset = Stock.objects.all()
    serializer_class = StockSerializerV1
    pagination_class = CustomPagination


class StockHistoryView(generics.ListAPIView):
    search_fields = ['device_name', "serial_number", "terminal_id"]
    permission_classes = [CustomIsAuthenticated]

    search_fields = search_fields
    filter_backends = (filters.SearchFilter,)
    queryset = StockHistory.objects.all().order_by("-id")
    serializer_class = StockHistorySerializer
    pagination_class = CustomPagination



class StockRequestView(generics.ListAPIView):
    """
    List all stock requests
    """
    permission_classes = [CustomIsAuthenticated]
    search_fields = ["date_created","request_id", "status"]
    filter_backends = (filters.SearchFilter,)
    serializer_class = StockRequestSerializer
    pagination_class = CustomPagination

    def get_queryset(self):
        queryset = StockRequest.objects.filter(pending_approval = True)
        return queryset


class StockRequestDetailsView(APIView):
    def get(self, request):
        stock_request_id = request.GET.get("stock_request_id")
        stock_obj = StockRequest.objects.filter(request_id=stock_request_id)
        serilaize_obj = StockRequestSerializer(stock_obj, many=True)
        return Response(data = serilaize_obj.data, status=status.HTTP_200_OK)


class StockRequestApprovalView(APIView):
    def post(self, request):
        serializer = StockRequestApprovalSerializer(data=request.data)
        serializer.is_valid(raise_exception=True) 
        stock_request_id = serializer.validated_data.get("request_id")
        stock_qs = StockRequest.objects.filter(request_id=stock_request_id).last()
        
        if not stock_qs:
            data = {
                "message": "Stock request does not exist"
            }
            return Response (data, status=status.HTTP_400_BAD_REQUEST)
    
        sales_rep = SalesRep.objects.filter(id = stock_qs.sales_rep.id).last()



        StockModelsHelper().approve_stock_request(request.user,sales_rep,serializer.data)
        return Response(status=status.HTTP_200_OK)