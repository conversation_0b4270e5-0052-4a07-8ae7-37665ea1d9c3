from django.dispatch import Signal
from django.dispatch import receiver
from django.db.models.signals import post_save
from admin_dashboard.models import AdminDashboardLogHistory, NewUploadDisputeTable
from django.contrib.contenttypes.models import ContentType

action_performed_signal = Signal()

def history_log_receiver(sender, instance, request, *args, **kwargs):
    AdminDashboardLogHistory.objects.create(
        user = request.user,
        content_object = ContentType.objects.get_for_model(sender),
        object_id = instance.id
    )

action_performed_signal.connect(history_log_receiver)


