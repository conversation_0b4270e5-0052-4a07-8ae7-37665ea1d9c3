from typing import Sequence
from django.contrib import admin
from django.http.request import HttpRequest
from admin_dashboard.models import (
    AssignedTerminal, Branch, Dispute, SalesRep, Stock, 
    StockHistory, ProspectiveAgent, StockRequest, Zone, StockInTransit,
    DisputeBatchUpload, BunchOfBalance, AdminDashboardLogHistory, TerminalRequest,
    StockRecovery, NewUploadDisputeTable, ProspectiveAgentsVerifiedBvnTable,
    DevelopersEmail
    )
from import_export import resources
from import_export.admin import ImportExportModelAdmin
from admin_dashboard.resources import DisputeResource, DevelopersEmailResource
from admin_dashboard.role.models import Role

# Register your models here.


class NewUploadDisputeTableResource(resources.ModelResource):
    class Meta:
        model = NewUploadDisputeTable
    
    fields = (
        ('transaction_date', 'Transaction Date (yyyy-MM-dd hh:mm)'),
    )

    def after_save_instance(
        self, instance: NewUploadDisputeTable, using_transactions: bool, dry_run: bool,
    ):
        super().after_save_instance(instance, using_transactions, dry_run)
        if dry_run is False:
            print("I am going in id: ", instance.id)
            if instance.notification_sent == False:
                from admin_dashboard.tasks import send_sms_and_email_on_success_dispute_add
                
                task_id = send_sms_and_email_on_success_dispute_add.apply_async(
                    # queue="processbulksheet",
                    queue="ussdbills",
                    kwargs={
                        "rrn": instance.retrieval_reference
                    }
                )


class NewUploadDisputeTableResourceAdmin(ImportExportModelAdmin):
    resource_class = NewUploadDisputeTableResource
    list_filter = (
        ('date_created', 'retrieval_reference')
    )
    date_hierarchy = 'date_created'


    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    

    
admin.site.register(NewUploadDisputeTable, NewUploadDisputeTableResourceAdmin)


@admin.register(SalesRep)
class SalesRepAdmin(admin.ModelAdmin):
    autocomplete_fields = ["sales_rep"]
    list_display = ["id", "sales_rep", "sales_rep_code", "ussd_code","created_at", "updated_at"]
    search_fields = ["sales_rep__email", "sales_rep_code"]
 
@admin.register(BunchOfBalance)
class BunchOfBalanceAdmin(admin.ModelAdmin):
    list_display = ["id", "balance_name", "amount", "date_added", "last_updated"]
    search_fields = ["balance_name"]
    date_hierarchy = 'date_added'

    list_filter = (
        ('date_added',)
    )
 

@admin.register(Stock)
class StockRepAdmin(admin.ModelAdmin):
    list_display = ["id", "device_name", "serial_number", "model_number", "is_assigned", "is_active", "terminal_id", "date_added", "status", "suspension_count"]

    search_fields = ["device_name", "serial_number", "model_number", "terminal_id"]

    date_hierarchy = 'date_added'

    list_filter = (
        ('date_added', 'device_name')
    )


@admin.register(StockHistory)
class StockHistoryAdmin(admin.ModelAdmin):
    list_display = ["id", "admin", "sales_repr", "stock","batch_id", "batch_date_created", "status", "action", "date_created", "approved_date", "pick_up_date"]

    search_fields = ["id", "admin"]

    date_hierarchy = 'date_created'

    list_filter = (
        ('date_created', 'pick_up_date', 'approved_date')
    )
@admin.register(ProspectiveAgent)
class ProspectiveAgent(admin.ModelAdmin):
    list_display = ["id",
                    "sales_rep",
                    "first_name",
                    "last_name",
                    "phone_number",
                    "email", 
                    "source_account",
                    "prospect_action",
                    "prospect_status",
                    "first_address",
                    "second_address",
                    "city",
                    "state",
                    "lga",
                    "postal_zipcode",
                    "bvn_number",
                    "business_name",
                    "device",
                    "unique_id",
                    "pick_up_option",
                    "next_of_kin_name",
                    "next_of_kin_phone_number",
                    "next_of_kin_address",
                    "location",
                    "date" 
                ]

@admin.register(ProspectiveAgentsVerifiedBvnTable)
class ProspectiveAgentsVerifiedBvnTableAdmin(admin.ModelAdmin):

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]

@admin.register(StockRequest)
class StockRequestHistoryAdmin(admin.ModelAdmin):
    list_display = ["id", "sales_rep", "liberty_pay_plus", "hoirzon_pay","nine_naira_point", "npsb", "paydia", "request_id", "is_approved", "date_created", "date_updated"]

    search_fields = ["id", "sales_rep"]

    date_hierarchy = 'date_created'

    list_filter = (
        ('liberty_pay_plus', 'hoirzon_pay', 'nine_naira_point', 'npsb', 'paydia')
    )



@admin.register(AssignedTerminal)
class AssignedTerminalAdmin(admin.ModelAdmin):
    list_display = ["id", "assigned_by", "sales_rep","stock", "status", "request_id", "date_created", "date_updated"]

    search_fields = ["id", "assigned_by", "sales_rep"]

    date_hierarchy = 'date_created'



@admin.register(Branch)
class BranchResourceAdmin(ImportExportModelAdmin):
    list_display = ["id", "zone", "branch_name", "reserved_terminals"]
    search_fields = ["zone__name", "zone__id"]



@admin.register(Dispute)
class DisputeResourceAdmin(ImportExportModelAdmin):
    list_display = ["user", "dispute_type", "date_created", "is_resolved"]


@admin.register(Zone)
class ZoneResourceAdmin(ImportExportModelAdmin):
    list_display = ["id", "name"]

@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    list_display = ["id", "name", "description", "permissions", "unique_id", "tag"]

    search_fields = ["name", "unique_id", "description", "permissions"]

@admin.register(TerminalRequest)
class TerminalRequestAdmin(admin.ModelAdmin):
    pass

@admin.register(StockInTransit)
class StockIntransitAdmin(admin.ModelAdmin):
    list_display = ["id", "zone", "branch", "comment", "stocks"]

@admin.register(DisputeBatchUpload)
class DisputeBatchUploadAdmib(admin.ModelAdmin):
    list_display = ["id", "disputes", "date_uploaded"]

@admin.register(AdminDashboardLogHistory)
class AdminDashboardLogHistoryAdmin(admin.ModelAdmin):
    list_display = ["user", "content_type", "object_id", "content_object", "action_time"]


@admin.register(StockRecovery)
class StockRecoveryAdmin(admin.ModelAdmin):
    list_display = ["id", "terminal_id", "stock", "recovered_by", "status", "date_recovered", "date_updated"]


@admin.register(DevelopersEmail)
class DevelopersEmailResourceAdmin(ImportExportModelAdmin):
    resource_class = DevelopersEmailResource

    search_fields = ("email", "date_created")
