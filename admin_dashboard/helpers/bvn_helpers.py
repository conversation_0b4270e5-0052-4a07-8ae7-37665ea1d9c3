# import requests
# import json
# import simplejson as json
# from django.conf import settings

# class SalesRepHelper:
    
#     def verify_bvn(**kwargs):
#         class SetEncoder(json.JSONEncoder):
#             def default(self, obj):
#                 if isinstance(obj, set):
#                     return list(obj)
#                 return json.JSONEncoder.default(self, obj)
#         url = "http://0a4b-102-222-96-116.ngrok.io/kyc/user/verify_bvn_non_user/"
#         header = {"Content-type": "application/json"}
#         print(json.dumps(kwargs))
#         response = requests.request("POST", url, headers=header, data=json.dumps(kwargs, cls=SetEncoder))
#         if response.status_code == 200:
#             return response.json()
#         else:
#             return {
#                 "data":{
#                     "status":400
#                     },
#                 "status":400
#             }
    
#     def verify_otp(**kwargs):
#         class SetEncoder(json.JSONEncoder):
#             def default(self, obj):
#                 if isinstance(obj, set):
#                     return list(obj)
#                 return json.JSONEncoder.default(self, obj)
#         url = "http://0a4b-102-222-96-116.ngrok.io/kyc/user/confirm_bvn_otp_non_user/"
#         header = {"Content-type": "application/json"}
#         print(json.dumps(kwargs))
#         response = requests.request("POST", url, headers=header, data=json.dumps(kwargs, cls=SetEncoder))
#         if response.status_code == 200:
#                 return response.json()
#         else:
#             return {
#                 "data":{
#                     "status":400
#                     },
#                 "status":400
#             }
    

          