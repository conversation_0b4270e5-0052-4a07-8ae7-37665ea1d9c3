from string import Template
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Mail, HtmlContent
import os
from django.conf import settings



def sale_rep_onboarding_email(email,pwd,name,user_exists=False):
    """
    This function sends an email to a user when they register as a user and sales rep.
    """

    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    

    

    if user_exists is False:
        html_temp = os.path.join(BASE_DIR, "templates/sales_rep_onboarding_with_email.html")
        with open(html_temp) as temp_file:
            template = temp_file.read()


        template = Template(template).safe_substitute(
            pwd=pwd,
            name=name,
            email=email
        )
    else:
        html_temp = os.path.join(BASE_DIR, "templates/sales_rep_onboarding.html")
        with open(html_temp) as temp_file:
            template = temp_file.read()

        template = Template(template).safe_substitute(name=name)
    message = Mail(
        from_email="Liberty Agency Banking <<EMAIL>>",
        to_emails=f"{email}",
        subject="Sales Rep Onboarding",
        html_content=HtmlContent(template),
    )
    try:
        res = SendGridAPIClient(settings.SENDGRID_API_KEY)
        res.send(message)
    except Exception:
        pass