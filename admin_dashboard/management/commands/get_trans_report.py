from django.core.management import BaseCommand
from django.conf import settings
from admin_dashboard.tasks import get_trans_report
import os



class Command(BaseCommand):
    def handle(self, *args, **kwargs):
        last_file = os.path.join(settings.BASE_DIR, 'media/agent_report/last/agent_activity_last.xlsx')
        
        with open(last_file, "rb") as excel_file:
            agent_activities = excel_file.read()
            excel_file.close()

        get_trans_report(file=agent_activities)

        return "DONE"