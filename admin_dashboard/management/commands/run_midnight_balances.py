from django.core.management import BaseCommand
from django.db.models import Sum, Q
from admin_dashboard.models import BunchOfBalance
from accounts.models import WalletSystem, OpeningClosingBalance
from accounts.helpers.vfdbank_manager import VFDBank

from datetime import datetime, timedelta


class Command(BaseCommand):

    def handle(self, *args, **kwargs):
        float_user = WalletSystem.get_float_user()

        base_wallet_qs = WalletSystem.objects.exclude(user=float_user).filter(wallet_type="COLLECTION")

        wallets_overall = list(base_wallet_qs.aggregate(Sum('available_balance')).values())[0]
        pos_wallets = list(base_wallet_qs.filter(user__terminal_id__isnull=False).aggregate(Sum('available_balance')).values())[0]
        mobile_wallets = list(base_wallet_qs.filter(user__terminal_id__isnull=True).aggregate(Sum('available_balance')).values())[0]

        float_balance = VFDBank.get_vfd_float_balance() if VFDBank.get_vfd_float_balance() is not None else 0
            # float_balance = WalletSystem.objects.filter(user=float_user, wallet_type="FLOAT").last().available_balance
        # except:
        #     float_balance = 0

        # Overall
        get_midnight_overall_wallet_balance, created = BunchOfBalance.objects.get_or_create(
            balance_name = "midnight_overall_wallet_balance"
        )
        get_midnight_overall_wallet_balance.amount = wallets_overall if wallets_overall else 0.00
        get_midnight_overall_wallet_balance.save()
        # POS Wallet
        get_midnight_pos_wallet_balance, created = BunchOfBalance.objects.get_or_create(
            balance_name = "midnight_pos_wallet_balance"
        )
        get_midnight_pos_wallet_balance.amount = pos_wallets if pos_wallets else 0.00
        get_midnight_pos_wallet_balance.save()

        # Mobile Wallet
        get_midnight_mobile_wallet_balance, created = BunchOfBalance.objects.get_or_create(
            balance_name = "midnight_mobile_wallet_balance"
        )
        get_midnight_mobile_wallet_balance.amount = mobile_wallets if mobile_wallets else 0.00
        get_midnight_mobile_wallet_balance.save()

        # Float Account
        get_float_account_balance, created = BunchOfBalance.objects.get_or_create(
            balance_name = "midnight_float_account_balance"
        )
        get_float_account_balance.amount = float_balance if float_balance else 0.00
        get_float_account_balance.save()

        result = {
            "overall": wallets_overall,
            "pos": pos_wallets,
            "mobile": mobile_wallets,
            "float": float_balance,
        }

        today = datetime.now().date()

        yesterday = today - timedelta(days=1)

        open_bal, created = OpeningClosingBalance.objects.get_or_create(
            date_created__date=today
        )
        close_bal, created = OpeningClosingBalance.objects.get_or_create(
            date_created__date=yesterday
        )

        close_bal.closing_bank = float_balance
        close_bal.closing_all_wallet = wallets_overall if wallets_overall else 0.00
        close_bal.closing_pos_wallet = pos_wallets if pos_wallets else 0.00
        close_bal.closing_mobile_wallet = mobile_wallets if mobile_wallets else 0.00

        open_bal.opening_bank = float_balance
        open_bal.opening_all_wallet = wallets_overall if wallets_overall else 0.00
        open_bal.opening_pos_wallet = pos_wallets if pos_wallets else 0.00
        open_bal.opening_mobile_wallet = mobile_wallets if mobile_wallets else 0.00

        close_bal.save()
        open_bal.save()



