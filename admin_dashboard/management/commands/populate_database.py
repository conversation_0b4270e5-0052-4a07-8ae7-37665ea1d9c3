from django.core.management import BaseCommand
from admin_dashboard.helpers.helpers import send_agent_activity_email
from main.models import User
from faker import Faker
from accounts.models import AccountSystem, WalletSystem
import random
from faker.providers import BaseProvider
from admin_dashboard.models import Transaction

import os
from datetime import datetime




WALLET_TYPES = [
    "SPEND",
    "COLLECTION",
    "SAVINGS",
    "COMMISSIONS",
    "FLOAT",
    "WHISPER",
    ]

class Provider(BaseProvider):
    def wallet_types(self):
        return self.random_element(WALLET_TYPES)
    

class Command(BaseCommand):
    help = """Create items to populate database"""

    def handle(self, *args, **kwargs):
        """populate database with dummy data"""
        # fake = Faker()
        # fake.add_provider(Provider)
        # password = fake.password()
        # first_name = fake.name()
        # last_name = fake.name()
        # email = fake.email()
        # terminal_id = fake.name()
        # terminal_serial = fake.name()
        # #return fake.wallet_types()

        # Transaction.objects.create()

        # file_path = os.path.abspath(f"media/agent_activity_{datetime.today().strftime('%d-%m-%Y')}.xlsx")

        # base_name = os.path.basename(file_path)
        
        # with open(file_path, "rb") as excel_file:
        #     agent_activities = excel_file.read()
        #     excel_file.close()

        # send_agent_activity_email(
        #     message="This is a report on agent activities for the past week",
        #     file=agent_activities, 
        #     file_name = base_name
        # )

        pass


            
            
        # Create Wallets
        # wallet = WalletSystem.objects.get_or_create()






