from django.contrib.auth.hashers import make_password
from django.conf import settings
from django.http import FileResponse

from django.utils import timezone
from django.db.models import Sum, Q

from rest_framework import status, generics, filters
from rest_framework.views import APIView
from rest_framework.response import Response

from admin_dashboard.helpers.email import sale_rep_onboarding_email
from admin_dashboard.resources import DisputeResource
from admin_dashboard.role.models import Role
from admin_dashboard.serializers import (
        CustomerDetailsSerializer, CustomerTransactionSerializer, 
        SendEmailMessageSerializer, OtherCommissionsRecordSerializer, DisputeSerializer,
        GetCustomerTransactiondDetailSerializer, AdminDashboardLogHistorySerializer, 
        SalesRepSerializer, CustomerTransactionDetailSerializer,StockRequestSerializer,
        AgentAssignTerminalSerializer, AssignTerminalSerializer, StockSerializer, 
        AssignStockSerializer, RoleSerializer, AdminDashboardUserSerializer,
        ProspectiveAgentSerializer, GetOneProspectiveAgentSerializer, 
        AddAgentSerializer, VerifyBVNSerializer, StockInTransitSerializer, StockRecoverySerializer,
        WithdrawalBankDetailsSerializer, StockRequestUpdateSerializer, 
        DisputeBatchUploadSerializer, AddAgentWithIdSerializer, ApproveStockRequestSerializer,
        StockHistorySerializer, ZoneSerializer, BranchSerializer, 
        AccountStatementSerializer, AssignRoleSerializer, ProspectiveAgentGuarantorSerializer
    )
from admin_dashboard.models import (
    Branch, Dispute, SalesRep, ProspectiveAgent, Stock, StockHistory, AssignedTerminal, StockInTransit,
    StockRequest, AgentTerminal, Zone, StockRecovery, WithdrawalBankDetails, DisputeBatchUpload, AdminDashboardLogHistory,
    ProspectiveAgentsVerifiedBvnTable
)
from admin_dashboard.helpers.helpers import (
    Paginator, send_dispute_email, send_sms_to_pos_agent, format_phone_number, notify_admin_whatsapp_on_disputes_log,
    send_pos_agent_email, create_admin_dashboard_history, filter_by_date_two
)
from admin_dashboard.services import (
    AccountStatement, UserChartData, TerminalsChart, TransactionAmountChart, TransactionCountChart, 
    CommissionCharts, AverageCommissionChart, DashData, TransactionComparatives, WalletTransactionDetails,
    DashDataTwo, TransactionActivities, MerchantsTransactionsTop, CustomerCount, NewCustomers, ChurnCustomers,
    AgentCommission, TransactionList, OtherCommission, TerminalByPerformance, ActiveTerminals, TerminalDetails,
    StockHeadOffice, StockProviders, DeviceStockDetails, ZoneStockDetail, StateAllTerminals, IndividualTerminalTrail,
    ZoneHeadBranches, StockHistoryDetails, StockHistoryDetail, BranchStockDetails, ZoneStockRequestHistoryDet,
    StockHistoryDetailMore, TransactionAmountDay, PosAgentsDetails, PosAgentWallet, PosAgentTransactions, PosAgentsActiveSessions,
    PosAgentsDisputesList, PosAgentFormDetails, ZonesDeliveryRequest, ZoneDeliveryRequestView, ZoneStockInRequest,
    BranchStockInRequest, MerchantDetails, ServiceQuality, SalesTable, SalesBar, RepresentativeData, SalesRepDetail
)

from main.models import User, ConstantTable
from main.permissions import OtherServiceOtherPermissions, CustomIsAuthenticated, CheckIPAddresses, IsAdminUserRole
from main.views import CustomPagination
from main.serializers import CreateUserDetailInfoSerializer, UserSerializer

from accounts.models import OtherCommissionsRecord, Transaction, WalletSystem, AccountSystem
from accounts.authentication import CustomTokenAuthentication

from kyc_app.models import BVNDetail
from kyc_app.helpers.helper_functions import verify_bvn_with_uverify

from horizon_pay.models import CardTransaction, TerminalSerialTable
from horizon_pay.serializers import CardTransactionReportSerializer

from botocore.exceptions import ClientError
from datetime import datetime, date, timedelta
from tablib import Dataset
from urllib.error import URLError
from math import floor

import pandas as pd
import ast
import boto3
import botocore
import email
import json
import os
import pytz
import random
import string



search_fields = ['first_name', "last_name", "username", "email", "phone_number"]
search_prospect_fields = ['first_name', "last_name", "email", "phone_number"]


# Create your views here.

class GetReferalCode(APIView):
    permission_classes = [CustomIsAuthenticated]

    def get(self, request):
        request_user = request.user

        get_referer = SalesRep.objects.filter(sales_rep=request_user).last()
        if get_referer:

            response = {
                "message": "Referal Code Found",
                "code": get_referer.sales_rep_code
            }
            return Response(response, status=status.HTTP_200_OK)

        else:

            response = {
                "error": "550",
                "message": "No Referal Code Found"
            }

            return Response(response, status=status.HTTP_404_NOT_FOUND)


class CustomersCountView(APIView):
    serializer_class = UserSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    # queryset = User.objects.all()

    def get(self, request):
        date_filter = filter_by_date_two(request=request, user=User, datetime=datetime)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        try:
            response = CustomerCount().get_customer_count(start_date=start_date, end_date=end_date, request=request)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AllCustomersView(generics.ListAPIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    search_fields = search_fields
    filter_backends = (filters.SearchFilter,)
    queryset = User.objects.all().order_by("-id")
    serializer_class = UserSerializer
    pagination_class = CustomPagination

    def get(self, request):
        user_role = request.user.role

        date_filter = filter_by_date_two(request=self.request, datetime=datetime, user=User)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        queryset =  self.queryset.filter(date_joined__range=[start_date, end_date], role=user_role)
        queryset = Paginator.paginate(queryset=queryset, request=request)

        queryset_list = []
        for data in queryset:
            data = {
            "id": data.id,
            "unique_id": data.unique_id,
            "last_login": data.last_login if data.last_login else "",
            "date_joined": data.date_joined,
            "tag": data.tag,
            "name": data.get_full_name(),
            "email": data.email,
            "kyc_level": "completed" if data.kyc_level > 1 else "not completed",
            "device": "POS" if data.terminal_id else "Mobile"
            }

            queryset_list.append(data)

        response = {
            "customers": queryset_list,
            "count": len(queryset_list)
        }
        return Response(response, status=status.HTTP_200_OK)

    
class ActiveCustomersView(generics.ListAPIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    search_fields = search_fields
    filter_backends = (filters.SearchFilter,)
    serializer_class = UserSerializer
    pagination_class = CustomPagination

    def get(self, request):
        user_role = request.user.role

        date_filter = filter_by_date_two(request=self.request, datetime=datetime, user=User)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        active_users_qs = Transaction.objects.exclude(status = "FAILED").distinct("user__email").values("user__email")
        active_users =  User.objects.filter(date_joined__range=[start_date, end_date], email__in=active_users_qs, role=user_role)
        active_users = Paginator.paginate(request=request, queryset=active_users)

        active_users_list = []
        for data in active_users:
            data = {
            "id": data.id,
            "unique_id": data.unique_id,
            "last_login": data.last_login,
            "date_joined": data.date_joined,
            # "tag": data.tag,
            "name": data.get_full_name(),
            "email": data.email,
            "kyc_level": "completed" if data.kyc_level > 1 else "not completed",
            "device": "POS" if data.terminal_id else "Mobile"
            }

            active_users_list.append(data)

        response = {
            "active_customers": active_users_list,
            "count": len(active_users_list)
        }
        return Response(response, status=status.HTTP_200_OK)


class InActiveCustomersView(generics.ListAPIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    search_fields = search_fields
    filter_backends = (filters.SearchFilter,)
    serializer_class = UserSerializer
    pagination_class = CustomPagination

    def get(self, request):
        user_role = request.user.role

        date_filter = filter_by_date_two(request=self.request, datetime=datetime, user=User)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        active_users_qs = Transaction.objects.exclude(status = "FAILED").distinct("user__email").values("user__email")
        inactive_users =  User.objects.filter(Q(date_joined__range=[start_date, end_date]) & ~Q(email__in=active_users_qs), role=user_role)
        inactive_users = Paginator.paginate(request=request, queryset=inactive_users)

        inactive_users_list = []
        for data in inactive_users:
            data = {
            "id": data.id,
            "unique_id": data.unique_id,
            "last_login": data.last_login,
            "date_joined": data.date_joined,
            # "tag": data.tag,
            "name": data.get_full_name(),
            "email": data.email,
            "kyc_level": "completed" if data.kyc_level > 1 else "not completed",
            "device": "POS" if data.terminal_id else "Mobile"
            }

            inactive_users_list.append(data)

        response = {
            "active_customers": inactive_users_list,
            "count": len(inactive_users_list)
        }
        return Response(response, status=status.HTTP_200_OK)


class NewCustomersView(generics.ListAPIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    search_fields = search_fields
    filter_backends = (filters.SearchFilter,)
    serializer_class = UserSerializer
    pagination_class = CustomPagination

    def get(self, request):
        date_filter = filter_by_date_two(request=self.request, datetime=datetime, user=User)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        try:
            response = NewCustomers().get_new_customers(request, start_date, end_date)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class ChurnCustomersView(generics.ListAPIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    search_fields = search_fields
    filter_backends = (filters.SearchFilter,)
    serializer_class = UserSerializer
    pagination_class = CustomPagination

    def get(self, request):
        date_filter = filter_by_date_two(request=self.request, datetime=datetime, user=User)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        try:
            response = ChurnCustomers().get_churn_customers(request, start_date, end_date)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SuspendedCustomersView(generics.ListAPIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    search_fields = search_fields
    filter_backends = (filters.SearchFilter,)
    queryset = User.objects.all().order_by("-date_joined")
    serializer_class = UserSerializer
    pagination_class = CustomPagination

    def get(self, request):
        user_role = request.user.role

        date_filter = filter_by_date_two(request=self.request, datetime=datetime, user=User)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        suspended_customers = self.queryset.filter(date_joined__range=[start_date, end_date], is_suspended=True, role=user_role)
        suspended_customers = Paginator.paginate(request=request, queryset=suspended_customers)
        
        suspended_customers_list = []
        for data in suspended_customers:
            data = {
            "id": data.id,
            "unique_id": data.unique_id,
            "last_login": data.last_login,
            "date_joined": data.date_joined,
            # "tag": data.tag,
            "name": data.get_full_name(),
            "email": data.email,
            "kyc_level": "completed" if data.kyc_level > 1 else "not completed",
            "device": "POS" if data.terminal_id else "Mobile"
            }

            suspended_customers_list.append(data)

        response = {
            "suspended_customers": suspended_customers_list,
            "count": len(suspended_customers_list)
        }
        return Response(response, status=status.HTTP_200_OK)




class CustomerDetailsView(APIView):
    serializer_class = CustomerDetailsSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = User.objects.all()

    def get(self, request, id):
        user = self.queryset.get(id=id)
        user_account =  AccountSystem.objects.filter(user=user).last()
        kyc_status = user.kyc_level / 3 * 100

        response = {
            "name": user.get_full_name(),
            "username": user.first_name,
            "email": user.email,
            "phone_number": user.phone_number,
            "last_login": user.last_login,
            "device_type": "POS" if user.terminal_id else "Mobile",
            "kyc_status": kyc_status,

            "address": {
                    "state": user.state,
                    "city": user.state,
                    "local_goverment": user.lga,
                    "street": user.street,
                    "gender": user.gender,
                    "marital_status": user.marital_status,
                    "bvn": user.check_kyc.bvn_rel.bvn_number
                },
            "account_details": {
                "bank_name": user_account.bank_name,
                "account_number": user_account.account_number
            }
        }

        return Response(response, status=status.HTTP_200_OK)


class CustomerWalletsBalanceView(APIView):
    serializer_class = CustomerDetailsSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request, id):
        user = User.objects.get(id=id)

        overall_wallet_balance = WalletSystem.objects.filter(user=user, wallet_type="COLLECTION").last()
        bills_utilities_commission_wallet_qs = WalletSystem.objects.filter(user=user, wallet_type="COMMISSIONS").last()
        spend_wallet_qs = WalletSystem.objects.filter(user=user, wallet_type="SPEND").last()
        quick_savings_wallet_qs = WalletSystem.objects.filter(user=user, wallet_type="SAVINGS").last()

        response = {
            "general": {
                "wallet": overall_wallet_balance.available_balance if overall_wallet_balance else 0.00,
                "bills_utilities_commission": bills_utilities_commission_wallet_qs.available_balance if bills_utilities_commission_wallet_qs else 0.00,
                "referral_balance": 0.00
            },
            "savings_balance": {
                "quick_savings": quick_savings_wallet_qs.available_balance if quick_savings_wallet_qs else 0.00,
                "hala_savings": 0.00,
                "chest_lock_savings": 0.00
            },
            "connected_banks": {
                "balance": 0.00
            },
            "budget": {
                "spend_wallet_balance": spend_wallet_qs.available_balance if spend_wallet_qs else 0.00
            }
        }

        return Response(response, status=status.HTTP_200_OK)


class CustomerTransactionsView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    search_fields = ["user__email", "status"]
    filter_backends = (filters.SearchFilter,)
    queryset = Transaction.objects.exclude(transaction_leg__in=["INTERNAL", "COMMISSIONS"], is_reversed=False).order_by("-date_created")
    serializer_class = CustomerTransactionSerializer
    pagination_class = CustomPagination

    def get(self, request, id):
        date_filter = filter_by_date_two(request=self.request, datetime=datetime, user=User)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        user = User.objects.get(id=id)
        queryset = self.queryset.filter(date_created__range=[start_date, end_date], user=user)
        queryset = Paginator.paginate(request=request, queryset=queryset)
        serializer = self.serializer_class(queryset, many=True)
        
        response = {
            "transactions": serializer.data,
            "count": len(serializer.data)
        }

        return Response(response, status=status.HTTP_200_OK)


class GetCustomerTransactionDetails(APIView):
    serializer_class = GetCustomerTransactiondDetailSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def post(self, request):
        serializer = GetCustomerTransactiondDetailSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        email = serializer.validated_data.get("email")
        trans_id = serializer.validated_data.get("trans_id")

        user_queryset = User.objects.filter(email=email).first()

        try:
            transaction_queryset = Transaction.objects.get(transaction_id=trans_id, user = user_queryset)
        except Transaction.DoesNotExist:
            data = {}
            return Response(data, status=status.HTTP_200_OK)
        else:
            transaction_serializer = CustomerTransactionSerializer(transaction_queryset)

            return Response(transaction_serializer.data, status=status.HTTP_200_OK)

class IncompleteKycView(APIView):
    serializer_class = UserSerializer
    queryset_class =  User.objects.all().order_by("-date_joined")
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request):
        user_role = request.user.role

        date_filter = filter_by_date_two(request=self.request, datetime=datetime, user=User)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        incomplete_kyc_customers = self.queryset_class.filter(date_joined__range=[start_date, end_date], kyc_level__lt=2, role=user_role)
        incomplete_kyc_customers = Paginator.paginate(request=request, queryset=incomplete_kyc_customers)
        # serializer = self.serializer_class(incomplete_kyc_customers, many=True)

        incomplete_kyc_customers_list = []
        for data in incomplete_kyc_customers:
            data = {
            "id": data.id,
            "unique_id": data.unique_id,
            "last_login": data.last_login,
            "date_joined": data.date_joined,
            # "tag": data.tag,
            "name": data.get_full_name(),
            "email": data.email,
            "kyc_level": "completed" if data.kyc_level > 1 else "not completed",
            "device": "POS" if data.terminal_id else "Mobile"
            }

            incomplete_kyc_customers_list.append(data)

        response = {
            "incomplete_customers": incomplete_kyc_customers_list,
            "count": len(incomplete_kyc_customers_list)
        }
        return Response(response, status=status.HTTP_200_OK)

class IncompleteCustomerRegistrationsView(APIView):
    serializer_class = UserSerializer
    queryset_class =  User.objects.all()
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request):
        user_role = request.user.role

        date_filter = filter_by_date_two(request=self.request, datetime=datetime, user=User)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        incomplete_customer_registrations = self.queryset_class.filter(date_joined__range=[start_date, end_date], has_login_pin=False, role=user_role)
        incomplete_customer_registrations = Paginator.paginate(request=request, queryset=incomplete_customer_registrations)

        incomplete_customers_list = []
        for data in incomplete_customer_registrations:
            data = {
            "id": data.id,
            "unique_id": data.unique_id,
            "last_login": data.last_login,
            "date_joined": data.date_joined,
            # "tag": data.tag,
            "name": data.get_full_name(),
            "email": data.email,
            "kyc_level": "completed" if data.kyc_level > 1 else "not completed",
            "device": "POS" if data.terminal_id else "Mobile"
            }

            incomplete_customers_list.append(data)

        response = {
            "incomplete_customers": incomplete_customers_list,
            "count": len(incomplete_customers_list)
        }
        return Response(response, status=status.HTTP_200_OK)


class SalesRepOnboardingView(APIView):
    serializer_class = CreateUserDetailInfoSerializer
    permission_classes = [OtherServiceOtherPermissions]

    def post(self, request):

        serializer = CreateUserDetailInfoSerializer(data=request.data)

        if serializer.is_valid():
            phone_number = User.format_number_from_back_add_234(
                serializer.validated_data["phone_number"]
            )
            username = serializer.validated_data["username"]
            first_name = serializer.validated_data["first_name"]
            last_name = serializer.validated_data["last_name"]
            email = serializer.validated_data["email"]
            state = serializer.validated_data["state"]
            lga = serializer.validated_data["lga"]
            nearest_landmark = serializer.validated_data["nearest_landmark"]
            street = serializer.validated_data["street"]
            type_of_user = serializer.validated_data["type_of_user"]

            len_of_phone_number = len(serializer.validated_data["phone_number"])
            raw_phone_number = serializer.validated_data["phone_number"]

            if not first_name.isalpha():
                response = {
                    "error": "172",
                    "message": "invalid first name. first name contains special characters" 
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if not last_name.isalpha():
                response = {
                    "error": "173",
                    "message": "invalid first name. last name contains special characters"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)


            if len_of_phone_number == 13 and not raw_phone_number.startswith("234"):
                response = {
                    "error": "174",
                    "message": f"entered phone number is incorrect. it is {len_of_phone_number} numbers."
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)


            if len_of_phone_number != 11 and len_of_phone_number != 13:
                response = {
                    "error": "177",
                    "message": f"entered phone number is incorrect. it is {len_of_phone_number} numbers."
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)


            check_user = User.objects.filter(Q(email=email) | Q(username=username) | Q(phone_number=phone_number)).last()
            if check_user:
                get_sales_rep = SalesRep.objects.filter(sales_rep=check_user).last()
                if get_sales_rep:
                    # user already exists as sales rep
                    response = {
                        "error": "129",
                        "message": "user with email, phone number or username already exists as a sales rep",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)
                else:
                    check_user.type_of_user = "SALES_REP"
                    check_user.save()

                    sales_rep_code = SalesRep.objects.create(sales_rep=check_user)

                    send_sales_rep_email = sale_rep_onboarding_email(
                        email=check_user.email,
                        pwd="",
                        name=check_user.first_name,
                        user_exists=True
                    )

                    response = {
                        "message": "Account found and converted successfully to a Sales REP account",
                        "data": {
                            "sales_rep_code": sales_rep_code.sales_rep_code
                        }
                    }
                    return Response(data = response, status=status.HTTP_201_CREATED)
            else:
                pwd = "".join(random.choice(string.ascii_uppercase + string.digits) for _ in range(8))
                user_instance = User.objects.create(
                    username=username,
                    first_name=first_name,
                    last_name=last_name,
                    phone_number=phone_number,
                    email=email,
                    state=state,
                    lga=lga,
                    nearest_landmark=nearest_landmark,
                    street=street,
                    type_of_user=type_of_user,
                    password=make_password(pwd)

                )

                sales_rep_code = SalesRep.objects.create(sales_rep = user_instance)

                response = {
                    "message": "Sales REP created successfully",
                    "data": {
                        "sales_rep_code": sales_rep_code.sales_rep_code
                    }
                }

                send_sales_rep_email = sale_rep_onboarding_email(
                    email=user_instance.email,
                    pwd=pwd,
                    name=user_instance.first_name,
                    user_exists=False
                )
                return Response(response, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)




        user = User.objects.filter(Q(email=email) | Q(username=username) | Q(phone_number=phone_number)).first()
        if user:
            # user already exists
            # create sales rep instance
            if SalesRep.objects.filter(user=user).exists():
                # user already exists as sales rep
                data = {
                    "message": "User already exists as sales rep",
                    "status": "error",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            sales_rep_code = SalesRep.objects.create(user = user)

            sale_rep_onboarding_email(user.email,"", user.first_name,user_exists = True)
            data = {
                "message": "Created",
                "data": {
                    "sales_rep_code": sales_rep_code.sales_rep_code
                }
            }
            return Response(data = data, status=status.HTTP_201_CREATED)

        else: # create user and sales rep instance
            pwd = "".join(random.choice(string.ascii_uppercase + string.digits) for _ in range(8))
            user_instance = User.objects.create(
                username=username,
                first_name=first_name,
                last_name=last_name,
                phone_number=phone_number,
                email=email,
                state=state,
                lga=lga,
                nearest_landmark=nearest_landmark,
                street=street,
                password=make_password(pwd)
            )

            sales_rep_code = SalesRep.objects.create(user = user_instance)
            data = {
                "message": "Created",
                "data": {
                    "sales_rep_code": sales_rep_code.sales_rep_code
                }
            }
            sale_rep_onboarding_email(user_instance.email,pwd, user_instance.first_name,user_exists = False)
            return Response(data = data, status=status.HTTP_201_CREATED)


class SuspendCustomerView(APIView):
    serializer_class = CustomerDetailsSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = User.objects.all()

    def patch(self, request, id):
        customer = self.queryset.get(id=id)

        self.serializer_class.is_valid(raise_exception=True)

        reason = self.serializer_class.validated_data["reason"]

        User.suspend_user(
            user=customer,
            reason=reason,
            request=request
        )

        return Response({"message": "customer suspended"}, status=status.HTTP_200_OK)


###################################################################################################################################
# DASHBOARD

class DashboardAPIView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    filter_backends = (filters.SearchFilter,)
    pagination_class = CustomPagination

    def get(self, request):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        try:
            response = DashData().get_dashboard_data(start_date=start_date, end_date=end_date, request=request)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error":str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DashboardTwoView(APIView):
    serializer_class = CustomerTransactionSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        try:
            response = DashDataTwo.get_dashboard_data_two(start_date=start_date, end_date=end_date, request=request)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class DashboardUsersChartView(APIView):
    serializer_class = UserSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request):

        try:
            queryset = UserChartData().get_user_chart_data(request=request)
            return Response(queryset, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class DashboardTransactionComparatives(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    filter_backends = (filters.SearchFilter,)
    pagination_class = CustomPagination

    def get(self, request):
        try:
            response = TransactionComparatives(request).get_transaction_comparatives()
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DashboardCashoutComparatives(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    filter_backends = (filters.SearchFilter,)
    pagination_class = CustomPagination

    def get(self, request):
        try:
            response = TransactionComparatives(request).get_cashout_comparatives()
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DashboardTransfersComparatives(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    filter_backends = (filters.SearchFilter,)
    pagination_class = CustomPagination

    def get(self, request):
        try:
            response = TransactionComparatives(request).get_transaction_comparatives_transfer_data()
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DashboardTopTransactionsComparatives(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    filter_backends = (filters.SearchFilter,)
    pagination_class = CustomPagination

    def get(self, request):
        try:
            response = TransactionComparatives(request).get_comparatives_top10_transactions()
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DashboardTopTransactionsComparativesChart(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    filter_backends = (filters.SearchFilter,)
    pagination_class = CustomPagination

    def get(self, request):
        try:
            response = TransactionComparatives(request).get_transaction_comparatives_chart()
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DashboardTransactionActivitesView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = CustomerTransactionSerializer

    def get(self, request):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        try:
            response = TransactionActivities.get_transaction_activites(start_date=start_date, end_date=end_date, request=request)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class DashboardAgentCommissionView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = OtherCommissionsRecordSerializer
    queryset =  OtherCommissionsRecord.objects.filter(Q(transaction_owner="AGENT") | Q(transaction_owner="LIBERTY"))

    def get(self, request):
        queryset = self.queryset
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")
      
        try:
            response = AgentCommission().get_agent_commission(queryset, start_date, end_date)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error":str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DashboardChartTerminalsView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    filter_backends = (filters.SearchFilter,)
    # pagination_class = CustomPagination
    def get(self, request):
        """
        This view returns the the data for all the terminals that may be required anywhere on the admin dashboard charts
        """
        try:
            response = TerminalsChart().get_terminals_chart(request)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DashboardChartsTransactionAmountView(APIView):
    serializer_class = CustomerTransactionSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        try:
            response = TransactionAmountChart().get_transactions_chart(request)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

class DashboardChartsTransactionCountView(APIView):
    serializer_class = CustomerTransactionSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request):
        try:
            response = TransactionCountChart().get_transaction_count_chart(request)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DashboardChartsCommissionView(APIView):
    serializer_class = CustomerTransactionSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request):        
        try:
            response = CommissionCharts().get_commission_charts(request)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        
class DashboardChartsAverageCommissionView(APIView):
    serializer_class = CustomerTransactionSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request):
        try:
            response = AverageCommissionChart().get_average_commission_chart(request)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DashboardWalletTransactionsDetails(APIView):
    serializer_class = CustomerTransactionSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = Transaction.objects.all()

    def get(self, request):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        try:
            response = WalletTransactionDetails.get_wallet_transaction_details(request=request, start_date=start_date, end_date=end_date)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error":str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DashboardFloatAccountsView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = UserSerializer

    def get(self, request):
        """get float accounts balance"""
        float_user = WalletSystem.get_float_user()
        wallet_balance = WalletSystem.objects.filter(user=float_user, wallet_type="FLOAT").last().available_balance
        # vfd_float_balance = AccountSystem.objects.filter(user=float_user, wallet_type="FLOAT", account_provider="VFD").last().available_balance
        # vfd_float_balance = AccountSystem.objects.filter(user=float_user, wallet_type="FLOAT", account_provider="WOVEN").last().available_balance
        # vfd_float_balance = AccountSystem.objects.filter(user=float_user, wallet_type="FLOAT", account_provider="VFD").last().available_balance

        response = {
            "vfd": wallet_balance,
            "woven": None,
            "wema": None,
            "parallex": None
        }

        return Response(response, status=status.HTTP_200_OK)


# Prospective Agents
class AllProspectiveAgentView(generics.ListAPIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    search_fields = search_prospect_fields
    filter_backends = (filters.SearchFilter,)
    queryset = ProspectiveAgent.objects.all().order_by("-id")
    serializer_class = ProspectiveAgentSerializer
    pagination_class = CustomPagination


class GetOneProspectiveAgentView(APIView):
    permission_classes = [CustomIsAuthenticated  ]
    def get(self, request, id):
        serializer = GetOneProspectiveAgentSerializer()
        # serializer.is_valid(raise_exception=True)
        try:
            agent_instance = ProspectiveAgent.objects.get(id=id)
            sales_rep_instance = User.objects.filter(email=agent_instance.sales_rep.sales_rep.email).first()
            if sales_rep_instance:
                response = {
                    "sales_rep": f"{sales_rep_instance.first_name} {sales_rep_instance.last_name}",
                    "prospect_agent": {
                        "name":f"{agent_instance.first_name} {agent_instance.last_name}",
                        "phone_number":agent_instance.phone_number,
                        "email":agent_instance.email,
                        "source_account":agent_instance.source_account,
                        "prospect_action":agent_instance.prospect_action,
                        "prospect_status":agent_instance.prospect_status,
                        "first_address":agent_instance.first_address,
                        "second_address":agent_instance.second_address,
                        "city":agent_instance.city,
                        "state":agent_instance.state,
                        "lga":agent_instance.lga,
                        "postal_zipcode":agent_instance.postal_zipcode,
                        "bvn_number":agent_instance.bvn_number,
                        "business_name":agent_instance.business_name,
                        "device":agent_instance.device,
                        "unique_id":agent_instance.unique_id,
                        "pick_up_option":agent_instance.pick_up_option,
                        "next_of_kin_name":agent_instance.next_of_kin_name,
                        "next_of_kin_phone_number":agent_instance.next_of_kin_phone_number,
                        "next_of_kin_address":agent_instance.next_of_kin_address,
                        "location":agent_instance.location,
                        "date":agent_instance.date,
                        }
                }
                return Response(response,status=status.HTTP_200_OK)
            else:
                response = {
                    "sales_rep": None,
                    "prospect_agent": {
                        "name":f"{agent_instance.first_name} {agent_instance.last_name}",
                        "phone_number":agent_instance.phone_number,
                        "email":agent_instance.email,
                        "source_account":agent_instance.source_account,
                        "prospect_action":agent_instance.prospect_action,
                        "prospect_status":agent_instance.prospect_status,
                        "first_address":agent_instance.first_address,
                        "second_address":agent_instance.second_address,
                        "city":agent_instance.city,
                        "state":agent_instance.state,
                        "lga":agent_instance.lga,
                        "postal_zipcode":agent_instance.postal_zipcode,
                        "bvn_number":agent_instance.bvn_number,
                        "business_name":agent_instance.business_name,
                        "device":agent_instance.device,
                        "unique_id":agent_instance.unique_id,
                        "pick_up_option":agent_instance.pick_up_option,
                        "next_of_kin_name":agent_instance.next_of_kin_name,
                        "next_of_kin_phone_number":agent_instance.next_of_kin_phone_number,
                        "next_of_kin_address":agent_instance.next_of_kin_address,
                        "location":agent_instance.location,
                        "date":agent_instance.date,
                        }
                }
                return Response(response,status=status.HTTP_200_OK)

        except ProspectiveAgent.DoesNotExist:
            return Response({"message":"user not found"}, status=status.HTTP_200_OK)


class AddAgentView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    def post(self, request):
        serializer = AddAgentSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        phone_number = ProspectiveAgent.format_number_from_back_add_234(
                serializer.validated_data.get("phone_number")
            )
        next_of_kin_phone_number = ProspectiveAgent.format_number_from_back_add_234(
                serializer.validated_data.get("next_of_kin_phone_number")
            )
        if len(serializer.validated_data.get("phone_number")) < 11 or len(serializer.validated_data.get("next_of_kin_phone_number")) <11:
            serializer.error_message = {"message":"phone number must be between 0 and 11 characters"}
            return Response(serializer.error_message, status=status.HTTP_400_BAD_REQUEST)
        # check if prospect_agent already exists
        if ProspectiveAgent.objects.filter(
                                            Q(phone_number=phone_number) |
                                            Q(email=serializer.validated_data["email"])
                                        ).exists() or User.objects.filter(email=email).exists():
            data = {
                "message": "Prospect already exists"
            }
            return Response (data, status=status.HTTP_400_BAD_REQUEST)

        sales_rep = SalesRep.objects.filter(sales_rep = request.user.id).last()
        serializer.validated_data["phone_number"] = phone_number
        serializer.validated_data["next_of_kin_phone_number"] = next_of_kin_phone_number
        serializer.validated_data["sales_rep"] = sales_rep
        serializer.validated_data["onboarding_stage_level"] = 1

        ProspectiveAgent.objects.create(**serializer.validated_data)
        sales_agent_qs = ProspectiveAgent.objects.filter(phone_number=phone_number)
        data = ProspectiveAgentSerializer(sales_agent_qs.first())

        response = {
                    "data": {
                        "first_name": data.data["first_name"],
                        "last_name": data.data["last_name"],
                        "phone_number": data.data["phone_number"],
                        "email": data.data["email"],
                        "first_address": data.data["first_address"],
                        "state": data.data["state"],
                        "postal_zipcode": data.data["postal_zipcode"],
                        "city": data.data["city"],
                        "lga": data.data["lga"],
                        "bvn_number": data.data["bvn_number"],
                    },
                    "message":"Prospective agent added successfully"
                }
        return Response(response, status=status.HTTP_201_CREATED)

# Onboard Agent With AgentId
class AddAgentWithAgentId(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = AddAgentWithIdSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid(raise_exception=True):
            agent_id = serializer.validated_data["agent_id"]

            agent_data = User.objects.get(unique_id=agent_id)
            agent_bvn_num = BVNDetail.objects.get(kyc__user=agent_data).bvn_number

            response = {
                "first_name": agent_data.first_name if agent_data.first_name else "",
                "last_name": agent_data.last_name if agent_data.last_name else "",
                "username": agent_data.username if agent_data.username else "",
                "phone_number": agent_data.phone_number if agent_data.phone_number else "",
                "email": agent_data.email if agent_data.email else "",
                "first_address": agent_data.street if agent_data.street else "",
                "second_address": "",
                "city": "",
                "state": agent_data.state if agent_data.state else "",
                "lga": agent_data.lga if agent_data.lga else "",
                "postal_zipcode": "",
                "pick_up_option": "",
                "profile_picture": "",
                "next_of_kin_name": "",
                "next_of_kin_phone_number": "",
                "next_of_kin_relationship": "",
                "source_account": "",
                "sales_rep": "",
                "bvn_number": agent_bvn_num if agent_bvn_num else ""
                }

            return Response(response, status=status.HTTP_200_OK)                  


"""Verify Agent BVN"""
class VerifyAgentBVNView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    def post(self, request, **params):
        agent_phone_number = params["phone_number"]
        serializer = VerifyBVNSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        bvn_number = serializer.validated_data["bvn_number"]
        last_name = serializer.validated_data["last_name"]

        existing_successful_verification = ProspectiveAgentsVerifiedBvnTable.objects.filter(
            phone_number = agent_phone_number,
            bvn_number = bvn_number,
            verification_successful = True
            ).last()

        # Verification is done only once for give payload details for a particular user
        # Records are kept for similar subsequent verifications
        if existing_successful_verification is None:
            response = verify_bvn_with_uverify(bvn_number=bvn_number, last_name=last_name)
            # print("!!!!!!!!!!!!!We are hitting YouVerify")
            if response["statusCode"] == 200:
                try:
                    firstName=response["data"]["data"]["firstName"]
                    middleName=response["data"]["data"]["middleName"]
                    lastName=response["data"]["data"]["lastName"]
                    dateOfBirth=response["data"]["data"]["dateOfBirth"]
                    email=response["data"]["data"].get("email")
                    mobile=response["data"]["data"]["mobile"]

                    bvn_data = {
                        "bvn_number": bvn_number,
                        "firstName": firstName,
                        "middleName": middleName,
                        "lastName": lastName,
                        "dateOfBirth": dateOfBirth,
                        "email": email,
                        "mobile": mobile
                        }
                    
                    if agent_phone_number == bvn_data.get("mobile"):
                        detail = {
                                "bvn_data":bvn_data,
                                "message":"Bvn Verified Successfully"
                            }
                        verification_successful = True
                        return Response(detail, status=status.HTTP_200_OK)
                    else:
                        detail = {
                                "bvn_data":bvn_data,
                                "message":"Phone number fails to match BVN phone number"
                            }
                        verification_successful = False
                        return Response(detail, status=status.HTTP_400_BAD_REQUEST)
                finally:
                    verification_data = {
                    "phone_number": agent_phone_number,
                    "bvn_number": bvn_number,
                    "uverify_payload": json.dumps(bvn_data),
                    "verification_successful": verification_successful
                    }
                    ProspectiveAgentsVerifiedBvnTable.objects.create(**verification_data)
            else:
                detail = {
                            "message":"Error Verifying BVN"
                        }
                return Response(detail, status=status.HTTP_400_BAD_REQUEST)
        else:
            bvn_data = json.loads(existing_successful_verification.uverify_payload)
            detail = {
                        "bvn_data":bvn_data,
                        "message":"Bvn Verified Successfully"
                        }
            # print("We saved Money on this verification!!!!!!!!!!!!!!!!!!!!!!")
            return Response(detail, status=status.HTTP_200_OK)

        # try:
        #     user = ProspectiveAgent.objects.get(phone_number=agent_phone_number)
        #     sales_agent_qs = ProspectiveAgent.objects.filter(id=user.id)
        #     data = ProspectiveAgentSerializer(sales_agent_qs.first())
        #     if sales_agent_qs.exists():
        #         ### verify agent BVN
        #         response = verify_bvn_with_uverify(bvn_number=bvn_number, last_name=user.last_name)
        #         if response["statusCode"] == 200:
        #             firstName=response["data"]["data"]["firstName"]
        #             middleName=response["data"]["data"]["middleName"]
        #             lastName=response["data"]["data"]["lastName"]
        #             dateOfBirth=response["data"]["data"]["dateOfBirth"]
        #             email=response["data"]["data"].get("email")
        #             mobile=response["data"]["data"]["mobile"]

        #             bvn_data = {
        #                 "firstName": firstName,
        #                 "middleName": middleName,
        #                 "lastName": lastName,
        #                 "dateOfBirth": dateOfBirth,
        #                 "email": email,
        #                 "mobile": mobile,
        #             }
        #             detail = {
        #                         "user_data":data.data,
        #                         "bvn_data":bvn_data,
        #                         "message":"Next Page Compare User Data"
        #                     }
        #             user.bvn_payload = response
        #             user.bvn_number = bvn_number
        #             user.save()
        #             return Response(detail,status=status.HTTP_200_OK)
        #         else:
        #             detail = {
        #                         "user_data":data.data,
        #                         "bvn_data":response,
        #                         "message":"Error Verifying BVN"
        #                     }
        #             return Response(detail, status=status.HTTP_400_BAD_REQUEST)
        #     else:
        #         return Response({"message":"user does not exist"},status=status.HTTP_400_BAD_REQUEST)
        # except ProspectiveAgent.DoesNotExist:
        #     return Response({"message":"user does not exist"},status=status.HTTP_400_BAD_REQUEST)
            #   return Response("Hi")
        # return Response("hI")

class ProspectiveAgentGuarantorFormView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = ProspectiveAgentGuarantorSerializer

    def post(self, request, phone_number):
        try:
            prospective_agent = ProspectiveAgent.objects.get(phone_number=phone_number)
        except ProspectiveAgent.DoesNotExist:
            return Response({"error": "This agent Does not exist. Please fill Agents onboarding form"}, 
                            status=status.HTTP_400_BAD_REQUEST)
        
        serializer = self.serializer_class(data = request.data)

        if serializer.is_valid():
            guarantor_one_email = serializer.validated_data.get("guarantor_one_email", "0")
            guarantor_two_email = serializer.validated_data.get("guarantor_two_email", "0")
            guarantor_one_phone = serializer.validated_data.get("guarantor_one_phone_number", "0")
            guarantor_two_phone = serializer.validated_data.get("guarantor_two_phone_number", "0")

            if (guarantor_one_email == prospective_agent.guarantor_one_email or
                guarantor_one_email == prospective_agent.guarantor_two_email or
                guarantor_one_phone == prospective_agent.guarantor_one_phone_number or
                guarantor_one_phone == prospective_agent.guarantor_two_phone_number or
                guarantor_two_phone == prospective_agent.guarantor_one_phone_number or
                guarantor_two_email == prospective_agent.guarantor_one_email):
            
                return Response({"error": "You have registered this guarantor in the past"}, status=status.HTTP_400_BAD_REQUEST)
            
            serializer.validated_data["phone_number"] = phone_number
            if prospective_agent.onboarding_stage_level == 1:
                serializer.validated_data["onboarding_stage_level"] = 2
            elif prospective_agent.onboarding_stage_level == 2:
                serializer.validated_data["onboarding_stage_level"] = 3
            else:
                pass
            ProspectiveAgent.objects.update(**serializer.validated_data)
        else:
            return Response({"error": "Invalid details provided"}, status=status.HTTP_400_BAD_REQUEST)
        return Response({"message": "Guarantor details updated"}, status=status.HTTP_201_CREATED)
        

class AvailableTerminalView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    def get(self, request, **params):
        agent_phone_number = params["phone_number"]

        try:
            user = ProspectiveAgent.objects.get(phone_number=agent_phone_number)


            available = AssignedTerminal.objects.filter(
                                                            Q(sales_rep=user.sales_rep.id) &
                                                            Q(status="PICKED_UP")
                                                    )
            if available.exists():
                terminals = []
                for x in available:
                    find_term = User.objects.filter(terminal_id=x.stock.terminal_id).first()
                    if not find_term:
                        terminals.append(x.stock.terminal_id)
                    else:
                        pass

                response = {
                    "data": terminals,
                    "sales_rep": f'{request.user.first_name} {request.user.last_name}',
                    "message": "Next Page Assign Terminal"
                }
                return Response(response,status=status.HTTP_200_OK)
            else:
                response = {
                    "sales_rep": f'{request.user.first_name} {request.user.last_name}',
                    "message": "Previous Page No available terminal"
                }
                return Response(response,status=status.HTTP_200_OK)


        except ProspectiveAgent.DoesNotExist:
            return Response({"message":"user does not exist"},status=status.HTTP_400_BAD_REQUEST)

# Prospective Agents Send Sms
class SendProspectiveAgentSMS(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = ProspectiveAgentSerializer
    queryset = ProspectiveAgent.objects.all()
    # lookup_field = "id"

    def post(self, request, id):
        serializer = SendEmailMessageSerializer(data=request.data)
        if serializer.is_valid(raise_exception=True):
            message = serializer.data["message"]
            title = serializer.data["title"]
        try:
            agent = self.queryset.get(id=id)
            agent_phone_number = agent.phone_number
            agent_first_name = agent.first_name
            try:
                send_sms_to_pos_agent(phone_number=agent_phone_number, message=message)
                return Response(serializer.data, status=status.HTTP_200_OK)
            except URLError:
                return Response({"message" :"message not sent. Please try again with a better internet"})
        except ProspectiveAgent.DoesNotExist:
            return Response({"error": "Agent does not exist"})

# Prospective Agents Send Email
class SendProspectiveAgentEmail(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = ProspectiveAgentSerializer
    queryset = ProspectiveAgent.objects.all()

    def post(self, request, id):
        serializer = SendEmailMessageSerializer(data=request.data)
        if serializer.is_valid(raise_exception=True):
            message = serializer.data["message"]
            title = serializer.data["title"]
            schedule = serializer.data["schedule"]

            try:
                attach = request.FILES.getlist("attach")
            except KeyError:
                attach = ""
        try:
            agent = self.queryset.get(id=id)
            agent_email = agent.email

            send_pos_agent_email(
                                  email=agent_email,
                                  template="pos_agent_email.html",
                                  email_subject=title,
                                  message = message,
                                  files = attach
                                )
        except SyntaxError:
            return Response("email send failed")

        return Response({"message": f"email sent to {agent_email}"}, status=status.HTTP_200_OK)





# @method_decorator()
# @allowed_users(allowed_roles=['Admin'])
# @method_decorator(allowed_users(allowed_roles=['Admin']), name="dispatch")
class AssignTerminalView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    def post(self, request, **params):
        phone_number= params["phone_number"]
        serializer = AssignTerminalSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        terminal_id = serializer.validated_data["terminal_id"]
        try:
            user = ProspectiveAgent.objects.get(phone_number=phone_number)
            terminals = Stock.objects.filter(terminal_id=terminal_id).last()
            if terminals:

                terminal_data = {
                    "terminal_id":terminals.terminal_id,
                    "device_name":terminals.device_name,
                    "serial_number":terminals.serial_number,
                    "model_number":terminals.model_number,
                }
                response = {
                    "terminal_id": terminal_id,
                    "terminal_assigner": f"{request.user.first_name} {request.user.last_name}",
                    "terminal_data": terminal_data
                }

                # remove_available = AssignedTerminal.objects.get(terminal_id=terminal_data)

                # remove_available.status = "AGENT_ASSIGNED"
                # remove_available.save()

                return Response(response,status=status.HTTP_200_OK)
            else:

                response ={
                    "terminal_id": terminal_id,
                    "terminal_assigner": f"{request.user.first_name} {request.user.last_name}",
                    "message":"terminal not in stock"
                }
                return Response (response, status=status.HTTP_404_NOT_FOUND)
        except ProspectiveAgent.DoesNotExist:
            return Response({"message":"user does not exist"},status=status.HTTP_400_BAD_REQUEST)


class AgentAssignTerminalView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    def post(self, request, **params):
        phone_number= params["phone_number"]
        serializer = AgentAssignTerminalSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        terminal_id = serializer.validated_data.get('terminal_id')
        try:
            user = ProspectiveAgent.objects.get(phone_number=phone_number)

            # check if terminal has been assigned to someone
            agent_instance = User.objects.filter(phone_number=phone_number).first()
            if agent_instance:
                if agent_instance.terminal_id is not None:
                    return Response({"message":"agent has already been assigned a terminal"},status=status.HTTP_400_BAD_REQUEST)
                else:
                    terminals = User.objects.filter(terminal_id=terminal_id).last()
                    if terminals:
                        response = {
                            "terminal_id": terminal_id,
                            "terminal_assigner": "terminal already assigned",
                        }
                        return Response(response,status=status.HTTP_200_OK)
                    else:
                        terminal_instance = Stock.objects.filter(terminal_id=terminal_id).last()
                        agent_instance.terminal_id = terminal_instance.terminal_id
                        agent_instance.terminal_serial = terminal_instance.serial_number
                        agent_instance.terminal_provider = terminal_instance.device_name
                        agent_instance.save()
                        return Response({"message":"terminal assigned successfully"}, status=status.HTTP_200_OK)
            else:
                terminals = User.objects.filter(terminal_id=terminal_id).last()
                if terminals:
                    response = {
                        "terminal_id": terminal_id,
                        "terminal_assigner": "terminal already assigned",
                    }
                    return Response(response,status=status.HTTP_200_OK)
                else:
                    terminal_instance = Stock.objects.filter(terminal_id=terminal_id).last()
                    sales_rep = SalesRep.objects.filter(sales_rep = request.user.id).last()
                    User.objects.create(terminal_id= terminal_instance.terminal_id, terminal_serial=terminal_instance.serial_number, terminal_provider=terminal_instance.device_name, phone_number=user.phone_number, email=user.email, first_name=user.first_name, last_name=user.last_name,  state=user.state, nearest_landmark=user.first_address, sales_rep_upline_code=sales_rep.sales_rep_code)
                    return Response({"message":"terminal assigned successfully"}, status=status.HTTP_200_OK)
        except ProspectiveAgent.DoesNotExist:
            return Response({"message":"user does not exist"},status=status.HTTP_400_BAD_REQUEST)


class SalesRepProfileView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    def get(self, request):
        try:
            user = User.objects.get(id=request.user.id)
            ## Emeka review this line
            account_instance = AccountSystem.objects.filter(user=request.user.id).last()
            if account_instance:
                account_details = {
                    "account_number": account_instance.account_number,
                    "bank_name": account_instance.bank_name,
                    "account_name": account_instance.account_name
                }
            else:
                  account_details = {
                    "account_number": None,
                    "bank_name": None,
                    "account_name": None
                }
            profile = {
                "full_name":user.full_name,
                "phone_number": user.phone_number,
                "email":user.email,
                "role": user.type_of_user,
                "account_details": account_details
            }

            return Response(profile, status=status.HTTP_200_OK)

        except User.DoesNotExist:
            response = {"message":"no user found"}
            return Response(response, status=status.HTTP_403_FORBIDDEN)

# TRANSACTIONS AND COMMISSIONS
# VAS Transactions
class TransactionListView(APIView):
    serializer_class = CustomerTransactionSerializer
    # filter_backends = (filters.SearchFilter,)
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    # pagination_class = CustomPagination
    queryset = Transaction.objects.all()

    def get(self, request):
        """
        Returns a list of all transactions
        """
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")
        serializer_class =  self.serializer_class
        
        try:
            response = TransactionList().get_transactions_list(serializer_class, self.request, start_date, end_date)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)       


class TransactionDetailView(generics.RetrieveAPIView):
    serializer_class = CustomerTransactionDetailSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = Transaction.objects.all()


# COMMISSIONS
class OtherCommissionView(APIView):
    serializer_class = OtherCommissionsRecordSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = OtherCommissionsRecord.objects.all()

    def get(self, request):
        date_filter = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        queryset = self.queryset
        serializer_class = self.serializer_class

        try:
            response = OtherCommission().get_other_commission(queryset, serializer_class, request, start_date, end_date)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


#==============================================================================================
#TERMINAL STATUS DASHBOARD

#Terminal Status Overview
class TerminalStatusOverView(APIView):
    serializer_class = StockRequestSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request):
        user_role = request.user.role
        terminals_qs = User.objects.filter(Q(terminal_id__isnull=False) & ~Q(terminal_id=""), role=user_role)
        active_terminals = terminals_qs.filter(terminal_status="ACTIVE").count()
        inactive_terminals = terminals_qs.filter(terminal_status="INACTIVE").count()
        suspended_terminals = terminals_qs.filter(terminal_suspended=True).count()
        dormant_terminals = terminals_qs.filter(terminal_status="DORMANT").count()

        response = {
            "active_terminals": active_terminals,
            "inactive_terminals": inactive_terminals,
            "suspended_terminals": suspended_terminals,
            "dormant_terminals": dormant_terminals
        }

        return Response(response, status=status.HTTP_200_OK)


# Agent Details
class TerminalAgentDetailsView(APIView):
    serializer_class = UserSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = AgentTerminal.objects.all()

    def get(self, request):
        user_role = request.user.role

        date_filter = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")
        status_filter = request.GET.get("status_filter")

        if not status_filter or status_filter == "":
            status_choice_list = ["ACTIVE", "INACTIVE", "PARTIALLY_INACTIVE",
                                "RECOVERY", "RECOVERED", "SUSPENDED", "DORMANT"]
        else:
            status_choice_list = [status_filter]

        agents = User.objects.filter(Q(date_joined__range=[start_date, end_date], role=user_role,
                terminal_id__isnull=False, terminal_status__in=status_choice_list) & ~Q(terminal_id="")).order_by("-date_joined")
        agents = Paginator.paginate(request=request, queryset=agents)
        agents_list = []
        for agent in agents:
            data = {
                "id": agent.id,
                "terminal_id":agent.terminal_id,
                "name": agent.first_name + " " + agent.last_name,
                "status": agent.terminal_status,
                "email": agent.email,
                "date_joined": agent.date_joined,
                "last_login": agent.last_login if agent.last_login else ""
                }
            agents_list.append(data)
        response = {
            "agents_details": agents_list,
            "count": len(agents_list)
        }
        return Response(response, status=status.HTTP_200_OK)


# Terminal Performance
class TerminalPerformance(APIView):
    serializer_class = UserSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = Transaction.objects.all()

    def get(self, request):
        date_filter = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")
        status_filter = request.GET.get("status_filter")

        try:
            response = TerminalByPerformance.get_terminal_performance(self.request, start_date, end_date, status_filter)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


# Active Terminals
class ActiveTerminalStatus(APIView):
    serializer_class = UserSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = Transaction.objects.all()

    def get(self, request):
        date_filter = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        try:
            response = ActiveTerminals.get_active_terminals_status(self.request, start_date, end_date)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

# Inactive Terminals
class InactiveTerminalStatus(APIView):
    serializer_class = UserSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = Transaction.objects.all()

    def get(self, request):
        user_role = request.user.role

        date_filter = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        inactive_terminals = User.objects.filter(Q(date_joined__range=[start_date, end_date],
                            terminal_id__isnull=False, terminal_status="INACTIVE")
                            & ~Q(terminal_id=""), role=user_role).order_by("-date_joined")

        inactive_agents = []
        for agent in inactive_terminals:
            data = {
                    "id": agent.id,
                    "terminal_id":agent.terminal_id,
                    "name": agent.first_name + " " + agent.last_name,
                    "status": "Inactive",
                    "email": agent.email,
                    "date_joined": agent.date_joined,
                    "last_login": agent.last_login
                }

            inactive_agents.append(data)

        response = {
            "inactive_agent": inactive_agents,
            "count": len(inactive_agents)
        }
        return Response(response, status=status.HTTP_200_OK)


# SUSPENDED TERMINALS
class SuspendedTerminalsView(APIView):
    serializer_class = UserSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = User.objects.all()

    def get(self, request):
        user_role = request.user.role

        date_filter = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        terminals = User.objects.filter(Q(date_joined__range=[start_date, end_date], terminal_id__isnull=False,
                    terminal_suspended=True) & ~Q(terminal_id=""), role=user_role)
        terminals = Paginator.paginate(request=request, queryset=terminals)

        suspended_terminals = []
        for user in terminals:
            data = {
                "id": user.id,
                "terminal_id":user.terminal_id,
                "name": user.first_name + " " + user.last_name,
                "status": "suspended",
                "email": user.email,
                "date_joined": user.date_joined,
                "last_login": user.last_login
            }
            suspended_terminals.append(data)
        response = {
            "suspended_terminals": suspended_terminals,
            "count": len(suspended_terminals)
        }

        return Response(response, status=status.HTTP_200_OK)

# DORMANT TERMINALS
class DormantTerminalsView(APIView):
    serializer_class = UserSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = StockHistory.objects.all()

    def get(self, request):
        user_role = request.user.role

        date_filter = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        terminals = User.objects.filter(Q(date_joined__range=[start_date, end_date],
                            terminal_id__isnull=False, terminal_status="DORMANT")
                            & ~Q(terminal_id=""), role=user_role)

        terminals = Paginator.paginate(request=request, queryset=terminals)
        dormant_terminals = []
        for user in terminals:
            data = {
                "id": user.id,
                "terminal_id":user.terminal_id,
                "name": user.first_name + " " + user.last_name,
                "status": "dormant",
                "email": user.email,
                "date_joined": user.date_joined,
                "last_login": user.terminal_last_login if user.terminal_last_login is not None else ""
            }
            dormant_terminals.append(data)
        response = {
            "dormant_terminals": dormant_terminals,
            "count": len(dormant_terminals)
        }

        return Response(response, status=status.HTTP_200_OK)

# TERMINAL DETAILS
class TerminalDetailsView(generics.RetrieveAPIView):
    serializer_class = UserSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = User.objects.all()
    lookup_field = "id"

    def get(self, request, id):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")
        try:
            response = TerminalDetails.get_terminal_details(request, start_date, end_date, id=id)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        
# Written by Dan
#
# SUSPEND TERMINAL VIEW
class SuspendTerminalView(generics.RetrieveUpdateAPIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class =  UserSerializer
    queryset = User.objects.all()
    lookup_field = "id"

    def patch(self, request, id):
        try:
            user = User.objects.get(id=id)
            user.terminal_suspended = True
            user.save()
            return Response({"message":"Terminal Suspended"}, status=status.HTTP_200_OK)
        except:
            return Response({"message":"This terminal does not exist"}, status=status.HTTP_400_BAD_REQUEST)


##############################################################################
# DISPUTES

class DisputesOverview(APIView):
    serializer_class = DisputeSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request):
        user_role = request.user.role

        """
        This views summarizes the disputes logs
        """
        start_of_all_disputes = Dispute.objects.order_by("date_created").first().date_created

        date_filter_gte = self.request.GET.get("date_filter_gte", None)
        date_filter_lte = self.request.GET.get("date_filter_lte", None)

        if date_filter_gte and date_filter_lte:
            start_date = datetime.strptime(date_filter_gte, "%Y-%m-%d")
            end_date = datetime.strptime(date_filter_lte, "%Y-%m-%d")
        else:
            start_date = datetime.today() - timedelta(weeks=52)
            end_date = datetime.today()


        resolved_disputes = Dispute.objects.filter(is_resolved=True, user__role=user_role, date_created__range=[start_date, end_date]).count()
        unresolved_disputes = Dispute.objects.filter(is_resolved=False, user__role=user_role, date_created__range=[start_date, end_date]).count()

        response = {
            "resolved_disputes": resolved_disputes,
            "need_response": unresolved_disputes
        }

        return Response(response, status=status.HTTP_200_OK)


class DisputeListView(APIView):
    serializer_class = DisputeSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = Dispute.objects.all()

    def get(self, request):
        """
        This view provides a list of all existing disputes
        """
        user_role = request.user.role
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        disputes = self.queryset.filter(date_created__range=[start_date, end_date], user__role=user_role)

        disputes = Paginator.paginate(request=request, queryset=disputes)
        serializer = self.serializer_class(disputes,  many=True)
        data = serializer.data
        response = {
            "data": data,
            "count": len(data)
        }
        return Response(response, status=status.HTTP_200_OK)


class CustomerDisputeListView(APIView):
    serializer_class = DisputeSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = Dispute.objects.all()

    def get(self, request):
        """
        This view provides a list of all existing disputes
        """
        user_role = request.user.role
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        disputes = self.queryset.filter(date_created__range=[start_date, end_date], user__role=user_role)

        disputes = Paginator.paginate(request=request, queryset=disputes)
        serializer = self.serializer_class(disputes,  many=True)
        data = serializer.data
        response = {
            "data": data,
            "count": len(data)
        }
        return Response(response, status=status.HTTP_200_OK)


class DisputeCreateView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = DisputeSerializer

    def post(self, request):
        """
        This view is useful for creating new disputes and sending
        dispute notificatons to admin email
        """
        serializer = self.serializer_class(data=request.data)
        user = request.user
        agent_email = user.email
        agent_phone_number = user.phone_number

        if not settings.ENVIRONMENT == "development":
            email_list = [
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            agent_email
                        ]
        else:
            email_list = ["<EMAIL>", agent_email]

        if serializer.is_valid(raise_exception=True):
            serializer.save(user=request.user)
            dispute_type = serializer.validated_data["dispute_type"]
            account_number = serializer.validated_data.get("receiver_account_number", "-")
            customer_phone = serializer.validated_data.get("customer_mobile_number", "-")
            support_id = serializer.data.get("support_id")
            date_created = serializer.data.get("date_created")
            card_first_six = serializer.data.get("card_first_six_digits", "-")
            card_last_four = serializer.data.get("card_last_four_digits", "-")
            card_details = f"{card_first_six}******{card_last_four}"
            transaction_rrn = serializer.validated_data.get("transaction_rrn", "-")
            transaction_date = serializer.validated_data.get("transaction_date", "-")
            pAN = serializer.validated_data.get("pAN")

            message = f"Your dispute on {dispute_type} issues has been received and is being resolved. \
                        Your support ID is {support_id}."

            title = f"{dispute_type} Dispute created on: {date_created}"
            try:
                for email in email_list:
                    pass
                    send_dispute_email(
                        message=message,
                        email=email,
                        email_subject=title,
                        dispute_type=dispute_type,
                        account_number= account_number,
                        support_id=support_id,
                        card_details = card_details,
                        date_created = date_created,
                        customer_phone=customer_phone,
                        template="dispute_log_notification.html"
                    )
            except URLError:
                pass
            agent_phone_number = format_phone_number(phone_number=agent_phone_number)
            customer_phone_number = format_phone_number(phone_number=customer_phone)
            try:
                send_sms_to_pos_agent(phone_number=agent_phone_number, message=message)
            except:
                pass
            try:
                notify_admin_whatsapp_on_disputes_log(phone_number=agent_phone_number, message=message)
                notify_admin_whatsapp_on_disputes_log(phone_number=customer_phone_number, message=message)
            except:
                pass
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class DisputeDetailView(generics.RetrieveAPIView):
    """
    This view provides the details of single disputes
    """
    serializer_class = DisputeSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = Dispute.objects.all()


class DisputeResolveView(generics.RetrieveUpdateAPIView):
    """
    This view updates the status of disputes to resolved."""
    serializer_class = DisputeSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = Dispute.objects.all()
    lookup_field = "id"

    def put(self, request, id=id):
        dispute = self.queryset.get(id=id)
        dispute.is_resolved = True
        dispute.resolved_by = request.user.full_name
        dispute.respond_time = timezone.now()
        dispute.status = "RESOLVED"
        dispute.save()

        return Response({"message":"dispute resolved"}, status=status.HTTP_200_OK)



class DisputeResourceExport(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = DisputeSerializer
    queryset = Dispute.objects.all()

    def put(self, request):
        dispute_resource = DisputeResource()
        dataset = dispute_resource.export()

        disputes = self.queryset.all()
        serializer = self.serializer_class(disputes, many=True)

        df = pd.DataFrame
        disputes_excel = df.from_dict(serializer.data)
        ran_no = random.randint(1,100)
        disputes_file = disputes_excel.to_excel(f"disputes_file_{ran_no}_{datetime.today().strftime('%d-%m-%Y')}.xlsx")

        path = os.path.abspath(f"disputes_file_{ran_no}_{datetime.today().strftime('%d-%m-%Y')}.xlsx")
        file_name = os.path.basename(path)

        with open(path, "rb") as dispute_file:
            disputes_file = dispute_file.read()

        bucket = settings.AWS_STORAGE_BUCKET_NAME
        s3_credentials = {
            "endpoint_url": settings.AWS_S3_ENDPOINT_URL,
            "aws_access_key_id": settings.AWS_ACCESS_KEY_ID,
            "aws_secret_access_key": settings.AWS_SECRET_ACCESS_KEY,
        }
        config = botocore.client.Config(signature_version=botocore.UNSIGNED)
        s3_client = boto3.client('s3', **s3_credentials, config=config)
        try:
            url = s3_client.generate_presigned_url(
                ClientMethod="put_object",
                Params={
                    "Bucket": bucket,
                    "Key": f"{file_name}",
                },
                ExpiresIn=3600,
            )
            os.remove(path)
        except ClientError as e:
            return Response(
                {'resp': "something went wrong with the file upload. Please retry!"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


        return Response(
            {"resp": url}, status=status.HTTP_200_OK
        )


class DisputeImportResource(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    def put(self, request):
        """Updates the status of existing disputes"""
        try:
            dispute_resource = DisputeResource()
            dataset = Dataset()
            new_dispute = request.FILES['file']

            imported_data = dataset.load(new_dispute.read())
            result = dispute_resource.import_data(imported_data, dry_run=True)

            if not result.has_errors():
                dispute_resource.import_data(imported_data, dry_run=False)

            return Response({"data":"disputes status updates successful"}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error":str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AgentDisputeBatchUpload(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = DisputeBatchUploadSerializer
    queryset = DisputeBatchUpload.objects.all()

    def put(self, request):
        """
        This view provides a list of all existing disputes
        """
        excel_file = request.FILES["file"]
        read_excel = pd.ExcelFile(excel_file, engine="openpyxl")

        sheet = read_excel.sheet_names[0]
        decoded_file = read_excel.parse(sheet)

        disputes_list = []
        for i, row in decoded_file.iterrows():
            try:
                data = {
                        "Pan": row["PAN"],
                        "Stan": row["STAN"],
                        "TerminalId": row["Terminal ID"],
                        "DispenseType": row["Dispense Type"],
                        "TransactionDate": str(row["Transaction Date (yyyy-MM-dd hh:mm)"]),
                        "RetrievalReferenceNumber": row["Retrieval Reference Number"],
                        "LogComments": row["Log Comments"],
                        "RequestedAmount": row["Requested Amount"],
                        "DispensedAmount": row["Dispensed Amount"],
                        "Status": row["Status"]
                        }
                disputes_list.append(data)
            except KeyError:
                return Response({"error": "Invalid file upload"}, status = status.HTTP_400_BAD_REQUEST)

        request_data = {
                        "disputes": str(disputes_list)
                        }

        # request_data = json.dumps(request_data)
        serializer = self.serializer_class(data=request_data)
        if serializer.is_valid(raise_exception=True):
            serializer.save()
        return Response(serializer.data, status = status.HTTP_201_CREATED)

class BatchDisputesUploadList(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = DisputeBatchUploadSerializer
    queryset = DisputeBatchUpload.objects.all()

    def get(self, request):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        batch_disputes = self.queryset.filter(date_uploaded__range=[start_date, end_date])
        batch_disputes = Paginator.paginate(request=request, queryset=batch_disputes)
        serializer = self.serializer_class(batch_disputes, many=True)

        all_batches_list = []
        for batch in serializer.data:
            date = batch["date_uploaded"]
            disputes = ast.literal_eval(batch["disputes"])
            data = {
                    "id": batch["id"],
                    "date": date,
                    "disputes_list": disputes
                }
            all_batches_list.append(data)
        response = {
            "batch_uploads": all_batches_list,
            "count": len(all_batches_list)
        }
        return Response(response, status=status.HTTP_200_OK)


class BatchDisputesUploadDetails(generics.RetrieveAPIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = DisputeBatchUploadSerializer
    queryset = DisputeBatchUpload.objects.all()

    def get(self, request, id, stan):
        disputes_batch = self.queryset.get(id=id)
        serializer = self.serializer_class(disputes_batch)
        batch_disputes = serializer.data["disputes"]

        disputes_list = ast.literal_eval(batch_disputes)
        # all_disputes_list = []
        for dispute in disputes_list:
            if dispute["Stan"] == stan:
                data = {
                        "dispute_type": "",
                        "terminal_id": dispute["TerminalId"],
                        "stan": dispute["Stan"],
                        "rrn": "",
                        "customer_name": "",
                        "mobile_number": "",
                        "customer_account_number": "",
                        "amount": dispute["RequestedAmount"],
                        "atm_digits": dispute["Pan"],
                        "transaction_date": dispute["TransactionDate"],
                        "narration": "",
                        "date_logged": disputes_batch.date_uploaded,
                        "approved_by": "",
                        "status": dispute["Status"],
                        }
            # all_disputes_list.append(data)

                return Response(data, status=status.HTTP_200_OK)
        return Response({"error": "Not Found"}, status=status.HTTP_400_BAD_REQUEST)


class AdminCreateBulkDisputes(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    
    def post(self, request):
        Dispute.admin_create_bulk_disputes(payload=request.data.get("payload"))
        return Response({"message": "disputes created successfully"}, status=status.HTTP_200_OK)



######################################################################################################################
# LOG HISTORY

class LogHistoryListView(APIView):
    serializer_class = AdminDashboardLogHistorySerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    get_queryset = AdminDashboardLogHistory.objects.all()

    def get(self, request):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        user_role = request.user.role

        history = AdminDashboardLogHistory.objects.filter(action_time__range=[start_date,end_date], user__role=user_role)
        history = Paginator.paginate(request=request, queryset=history)
        output = []
        for log in history:
            user = log.user
            last_session_duration = log.action_time - user.last_login
            days, hours = divmod(last_session_duration.seconds, 3600)

            data = {
                "id": user.id,
                "user": str(user),
                "role": user.type_of_user,
                "name": user.get_full_name(),
                "status": "Active" if user.is_authenticated else "Inactive",
                # "device": log.device,
                "date": log.action_time,
                "last_session": user.last_login,
                "last_session_duration": f"{days} days, {floor(hours/60)} minutes" if days>1 else f"{days} day, {floor(hours/60)} minutes",
                "device": log.device if log.device else "",
                "operation_type": log.action_performed if log.action_performed else "",
            }
            output.append(data)
            
        response = {
            "logs": output,
            "count": len(output)
        }

        return Response(response, status=status.HTTP_200_OK)

class AuditTrailView(APIView):
    serializer_class = AdminDashboardLogHistorySerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    get_queryset = AdminDashboardLogHistory.objects.all()

    def get(self, request):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        user_role = request.user.role

        history = AdminDashboardLogHistory.objects.filter(action_time__range=[start_date,end_date], user__role=user_role)
        history = Paginator.paginate(request=request, queryset=history)
        output = []
        for log in history:
            user = log.user
            last_session_duration = log.action_time - user.last_login
            days, hours = divmod(last_session_duration.seconds, 3600)

            data = {
                "id": user.id,
                "user": str(user),
                "role": user.type_of_user,
                "name": user.get_full_name(),
                # "status": "Active" if user.is_authenticated else "Inactive",
                # "device": log.device,
                "date": log.action_time,
                # "last_session": user.last_login,
                # "last_session_duration": f"{days} days, {floor(hours/60)} minutes" if days>1 else f"{days} day, {floor(hours/60)} minutes",
                "device": log.device if log.device else "",
                "operation_type": log.action_performed if log.action_performed else "",
            }
            output.append(data)
            
        response = {
            "logs": output,
            "count": len(output)
        }

        return Response(response, status=status.HTTP_200_OK)


class LogHistoryDetailView(generics.RetrieveAPIView):
    serializer_class = AdminDashboardLogHistorySerializer
    queryset = AdminDashboardLogHistory.objects.all()
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request, id):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        try:
            user = User.objects.get(id=id)
            history = AdminDashboardLogHistory.objects.filter(action_time__range=[start_date,end_date], user=user)
            history = Paginator.paginate(request=request, queryset=history)
            output = []
            for log in history:

                data = {
                    "status": log.status,
                    "date": log.action_time,
                    "device": log.device if log.device else "",
                    "operation_type": log.action_performed if log.action_performed else "",
                }
                output.append(data)
                
            response = {
                "overview": {
                    "name": user.get_full_name(),
                    "role": user.type_of_user,
                    },
                "logs": output,
                "count": len(output)
            }

            return Response(response, status=status.HTTP_200_OK)
        
        except Exception as e:
            return Response({"error": "view does not exist"}, status=status.HTTP_404_NOT_FOUND)



################################################3
# Roles & Permissions
################################################3


class RoleView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def post(self, request):
        try:
            data = request.data
            permissions = data['permissions']
            try:
                tmp = Role.objects.get(name=data['name'])
                if tmp:
                    return Response({
                        'message': "Role with name already exists"
                    }, status=status.HTTP_400_BAD_REQUEST)
            except Role.DoesNotExist:
                pass
            role = Role.objects.create(
                name=data['name'],
                description=data['description'],
            )
            if type(permissions) == list:
                for permission in permissions:
                    role.add_single_permission(permission)
            elif type(permissions) == str:
                role.add_single_permission(permissions)
            else:
                return Response("Permission is invalid!", status=status.HTTP_400_BAD_REQUEST)
            role.save()
            serializer = RoleSerializer(role)
            return Response({"message": "role created successfully", "data": serializer.data}, status=status.HTTP_201_CREATED)
        except KeyError as e:
            return Response({'message': f'{e} is required to create roles!'},  status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)


    # GET ALL ROLES
    def get(self, request):
        queryset = Role.objects.all()
        queryset = Paginator.paginate(queryset=queryset, request=request)
        serializer = RoleSerializer(queryset, many=True)
        return Response({"data":serializer.data, "count":len(serializer.data)}, status=status.HTTP_200_OK)


class RoleDetailsView(APIView):
    queryset = User.objects.all()
    serializer_class = AdminDashboardUserSerializer
    def get(self, request, id):
        role = Role.objects.get(id=id)
        users = self.queryset.filter(role=role.name)
        users =  Paginator.paginate(request=request, queryset=users)
        serializer = self.serializer_class(users, many=True)

        return Response({"data": serializer.data, "count":len(serializer.data)}, status=status.HTTP_200_OK)
    

class RoleTagUpdateView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def put(self, request, roleId):
        try:
            role = Role.objects.get(id=roleId)
            
            tags = ["ACTIVE", "INACTIVE", "DISABLED"]
            tag = request.data['tag'].upper()
            if tag not in tags:
                resp = {
                    'status': "error",
                    'message': f"Tag is not valid. It has to be a member of these {tags}"
                }
                return Response(resp, status=status.HTTP_400_BAD_REQUEST)
            role.tag = tag
            role.save()
            serializer = RoleSerializer(role)
            return Response({
                'status': 'success',
                'message': '',
                'data': serializer.data
            }, status=status.HTTP_200_OK)
        except KeyError as e:
            return Response({
                'status': "error",
                'message': f'{e} is required to update roles!'
            }, status=status.HTTP_400_BAD_REQUEST)
        except Role.DoesNotExist:
            raise Response({
                "status": "error",
                "message": "Role with Id does not exist!"
            }, status=status.HTTP_400_BAD_REQUEST)


class RolePermissionsRemoveView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def put(self, request, roleId):
        try:
            role = Role.objects.get(id=roleId)
            permissions = request.data['permissions']
            if type(permissions) == list:
                for permission in permissions:
                    role.remove_permission(permission.upper())
            elif type(permissions) == str:
                role.remove_permission(permissions.upper())
            else:
                return Response({
                    'status': 'success',
                    'message': 'Permission is invalid!'
                }, status=status.HTTP_400_BAD_REQUEST)
            role.save()
            serializer = RoleSerializer(role)
            return Response({
                'status': 'success',
                'message': '',
                'data': serializer.data
            }, status=status.HTTP_200_OK)
        except Role.DoesNotExist:
            return Response({
                'status': "error",
                'message': 'Role with Id not found'
            },  status=status.HTTP_404_NOT_FOUND)
        except KeyError as e:
            return Response({               
                'status': "error",
                'message': f'{e} is required to remove roles!'
            },  status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'status': "error",
                'message': str(e)
            },  status=status.HTTP_400_BAD_REQUEST)


class RolePermissionsAddView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def put(self, request, roleId):
        try:
            role = Role.objects.get(id=roleId)
            permissions = request.data['permissions']
            if type(permissions) == list:
                for permission in permissions:
                    role.add_single_permission(permission.upper())
            elif type(permissions) == str:
                role.add_single_permission(permissions.upper())
            else:
                return Response({
                    'status': 'success',
                    'message': 'Permission is invalid!'
                }, status=status.HTTP_400_BAD_REQUEST)
            role.save()
            serializer = RoleSerializer(role)
            return Response({
                'status': 'success',
                'message': '',
                'data': serializer.data
            }, status=status.HTTP_200_OK)
        except Role.DoesNotExist:
            return Response({
                'status': "error",
                'message': 'Role with Id not found'
            },  status=status.HTTP_404_NOT_FOUND)
        except KeyError as e:
            return Response({               
                'status': "error",
                'message': f'{e} is required to remove roles!'
            },  status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'status': "error",
                'message': str(e)
            },  status=status.HTTP_400_BAD_REQUEST)


class UserRolesView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    pagination_class = CustomPagination

    def put(self, request):
        serializer = AssignRoleSerializer(data=request.data)
        if serializer.is_valid(raise_exception=True):
            email = serializer.validated_data["email"]
            role = serializer.validated_data["role"]

        user = User.objects.filter(email=email)
        if user.exists():
            user = User.objects.filter(email=email).last()
            user.role = role
            user.save()
            return Response({
            'message': 'User created successfully',
            'data': serializer.data
        }, status=status.HTTP_200_OK)
        else:
            return Response({"error": "No user with this email address"}, status=status.HTTP_400_BAD_REQUEST)
        
        
    def get(self, request):
        queryset = User.objects.filter(Q(type_of_user="ADMIN") | Q(type_of_user="SALES_REP"))
        queryset = Paginator.paginate(request=request, queryset=queryset)
        if len(queryset) < 1:
            return Response("Admin users not found!", status=status.HTTP_400_BAD_REQUEST)
        serializer = AdminDashboardUserSerializer(queryset, many=True)
        return Response({"data": serializer.data, "count":len(serializer.data)}, status=status.HTTP_200_OK)

class UserRoleDetails(generics.RetrieveAPIView):
    queryset = User.objects.all()
    serializer_class = AdminDashboardUserSerializer
    lookup_field = "id"

class AuthUserRoleDetails(generics.RetrieveAPIView):
    queryset = User.objects.all()
    serializer_class = AdminDashboardUserSerializer
    
    def get(self, request):
        user = request.user
        serializer = self.serializer_class(user, many=False)
        return Response({"data": serializer.data}, status=status.HTTP_200_OK)
    


#####################################################################################################################
# STOCK REQUEST
# A stock request view already exists. Created by Joseph Chinedu
#
# class StockRequestView(APIView):
#     permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
#     serializer_class = StockRequestSerializer


#####################################################################################################################
# STOCKS - BRANCH - ZONE

# PROVIDERS

# UPLOAD STOCKS
class StockUploadView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = StockSerializer
    # parser_classes = (FileUploadParser,)

    def post(self, request, filename, format=None):
        user_agent = request.META.get("HTTP_USER_AGENT")

        try:
            file_obj = request.FILES['file']
            df1 = pd.ExcelFile(file_obj, engine="openpyxl")
            sheets = df1.sheet_names

            zone_count_list = 0
            branch_count_list = []
            stock_count_list = []

            for sheet in sheets:
                table_data = df1.parse(sheet)
                zone_obj, created = Zone.objects.get_or_create(name=sheet)

                if created:
                    zone_count_list += 1


                # table_data_list.append(table_data)
                # zone_count_list.append(zone_obj.id)

                for i,n in table_data.iterrows():
                    branch = n["BRANCH"]
                    tid = n["TID"]
                    device_name = n["DEVICE_NAME"]
                    serial_number = n["SERIAL_NUMBER"]
                    model_number = n["MODEL_NUMBER"]

                    if Branch.objects.filter(Q(branch_name=branch)).last() is not None:
                        stock_branch = Branch.objects.filter(Q(branch_name=branch) & Q(zone=zone_obj)).last()
                    else:
                        stock_branch = Branch.objects.create(branch_name=branch, zone=zone_obj)
                        stock_branch.save()

                    # stock_branch, created = Branch.objects.get_or_create(branch_name=branch, zone=zone_obj)

                    stock_obj, created = Stock.objects.get_or_create(
                                            branch=stock_branch, terminal_id=tid, device_name=device_name,
                                            serial_number=serial_number, model_number=model_number, zone=zone_obj
                                            )
                    if created:
                        stock_count_list.append(stock_obj.id)

            # Create History Log for successful upload
            create_admin_dashboard_history(
                                    user=request.user, 
                                    model=Stock, 
                                    action_performed="Uploaded Excel sheet for stocks", 
                                    status="SUCCESS",
                                    device_type="DESKTOP",
                                    user_agent=user_agent
                                    )

            return Response({"message":f"{len(stock_count_list)} terminals have been successfully shared to {zone_count_list} zones"}, \
                            status=status.HTTP_201_CREATED)
        except:
            # Create History Log for Failed Upload
            create_admin_dashboard_history(
                                    user=request.user, 
                                    model=Stock, 
                                    action_performed="Uploaded Excel sheet for stocks", 
                                    status="FAILED",
                                    device_type="DESKTOP",
                                    user_agent=user_agent
                                    )
            return Response({"error": "The uploaded file is invalid"}, status=status.HTTP_400_BAD_REQUEST)

# ----> Get LIST OF stocks

class ZonesListView(generics.ListAPIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = Zone.objects.all()
    serializer_class = ZoneSerializer

class BranchListView(generics.RetrieveAPIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = Zone.objects.all()
    serializer_class = BranchSerializer
    # lookup_field = "id"

    def get(self, request, id):
        zone = Zone.objects.get(id=id)
        branches = Branch.objects.filter(zone=zone)
        serializer = self.serializer_class(branches, many=True)

        return Response(serializer.data, status=status.HTTP_200_OK)
        

# STOCK HEAD OFFICE PROVIDERS SECTION

class StockHeadOfficeDashboardDataView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = StockSerializer
    queryset = Stock.objects.all()

    def get(self, request):

        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")
        queryset = self.queryset

        try:
            response = StockHeadOffice.get_stock_head_office(queryset, start_date, end_date)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


# INPUT TERMINAL DETAILS
class InputTerminalDetails(APIView):
    serializer_class = StockSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def post(self, request):
        user_agent = request.META.get("HTTP_USER_AGENT")
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid(raise_exception=True):
            device_name =  serializer.validated_data["device_name"]
            serial_number = serializer.validated_data["serial_number"]
            model_number = serializer.validated_data["model_number"]
            terminal_id = serializer.validated_data["terminal_id"]
            zone = serializer.validated_data["zone"]

            stock_object = Stock.objects.create(
                device_name = device_name,
                serial_number = serial_number,
                model_number = model_number,
                terminal_id = terminal_id,
                zone = zone
            )

            create_admin_dashboard_history(
                                    user=request.user, 
                                    model=Stock, 
                                    action_performed="Added a new terminal to stocks", 
                                    status="SUCCESS",
                                    device_type="DESKTOP",
                                    user_agent=user_agent
                                    )

            stock_object.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)


# PROVIDERS
class StockProvidersView(APIView):
    """list of stock providers with asset data"""
    serializer_class = StockSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = Stock.objects.all()

    def get(self, request):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")
        queryset = self.queryset

        try:
            response = StockProviders.get_stock_providers(queryset, start_date, end_date)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


# DOWNLOAD SAMPLE EXCEL STOCK FILE
class DownloadSampleExcelFile(APIView):
    # permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = StockSerializer
    queryset = Stock.objects.all()

    def get(self, request):
        # df = pd.read_excel("sample_excelfile.xlsx")
        file_name = os.path.basename("sample_excelfile.xlsx")
        file_path = os.path.join(settings.BASE_DIR, file_name)

        with open(file_path, "rb") as file_download:
            read_file = file_download.read()
            file_download.close()

        return FileResponse(open(f"{file_path}", 'rb'), as_attachment=True)


# DEVICE STOCK DETAILS
class DeviceStockDetailsView(generics.RetrieveAPIView):
    serializer_class = StockSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = Stock.objects.all()

    def get(self, request, id):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        try:
            response = DeviceStockDetails.get_device_stock_details(request, self.queryset, self.serializer_class, start_date, end_date, id=id)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


# ASSIGN TERMINAL --- A SINGLE TERMINAL ID
class AssignSingleTerminalView(APIView):
    serializer_class = AssignStockSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = Stock.objects.all()

    def put(self, request):
        user_agent = request.META.get("HTTP_USER_AGENT")
        serializer = self.serializer_class(data = request.data)

        if serializer.is_valid(raise_exception=True):
            branch = serializer.validated_data.get("branch", "-")
            zone = serializer.validated_data.get("state", "-")
            terminal_id = serializer.validated_data.get("terminal_id", "-")

            zone = serializer.validated_data["zone"]
            stock_obj = Stock.objects.filter(terminal_id=terminal_id).last()

            stock_obj.branch = branch
            stock_obj.zone = zone
            stock_obj.save()

            create_admin_dashboard_history(
                                    user=request.user, 
                                    model=Stock, 
                                    action_performed="Assigned Terminal to a branch", 
                                    status="SUCCESS",
                                    device_type="DESKTOP",
                                    user_agent=user_agent
                                    )

        return Response({"response":f"Terminal {terminal_id} has been assigned to {branch} branch"}, status=status.HTTP_200_OK)


# ASSIGN TERMINAL --- MULTIPLE TERMINAL IDS
class AssignMultipleTerminalView(APIView):
    serializer_class = AssignStockSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = Stock.objects.all()

    def put(self, request):
        # all_stocks = self.queryset
        user_agent = request.META.get("HTTP_USER_AGENT")

        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid(raise_exception=True):
            branch = serializer.data["branch"]
            zone = serializer.data["state"]
            terminal_id = serializer.data["terminal_id"]

            try:
                stock_item, created = self.queryset.update_or_create(
                    terminal_id=terminal_id,
                    branch = branch,
                    branch__zone = zone)

                stock_item.is_assigned = True

                create_admin_dashboard_history(
                                    user=request.user, 
                                    model=Stock, 
                                    action_performed="Assigned Multiple Terminals", 
                                    status="SUCCESS",
                                    device_type="DESKTOP",
                                    user_agent=user_agent
                                    )
            except Stock.DoesNotExist:
                create_admin_dashboard_history(
                                    user=request.user, 
                                    model=Stock, 
                                    action_performed="Assigned Multiple Terminals", 
                                    status="FAILED",
                                    device_type="DESKTOP",
                                    user_agent=user_agent
                                    )
                return Response({"message": "This stock item does not exist"})
            return Response({"message": f"{terminal_id} has been assigned to {branch} branch"}, status=status.HTTP_200_OK)


# STOCK HEAD OFFICE ZONES SECTION
class StockHeadOfficeZonesView(APIView):
    serializer_class = StockSerializer
    queryset = Stock.objects.all()
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        zones = Zone.objects.all()
        zones = Paginator.paginate(request=request, queryset=zones)

        data_list = []
        for zone in zones:
            branch_set = zone.branch_set.all()
            number_of_branches = len(branch_set)
            number_of_terminals = Stock.objects.filter(Q(date_added__range=[start_date, end_date]) & Q(zone=zone)).count()
            data = {
                "id": zone.id,
                "state": zone.name,
                "number_of_branches": number_of_branches,
                "number_of_terminals": number_of_terminals
            }

            data_list.append(data)

        response = {
            "zones_list": data_list,
            "count":len(data_list)
            }
        return Response(response, status=status.HTTP_200_OK)


# ZONE ALL TERMINAL - DETAILS
class ZoneStockDetails(generics.RetrieveAPIView):
    serializer_class = StockSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    lookup_field = "id"

    def get(self, request, id):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        try:
            response = ZoneStockDetail.get_zone_stock_detail(request, self.serializer_class, start_date, end_date, id=id)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class ReAssignTerminal(generics.RetrieveUpdateAPIView):
    serializer_class = AssignStockSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    lookup_field = "id"

    def put(self, request):
        user_agent = request.META.get("HTTP_USER_AGENT")
        serializer = self.serializer_class(data = request.data)

        if serializer.is_valid(raise_exception=True):
            terminal_id = serializer.validated_data["terminal_id"]
            branch = serializer.validated_data["branch"]

            zone = serializer.validated_data["zone"]
            stock_obj = Stock.objects.filter(terminal_id=terminal_id).last()

            stock_obj.branch = branch
            stock_obj.zone = zone
            stock_obj.save()

            create_admin_dashboard_history(
                                    user=request.user, 
                                    model=Stock, 
                                    action_performed="Reassigned Terminal", 
                                    status="FAILED",
                                    device_type="DESKTOP",
                                    user_agent=user_agent
                                    )

        return Response({"response":f"Terminal {terminal_id} has been reassigned to {branch} branch"}, status=status.HTTP_200_OK)


# TERMINAL STATUS ACTION
class TerminalStatusAction(generics.RetrieveUpdateAPIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = StockSerializer
    queryset = Stock.objects.all()
    # lookup_field = "id"

    def put(self, request, id):
        try:
            stock_obj =  self.queryset.get(id=id)
        except Stock.DoesNotExist:
            return Response({"message":"This detail does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        request_data = request.data
        user_agent = request.META.get("HTTP_USER_AGENT")

        data = {
                "device_name": stock_obj.device_name,
                "serial_number": stock_obj.serial_number,
                "model_number": stock_obj.model_number,
                "terminal_id": stock_obj.terminal_id,
                "zone": stock_obj.zone.id if stock_obj.zone else "",
                "branch": stock_obj.branch.id if stock_obj.branch else "",
                "stock_location": request.data["stock_location"],
                # "stock_location_evidence": request.data["stock_location_evidence"],
                # "comment": request.data["comment"]
                }

        serializer = self.serializer_class(stock_obj, data=data)

        if serializer.is_valid(raise_exception=True):
            serializer.save()
            create_admin_dashboard_history(
                                    user=request.user, 
                                    model=Stock, 
                                    action_performed="Updated Stock Location", 
                                    status="SUCCESS",
                                    device_type="DESKTOP",
                                    user_agent=user_agent
                                    )

        return Response(serializer.data, status=status.HTTP_200_OK)


#ZONE BRANCH DETAILS
class ZoneBranchDetails(generics.RetrieveAPIView):
    serializer_class = StockSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    lookup_field = "id"

    def get(self, request, id):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        try:
            zone = Zone.objects.get(id=id)
        except Zone.DoesNotExist:
            return Response({"message":"This detail does not exist"}, status=status.HTTP_400_BAD_REQUEST)

        stocks = Stock.objects.filter(Q(date_added__range=[start_date, end_date]) & Q(zone=zone) & \
                        Q(terminal_id__isnull=False))
        stocks_count = stocks.count()

        zone_branches = zone.branch_set.all()
        zone_branch_qs = zone_branches
        zone_branches = Paginator.paginate(request=request, queryset=zone_branches)

        zone_branch_details = []
        for branch in zone_branches:
            number_of_terminals = Stock.objects.filter(branch=branch).count()
            number_of_sales_reps = SalesRep.objects.filter(branch=branch).count()
            data = {
                "id": branch.id,
                "branch": branch.branch_name,
                "number_of_terminals": number_of_terminals,
                "number_of_sales_reps": number_of_sales_reps
                }
            zone_branch_details.append(data)

        response = {
            "overview": {
                "state": zone.name,
                "number_of_branches": zone_branch_qs.count(),
                "terminal_in_stock": stocks_count
            },
            "branch_list": zone_branch_details,
            "count": len(zone_branch_details)
        }

        return Response(response, status=status.HTTP_200_OK)


# ZONE BRANCH TERMINAL DETAILS
class StateTerminalsDetails(generics.RetrieveAPIView):
    serializer_class = StockSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    lookup_field = "id"

    def get(self, request, id):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        try:
            branch = Branch.objects.get(id=id)
        except Branch.DoesNotExist:
            return Response({"message":"This detail does not exist"}, status=status.HTTP_400_BAD_REQUEST)

        zone = Zone.objects.filter(branch=branch).last()

        stocks = Stock.objects.filter(Q(date_added__range=[start_date, end_date]) & Q(branch=branch) & \
                Q(zone=zone) & Q(terminal_id__isnull=False))

        serializer = self.serializer_class(stocks, many=True)

        delivered_stocks_count = stocks.filter(Q(stock_location="COLLECTION_IN_PERSON_AGENT")).count()
        terminals_in_stock = stocks.count()

        stocks = Paginator.paginate(request=request, queryset=stocks)

        terminals_list = []
        for terminal in serializer.data:
            data = {
                    "id": 1304,
                    "provider": terminal["device_name"],
                    "serial_number": terminal["serial_number"],
                    "model_number": terminal["model_number"],
                    "terminal_id": terminal["terminal_id"],
                    "status": terminal["stock_location"],
                    "payment_status": "paid" if terminal["payment_status"]=="PAID" else "not paid",
                    "is_assigned": "Assigned" if terminal["is_assigned"] else "Re-assign",
                    # "zone": terminal["zone"],
                    # "branch": terminal["branch"],
                    # "comment": terminal["comment"]
                }
            terminals_list.append(data)

        response = {
            "overview": {
                "state": zone.name,
                "branch": branch.branch_name,
                "terminals_in_stock": terminals_in_stock,
                "number_of_delivered": delivered_stocks_count
            },
            "stock_list": terminals_list,
            "count": len(terminals_list)
        }
        return Response(response, status=status.HTTP_200_OK)


# ALL TERMINALS VIEW (PER ZONE)
class StateAllTerminalsView(generics.RetrieveAPIView):
    serializer_class = StockSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    # lookup_field = "id"

    def get(self, request, id):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        try:
            zone = Zone.objects.get(id=id)
            response = StateAllTerminals.get_state_all_terminals(request, self.serializer_class, zone, start_date, end_date)
            return Response(response, status=status.HTTP_200_OK)
        except Zone.DoesNotExist:
            return Response({"message":"This detail does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


# DELAYED TERMINALS
class DelayedTerminalsView(APIView):
    serializer_class = StockInTransitSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request):
        date_filters = filter_by_date_two(request=request, user=User, datetime=datetime)
        start_date = date_filters.get("start_date")
        end_date = date_filters.get("end_date")

        stock_in_transit_queryset = StockInTransit.objects.filter(date_started__range=[start_date, end_date])
        stock_in_transit_queryset = Paginator.paginate(request=request, queryset=stock_in_transit_queryset)

        transit_serializer = StockInTransitSerializer(stock_in_transit_queryset, many=True)

        trail_list = []
        for trail in transit_serializer.data:
            terminals_in_trail = trail["stocks"]
            terminals = ast.literal_eval(terminals_in_trail[0])

            data = {
                "id": trail["id"],
                "provider": "LibertyPay",
                "serial_number": "",
                "model_number": "",
                "number_of_terminals": len(terminals) if terminals else 0,
                "status": "In transit" if trail["stock_location"]=="IN_TRANSIT" else trail["stock_location"]
                }

            trail_list.append(data)

        response = {
            "terminal_details": trail_list,
            "count": len(transit_serializer.data)
            }

        return Response(response, status=status.HTTP_200_OK)


# TERMINAL TRAIL
class AllTerminalTrailsView(generics.ListAPIView):
    serializer_class = StockInTransitSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request):
        date_filters = filter_by_date_two(request=request, user=User, datetime=datetime)
        start_date = date_filters.get("start_date")
        end_date = date_filters.get("end_date")

        stock_in_transit_queryset = StockInTransit.objects.filter(date_started__range=[start_date, end_date])
        stock_in_transit_queryset = Paginator.paginate(request=request, queryset=stock_in_transit_queryset)

        transit_serializer = StockInTransitSerializer(stock_in_transit_queryset, many=True)

        trail_list = []
        for trail in transit_serializer.data:
            terminals_in_trail = trail["stocks"]
            terminals = ast.literal_eval(terminals_in_trail[0])

            data = {
                "id": trail["id"],
                "provider": "LibertyPay",
                "serial_number": "",
                "model_number": "",
                "number_of_terminals": len(terminals) if terminals else 0,
                "status": "In transit" if trail["stock_location"]=="IN_TRANSIT" else trail["stock_location"]
                }

            trail_list.append(data)

        response = {
            "terminal_details": trail_list,
            "count": len(transit_serializer.data)
            }

        return Response(response, status=status.HTTP_200_OK)


class TerminalTrailView(generics.RetrieveAPIView):
    serializer_class = StockInTransitSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    lookup_field = "id"

    def get(self, request, id):
        date_filters = filter_by_date_two(request=request, user=User, datetime=datetime)
        start_date = date_filters.get("start_date")
        end_date = date_filters.get("end_date")

        try:
            branch = Branch.objects.get(id=id)
        except Branch.DoesNotExist:
            return Response({"message":"This detail does not exist"}, status=status.HTTP_400_BAD_REQUEST)

        zone = Zone.objects.filter(branch=branch).last()

        stock_in_transit_queryset = StockInTransit.objects.filter(
            date_started__range=[start_date, end_date], branch=branch, zone=zone
            )
        stock_in_transit_queryset = Paginator.paginate(request=request, queryset=stock_in_transit_queryset)

        transit_serializer = StockInTransitSerializer(stock_in_transit_queryset, many=True)

        terminals_in_stock = Stock.objects.filter(zone=zone, branch=branch)

        trail_list = []
        for trail in transit_serializer.data:
            terminals_in_trail = trail["stocks"]
            terminals = ast.literal_eval(terminals_in_trail[0])

            data = {
                "id": trail["id"],
                "provider": "LibertyPay",
                "number_of_terminals": len(terminals) if terminals else 0,
                "status": "In transit" if trail["stock_location"]=="IN_TRANSIT" else trail["stock_location"]
                }

            trail_list.append(data)

        response = {
            "overview": {
                "state": zone.name,
                "branch": branch.branch_name,
                "terminals_in_stock": terminals_in_stock.count(),
            },
            "terminal_details": trail_list,
            "count": len(transit_serializer.data)
        }

        return Response(response, status=status.HTTP_200_OK)


# Zone All Terminal Trail
class ZoneTerminalTrailView(generics.RetrieveAPIView):
    serializer_class = StockInTransitSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    # lookup_field = "id"

    def get(self, request, id):
        date_filters = filter_by_date_two(request=request, user=User, datetime=datetime)
        start_date = date_filters.get("start_date")
        end_date = date_filters.get("end_date")

        try:
            zone = Zone.objects.get(id=id)
        except Zone.DoesNotExist:
            return Response({"message":"This detail does not exist"}, status=status.HTTP_400_BAD_REQUEST)

        branches = Branch.objects.filter(zone=zone)

        stock_in_transit_queryset = StockInTransit.objects.filter(
            date_started__range=[start_date, end_date], zone=zone
            )
        stock_in_transit_queryset = Paginator.paginate(request=request, queryset=stock_in_transit_queryset)

        transit_serializer = StockInTransitSerializer(stock_in_transit_queryset, many=True)

        terminals_in_stock = Stock.objects.filter(zone=zone)

        trail_list = []
        for trail in transit_serializer.data:
            terminals_in_trail = trail["stocks"]
            terminals = ast.literal_eval(terminals_in_trail[0])

            data = {
                "id": trail["id"],
                "provider": "LibertyPay",
                "number_of_terminals": len(terminals) if terminals else 0,
                "status": "In transit" if trail["stock_location"]=="IN_TRANSIT" else trail["stock_location"]
                }

            trail_list.append(data)

        response = {
            "overview": {
                "state": zone.name,
                "terminals_in_stock": terminals_in_stock.count(),
                "number_of_branches": branches.count()
            },
            "terminal_details": trail_list,
            "count": len(transit_serializer.data)
        }

        return Response(response, status=status.HTTP_200_OK)


# TERMINAL TRAIL DETAILS
class TerminalTrailDetailsView(generics.RetrieveAPIView):
    serializer_class = StockInTransitSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = StockInTransit.objects.all()

    def get(self, request, id):
        date_filters = filter_by_date_two(request=request, user=User, datetime=datetime)
        start_date = date_filters.get("start_date")
        end_date = date_filters.get("end_date")

        try:
            stock_trail = self.queryset.get(id=id)
        except StockInTransit.DoesNotExist:
            return Response({"message": "This stock is not in transit"}, status=status.HTTP_400_BAD_REQUEST)
        zone = stock_trail.zone
        branches = Branch.objects.filter(zone=zone)
        stock_trail_serializer = self.serializer_class(stock_trail, many=False)

        try:
            stocks = ast.literal_eval(stock_trail_serializer.data["stocks"][0])
            stocks = Paginator.paginate_list(request=request, queryset=stocks)
        except Exception as e:
            return Response({"message": f"{e}"}, status=status.HTTP_400_BAD_REQUEST)

        terminals_at_head_office = 0
        terminals_in_transit = 0
        terminals_at_zone = 0

        terminals_detail_table_list = []
        for terminal in stocks:
            terminal_details = Stock.objects.filter(terminal_id=terminal).last()
            if Stock.objects.filter(terminal_id=terminal, stock_location="HEAD_OFFICE"):
                terminals_at_head_office += 1
            elif Stock.objects.filter(terminal_id=terminal, stock_location="IN_TRANSIT"):
                terminals_in_transit += 1
            elif Stock.objects.filter(terminal_id=terminal, stock_location="IN_STATE"):
                terminals_at_zone += 1

            data = {
                "id": terminal_details.id,
                "provider": "LibertyPay",
                "serial_number": terminal_details.terminal_id,
                "model_number": terminal_details.model_number,
                "number_of_terminals": 1,
                "status": terminal_details.stock_location
                }
            terminals_detail_table_list.append(data)

        branches_data = []
        branches = zone.branch_set.all().filter()
        # branches = Branch.objects.filter(zone=zone)
        # branches = Paginator.paginate(request=request, queryset=branches)

        for branch in branches:
            stock_at_branch_count = 0
            with_ro_count = 0
            delivered_to_agent_count = 0
            for terminal in stocks:
                if Stock.objects.filter(terminal_id=terminal, stock_location="AT_BRANCH", branch=branch):
                    stock_at_branch_count+=1
                elif Stock.objects.filter(terminal_id=terminal, stock_location="WITH_RO", branch=branch):
                    with_ro_count+=1
                elif Stock.objects.filter(terminal_id=terminal, stock_location="WITH_AGENT", branch=branch):
                    delivered_to_agent_count+=1

            data = {
                "branch_name": branch.branch_name,
                "terminals_received_at_branch": f"{stock_at_branch_count}/{len(stocks)}",
                "received_time": stock_trail.date_started,
                "terminals_with_ro": f"{with_ro_count}/{len(stocks)}",
                "terminals_with_agent": f"{delivered_to_agent_count}/{len(stocks)}"
                }
            branches_data.append(data)

        response = {
                "overview": {
                    "delivery_to": zone.name,
                    "number_of_branches": len(branches),
                    "start_date": stock_trail.date_started,
                    "number_of_terminals": len(stocks),
                    },
                "trail": {
                    "head_office": f"{terminals_at_head_office}/{len(stocks)}",
                    "in_transit": f"{terminals_in_transit}/{len(stocks)}",
                    "zone": f"{terminals_at_zone}/{len(stocks)}",
                    },
                "branch_trail": branches_data,
                "terminal_details_table": {
                    "table": terminals_detail_table_list,
                    "count": len(terminals_detail_table_list)
                }
                }

        return Response(response, status=status.HTTP_200_OK)


# INDIVIDUAL TEMINAL TRAIL
class IndividualTerminalTrailView(generics.RetrieveAPIView):
    serializer_class = StockSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request, id):
        try:
            stock = Stock.objects.get(id=id)
            response = IndividualTerminalTrail.get_individual_terminal_trail(self.serializer_class, stock)
            return Response(response, status=status.HTTP_200_OK)
        except Stock.DoesNotExist:
            return Response({"error": "This stock item does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"error": f"{e}"}, status=status.HTTP_400_BAD_REQUEST)
        

# ZONE HEAD VIEW
# -- BRANCHES
class ZoneHeadBranchesView(generics.RetrieveAPIView):
    serializer_class = StockSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request, id):
        filters = filter_by_date_two(request=request, datetime=datetime, user=User)

        start_date = filters.get("start_date")
        end_date = filters.get("end_date")

        try:
            zone = Zone.objects.get(id=id)
            response = ZoneHeadBranches.get_zone_head_branches(zone, start_date, end_date)
            return Response(response, status=status.HTTP_200_OK)
        except Zone.DoesNotExist:
            return Response({"error":"This zone does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"error": f"{e}"}, status=status.HTTP_400_BAD_REQUEST)
        

class SetStockInTransit(APIView):
    serializer_class = StockInTransitSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def put(self, request):
        user_agent = request.META.get("HTTP_USER_AGENT")
        serializer = self.serializer_class(data=request.data)
        stock_list = serializer.initial_data["stocks"].strip("\\").strip("[").strip("]").strip().split(",")
        if serializer.is_valid(raise_exception=True):

            zone = serializer.validated_data["zone"]
            branch = serializer.validated_data["branch"]
            stock_location_evidence = serializer.validated_data["stock_location_evidence"]

            for tid in stock_list:
                tid = json.loads(tid.strip())

                if Stock.objects.filter(terminal_id=tid).exists():
                    stock_obj = Stock.objects.filter(terminal_id=tid).last()
                    stock_obj.zone = zone
                    stock_obj.branch = branch
                    stock_obj.stock_location = "IN_TRANSIT"
                    stock_obj.stock_location_evidence = stock_location_evidence
                    stock_obj.save()
            serializer.save()

            create_admin_dashboard_history(
                                    user=request.user, 
                                    model=Stock, 
                                    action_performed="Sent out stock. Put stock in transit", 
                                    status="SUCCESS",
                                    device_type="DESKTOP",
                                    user_agent=user_agent
                                    )

            response = {
                    "trail_details": {
                                    "branch": branch.branch_name,
                                    "zone": zone.name,
                                    "comment": "I have a comment",
                                    "stock_location_evidence": serializer.data["stock_location_evidence"],
                                    "date_started": "2022-10-12T12:12:23.772848+01:00",
                                    "number_of_terminals": 2
                                }
                    }
        return Response(response, status=status.HTTP_201_CREATED)


# TERMINAL TRAIL DETAIL
class ZoneHeadTerminalTrailDetails(generics.RetrieveAPIView):
    serializer_class = StockSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    lookup_field = "id"
    queryset = Stock.objects.all()

    def get(self, request, id):
        try:
            zone = Zone.objects.get(id=id)
        except Zone.DoesNotExist:
            return Response({"error":"This detail does not exist"}, status=status.HTTP_400_BAD_REQUEST)

        filter_dates = filter_by_date_two(request=request, datetime=datetime, user=User)

        start_date = filter_dates.get("start_date")
        end_date = filter_dates.get("end_date")

        stock_requests_qs = Stock.objects.filter(branch__zone=zone)
        serializer = self.serializer_class(stock_requests_qs, many=True)

        # total_number_of_terminals = 0
        # for stock_request in serializer.data:
        #     total_number_of_terminals += stock_request["number_of_terminals"]

        #     stock_at_head_office = 0
        #     stock_in_transit = 0
        #     stock_in_zone = 0
        #     if stock_request["stock_request_location"] == "HEAD_OFFICE":
        #         stock_at_head_office += stock_request["number_of_terminals"]


        #     elif stock_request["stock_request_location"] == "IN_TRANSIT":
        #         stock_in_transit += stock_request["number_of_terminals"]

        #     elif stock_request["stock_request_location"] == "IN_STATE":
        #         stock_in_zone += stock_request["number_of_terminals"]

        response = {
            "overview": {
                "delivery_to": zone.name,
                "number_of_branches": zone.branch_set.count(),
                "trail_start_date": stock_requests_qs.order_by("date_added").first().date_created if stock_requests_qs else "",
                # "number_of_terminals": total_number_of_terminals
            },

            # "trailTable": {
            #     "stock_at_branch" : f"{stock_at_head_office}/{total_number_of_terminals}",
            #     "stock_in_transit": f"{stock_in_transit}/{total_number_of_terminals}",
            #     f"stock_in_zone": f"{stock_in_zone}/{total_number_of_terminals}"
            # }
        }

        return Response(response, status=status.HTTP_200_OK)



# STOCK HISTORY

# PROVIDERS HISTORY
class StockHistoryDetailsView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = StockSerializer
    queryset = Stock.objects.all()

    def get(self, request):
        filter_date = filter_by_date_two(request=request, datetime=datetime, user=User)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")
        stock_qs = self.queryset

        try:
            response = StockHistoryDetails.get_stock_history_detail(stock_qs, start_date, end_date)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

# Stock History
class StockHistoryDetailView(generics.RetrieveAPIView):
    serializer_class = StockSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = Stock.objects.all()

    def get(self, request, id):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")
        queryset = self.queryset

        try:
            response = StockHistoryDetail.get_stockhistory_detail(queryset, start_date, end_date)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)})
        

# ---->>>> Device Stock Details
class BranchStockDetailsView(generics.RetrieveAPIView):
    serializer_class = StockSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = Stock.objects.all()

    def get(self, request, id):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")
        queryset = self.queryset

        try:
            branch = Branch.objects.get(id=id)
            response = BranchStockDetails.get_branch_stock_details(request, queryset, branch, start_date, end_date)
            return Response(response, status=status.HTTP_200_OK)
        except Branch.DoesNotExist:
            return Response({"error": "This detail does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    
# ----> Request History ----->
class ZoneStockRequestHistory(generics.RetrieveAPIView):
    serializer_class = StockRequestSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = StockRequest.objects.all()

    def get(self, request):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        zones = Zone.objects.all()
        stock_requests = self.queryset.all()
        
        stock_requests = Paginator.paginate(request=request, queryset=stock_requests)
        zone_terminals_list = []
        for zone in zones:
            stocks = self.queryset.filter(date_created__range=[start_date, end_date], zone=zone)
            salesrep = SalesRep.objects.filter(branch__zone=zone)
         
            data  = {
                "id": zone.id,
                "zone": zone.name,
                "number_of_terminals": len(stocks),
                "number_of_salesrep": len(salesrep),
                "last_updated": datetime.strftime(timezone.now(), "%Y-%m-%d")
                }
            zone_terminals_list.append(data)

        response = {
            "overview": {
                "number_of_zones": len(zones),
                "total_requests": len(stock_requests)
            },
            "stocks": zone_terminals_list,
            "count": len(zone_terminals_list)
        }
        return Response(response, status=status.HTTP_200_OK)

class ZoneStockRequestHistoryDetail(generics.RetrieveAPIView):
    serializer_class = StockRequestSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = StockRequest.objects.all()

    def get(self, request, id):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")
        queryset = self.queryset

        try:
            zone = Zone.objects.get(id=id)
            response = ZoneStockRequestHistoryDet.get_zone_stock_history(request, queryset, zone, start_date, end_date)
            return Response(response, status=status.HTTP_200_OK)     
        except Zone.DoesNotExist:
            return Response({"error": "This detail does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

# STOCK HISTORY ---> HISTORY
class StockHistoryView(APIView):
    serializer_class = StockHistorySerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = StockHistory.objects.all()

    def get(self, request):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        stock_history = self.queryset.filter(date_created__range=[start_date, end_date])
        stock_history = Paginator.paginate(request=request, queryset=stock_history)
        serializer = self.serializer_class(stock_history, many=True)
        sn = 0
        stock_history_list = []
        for history in stock_history:
            sn+=1
            data = {
                "sn": sn,
                "id": history.id,
                "date_created": history.date_created,
                "action_done": history.action,
                "action_by": history.admin.get_full_name() if history.admin else "",
                "zone": history.zone.name if history.zone else "",
                "report": history.comment if history.comment else ""
                }
            stock_history_list.append(data)

        response = {
            "history_list": stock_history_list,
            "count": len(stock_history_list)
        }

        return Response(response, status=status.HTTP_200_OK)

class StockHistoryDetailMoreView(APIView):
    serializer_class = StockHistorySerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = StockHistory.objects.all()

    def get(self, request, id):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        try:
            stock_history = self.queryset.get(id=id)
            response = StockHistoryDetailMore.get_stockhistory_detail_more(self.serializer_class, stock_history)
            return Response(response, status=status.HTTP_200_OK)
        except StockHistory.DoesNotExist:
            return Response({"error":"This detail does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST) 




########################################
# Card transactions Report
########################################

class CardTransactionReportView(APIView):
    # requires more permissions
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    timezone = pytz.timezone(settings.TIME_ZONE)
    file_path = "card_transaction_report.xlsx"

    def post(self, request):

        try:
            sd = request.data['start_date'].split('-')
            if sd[1][0] == '0':
                sd[1] = sd[1][-1]
            start_date = date(int(sd[0]), int(sd[1]), int(sd[2]))
            ed = request.data['end_date'].split('-')
            if ed[1][0] == '0':
                ed[1] = ed[1][-1]
            end_date = date(int(ed[0]), int(ed[1]), int(ed[2]))

            start = self.timezone.localize(datetime.combine(start_date, datetime.min.time()))
            end = self.timezone.localize(datetime.combine(end_date, datetime.min.time()))
            card_transactions = CardTransaction.objects.filter(date_created__lte=end).filter(date_created__gte=start)

            # card_transactions = CardTransaction.objects.all()

            cardTransaction_serializer = CardTransactionReportSerializer(card_transactions, many=True)
            all_transctions = []
            for value in cardTransaction_serializer.data:
                transaction = {k: v for k, v in value['transaction'].items() if k != 'user'}
                user_info = {k: v for k, v in value['transaction']['user'].items()}
                card_info = {k: v for k, v in value.items() if k != "transaction"}
                trn = dict(transaction, **card_info, **user_info)
                all_transctions.append(trn)

            # convert to pd dataframe
            df = pd.DataFrame.from_dict(all_transctions)
            df.to_excel(self.file_path)

            # upload file to s3
            # send url resp to frontend
            bucket = settings.AWS_STORAGE_BUCKET_NAME
            s3_credentials = {
                "endpoint_url": settings.AWS_S3_ENDPOINT_URL,
                "aws_access_key_id": settings.AWS_ACCESS_KEY_ID,
                "aws_secret_access_key": settings.AWS_SECRET_ACCESS_KEY,
            }
            config = botocore.client.Config(signature_version=botocore.UNSIGNED)
            s3_client = boto3.client('s3', **s3_credentials, config=config)
            url = s3_client.generate_presigned_url(
                ClientMethod="put_object",
                Params={
                    "Bucket": bucket,
                    "Key": f"uploads/{self.file_path}",
                },
                ExpiresIn=3600,
            )
            return Response(
                {"resp": url}, status=status.HTTP_200_OK
            )
        except KeyError as e:
            return Response({
                'message': f'{e} is required to get card transactions'
            }, status=status.HTTP_400_BAD_REQUEST)
        except ClientError as e:
            return Response(
                {'resp': "something went wrong with the file upload. Please retry!"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )



####################################################################################################################
# POS AGENTS
class TransactionAmountDaily(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_classes = CustomerTransactionSerializer
    queryset = Transaction.objects.all()

    def get(self, request):
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        try:
            response = TransactionAmountDay().get_transaction_amount_daily(request, start_date, end_date)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)        


# POS -> AGENT DETAILS
class PosAgentDetailsView(APIView):
    serializer_class = UserSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = User.objects.all()

    def get(self, request):
        date_filter = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")
        status_filter = request.GET.get("status_filter")
        jresponse = Response

        try:
            response = PosAgentsDetails.get_pos_agent_details(self.request, start_date, end_date, jresponse, status, status_filter)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


# POS AGENTS WALLETS DETAILS
class PosAgentWalletView(generics.RetrieveAPIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = UserSerializer
    queryset = User.objects.all()
    lookup_field = "id"

    def get(self, request, id):
        try:
            response = PosAgentWallet.get_pos_agent_wallet(id=id)            
            return Response(response, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return Response({"error": "Detail does not exist"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


# POS AGENT TRANSACTIONS
class PosAgentTransactionsView(generics.RetrieveAPIView):
    serializer_class = UserSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = User.objects.all()
    lookup_field = "id"

    def get(self, request, id):
        date_filter = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")
        queryset = self.queryset

        try:
            response = PosAgentTransactions.get_pos_agent_transactions(request, queryset, start_date, end_date, id=id)
            return Response(response, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return Response({"error": "Detail does not exist"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


# POS AGENTS ACTIVE SESSIONS
class PosAgentActiveSessionsView(generics.RetrieveAPIView):
    serializer_class = UserSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = User.objects.all()
    # lookup_field = "id"

    def get(self, request, id):
        date_filter = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")
        queryset = self.queryset

        try:
            response = PosAgentsActiveSessions.get_agent_active_sessions(request, queryset, start_date, end_date, id=id)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)})


# POS AGENTS DISPUTES
class PosAgentDisputesListView(generics.RetrieveAPIView):
    serializer_class = UserSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = User.objects.all()
    lookup_field = "id"

    def get(self, request, id):
        date_filter = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")
        queryset = self.queryset

        try:
            response = PosAgentsDisputesList.get_pos_agents_disputes_list(request, queryset, start_date, end_date, id=id)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
     

# POS AGENTS DISPUTE DETAILS
class PosAgentDisputeDetailView(generics.RetrieveAPIView):
    serializer_class = DisputeSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = Dispute.objects.all()
    lookup_field = "id"

    def get(self, request, id):
        dispute = Dispute.objects.get(id=id)
        response = {
                "id": dispute.id,
                "support_id": dispute.support_id,
                "date": dispute.date_created,
                "dispute_type": dispute.dispute_type,
                "customer_name": dispute.user.first_name + " " + dispute.user.last_name,
                "customer_account_number": dispute.customer_account_number,
                "mobile_number": dispute.customer_mobile_number,
                "terminal_id": dispute.user.terminal_id,
                "atm_digits": dispute.card_first_six_digits + "xxxx" + dispute.card_last_four_digits,
                "receiver_account_number": dispute.receiver_account_number,
                "narration": dispute.narration,
            }
        return Response(response, status=status.HTTP_200_OK)


# POS -> AGENT DETAILS FORM
class PosAgentFormDetailsView(generics.RetrieveAPIView):
    serializer_class = UserSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = User.objects.all()
    lookup_field = "id"

    def get(self, request, id):
        try:
            response = PosAgentFormDetails.get_pos_agent_form_details(self.request, self.queryset, id)
            return Response(response, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return Response({"error": "Detail does not exist"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


# POS AGENT SEND EMAIL
class SendPosAgentEmail(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = UserSerializer
    queryset = User.objects.all()
    # lookup_field = "id"

    def post(self, request, id):
        user_agent = request.META.get("HTTP_USER_AGENT")
        serializer = SendEmailMessageSerializer(data=request.data)
        if serializer.is_valid(raise_exception=True):
            message = serializer.data.get("message")
            title = serializer.data.get("title")
            schedule = serializer.data.get("schedule")

            try:
                attach = request.FILES.getlist("attach")
            except KeyError:
                attach = ""
        try:
            user = self.queryset.get(id=id)
            agent_email = user.email

            send_pos_agent_email(
                                  email=agent_email,
                                  template="pos_agent_email.html",
                                  email_subject=title,
                                  message = message,
                                  files = attach
                                )

            create_admin_dashboard_history(
                                    user=request.user, 
                                    model=Stock, 
                                    action_performed="Sent an email to an agent", 
                                    status="SUCCESS",
                                    device_type="DESKTOP",
                                    user_agent=user_agent
                                    )
        except SyntaxError:
            return Response({"error": "email send failed"}, status=status.HTTP_400_BAD_REQUEST)
        except User.DoesNotExist:
            return Response({"error": "User does not exist"}, status=status.HTTP_400_BAD_REQUEST)

        return Response({"message": f"email sent to {agent_email}"}, status=status.HTTP_200_OK)


# POS AGENT SEND SMS
class SendPosAgentSMS(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = UserSerializer
    queryset = User.objects.all()
    lookup_field = "id"

    def post(self, request, id):
        user_agent = request.META.get("HTTP_USER_AGENT")
        serializer = SendEmailMessageSerializer(data=request.data)
        if serializer.is_valid(raise_exception=True):
            message = serializer.data["message"]
            title = serializer.data["title"]
        try:
            user = self.queryset.get(id=id)
            agent_phone_number = user.phone_number
            agent_first_name = user.first_name
            try:
                send_sms_to_pos_agent(phone_number=agent_phone_number, message=message)
                create_admin_dashboard_history(
                                    user=request.user, 
                                    model=Stock, 
                                    action_performed="Sent SMS to an agent", 
                                    status="SUCCESS",
                                    device_type="DESKTOP",
                                    user_agent=user_agent
                                    )
                return Response(serializer.data, status=status.HTTP_200_OK)
            except  URLError:
                return Response("message not sent. Please try again with a better internet")
        except SyntaxError:
            return Response("sms send failed")
        except User.DoesNotExist:
            return Response({"error": "User does not exist"}, status=status.HTTP_400_BAD_REQUEST)



# POS AGENTS ONBOARD AGENTS
class ProspectiveAgents(generics.ListCreateAPIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_classes = ProspectiveAgentSerializer



########################################################
# SalesRep & Branch Head View
########################################################


class SalesRepDetailView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    pagination_class = CustomPagination

    def get(self, request):
        response = SalesRepDetail.get_salesrep_detail(
            request = request,
            jresponse = Response,
            status=status
            )
        sales_bar_obj = SalesBar(
            sales_reps=SalesRep.objects.all(),
            all_terminals=TerminalSerialTable.objects.all(),
            terminal_users=User.objects.filter(terminal_id__isnull=False),
            transactions=Transaction.objects.filter(terminal_id__isnull=False, status="SUCCESSFUL")
        )

        total_pos_sold_bar = sales_bar_obj.get_total_pos_sold_chart()["data"]

        response = {
            "data": response,
            "sales_rep_bar": total_pos_sold_bar,
            "total_sold_graph": {
                "weeks": ["Mon", "Tue", "Wed", "Thur", "Fri", "Sat", "Sun"],
                "values": [0, 10, 0, 5, 0, 0, 0]
            },
            "recovery": {
                "number_of_terminals_to_recover": 0,
                "number_of_terminals_recovered": 0
            }
        }
        return Response(response, status=status.HTTP_200_OK)
        

class SalesRepBarView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request):
        sales_bar_obj = SalesBar(
            sales_reps=SalesRep.objects.select_related("sales_rep").all(),
            all_terminals=TerminalSerialTable.objects.select_related("user").all(),
            terminal_users=User.objects.filter(terminal_id__isnull=False),
            transactions=Transaction.objects.select_related("user").filter(terminal_id__isnull=False, status="SUCCESSFUL")
        )
        total_pos_sold_bar = sales_bar_obj.get_total_pos_sold_chart()
        total_transactions_count_bar = sales_bar_obj.get_total_transaction_count()
        total_transaction_amount_bar = sales_bar_obj.get_total_transaction_amount()

        response = {
            "total_terminals_sold_bar": total_pos_sold_bar,
            "total_transactions_count_bar": total_transactions_count_bar,
            "total_transaction_amount_bar": total_transaction_amount_bar
            }
        return Response(response, status=status.HTTP_200_OK)        
        

class SalesRepresentativeView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    pagination_class = CustomPagination

    def get(self, request, sales_rep_id):
        try:
            user = User.objects.get(unique_id=sales_rep_id)
            sales_rep_instance = SalesRep.objects.get(sales_rep=user)
            if not sales_rep_instance.is_branch_head:
                return Response({
                    'status': 'error',
                    'message': 'Only branch head can access endpoint',
                }, status=status.HTTP_404_NOT_FOUND)
            sales_reps = SalesRep.objects.filter(branch=sales_rep_instance.branch)
            sales_reps = Paginator.paginate(request=request, queryset=sales_reps)
            sales_representatives = []
            for sr in sales_reps:
                sr_agents = User.objects.filter(sales_rep_upline_code=sr.sales_rep_code)
                tc = Transaction.objects.filter(user=sr.sales_rep)\
                    .filter(transaction_leg="EXTERNAL")\
                    .filter(status="SUCCESSFUL").count(),
                ta = Transaction.objects.filter(user=sr.sales_rep)\
                    .filter(transaction_leg="EXTERNAL")\
                    .filter(status="SUCCESSFUL").aggregate(Sum('amount'))
                sr_resp = {
                    'name': sr.sales_rep.first_name + " " + sr.sales_rep.last_name,
                    'transaction_count': tc,
                    'transaction_amount': ta,
                    'sold_terminals': len(sr_agents),
                    'active_terminals': 0,
                    'inactive_terminals': 0,
                    'suspended_terminals': 0
                }
                if len(sr_agents) > 0:
                    for agent in sr_agents:
                        stock = Stock.objects.get(terminal_id=agent.terminal_id)
                        if stock.status == "ACTIVE":
                            sr_resp['active_terminals'] += 1
                        elif stock.status == "INACTIVE":
                            sr_resp['inactive_terminals'] += 1
                        elif stock.status == "SUSPENDED":
                            sr_resp['suspended_terminals'] += 1
                        else:
                            pass
                sales_representatives.append(sr_resp)
            return Response({
                'status': 'success',
                'message': '',
                'data': {
                    'sales_rep': sales_representatives,
                    'count': len(sales_representatives)
                }
            }, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'User instance not found',
            }, status=status.HTTP_404_NOT_FOUND)
        except SalesRep.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Sales rep instance not found',
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e),
            }, status=status.HTTP_400_BAD_REQUEST)


class AgentDetails(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    pagination_class = CustomPagination

    def get(self, request):
        try:
            user = request.user
            sales_rep_instance = SalesRep.objects.filter(sales_rep=user).first()
            all_agents = User.objects.filter(sales_rep_upline_code=sales_rep_instance.sales_rep_code)
            all_agents = Paginator.paginate(request=request, queryset=all_agents)

            agents = []
            if len(all_agents) > 0:
                for agent in all_agents:
                    stock = TerminalSerialTable.objects.get(terminal_id=agent.terminal_id)
                    tc = Transaction.objects.filter(user=agent) \
                            .filter(transaction_leg="EXTERNAL") \
                            .filter(status="SUCCESSFUL").count()
                    tt = Transaction.objects.filter(user=agent) \
                            .filter(transaction_leg="EXTERNAL") \
                            .filter(status="SUCCESSFUL").aggregate(Sum('amount'))["amount__sum"]

                    load = {
                        "id": agent.id,
                        'name': agent.first_name + " " + agent.last_name,
                        'email': agent.email,
                        'terminal_id': agent.terminal_id,
                        'transaction_count': tc,
                        'total_transaction': tt,
                        'status': "Active"
                    }
                    agents.append(load)
            return Response({
                'data': {
                    'agents': agents,
                    'count': len(agents)
                }
            }, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'User instance not found',
            }, status=status.HTTP_404_NOT_FOUND)
        except SalesRep.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Sales rep instance not found',
            }, status=status.HTTP_404_NOT_FOUND)
        except Stock.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Stock instance not found',
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e),
            }, status=status.HTTP_400_BAD_REQUEST)


class SuperAgentSalesReps(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    pagination_class = CustomPagination

    def get(self, request):
        try:
            user = request.user
            user_role = user.role
            sales_rep_instance = SalesRep.objects.filter(sales_rep=user).first()
            all_sales_reps = SalesRep.objects.filter(branch=sales_rep_instance.branch, sales_rep__role=user_role)
            all_sales_reps = Paginator.paginate(request=request, queryset=all_sales_reps)

            sales_reps = []
            if len(all_sales_reps) > 0:
                for sales_rep in all_sales_reps:
                    stocks = Stock.objects.filter(branch=sales_rep_instance.branch)
                    transaction_count = Transaction.objects.filter(user=sales_rep.sales_rep) \
                            .filter(transaction_leg="EXTERNAL") \
                            .filter(status="SUCCESSFUL").count()
                    transaction_amount = Transaction.objects.filter(user=sales_rep.sales_rep) \
                            .filter(transaction_leg="EXTERNAL") \
                            .filter(status="SUCCESSFUL").aggregate(Sum('amount'))["amount__sum"]

                    load = {
                        "id": sales_rep.sales_rep.id,
                        'name': sales_rep.sales_rep.get_full_name(),
                        'devices_sold': stocks.count(),
                        'active_devices': 0,
                        'inactive_devices': 0,
                        'suspended_devices': 0,
                        'transaction_count': transaction_count,
                        'total_transaction': transaction_amount if transaction_amount else 0.00,
                        # 'status': "Active"
                    }
                    sales_reps.append(load)
            return Response({
                'data': {
                    'sales_reps': sales_reps,
                    'count': len(sales_reps)
                }
            }, status=status.HTTP_200_OK)

        except SalesRep.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Sales rep instance not found',
                }, status=status.HTTP_404_NOT_FOUND)
        except Stock.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Stock instance not found',
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e),
            }, status=status.HTTP_400_BAD_REQUEST)


class ProspectiveAgentsView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    pagination_class = CustomPagination
    serializer_class = ProspectiveAgentSerializer

    def get(self, request):
        try:
            user = request.user
            sales_rep_instance = SalesRep.objects.filter(sales_rep=user).first()
            prospective_agents = ProspectiveAgent.objects.filter(sales_rep=sales_rep_instance)
            prospective_agents = Paginator.paginate(request=request, queryset=prospective_agents)
            prospective_agent_serializer = self.serializer_class(prospective_agents, many=True)
            
            prospective_agent_list = []
            for agent in prospective_agent_serializer.data:
                data = {
                    "id": agent["id"], 
                    "date": agent["date"], 
                    "name": agent["first_name"] + agent["last_name"], 
                    "phone": agent["phone_number"], 
                    "email": agent["email"], 
                    "source": agent["source_account"],
                    "action": agent["prospect_status"] 
                    }
                prospective_agent_list.append(data)

            return Response({
                'data': {
                    "prospective_agents": prospective_agent_list,
                    "count": len(prospective_agent_serializer.data)
                    }
                })
        except User.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'User instance not found',
            }, status=status.HTTP_404_NOT_FOUND)
        except SalesRep.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Sales rep instance not found',
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e),
            }, status=status.HTTP_400_BAD_REQUEST)

class SalesRepTerminalsView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    pagination_class = CustomPagination
    serializer_class = StockSerializer

    def get(self, request):
        try:
            # all SALES REP reserved terminals since they are already paid for
            # all terminals that has been assigned but not yet collected by agent
            user = request.user
            sales_rep_instance = SalesRep.objects.filter(sales_rep=user).first()
            all_terminals = []
            all_terminal_ids = sales_rep_instance.get_reserved_terminals()
            terminals = Stock.objects.exclude(stock_location='WITH_AGENT')\
                    .exclude(stock_location='WITH_RO')\
                    .filter(is_assigned=True)\
                    .filter(assigned_to__sales_rep_upline_code=sales_rep_instance.sales_rep_code)
            # print('here 3')
            if len(terminals) > 0:
                serializer = StockSerializer(terminals, many=True)
                all_terminals.extend(serializer.data)
            if len(all_terminal_ids) > 0:
                for id in all_terminal_ids:
                    terminal = Stock.objects.get(terminal_id=id)
                    all_terminals.append(StockSerializer(terminal).data)
            all_terminals = Paginator.paginate_list(request=request, queryset=all_terminals)
            all_terminals = [i for i in all_terminals]
            return Response({
                "status": 'success',
                'message': '',
                'data': {
                    "all_terminals": all_terminals,
                    "count": len(all_terminals)
                }
            }, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'User instance not found',
            }, status=status.HTTP_404_NOT_FOUND)
        except SalesRep.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Sales rep instance not found',
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e),
            }, status=status.HTTP_400_BAD_REQUEST)
            

class RequestHistoryView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    pagination_class = CustomPagination
    serializer_class = StockRequestSerializer

    def get(self, request):
        try:
            user = request.user
            sales_rep_instance = SalesRep.objects.filter(sales_rep=user).first()
            requests_history = StockRequest.objects.filter(sales_rep=sales_rep_instance)
            requests_history = Paginator.paginate(request=request, queryset=requests_history)
            request_history_serializer = self.serializer_class(requests_history, many=True)

            request_history_list = []
            for requests in request_history_serializer.data:
                data = {
                        "date_requested": requests["date_created"],
                        "number_of_requested_terminals": requests["number_of_terminals"],
                        "number_of_assigned_terminals": requests["number_of_terminals"],
                        "date_assigned": requests["date_updated"],
                        "assigned_by": "Admin",
                        "request_type": "default request"
                        }
                request_history_list.append(data)
            return Response({
                'data': {
                    "requests_history": request_history_list,
                    "count": len(request_history_serializer.data)
                    }
                })
        except User.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'User instance not found',
            }, status=status.HTTP_404_NOT_FOUND)
        except SalesRep.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Sales rep instance not found',
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e),
            }, status=status.HTTP_400_BAD_REQUEST)


class WalletHistoryView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    pagination_class = CustomPagination
    serializer_class = CustomerTransactionSerializer

    def get(self, request):
        try:
            user = request.user
            sales_rep_instance = SalesRep.objects.filter(sales_rep=user).first()
            wallet_history = Transaction.objects.filter(user=sales_rep_instance.sales_rep)
            wallet_history = Paginator.paginate(request=request, queryset=wallet_history)
            wallet_history_serializer = self.serializer_class(wallet_history, many=True)
            return Response({
                'data': {
                    "wallet_history": wallet_history_serializer.data,
                    "count": len(wallet_history_serializer.data)
                }
            })
        except User.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'User instance not found',
            }, status=status.HTTP_404_NOT_FOUND)
        except SalesRep.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Sales rep instance not found',
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e),
            }, status=status.HTTP_400_BAD_REQUEST)


class SalesRepDisputesView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    pagination_class = CustomPagination
    serializer_class = DisputeSerializer

    def get(self, request):
        try:
            user = request.user
            sales_rep_instance = SalesRep.objects.filter(sales_rep=user).first()
            
            all_agents = User.objects.filter(sales_rep_upline_code=sales_rep_instance.sales_rep_code)
            all_disputes = []

            if len(all_agents) > 0:
                for agent in all_agents:
                    disputes = Dispute.objects.filter(user=agent)
                    serializer = self.serializer_class(disputes, many=True)
                    all_disputes.extend(serializer.data)
            all_disputes = Paginator.paginate_list(request=request, queryset=all_disputes)
            all_disputes = [i for i in all_disputes]
            return Response({
                # 'status': 'success',
                # 'message': '',
                'data': {
                    "all_disputes": all_disputes,
                    "count": len(all_disputes)
                }
            }, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'User instance not found',
            }, status=status.HTTP_404_NOT_FOUND)
        except SalesRep.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Sales rep instance not found',
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e),
            }, status=status.HTTP_400_BAD_REQUEST)


class SalesRepFullProfileView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request):
        try:
            user = request.user
            sales_rep_instance = SalesRep.objects.filter(sales_rep=user).first()
            profile = {
                "id": user.id,
               "name": sales_rep_instance.sales_rep.first_name + " " + sales_rep_instance.sales_rep.last_name,
                "email": sales_rep_instance.sales_rep.email,
                "phone": sales_rep_instance.sales_rep.phone_number,
                "branch": sales_rep_instance.branch if sales_rep_instance.branch else "",
                "employee_id": user.unique_id if user.unique_id else "",
                "terminal_last_login": sales_rep_instance.sales_rep.terminal_last_login,
                "mobile_last_login": sales_rep_instance.sales_rep.mobile_last_login,
                "web_last_login": sales_rep_instance.sales_rep.web_last_login,
                "state": sales_rep_instance.sales_rep.state,
                "lga": sales_rep_instance.sales_rep.lga,
                "city": sales_rep_instance.sales_rep.nearest_landmark,
                "street": sales_rep_instance.sales_rep.street,
                "gender": sales_rep_instance.sales_rep.gender,
                "marital_status": sales_rep_instance.sales_rep.marital_status,
                "bvn": ""
            }
            return Response({
                # 'status': 'success',
                # 'message': '',
                'data': profile
            }, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'User instance not found',
            }, status=status.HTTP_404_NOT_FOUND)
        except SalesRep.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Sales rep instance not found',
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e),
            }, status=status.HTTP_400_BAD_REQUEST)


class SalesRepFullProfileDetailView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request, id):
        try:
            sales_rep_instance = SalesRep.objects.get(id=id)
            profile = {
               "name": sales_rep_instance.sales_rep.first_name + " " + sales_rep_instance.sales_rep.last_name,
                "email": sales_rep_instance.sales_rep.email,
                "phone": sales_rep_instance.sales_rep.phone_number,
                "branch": sales_rep_instance.branch if sales_rep_instance.branch else "",
                "employee_id": sales_rep_instance.sales_rep.unique_id if sales_rep_instance.sales_rep.unique_id else "",
                "terminal_last_login": sales_rep_instance.sales_rep.terminal_last_login,
                "mobile_last_login": sales_rep_instance.sales_rep.mobile_last_login,
                "web_last_login": sales_rep_instance.sales_rep.web_last_login,
                "state": sales_rep_instance.sales_rep.state,
                "lga": sales_rep_instance.sales_rep.lga,
                "city": sales_rep_instance.sales_rep.nearest_landmark,
                "street": sales_rep_instance.sales_rep.street,
                "gender": sales_rep_instance.sales_rep.gender,
                "marital_status": sales_rep_instance.sales_rep.marital_status,
                "bvn": ""
            }
            return Response({
                # 'status': 'success',
                # 'message': '',
                'data': profile
            }, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'User instance not found',
            }, status=status.HTTP_404_NOT_FOUND)
        except SalesRep.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Sales rep instance not found',
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e),
            }, status=status.HTTP_400_BAD_REQUEST)





# Suspended Terminals List
class SuspendedTerminalsList(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = User.objects.all()
    serializer_class = UserSerializer
    # pagination_class = CustomPagination

    def get(self, request):
        user = request.user
        sales_rep = SalesRep.objects.filter(sales_rep=user).last()
        suspended_terminals = self.queryset.filter(terminal_id__isnull=False, terminal_suspended=True, sales_rep_upline_code=sales_rep.sales_rep_code)
        suspended_terminals = Paginator.paginate(queryset=suspended_terminals, request=request)
        serializer = self.serializer_class(suspended_terminals, many=True)

        response = []
        for terminal in serializer.data:
            data = {
                "id": terminal.get("id"),
                "terminal_id": terminal.get("terminal_id", ""),
                "name": terminal.get("first_name", "") + " " + terminal.get("last_name", ""),
                "status": "suspended" if terminal.get("terminal_suspended", "") else "",
                "email": terminal.get("email", ""),
                "recovery_days_count": 1
                }
            response.append(data)

        return Response({"terminals": response, "count": len(response)}, status=status.HTTP_200_OK)


class RecoveredTerminalsList(generics.ListAPIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = StockRecovery.objects.all()
    serializer_class = StockRecoverySerializer
    pagination_class = CustomPagination

    def get(self, request):
        user = request.user
        recovered_stocks = self.queryset.filter(terminal_id__isnull=False, recovered_by=user)
        recovered_stocks = Paginator.paginate(queryset=recovered_stocks, request=request)
        serializer = self.serializer_class(recovered_stocks, many=True)

        response = []
        for terminal in recovered_stocks:
            data = {
                "id": terminal.stock.id if terminal.stock else "",
                "provider": terminal.stock.device_name if terminal.stock else "",
                "serial_number": terminal.stock.serial_number if terminal.stock else "",
                "model_number": terminal.stock.model_number if terminal.stock else "",
                "recovered": terminal.date_recovered,
                "status": terminal.stock.stock_location if terminal.stock else ""
                }
            response.append(data)

        return Response({"terminals": response, "count": len(response)}, status=status.HTTP_200_OK)


# suspend stock or terminal
class SuspendStock(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def put(self, request, terminal_id):
        try:
            terminal = User.objects.get(terminal_id=terminal_id)
        except User.DoesNotExist:
            return Response({
                'status': 'error',
                "message": "terminal with Id not found"
            }, status=status.HTTP_404_NOT_FOUND)
        # if terminal.terminal_suspension_count <= 2:
        terminal.terminal_suspended = True
            # terminal.suspension_count += 1
        terminal.save()
        return Response({
            'status': 'success',
            'message': 'Terminal has been suspended!'
        }, status=status.HTTP_200_OK)

class RemoveStockSuspension(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def put(self, request, terminal_id):
        try:
            terminal = User.objects.get(terminal_id=terminal_id)
        except User.DoesNotExist:
            return Response({
                'status': 'error',
                "message": "terminal with Id not found"
            }, status=status.HTTP_404_NOT_FOUND)
        # if terminal.terminal_suspension_count <= 2:
        terminal.terminal_suspended = False
            # terminal.suspension_count += 1
        terminal.save()
        return Response({
            'status': 'success',
            'message': 'Terminal suspension has been removed!'
        }, status=status.HTTP_200_OK)


class RecoverStock(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def put(self, request, terminal_id):
        try:
            terminal = User.objects.get(terminal_id=terminal_id)
        except User.DoesNotExist:
            return Response({
                'status': 'error',
                "message": "Stock with Id not found"
            }, status=status.HTTP_404_NOT_FOUND)
        # if terminal.suspension_count > 2:
        terminal.terminal_status = "SUSPENDED"
        # terminal.is_active = False
        # terminal.is_assigned = False
        terminal.save()

        stock = Stock.objects.filter(terminal_id=terminal_id).last()
        stock_recovery = StockRecovery.objects.get_or_create(
            terminal_id=terminal_id,
            stock=stock,
            recovered_by=request.user
        )
        return Response(
            {'message': 'Terminal is being processed for recovery'}, status=status.HTTP_200_OK
        )
        # else:
        #     stock.status = "SUSPENDED"
        #     stock.suspension_count += 1
        #     stock.save()
        #     return Response(
        #         {'message': 'Terminal is being processed for recovery'}, status=status.HTTP_200_OK
        #     )


class RestoreStock(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def put(self, request, terminal_id):
        try:
            stock = Stock.objects.get(terminal_id=terminal_id)
        except Stock.DoesNotExist:
            return Response({
                'status': 'error',
                "message": "Stock with Id not found"
            }, status=status.HTTP_404_NOT_FOUND)

        try:
            stock_recovery = StockRecovery.objects.get(terminal_id=terminal_id)
        except StockRecovery.DoesNotExist:
            return Response({
                'status': 'error',
                "message": "Stock recovery with Id not found"
            }, status=status.HTTP_404_NOT_FOUND)
        stock_recovery.status = "RECOVERED"
        stock_recovery.save()

        try:
            user = User.objects.get(terminal_id=terminal_id)
            user.terminal_id = None
            user.save()
        except User.DoesNotExist:
            pass

        stock.suspension_count = 0
        stock.status = 'INACTIVE'
        stock.save()
        return Response({
            'status': 'success',
            'message': 'Terminal restored successfully!'
        }, status=status.HTTP_200_OK)


class SalesRepWithdraw(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    pagination_class = CustomPagination

    def post(self, request):
        user = request.user
        bank_name = request.data['bank_name']
        account_number = request.data['account_number']
        account_name = request.data['account_name']
        if request.data.save_info:
            detail = WithdrawalBankDetails.objects.create(
                bank_name=bank_name,
                account_name=account_name,
                account_number=account_number,
                user=user
            )
        # process withdrawal

    def get(self, request):
        user = request.user
        details = WithdrawalBankDetails.objects.filter(user__email=user.email)
        if len(details) < 1:
            return Response({'message': "You don't have any payment details saved"}, status=status.HTTP_200_OK)

        serializer = WithdrawalBankDetailsSerializer(details, many=True)
        return Response({
            'message': '',
            'data': serializer.data
        }, status=status.HTTP_200_OK)

class InitStockRequest(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    pagination_class = CustomPagination

    def post(self, request):
        user = request.user
        data = request.data
        try:
            sales_rep = SalesRep.objects.filter(sales_rep=user).first()
            branch = Branch.objects.filter(branch_name=sales_rep.branch).last()
            serializer = StockRequestSerializer(data=data)
            serializer.is_valid(raise_exception=True)
            serializer.validated_data["sales_rep"] = sales_rep
            if serializer.validated_data['request_type'] == "UNPAID":
                if not sales_rep.is_branch_head:
                    return Response({
                        'status': 'error',
                        'message': 'You do not have permission to do that!'
                    }, status=status.HTTP_400_BAD_REQUEST)

                serializer.validated_data['branch'] = branch
            else:
                serializer.validated_data['pending_approval'] = False
                # generate payment link
            resp = StockRequest.objects.create(**serializer.validated_data)
            resp = StockRequestSerializer(resp)
            return Response({
                'status': 'success',
                'message': 'Created!',
                'data': resp.data
            }, status=status.HTTP_201_CREATED)
        except SalesRep.DoesNotExist:
            return Response({'message': 'Sales rep does not exist'}, status=status.HTTP_404_NOT_FOUND)

    def get(self, request):
        user = request.user
        try:
            sales_rep = SalesRep.objects.get(sales_rep=user)
        except SalesRep.DoesNotExist:
            return Response({'message': 'Sales rep does not exist'}, status=status.HTTP_404_NOT_FOUND)
        requests = StockRequest.objects.filter(sales_rep=sales_rep)
        if len(requests) < 1:
            return Response({
                'status': 'error',
                'message': "You haven't made any requests"
            }, status=status.HTTP_200_OK)

        if sales_rep.is_branch_head:
            branch_requests, sales_rep_requests = [], []
            for stk_r in requests:
                if stk_r.branch == sales_rep.branch:
                    branch_requests.append(stk_r)
                else:
                    sales_rep_requests.append(stk_r)
            branch_serializer = StockRequestSerializer(branch_requests, many=True)
            sales_rep_req_serializer = StockRequestSerializer(sales_rep_requests, many=True)
            return Response({
                'status': 'success',
                'message': '',
                'data': {
                    'branch_requests': branch_serializer.data,
                    'sales_rep_requests': sales_rep_req_serializer.data
                }
            }, status=status.HTTP_200_OK)
        serializer = StockRequestSerializer(requests, many=True)

        return Response({
            'status': 'success',
            'data': serializer.data
        }, status=status.HTTP_200_OK)


class StockRequestView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request, stock_request_id):
        try:
            stock_request = StockRequest.objects.get(id=stock_request_id)
        except StockRequest.DoesNotExist:
            return Response({
                'status': 'error',
                "message": "Stock request with Id not found"
            }, status=status.HTTP_404_NOT_FOUND)
        serializer = StockRequestSerializer(stock_request)
        return Response({
            'message': '',
            'data': serializer.data
        }, status=status.HTTP_200_OK)

    def put(self, request, stock_request_id):
        data = request.data
        user = request.user
        try:
            stock_request = StockRequest.objects.get(id=stock_request_id)
        except StockRequest.DoesNotExist:
            return Response({
                'status': 'error',
                "message": "Stock request with Id not found"
            }, status=status.HTTP_404_NOT_FOUND)
        serializer = StockRequestUpdateSerializer(stock_request, data=data)
        if serializer.is_valid():
            serializer.save()
            return Response({
                'message': '',
                'data': serializer.data
            }, status=status.HTTP_200_OK)
        return Response({
                'message': 'error',
                'data': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)





####################################################



####################################################
# Written by Joseph Chinedu
####################################################

# class SalesRepStockRequest(APIView):
#     permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
#
#     def post(self, request):
#         serializer = StockRequestSerializer(data=request.data)
#         serializer.is_valid(raise_exception=True)
#         sales_rep = SalesRep.objects.filter(sales_rep__id=request.user.id).last()
#         # check if user is a sales rep
#         if not sales_rep:
#             data = {
#                 "message": "You are not a sales rep"
#             }
#             return Response(data, status=status.HTTP_400_BAD_REQUEST)
#
#         serializer.validated_data["sales_rep"] = sales_rep
#         StockRequest.objects.create(**serializer.validated_data)
#
#         return Response(status=status.HTTP_201_CREATED)
#
#
#
# class SalesRepStockRequestCount(generics.ListAPIView):
#     permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
#     search_fields = ["date_created", "request_id"]
#     filter_backends = (filters.SearchFilter,)
#     serializer_class = StockRequestCountSerializer
#     pagination_class = CustomPagination
#
#     def get_queryset(self):
#         queryset = StockRequest.objects.filter(sales_rep__user__id=self.request.user.id)
#         return queryset
#
#
# class AssignedTerminalView(generics.ListAPIView):
#     permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
#     search_fields = ["date_created", "request_id", "status"]
#     filter_backends = (filters.SearchFilter,)
#     serializer_class = AssignedTerminalSerializer
#     pagination_class = CustomPagination
#
#     def get_queryset(self):
#         queryset = AssignedTerminal.objects.filter(sales_rep__user__id=self.request.user.id)
#         return queryset



# End
#######################################################


# Download Agent Activities Views
# class ManualSendAgentActivitiesView(APIView):
#     permission_classes = [AllowAny]

#     def get(self, request):
#         all_terminals_qs = TerminalSerialTable.objects.filter(user__isnull=False)

#         id = 0
#         terminal_status_list = []
#         if all_terminals_qs:
#             for terminal in all_terminals_qs:

#                 agent = terminal.user
#                 id += 1

#                 # days computations
#                 date_today = timezone.now()
#                 assigned_date = terminal.date_assigned if terminal.date_assigned else terminal.date_created
#                 number_of_days_since_assigned = abs(date_today - assigned_date).days
#                 try:
#                     agent_first_transaction = Transaction.objects.filter(user=agent, terminal_id__isnull=False).order_by("date_created").first()
#                     first_transaction_date = agent_first_transaction.date_created
#                     days_before_first_transaction = abs(first_transaction_date - assigned_date).days
#                 except:
#                     days_before_first_transaction = None

#                 terminal_last_inactive = agent.terminal_last_inactive

#                 if terminal_last_inactive:
#                     number_of_days_since_last_inactive = abs(date_today - terminal_last_inactive).days
#                 else:
#                     number_of_days_since_last_inactive = None


#                 # number of weeks
#                 weeks_count = floor(number_of_days_since_assigned / 7)
#                 if number_of_days_since_assigned >= 7:
#                     number_of_days_since_assigned_less_weekends = (weeks_count * 7) - (weeks_count)
#                 else:
#                     number_of_days_since_assigned_less_weekends = number_of_days_since_assigned

#                 # transaction counts
#                 total_transactions_count = Transaction.objects.filter(terminal_id=terminal.terminal_id).count()

#                 # set terminal status
#                 if weeks_count >= 2:
#                     if number_of_days_since_last_inactive:
#                         average_daily_transaction_count = total_transactions_count / number_of_days_since_last_inactive
#                     else:
#                         average_daily_transaction_count = total_transactions_count / number_of_days_since_assigned_less_weekends

#                 else:
#                     average_daily_transaction_count = 0

#                 data = {
#                     "S/N": id,
#                     "terminal": terminal.terminal_id,
#                     "agent_name": agent.bvn_full_name if agent.bvn_first_name else agent.full_name,
#                     "terminal_status": agent.terminal_status,
#                     "number_of_days_since_terminal_last_inactive": number_of_days_since_last_inactive,
#                     "days_before_first_transaction": days_before_first_transaction if days_before_first_transaction else "N/A",
#                     "number_of_days_since_assignment": number_of_days_since_assigned,
#                     "total_transaction_count": total_transactions_count,
#                     "week_count": weeks_count,
#                     "average_daily_transaction_count": average_daily_transaction_count,
#                     "consecutive_weeks_inactive_count": agent.inactive_count,
#                     "terminal_suspended": agent.terminal_suspended
#                 }
#                 terminal_status_list.append(data)

#             df = pd.DataFrame
#             agent_activity = df.from_dict(terminal_status_list)

#             first_file_path = os.path.join(settings.BASE_DIR, 'media/agent_report/last')
#             imt = random.randint(0,2000)
#             agent_activity.to_excel(f"{first_file_path}/agent_activity_{imt}_{datetime.today().strftime('%d-%m-%Y')}.xlsx")

#             # file_path = os.path.abspath(f"media/agent_activity_{datetime.today().strftime('%d-%m-%Y')}.xlsx")
#             file_path = os.path.abspath(f"{first_file_path}agent_activity_{imt}_{datetime.today().strftime('%d-%m-%Y')}.xlsx")
#             base_name = os.path.basename(file_path)

#             with open(file_path, "rb") as excel_file:
#                 agent_activities = excel_file.read()
#                 excel_file.close()
#             try:
#                 send_agent_activity_email(
#                     message="This is a report on overall agent activities",
#                     file = agent_activities,
#                     file_name = base_name,
#                     email_subject = "Agent Activities Report"
#                 )
#             except:
#                 return Response({"error": "No internet connection"})

#         return Response({"message": "Agent Activities email sent"}, status=status.HTTP_200_OK)


# class TransactionActivitesReport(APIView):
#     permission_classes = [AllowAny]
#     serializer_class = CustomerTransactionDetailSerializer
#     queryset = Transaction.objects.all()

#     def get(self, request):
#         float_user = WalletSystem.get_float_user()
#         base_transactions = Transaction.objects.exclude(user=float_user)
#         filter_date = filter_by_date(user=User, request=request, datetime=datetime)
#         today = filter_date.get("today")

#         seven_days_ago = timezone.now() - timedelta(days=7)

#         transaction_qs = base_transactions.filter(date_created__range=[seven_days_ago, today])
#         serializer = self.serializer_class(transaction_qs, many=True)
#         transaction_activities = json.loads(json.dumps(serializer.data))

#         df = pd.DataFrame
#         transaction_activity = df.from_dict(transaction_activities)

#         first_file_path = os.path.join(settings.BASE_DIR, 'media/agent_report/last')
#         imt = random.randint(0,2000)
#         transaction_activity.to_excel(f"{first_file_path}/transaction_activity_{imt}_{datetime.today().strftime('%d-%m-%Y')}.xlsx")

#         # file_path = os.path.abspath(f"media/agent_activity_{datetime.today().strftime('%d-%m-%Y')}.xlsx")
#         file_path = os.path.abspath(f"{first_file_path}transaction_activity_{imt}_{datetime.today().strftime('%d-%m-%Y')}.xlsx")
#         base_name = os.path.basename(file_path)

#         with open(file_path, "rb") as excel_file:
#             transaction_activities = excel_file.read()
#             excel_file.close()
#         try:
#             send_agent_activity_email(
#                     message="This is a report on Transaction activities Report for the past week",
#                     file = transaction_activities,
#                     file_name = base_name,
#                     email_subject = "Transaction Activities Report"
#                 )
#         except:
#             return Response({"error": "No internet connection"})

#         return Response({"message": "Transaction Activities report sent to email"}, status=status.HTTP_200_OK)


######################################################
# Sales
######################################################

class TerminalAssignView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def post(self, request):
        data = request.data
        try:
            agent = User.objects.get(id=data['agent_user_id'])
            agent_profile = agent
            terminal = Stock.objects.get(terminal_id=data['terminal_id'])
            if agent.terminal_id and agent_profile.terminal_id:
                if agent.terminal_id != agent_profile.terminal_id:
                    # there is an issue
                    return Response({
                        'status': 'error',
                        "message": "Agent terminal ID does not correspond with agent profile"
                    }, status=status.HTTP_400_BAD_REQUEST)
            if agent.terminal_id != terminal.terminal_id:
                return Response({"message": "Agent has been assigned another terminal"}, status=status.HTTP_200_OK)
            if terminal.is_assigned:
                if agent.terminal_id == terminal.terminal_id:
                    return Response({
                        'message': 'Terminal assigned to Agent'
                    }, status=status.HTTP_200_OK)
                return Response({"error": "Terminal has been assigned to another agent"}, status=status.HTTP_400_BAD_REQUEST)
            agent.terminal_id = terminal.terminal_id
            agent_profile.terminal_id = terminal.terminal_id
            terminal.is_assigned = True
            terminal.assigned_to = agent_profile
            terminal.date_assigned = timezone.now()
            agent.save()
            agent_profile.save()
            terminal.save()
            return Response({
                "status": 'success',
                "message": "Agent has been assigned terminal successfully"
            }, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return Response({"message": "Agent with ID does not exist"}, status=status.HTTP_404_NOT_FOUND)
        except Stock.DoesNotExist:
            return Response({"message": "Stock with terminalId does not exist"}, status=status.HTTP_404_NOT_FOUND)


class SalesViewOverview(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    pagination_class = CustomPagination

    def get(self, request):
        sales_obj = SalesTable(
        terminal_serial_table=TerminalSerialTable, 
        constant_table=ConstantTable, 
        sales_rep_table=SalesRep, 
        stock_table=Stock,
        user_role = request.user.role
        )
        
        try:
            response = sales_obj.get_sales_overview()
            sales_table = sales_obj.get_monthly_sales_chart(request)
            return Response({"overview": response, "monthly_chart": sales_table}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SalesTableView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    pagination_class = CustomPagination

    def get(self, request):
        sales_obj = SalesTable(
            terminal_serial_table=TerminalSerialTable, 
            constant_table=ConstantTable, 
            sales_rep_table=SalesRep, 
            stock_table=Stock,
            user_role = request.user.role
            )

        try:
            response = sales_obj.get_sales_table(request)
            return Response({"sales_table": response, "count": len(response)}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

class TerminalsSoldView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    pagination_class = CustomPagination

    def get(self, request):
        user_role = request.user.role

        try:
            all_terminal_table = TerminalSerialTable.objects.filter(user__role=user_role)
            month = request.GET.get('month')
            if month:
                att = []
                for t in all_terminal_table:
                    if str(t.date_assigned.date().month) == month:
                        att.append(t)
                all_terminal_table = att
            if len(all_terminal_table) < 1:
                return Response([], status=status.HTTP_200_OK)
            terminal_sold = {'total': len(all_terminal_table)}
            ra = {}
            for terminal in all_terminal_table:
                stock_instance = Stock.objects.get(terminal_id=terminal.terminal_id)
                name = stock_instance.sold_by.sales_rep.first_name + " " + stock_instance.sold_by.sales_rep.last_name
                if name in ra.keys():
                    ra[name] += 1
                else:
                    ra[name] = 1
            terminal_sold['names'] = list(ra.keys())
            terminal_sold['count'] = list(ra.values())
            return Response({
                'status': 'success',
                'message': '',
                'data': terminal_sold
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': e
            }, status=status.HTTP_400_BAD_REQUEST)


class TransactionCountView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    pagination_class = CustomPagination

    def get(self, request):
        user_role = request.user.role

        try:
            all_terminal_table = TerminalSerialTable.objects.filter(user__role=user_role)
            month = request.GET.get('month')
            if month:
                att = []
                for t in all_terminal_table:
                    if str(t.date_assigned.date().month) == month:
                        att.append(t)
                all_terminal_table = att
            if len(all_terminal_table) < 1:
                return Response({
                    'status': 'success',
                    'message': '',
                    'data': []
                }, status=status.HTTP_200_OK)
            transaction_count = {}
            tc = {}
            for terminal in all_terminal_table:
                all_transactions = Transaction.objects.filter(terminal_id=terminal.terminal_id) \
                    .filter(transaction_leg="EXTERNAL") \
                    .filter(status="SUCCESSFUL")
                stock_instance = Stock.objects.get(terminal_id=terminal.terminal_id)
                name = stock_instance.sold_by.sales_rep.first_name + stock_instance.sold_by.sales_rep.last_name
                if name in tc.keys():
                    tc[name] += all_transactions.count()
                else:
                    tc[name] = all_transactions.count()
            transaction_count['names'] = list(tc.keys())
            transaction_count['count'] = list(tc.values())
            transaction_count['total'] = sum(transaction_count['count'])
            return Response({
                'status': 'success',
                'message': '',
                'data': transaction_count
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': e
            }, status=status.HTTP_400_BAD_REQUEST)


class TransactionAmountView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    pagination_class = CustomPagination

    def get(self, request):
        user_role = request.user.role

        try:
            all_terminal_table = TerminalSerialTable.objects.filter(user__role=user_role)
            month = request.GET.get('month')
            if month:
                att = []
                for t in all_terminal_table:
                    if str(t.date_assigned.date().month) == month:
                        att.append(t)
                all_terminal_table = att
            transaction_amount = {}
            ta = {}
            for terminal in all_terminal_table:
                all_transactions = Transaction.objects.filter(terminal_id=terminal.terminal_id) \
                    .filter(transaction_leg="EXTERNAL") \
                    .filter(status="SUCCESSFUL")
                stock_instance = Stock.objects.get(terminal_id=terminal.terminal_id)
                name = stock_instance.sold_by.sales_rep.first_name + stock_instance.sold_by.sales_rep.last_name
                this_amount = 0
                if len(all_transactions) > 0:
                    this_amount = all_transactions.aggregate(Sum('amount'))['amount__sum']

                if name in ta.keys():
                    ta[name] += this_amount
                else:
                    ta[name] = this_amount
            transaction_amount['names'] = list(ta.keys())
            transaction_amount['values'] = list(ta.values())
            transaction_amount['total'] = sum(transaction_amount['values'])
            return Response({
                'status': 'success',
                'message': '',
                'data': transaction_amount
            }, status=status.HTTP_200_OK)
        except Exception as e:
            print(e)
            return Response({
                'status': 'error',
                'message': e
            }, status=status.HTTP_400_BAD_REQUEST)


class RepresentativeDataView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    pagination_class = CustomPagination

    def get(self, request):
        try:
            response = RepresentativeData.get_representative_data(request)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                # 'status': 'error',
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class SalesRepZonesOverview(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request):
        user_role = request.user.role

        zone_count = Zone.objects.count()
        sales_rep_count = SalesRep.objects.filter(sales_rep__role=user_role).count()
        branch_count = Branch.objects.count()
        terminals_count = Stock.objects.filter()

        data = {
            "number_of_zones": zone_count,
            "number_of_branches": branch_count,
            "number_of_sales_reps": sales_rep_count
            }
        return Response(data, status=status.HTTP_200_OK)


class SalesRepZonesView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request):
        user_role = request.user.role

        zone_qs = Zone.objects.all()
        sales_rep_qs = SalesRep.objects.filter(sales_rep__role=user_role)
        branch_qs = Branch.objects.all()
        terminals_qs = Stock.objects.filter()

        zone_qs = Paginator.paginate(queryset=zone_qs, request=request)

        zone_details_list = []
        for zone in zone_qs:
            zone_branches = branch_qs.filter(zone=zone)
            number_of_branches = branch_qs.filter(zone=zone).count()
            number_of_representatives = sales_rep_qs.filter(branch__in=zone_branches).count()
            number_of_terminals = terminals_qs.filter(zone=zone).count()

            data = {
                "id": zone.id,
                "zone": zone.name,
                "number_of_branches": number_of_branches,
                "number_of_representatives": number_of_representatives,
                "number_of_terminals": number_of_terminals,
                }
            zone_details_list.append(data)

        return Response({"data": zone_details_list, "count":len(zone_details_list)}, status=status.HTTP_200_OK)


class SalesZoneDetailsView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request, id):
        try:
            zone_obj = Zone.objects.get(id=id)
            zone_branch_qs = Branch.objects.filter(zone=zone_obj)
            zone_sales_rep_qs = SalesRep.objects.filter(branch__in=zone_branch_qs)
            zone_terminals_qs = Stock.objects.filter(zone=zone_obj)
            zone_sales_rep_qs = Paginator.paginate(queryset=zone_sales_rep_qs, request=request)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        overview = {
            "state": zone_obj.name,
            "number_of_sales_reps": zone_terminals_qs.count()
            }
        
        sales_reps_list = []
        for sales_rep in zone_sales_rep_qs:
            data = {
                "name": sales_rep.sales_rep.get_full_name() if sales_rep.sales_rep else "",
                "email": sales_rep.sales_rep.email if sales_rep.sales_rep else "",
                "phone_number": sales_rep.sales_rep.phone_number if sales_rep.sales_rep else "",
                "state": zone_obj.name if zone_obj.name else "",
                "branch": sales_rep.branch if sales_rep.branch else ""
                }
            sales_reps_list.append(data)

        response = {
            "overview": overview,
            "sales_rep_details": sales_reps_list,
            "count": len(sales_reps_list)
            }

        return Response(response, status=status.HTTP_200_OK)


# TERMINAL MANAGEMENT

# Terminal Request View and Approval
class ZonesDeliveryRequestView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = ZoneSerializer
    queryset = Zone.objects.all()

    def get(self, request):
        date_filter = filter_by_date_two(request=request, user=User, datetime=datetime)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        try:
            response = ZonesDeliveryRequest.get_zone_delivery_request(request, self.queryset, self.serializer_class, start_date, end_date)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class ZonesStockInRequestView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = ZoneSerializer
    queryset = Zone.objects.all()

    def get(self, request):
        date_filter = filter_by_date_two(request=request, user=User, datetime=datetime)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        zones =  self.queryset.all()
        serializer = self.serializer_class(zones, many=True)
        zones = Paginator.paginate(request=request, queryset=zones)        

        stock_request_details = []
        for zone in zones:
            stock_request = StockRequest.objects.filter(date_created__range=[start_date, end_date], zone=zone)
            data =    {
                        "id": zone.id,
                        "zone": zone.name,
                        "number_of_devices": stock_request.count(),
                        "last_updated": stock_request.latest("date_created").date_created if stock_request else ""
                    }
            stock_request_details.append(data)
        response = {
            "requests_list": stock_request_details,
            "count": len(stock_request_details)
        }
        return Response(response, status=status.HTTP_200_OK)

  
class ZoneDeliveryRequestViewDetails(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = StockSerializer
    queryset = Stock.objects.all()

    def get(self, request, id):
        date_filter = filter_by_date_two(request=request, user=User, datetime=datetime)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        try:
            zone =  Zone.objects.get(id=id)
            response = ZoneDeliveryRequestView.get_zone_delivery_request(request, self.queryset, self.serializer_class, start_date, end_date, zone)
            return Response(response, status=status.HTTP_200_OK)
        except Zone.DoesNotExist:
            return Response({"error":"This detail does not exist"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

class ZoneStockInRequestDetails(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = StockRequestSerializer
    queryset = StockRequest.objects.all()

    def get(self, request, id):
        try:
            zone =  Zone.objects.get(id=id)
            response = ZoneStockInRequest.get_zone_stockin_request(self.queryset, self.serializer_class, zone)
            return Response(response, status=status.HTTP_200_OK)
        except Zone.DoesNotExist:
            return Response({"error": "This detail does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


# Branch Terminal Request Details
class BranchStockInRequestDetails(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = StockRequestSerializer
    queryset = StockRequest.objects.all()

    def get(self, request, branch_id):
        try:
            branch =  Branch.objects.get(id=branch_id)
            response = BranchStockInRequest.get_branch_stockin_request(self.queryset, self.serializer_class, branch)
            return Response(response, status=status.HTTP_200_OK)
        except Branch.DoesNotExist:
            return Response({"error": "This detail does not exist"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class StockRequestApprovalView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = ApproveStockRequestSerializer
    queryset = StockRequest.objects.all()

    def patch(self, request, id):
        try:
            stock_request_obj = self.queryset.get(id=id)
        except StockRequest.DoesNotExist:
            return Response({"message":"This detail does not exist"}, status=status.HTTP_400_BAD_REQUEST)

        serializer = self.serializer_class(stock_request_obj, data=request.data)
        if serializer.is_valid(raise_exception=True):
            serializer.save()
        stock_request_obj.is_approved = True
        stock_request_obj.save()
        return Response({"message": "Request Approved"}, status=status.HTTP_200_OK)


# Disapprove Stock Request
class StockRequestDisApprovalView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_class = ApproveStockRequestSerializer
    queryset = StockRequest.objects.all()

    def patch(self, request, id):
        try:
            stock_request_obj = self.queryset.get(id=id)
        except StockRequest.DoesNotExist:
            return Response({"error": "This detail does not exist"}, status=status.HTTP_400_BAD_REQUEST)

        serializer = self.serializer_class(stock_request_obj, data=request.data)
        if serializer.is_valid(raise_exception=True):
            serializer.save()
        stock_request_obj.is_disapproved = True
        stock_request_obj.save()
        return Response({"message": "Request disapproved"}, status=status.HTTP_200_OK)


# MERCHANTS
class MerchantsTopTransactions(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    serializer_classes = CustomerTransactionSerializer
    queryset = Transaction.objects.all()

    def get(self, request):
        
        filter_date = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = filter_date.get("start_date")
        end_date = filter_date.get("end_date")

        try:
            response = MerchantsTransactionsTop().get_merchants_top_transactions(request, start_date=start_date, end_date=end_date)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MerchantsDetailsView(APIView):
    serializer_class = UserSerializer
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]
    queryset = User.objects.all()

    def get(self, request):
        date_filter = filter_by_date_two(user=User, request=request, datetime=datetime)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")
        status_filter = request.GET.get("status_filter")

        if not status_filter or status_filter == "":
            status_choice_list = ["ACTIVE", "INACTIVE", "PARTIALLY_INACTIVE",
                                "RECOVERY", "RECOVERED", "SUSPENDED", "DORMANT"]
        else:
            status_choice_list = [status_filter]

        try:
            response = MerchantDetails.get_merchant_details(request, start_date, end_date, status_choice_list, status, jresponse=Response)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


# Service Quality
class ServiceQualityView(APIView):
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def get(self, request):
        date_filter = filter_by_date_two(request=request, user=User, datetime=datetime)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        try:
            response = ServiceQuality.get_service_quality(start_date, end_date, request)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

# Customer Statement of Account Feature
class CustomerAccountStatementView(APIView):
    serializer_class = AccountStatementSerializer

    def post(self, request):
        serializer = self.serializer_class
        try:
            queryset = AccountStatement.request_statement(request=request, serializer=serializer)
            return Response({"message": "customer bank statement sent by email"}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class GetUserKycDetailsView(APIView):
    authentication_classes = [CustomTokenAuthentication]
    permission_classes = [CustomIsAuthenticated, CheckIPAddresses(service_name="REQUISITION")]

    def post(self, request):
        email = request.data.get("user-email")
        bvn_number = request.data.get("bvn-number")

        if email == None or bvn_number == None:
            return Response({"error": "BVN and email are required"}, status = status.HTTP_400_BAD_REQUEST)
        else:
            user = User.objects.filter(email=email).last()
            if email and user:
                bvn_details = BVNDetail.objects.filter(kyc__user=user).last()
                if bvn_details:
                    data = {
                        "user_email": user.email,
                        "bvn_first_name": bvn_details.bvn_first_name if bvn_details.bvn_first_name else "",
                        "bvn_middle_name": bvn_details.bvn_middle_name if bvn_details.bvn_middle_name else "",
                        "bvn_last_name": bvn_details.bvn_last_name if bvn_details.bvn_last_name else "",
                        "bvn_gender": bvn_details.bvn_gender if bvn_details.bvn_gender else "",
                        "bvn_birthdate": bvn_details.bvn_birthdate if bvn_details.bvn_birthdate else "",
                        "bvn_number": bvn_details.bvn_number if bvn_details.bvn_number else "",
                        "bvn_phone_number": bvn_details.bvn_phone_number if bvn_details.bvn_phone_number else "",
                        "bvn_phone_number_2": bvn_details.bvn_phone_number_2 if bvn_details.bvn_phone_number_2 else "",
                        "kyc_level": user.kyc_level if user.kyc_level else "",
                        "bvn_nationality": bvn_details.bvn_nationality if bvn_details.bvn_nationality else "",
                        "bvn_watchlisted": bvn_details.bvn_watchlisted if bvn_details.bvn_watchlisted else "",
                        "bvn_marital_status": bvn_details.bvn_marital_status if bvn_details.bvn_marital_status else "", 
                        "bvn_lga_of_origin": bvn_details.bvn_lga_of_origin if bvn_details.bvn_lga_of_origin else "",
                        "bvn_residential_address": bvn_details.bvn_residential_address if bvn_details.bvn_residential_address else "",
                        "bvn_state_of_origin": bvn_details.bvn_state_of_origin if bvn_details.bvn_state_of_origin else "",
                        "verification_status": bvn_details.verification_status if bvn_details.verification_status else ""
                    }
                    return Response({"data": data}, status = status.HTTP_200_OK)
                else:
                    return Response({"error": "bvn details do not exist for this user"}, status = status.HTTP_400_BAD_REQUEST)
            else:
                return Response({"error": "user with this email or bvn does not exist"}, status = status.HTTP_400_BAD_REQUEST)


class LottoAgentsSuspensionView(APIView):
    """
    Call-back view for suspending inactive lotto agents
    """
    permission_classes = [CustomIsAuthenticated & IsAdminUserRole]

    def post(self, request):
        data = request.data
        terminal_id = data.get("terminal_id")
        agent = User.objects.filter(terminal_id=terminal_id).last()

        if agent is not None:
            User.suspend_user(
                user=agent,
                reason="Suspended due to poor sales performance",
                request=None
            )
            agent.terminal_suspended = True
            agent.save()
        else:
            pass
        return Response({"message": "process completed"}, status=status.HTTP_200_OK)