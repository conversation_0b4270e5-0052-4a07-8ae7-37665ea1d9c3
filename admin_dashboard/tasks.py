from django.utils import timezone
from django.conf import settings
from django.db.models import Sum
from django.contrib.auth import get_user_model
User = get_user_model()

from celery import shared_task
from statistics import mode

from admin_dashboard.models import (
    Dispute, SalesRep, NewUploadDisputeTable, 
    DevelopersEmail
)

from accounts.helpers.helper_func import notify_agent_activity_report_ran
from admin_dashboard.helpers.helpers import (
    send_agent_activity_email, send_pos_agent_email, 
    send_sms_to_pos_agent, 
    send_agent_activity_email_task,
    date_utility,
    notify_agent_transaction_monitor_ran,
    send_agent_activity_email_multiple_files,
    send_agent_performance_ranking_email,
    get_agent_transactions_performance_from_lotto,
    send_transaction_monitoring_email,
    is_last_day_of_month
)

from main.models import ConstantTable
from main.helper.helper_function import send_dynamic_sms
from main.helper.send_emails import send_dynamic_email

from accounts.models import Transaction
from horizon_pay.models import TerminalSerialTable, StartCashOutTran
from retail.services import DateUtility

from datetime import datetime, timedelta
from math import floor
from pathlib import Path

import pandas as pd
import os
from matplotlib import pyplot as plt
import json

from google.oauth2 import service_account
import gspread



# filter_date = date_utility(datetime=datetime)
# month_start = filter_date.get("month_start")
# previous_day_six_am = filter_date.get("previous_day_six_am")
# datetime_today_6am = filter_date.get("datetime_today_6am")
# today = filter_date.get("date_today")
# week_start = filter_date.get("week_start")
# previous_month_start = filter_date.get("previous_month_start")
# previous_month_end = filter_date.get("previous_month_end")


@shared_task
def get_trans_report(file):
        """Sends an excel report to specified email addresses"""

        if not settings.ENVIRONMENT == "development":
            agent_activity_email_list = [
                                "<EMAIL>",
                                "<EMAIL>",
                                "<EMAIL>",
                                "<EMAIL>",
                                "<EMAIL>",
                                "<EMAIL>",
                                "<EMAIL>",
                                "<EMAIL>",
                                "<EMAIL>",
                                "<EMAIL>"
                                ]
        else:
            agent_activity_email_list = [
                            "<EMAIL>",
                            "<EMAIL>"
                            ]

        for email in agent_activity_email_list:             
            send_agent_activity_email_task(
                message = "This is a report on agent activities for the past week",
                file = file,
                file_name = "agent_activity_last.xlsx",
                email_subject = "Agent Activities Report",
                email=email
                )

        return "DONE"


@shared_task
def set_terminal_active_inactive():
    """Checks to see that agents are performing transactions as required
    based on set expectations. Agents are set to inactive if they fail
    to reach target. Agents who have failed to reach the target for 3 consecutive
    weeks are marked for suspension.

    Also generates and shares an excel report based on the analytics.
    """
    date_today = DateUtility().today
    month_start = DateUtility().month_start
    week_number = date_today.isocalendar()[1]
    week_day = date_today.weekday()
    is_last_friday = is_last_day_of_month()


    all_terminals_qs = TerminalSerialTable.objects.filter(user__isnull=False)
    developers_email_list = DevelopersEmail.objects.values_list("email", flat=True)

    terminal_status_list = []

    if all_terminals_qs: #and date_today.weekday() == 4: # Make sure it's Friday
        for terminal in all_terminals_qs:
            if (terminal.user.terminal_id and
                terminal.user.type_of_user == "AGENT" and
                terminal.user.email not in developers_email_list
                ):
                agent = terminal.user

                # days computations
                date_today = timezone.now()
                assigned_date = terminal.date_assigned if terminal.date_assigned else terminal.date_created
                number_of_days_since_assigned = abs(date_today - assigned_date).days

                terminal_last_inactive = agent.terminal_last_inactive

                if terminal_last_inactive:
                    number_of_days_since_last_inactive = abs(date_today - terminal_last_inactive).days
                else:
                    number_of_days_since_last_inactive = None

                # Number of days since month start
                number_of_days_since_month_start = abs(date_today.date() - month_start.date()).days
                if number_of_days_since_month_start >= 7:
                    number_of_days_since_month_start_less_weekends = (
                        (number_of_days_since_month_start - floor(number_of_days_since_month_start/7))
                        )
                else:
                    number_of_days_since_month_start_less_weekends = number_of_days_since_month_start


                # number of weeks
                weeks_count = floor(number_of_days_since_assigned / 7)
                if number_of_days_since_assigned >= 7:
                    number_of_days_since_assigned_less_weekends = (weeks_count * 7) - (weeks_count)
                elif number_of_days_since_assigned < 1:
                    number_of_days_since_assigned_less_weekends = 1
                else:
                    number_of_days_since_assigned_less_weekends = number_of_days_since_assigned

                # transaction counts
                all_possible_transaction_list = [
                                            "SEND_BANK_TRANSFER",
                                            "FUND_BANK_TRANSFER", "FUND_PAYSTACK", "CARD_TRANSACTION_FUND",
                                            "CARD_TRANSACTION_FUND_TRANSFER", "LOTTO_PLAY", "LIBERTY_LIFE_PAYMENT", "CARD_PURCHASE"
                                            ]
                
                transactions_qs = Transaction.objects.filter(user=agent, status="SUCCESSFUL", is_reversed=False,
                                  transaction_type__in=all_possible_transaction_list).order_by("id")
                total_transactions_count = transactions_qs.count()
                total_transactions_amount = list(transactions_qs.aggregate(Sum("amount")).values())[0]
                
                agent_current_month_transactions_qs = transactions_qs.filter(date_created__gte=month_start)
                agent_current_month_transactions_amount = list(agent_current_month_transactions_qs.aggregate(Sum("amount")).values())[0]
                agent_current_month_transactions_count = agent_current_month_transactions_qs.count()
                
                last_transaction = transactions_qs.last()
                last_transaction_date = last_transaction.date_created if last_transaction else None
                last_transaction_type = last_transaction.transaction_type if last_transaction else None

                average_daily_transaction_count = total_transactions_count / number_of_days_since_assigned_less_weekends
                average_daily_transaction_amount = ((agent_current_month_transactions_amount if
                                                     agent_current_month_transactions_amount else 0)
                                                    / (number_of_days_since_month_start_less_weekends)
                                                    )
                
                current_month_average_daily_transactions_amount = (
                                                                (agent_current_month_transactions_amount if 
                                                                agent_current_month_transactions_amount else 0) / 
                                                                (number_of_days_since_month_start_less_weekends if 
                                                                number_of_days_since_month_start_less_weekends else 1)
                                                                )
                
                current_month_average_daily_transaction_count = (
                                                                agent_current_month_transactions_count / 
                                                                (number_of_days_since_month_start_less_weekends if 
                                                                number_of_days_since_month_start_less_weekends else 1)
                                                                )     
                
                try:
                    agent_first_transaction = transactions_qs.order_by("date_created").first()
                    first_transaction_date = agent_first_transaction.date_created
                    days_before_first_transaction = abs(first_transaction_date - assigned_date).days
                except AttributeError:
                    days_before_first_transaction = None

                # Set Truly Active/Inactive Terminal Status
                if average_daily_transaction_count < 10:
                        agent.terminal_truly_active = False
                else:
                    agent.terminal_truly_active = True

                # set terminal status
                if weeks_count >= 2:
                    if number_of_days_since_last_inactive:
                        total_transactions_count = transactions_qs.filter(date_created__date__gt=datetime.strftime(
                                                    terminal_last_inactive.date(), "%Y-%m-%d")).count()
                        average_daily_transaction_count = total_transactions_count / number_of_days_since_last_inactive
                    else:
                        average_daily_transaction_count = (agent_current_month_transactions_count / 
                                                        (number_of_days_since_month_start_less_weekends if 
                                                         number_of_days_since_month_start_less_weekends else 1)
                                                         )

                    if current_month_average_daily_transaction_count <= 3:
                        agent.terminal_status = "INACTIVE"
                        agent.inactive_count += 1
                        agent.terminal_last_inactive = date_today

                    elif current_month_average_daily_transaction_count > 3 and current_month_average_daily_transaction_count < 5:
                        agent.terminal_status = "PARTIALLY_INACTIVE"
                        agent.inactive_count = 0
                    elif current_month_average_daily_transaction_count >= 5:
                        agent.terminal_status = "ACTIVE"
                        agent.inactive_count = 0
                    else:
                        pass
                else:
                    pass

                agent.save()
                agent.refresh_from_db()

                # Mark Inactive terminals for Suspension
                marked_for_suspension = False
                if is_last_friday and agent.inactive_count >= 3 and agent.type_of_user == "AGENT" and date_today.weekday() >= 3:
                    marked_for_suspension = True

                data = {
                    "report_date": timezone.now().date(),
                    "terminal": terminal.terminal_id,
                    "agent_name": agent.bvn_full_name if agent.bvn_first_name else agent.full_name,
                    "agent_phone_number": agent.phone_number if agent.phone_number else "N/A",
                    "agent_email": agent.email if agent.email else "N/A",
                    "days_before_first_transaction": days_before_first_transaction if days_before_first_transaction else "N/A",
                    "terminal_status": agent.terminal_status,
                    "number_of_days_since_terminal_last_inactive": number_of_days_since_last_inactive,
                    "number_of_days_since_assignment": number_of_days_since_assigned,
                    "total_transaction_count": total_transactions_count,
                    "total_transaction_amount": total_transactions_amount if total_transactions_amount else 0.00,
                    "transaction_count_since_last_inactive": total_transactions_count if number_of_days_since_last_inactive else "N/A",
                    "week_count": weeks_count,
                    "current_month_average_daily_transaction_count": current_month_average_daily_transaction_count,
                    "current_month_average_daily_transactions_amount": current_month_average_daily_transactions_amount,
                    "current_month_transactions_count": agent_current_month_transactions_count,
                    "current_month_transactions_amount": agent_current_month_transactions_amount if agent_current_month_transactions_amount else 0.00,
                    "consecutive_weeks_inactive_count": agent.inactive_count,
                    "terminal_suspended": agent.is_suspended,
                    "terminal_serial": terminal.terminal_serial,
                    "last_transaction_date": last_transaction_date.date() if last_transaction_date and last_transaction_date.date() else "",
                    "last_transaction_type": last_transaction_type if last_transaction_type else "",
                    "agent_type": agent.type_of_user if agent.type_of_user else "N/A",
                    "marked_for_suspension": marked_for_suspension
                }
                terminal_status_list.append(data)

        df = pd.DataFrame()
        agent_activity = df.from_dict(terminal_status_list)

        last_file_path = os.path.join(settings.BASE_DIR, 'media/agent_report/last/') 
        last_file = os.path.join(f"{last_file_path}/agent_activity_last.xlsx") 

        my_file = Path(last_file) 
        if my_file.is_file(): 
            os.remove(last_file)

        try:
            os.mkdir(last_file_path)
        except Exception:
            pass

        agent_activity.to_excel(f"{last_file_path}/agent_activity_last.xlsx", index=False)
        final_file_path = os.path.join(settings.BASE_DIR, 'media/agent_report/last/agent_activity_last.xlsx')

        with open(final_file_path, "rb") as excel_file:
            agent_activities = excel_file.read()
            excel_file.close()

        get_trans_report(file=agent_activities)

        return terminal_status_list
    return "DONE"


@shared_task
def suspend_marked_terminals():
    """
    Suspends agents that must have been marked for suspension viz-a-viz
    the weekly agents activities check.
    """

    date_today = DateUtility().today
    month_start = DateUtility().month_start
    week_number = date_today.isocalendar()[1]
    week_day = date_today.weekday()
    is_last_friday = is_last_day_of_month()

    agents_qs = User.objects.filter(terminal_id__isnull=False)

    final_file_path = os.path.join(settings.BASE_DIR, 'media/agent_report/last/agent_activity_last.xlsx')
    excel_df = pd.read_excel(final_file_path)

    for n, row in excel_df.iterrows():
        agent_email = row["agent_email"]
        agent_instance = agents_qs.get(email=agent_email)
        
        marked_for_suspension = row["marked_for_suspension"]

        if marked_for_suspension == True and is_last_friday:
            agent_instance.terminal_suspended = True
            User.suspend_user(
                user=agent_instance,
                reason="Suspended Due to Inactivity from Agent Weekly Activities Check",
                request=None
            )
            agent_instance.save()

    return "DONE"


@shared_task
def notify_suspended_and_inactive_agents():
    """
    This method sends email and sms to notifications to inactive agents
    and suspended agents based on the most recently generated agents activities report.
    Also notifies relationship officers (RO) of suspended agents.
    """
    date_today = DateUtility().today
    month_start = DateUtility().month_start
    week_number = date_today.isocalendar()[1]
    week_day = date_today.weekday()
    is_last_friday = is_last_day_of_month()

    agents_qs = User.objects.filter(terminal_id__isnull=False)

    final_file_path = os.path.join(settings.BASE_DIR, 'media/agent_report/last/agent_activity_last.xlsx')
    excel_df = pd.read_excel(final_file_path)

    inactive_email_recipients = ["<EMAIL>"]
    suspended_email_recipients = ["<EMAIL>"]
    suspended_terminals_ros = [("09050151725", "TEST_tERMINAL_ID")]
    suspended_sms_recipients = ["2349050151725"]
    inactive_sms_recipients = ["2349050151725"]

    for n, row in excel_df.iterrows():
        agent_email = row["agent_email"]
        agent_instance = agents_qs.get(email=agent_email)
        
        terminal_status = row["terminal_status"]
        # terminal_id = row["terminal"]
        agent_phone_number = row["agent_phone_number"]
        terminal_suspended = row["terminal_suspended"]

        if terminal_status == "INACTIVE":
            inactive_email_recipients.append(agent_email)
            inactive_sms_recipients.append(agent_phone_number)

            inactive_sms_recipients.append(agent_phone_number if agent_phone_number else "0")
            while "0" in inactive_sms_recipients:
                inactive_sms_recipients.remove("0")

        if terminal_suspended == True:
            suspended_email_recipients.append(agent_email)

            suspended_sms_recipients.append(agent_phone_number if agent_phone_number else "0")
            while "0" in suspended_sms_recipients:
                suspended_sms_recipients.remove("0")

        # Get agent's R/O
        if agent_instance.has_sales_rep:
            try:
                agent_ro = SalesRep.objects.filter(sales_rep_code=agent_instance.sales_rep_upline_code).last()
                suspended_terminals_ros.append((agent_ro.sales_rep.phone_number, agent_instance.terminal_id))
            except AttributeError:
                pass

    # Send email notification to inactive agents
    for email in inactive_email_recipients:
        send_pos_agent_email(
            email=email,
            files="",
            template="pos_agent_email.html",
            email_subject="Inactivity Notification",
            message="""<p>Dear Partner, We hope this meets you in good spirit.</p>
            <p>Your transaction activity on the POS terminal has not been up to expectation. <br/>
            Kindly put your terminal to use to avoid suspension.</p>"""
            )

    # Send email notification to suspended agents
    if is_last_friday:
        for email in suspended_email_recipients:
            send_pos_agent_email(
                email = email,
                files = "",
                template="pos_agent_email.html",
                email_subject="Terminal Suspension Notification",
                message="""<p>Dear Partner,</p>
                <p>Your terminal has been suspended due to poor performance in transactions activity. <br/>
                Kindly contact your RO for guidance.</p>"""
            )

    # Send sms notification to suspended agents
    # if is_last_friday:
    #     for phone_number in suspended_sms_recipients:
    #         send_sms_to_pos_agent(
    #         phone_number=phone_number,
    #         message="""Dear Partner, Your terminal has been suspended due to poor performance in transaction activities.\nKindly contact support;\nEmail: <EMAIL>;\nPhone: 013300172"""
    #         )
          
    # Send sms notification to Inactive agents
    # for phone_number in inactive_sms_recipients:
    #     send_sms_to_pos_agent(
    #         phone_number=phone_number,
    #         message="""Dear Partner, Your transactions activity on the POS terminal has not been up to expectation.\nKindly transact more to avoid being suspended.\nContact support;\nEmail: <EMAIL>,\nPhone: 013300172"""
    #         )
        
    # Send sms notification to R/Os of suspended terminals
    sales_reps_phone_list = set([salesrep_phone for salesrep_phone, terminal_id in suspended_terminals_ros])

    if is_last_friday:
        pass
        # for phone in sales_reps_phone_list:
        #     salesrep_terminals = [terminal_id for salesrep_phone, terminal_id in suspended_terminals_ros if salesrep_phone==phone]

            # if len(salesrep_terminals) < 2:
            #     send_sms_to_pos_agent(
            #         phone_number=phone,
            #         message=f"""Dear Partner, Your POS device with the following terminal_id; {salesrep_terminals[0]} has been suspended due to inactivity. Kindly contact your downline for resolution."""
            #         )
            # else:
            #     send_sms_to_pos_agent(
            #         phone_number=phone, 
            #         message=f"""Dear Partner, Your devices with the following terminal_ids; {', '.join(salesrep_terminals)}, with your downlines have been suspended due to inactivity.\nKindly contact your downlines for resolution."""
            #         )


@shared_task
def send_agents_activities_report():
    """
    Retrieves and dispatches most recently generated agents activities report
    """
    
    final_file_path = os.path.join(settings.BASE_DIR, 'media/agent_report/last/agent_activity_last.xlsx')

    with open(final_file_path, "rb") as excel_file:
        agent_activities = excel_file.read()
        excel_file.close()

    get_trans_report(file=agent_activities)


@shared_task
def disputes_air(): # This tasks collates all AIR transactions for the day in an excel sheet and sends them to reconciliation dept.
    date_today = timezone.now()
    previous_day = date_today - timedelta(days=1)

    all_dispute_qs = Dispute.objects.filter(date_created__range=[previous_day, date_today]) #collect all existing disputes for the day
    air_transactions_list = []
    count = 0
    for dispute in all_dispute_qs:
        dispute_rrn = dispute.transaction_rrn
        agent = dispute.user

        dispute_transaction = Transaction.objects.filter(unique_reference=dispute_rrn, user=agent, user__terminal_id__isnull=False, status="SUCCESSFUL").last()
        if dispute_transaction:
            count += 1
            data = {
                # "s/n": count,
                "TID": dispute_transaction.terminal_id,
                "EMAIL ADDRESS": dispute.user.email,
                "Amount": dispute_transaction.amount,
                "Transaction Type": f"{dispute_transaction.transaction_type}",
                "RRN": dispute_transaction.unique_reference,
                "STATUS": dispute.approval_status
            }
            air_transactions_list.append(data)

    # write all air transactions into an excel sheet
    df = pd.DataFrame()
    air_disputes = df.from_dict(air_transactions_list)
    save_path = os.path.join(settings.BASE_DIR, "media/dispute")
    air_disputes.to_excel(f"{save_path}/air_disputes_{timezone.now().strftime('%Y-%M-%d')}.xlsx")
    file_path = os.path.abspath(f"{save_path}/air_disputes_{timezone.now().strftime('%Y-%M-%d')}.xlsx")
    file_name = os.path.basename(file_path)

    with open(file_path, "rb") as air_file:
        air_disputes_excel = air_file.read()
        air_file.close()

    if air_transactions_list:
        send_agent_activity_email(
                message = "This sheet contains a list of failed transactions that may be resolved manually",
                file = air_disputes_excel,
                file_name = file_name,
                email_subject = "Manual Agent Disputes Resolution Data",
                air = True
            )
    else:
        pass

    return air_transactions_list

def get_transactions_monitoring_last_file(transactions_available, success_rate=0, 
                                          failure_rate=0, overall_failure_cause=""
                                          ):

    if not settings.ENVIRONMENT == "development":
            agent_activity_email_list = [
                                "<EMAIL>",
                                "<EMAIL>",
                                "<EMAIL>",
                                "<EMAIL>",
                                "<EMAIL>",
                                "<EMAIL>",
                                "<EMAIL>",
                                "<EMAIL>"
                                ]
    else:
        agent_activity_email_list = [
                        "<EMAIL>",
                        "<EMAIL>"
                        ]
        
    if transactions_available:
        file_path = os.path.join(settings.BASE_DIR, "media/monitoring/transactions_monitoring.xlsx")

        with open(file_path, "rb") as excel_file:
            monitoring_file = excel_file.read()
            excel_file.close()
        for email in agent_activity_email_list:
            send_transaction_monitoring_email(
                message=f"Transactions monitoring from 6am yesterday to 6am today.",
                file=monitoring_file,
                file_name="transactions_monitoring.xlsx",
                email_subject="Transactions Monitoring Report",
                email=email,
                success_rate=success_rate,
                failure_rate=failure_rate,
                overall_failure_cause=overall_failure_cause
                )
    else:
        for email in agent_activity_email_list:
            send_transaction_monitoring_email(
                message="The transactions monitoring task ran but there was no transaction amount up to threshold value.",
                file=None,
                file_name="",
                email_subject="Transactions Monitoring Report",
                email=email
                )

    return "DONE"


@shared_task    
def monitor_daily_transactions():
    filter_date = date_utility(datetime=datetime)
    previous_day_six_am = filter_date.get("previous_day_six_am")

    trans_threshold = ConstantTable.get_constant_table_instance().trans_monitor_thresh
    transaction_qs = Transaction.objects.filter(
            amount__gte=trans_threshold,
            date_created__gte=previous_day_six_am,
            # date_created__lte=datetime_today_6am
            )
    all_transactions_qs = Transaction.objects.filter()
    
    successfull_transactions_qs = all_transactions_qs.filter(status="SUCCESSFUL")
    failed_transactions_qs = all_transactions_qs.filter(status="FAILED")

    total_transactions_amount = list(all_transactions_qs.aggregate(Sum("amount")).values())[0]
    successful_transactions_amount = list(successfull_transactions_qs.aggregate(Sum("amount")).values())[0]
    failed_transactions_amount = list(failed_transactions_qs.aggregate(Sum("amount")).values())[0]

    success_rate = (successful_transactions_amount if successful_transactions_amount else 0.00) / (total_transactions_amount if total_transactions_amount else 1.00) * 100
    failure_rate = (failed_transactions_amount if failed_transactions_amount else 0.00) / (total_transactions_amount if total_transactions_amount else 1.00) * 100
    
    provider_status_list = [transaction.provider_status if transaction.provider_status else "" for transaction in all_transactions_qs]
    # provider_status_list.pop("")
    overall_failure_cause = mode(provider_status_list)

    transactions_list = []
    for transaction in transaction_qs:
        trans_details = {
            "trans_date": transaction.transaction_date,
            "customer": transaction.user.get_full_name(),
            "terminal_id": transaction.user.terminal_id if transaction.user.terminal_id else "N/A",
            "amount": transaction.amount,
            "status": transaction.status,
            "transaction_id": transaction.transaction_id,
            "trans_date": transaction.date_created,
            "transaction_type": transaction.transaction_type,
            "trans_leg": transaction.transaction_leg,
            "trans_mode": transaction.transaction_mode,
            }
        transactions_list.append(trans_details)
    
    try:
        if transactions_list:
            df = pd.DataFrame()
            monitor_transactions = df.from_dict(transactions_list)
            monitor_transactions['trans_date'] = monitor_transactions['trans_date'].apply(lambda a: pd.to_datetime(a).to_datetime64())

            file_path = os.path.join(settings.BASE_DIR, "media/monitoring/")
            try:
                os.mkdir(file_path)
            except Exception:
                pass
            final_path = Path(f"{file_path}/transactions_monitoring.xlsx")
            if final_path.is_file():
                os.remove(final_path)

            transactions_monitoring = monitor_transactions.to_excel(f"{file_path}/transactions_monitoring.xlsx")
            get_transactions_monitoring_last_file(transactions_available=True,
                                                  success_rate=success_rate, failure_rate=failure_rate, 
                                                  overall_failure_cause=overall_failure_cause)
        else:
            get_transactions_monitoring_last_file(transactions_available=False)
    except Exception as e:
        notify_agent_transaction_monitor_ran("2349050151725", e)
            
    return transaction_qs


@shared_task
def weekly_agents_transactions_summary():
    date_today = DateUtility().today
    week_start = DateUtility().week_start
    week_number = date_today.isocalendar()[1]
    week_day = date_today.weekday()

    transactions = Transaction.objects.all()
    agents = TerminalSerialTable.objects.select_related("user").filter(user__isnull=False)
    
    for agent in agents:
        user = agent.user
        transactions_qs = Transaction.objects.filter(user=user, date_created__gte=week_start)

        # Transaction Amount
        total_failed_transactions = transactions_qs.filter(status="FAILED").aggregate(Sum("amount"))["amount__sum"]
        total_successful_transactions = list(transactions_qs.filter(status="SUCCESSFUL").aggregate(Sum("amount")).values())[0]
        total_transactions_amount = list(transactions_qs.aggregate(Sum("amount")).values())[0]

        amount_keys = ["Transaction Amount", "Successful Transactions", "Failed Transactions"]
        amount_values = [total_transactions_amount if total_transactions_amount else 0, 
                         total_successful_transactions if total_successful_transactions else 0, 
                         total_failed_transactions if total_failed_transactions else 0]

        amount_plot = plt.bar(amount_keys, amount_values, color="blue", edgecolor="white")
        save_path = os.path.join(settings.BASE_DIR, "media/summary")
        plt.ylabel("Transaction Amount")
        existing_path = Path(f"{save_path}/transaction_amount.jpeg")
        if existing_path.is_file():
            os.remove(existing_path)

        try:
            os.mkdir("media/summary")
        except Exception as e:
            pass

        plt.savefig(f"{save_path}/transaction_amount.jpeg")
        plt.close()

        # Transaction Counts
        total_failed_transactions_count = transactions_qs.filter(status="FAILED").count()
        total_successful_transactions_count = transactions_qs.filter(status="SUCCESSFUL").count()
        total_transactions_count = transactions_qs.count()

        count_keys = ["Total Count", "Successful Transactions", "Failed Transactions"]
        count_values = [total_transactions_count, 
                         total_successful_transactions_count, 
                         total_failed_transactions_count]
        
        count_bar = plt.bar(count_keys, count_values, color="red")
        
        plt.ylabel("Transactions Count")

        existing_path2 = Path(f"{save_path}/transaction_count.jpeg")
        if existing_path2.is_file():
            os.remove(existing_path2)

        try:
            os.mkdir("media/summary")
        except Exception as e:
            pass

        plt.savefig(f"{save_path}/transaction_count.jpeg")
        plt.close()

        with open(f"{save_path}/transaction_count.jpeg", "rb") as jfile1:
            file1 = jfile1.read()
            jfile1.close

        with open(f"{save_path}/transaction_amount.jpeg", "rb") as jfile2:
            file2 = jfile2.read()
            jfile2.close

        # Send Email to agent
        send_agent_activity_email_multiple_files(
            email_subject="Weekly Transactions Summary",
            message="This is a summary of your transaction activities for the past week",
            email=user.email,
            files=[("transaction_count.jpeg", file1), ("transaction_amount.jpeg", file2)],
        )

        
@shared_task
def end_of_month_agents_performance_ranking():
    date_today = DateUtility().today
    month_start = DateUtility().month_start
    previous_month_start = DateUtility().previous_month_start
    previous_month_end = DateUtility().previous_month_end
    week_number = date_today.isocalendar()[1]
    week_day = date_today.weekday()

    cashout_transaction_types = ["CARD_TRANSACTION_FUND", "CARD_TRANSACTION_FUND_TRANSFER"]
    transfer_transaction_types = ["SEND_BANK_TRANSFER", "FUND_BANK_TRANSFER"]

    agents =  User.objects.prefetch_related(
              "transactions").filter(terminal_id__isnull=False
                )
    
    performance_list = []

    for agent in agents:
        agent_cashout_qs = agent.transactions.filter(
            date_created__gte=previous_month_start, 
            date_created__lte=previous_month_end, 
            transaction_type__in=cashout_transaction_types,
            status="SUCCESSFUL")
        agent_transfers_qs = agent.transactions.filter(
            date_created__gte=previous_month_start, 
            date_created__lte=previous_month_end, 
            transaction_type__in=transfer_transaction_types, 
            status="SUCCESSFUL")

        agent_cashout_count = agent_cashout_qs.count()
        agent_transfer_count = agent_transfers_qs.count()

        agent_cashout_amount = list(agent_cashout_qs.aggregate(Sum("amount")).values())[0]
        agent_transfer_amount = list(agent_transfers_qs.aggregate(Sum("amount")).values())[0]

        results = {
            "terminal_id": agent.terminal_id,
            "agent_name": agent.get_full_name(),
            "agent_email": agent.email,
            "agent_phone": agent.phone_number,
            "cashout_count": agent_cashout_count,
            "transfer_count": agent_transfer_count,
            "cashout_amount": agent_cashout_amount if agent_cashout_amount else 0,
            "transfer_amount": agent_transfer_amount if agent_transfer_amount else 0,
            "overall_amount": (agent_cashout_amount if agent_cashout_amount else 0) + (agent_transfer_amount if agent_transfer_amount else 0),
            "overall_count": agent_cashout_count + agent_transfer_count
            }
        
        performance_list.append(results)
        
    cashout_count_sorted_list = sorted(performance_list, key=lambda d: d["cashout_count"])
    transfer_count_sorted_list = sorted(performance_list, key=lambda d: d["transfer_count"])

    cashout_amount_sorted_list = sorted(performance_list, key=lambda d: d["cashout_amount"])
    transfer_amount_sorted_list = sorted(performance_list, key=lambda d: d["transfer_amount"])

    overall_count_sorted_list = sorted(performance_list, key=lambda d: d["overall_count"])
    overall_amount_sorted_list = sorted(performance_list, key=lambda d: d["overall_amount"])


    overall_best_count = overall_count_sorted_list[-1]["overall_count"]
    overall_count_agent_email = overall_count_sorted_list[-1]["agent_email"]
    overall_count_agent_terminal_id = overall_count_sorted_list[-1]["terminal_id"]

    overall_best_amount = overall_amount_sorted_list[-1]["overall_amount"]
    overall_amount_agent_email = overall_amount_sorted_list[-1]["agent_email"]
    overall_amount_agent_terminal_id = overall_amount_sorted_list[-1]["terminal_id"]

    cashout_best_amount = cashout_amount_sorted_list[-1]["cashout_amount"]
    cashout_amount_agent_email = cashout_amount_sorted_list[-1]["agent_email"]
    cashout_amount_agent_terminal_id = cashout_amount_sorted_list[-1]["terminal_id"]

    cashout_best_count = cashout_count_sorted_list[-1]["cashout_count"]
    cashout_count_agent_email = cashout_count_sorted_list[-1]["agent_email"]
    cashout_count_agent_terminal_id = cashout_count_sorted_list[-1]["terminal_id"]

    transfer_best_amount = transfer_amount_sorted_list[-1]["transfer_amount"]
    transfer_amount_agent_email = transfer_amount_sorted_list[-1]["agent_email"]
    transfer_amount_agent_terminal_id = transfer_amount_sorted_list[-1]["terminal_id"]

    transfer_best_count = transfer_count_sorted_list[-1]["transfer_count"]
    transfer_count_agent_email = transfer_count_sorted_list[-1]["agent_email"]
    transfer_count_agent_terminal_id = transfer_count_sorted_list[-1]["terminal_id"]

    
    # Send ranking report email
    if not settings.ENVIRONMENT == "development":
            email_list = [
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>"
                            ]
    else:
        email_list = [
                    "<EMAIL>",
                    "<EMAIL>"
                    ]
        
    for email in email_list:
        send_agent_performance_ranking_email(
                                message="This is the transactions ranking report for the month of March", 
                                email_subject="Transactions Ranking Report for March", 
                                email=email,
                                overall_best_amount=overall_best_amount, 
                                overall_best_count=overall_best_count,
                                cashout_best_count=cashout_best_count,
                                cashout_best_amount=cashout_best_amount,
                                transfer_best_amount=transfer_best_amount,
                                transfer_best_count=transfer_best_count,
                                overall_best_agent_count=overall_count_agent_email,
                                overall_best_agent_amount=overall_amount_agent_email,
                                cashout_best_agent_amount=cashout_amount_agent_email,
                                cashout_best_agent_count=cashout_count_agent_email,
                                transfer_best_agent_amount=transfer_amount_agent_email,
                                transfer_best_agent_count=transfer_count_agent_email
                                )

        
@shared_task
def send_sms_and_email_on_success_dispute_add(rrn):

    get_instance = NewUploadDisputeTable.objects.filter(retrieval_reference=rrn).first()
    get_rrn = StartCashOutTran.objects.filter(rrn=rrn).first()

    if get_instance and get_rrn:
        user_email = get_rrn.user.email
        user_phone = get_rrn.user.phone_number
        amount = get_rrn.amount_started

        text_to_send = f"Dear Agent, your customer who performed a transaction with RRN - {rrn} and amount - N{amount} has successfully received a refund from their bank.\nThank you for always choosing LibertyPay"
        send_dynamic_email(user_email, "pos_agent_email.html", "DISPUTE RESOLUTION", text_to_send)


        template_id = settings.REFUNDED_CLAIMS_SMS_TEMPLATE
        place_holders = {
            "rrn": rrn,
            "amount": amount,
        }
        send_dynamic_sms(user_phone, template_id, place_holders)

        get_instance.notification_sent = True
        get_instance.save()

    
    return "DONE"


@shared_task
def measure_transactions_performance_rate():
    pass


@shared_task
def customer_service_issues_logs_automation():
    """
    This task checks reads a google sheet record.
    On intervals it looks through the sheet for logs
    that have not been treated since 24-hours.
    If such logs exist the management is notified via
    email and sms.
    """

    # Google Client Authentication
    google_json = settings.GOOGLE_SHEET_CREDENTIALS
    service_account_info = json.loads(google_json)
    credentials = service_account.Credentials.from_service_account_info(service_account_info)
    scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
    creds_with_scope = credentials.with_scopes(scope)
    client = gspread.authorize(creds_with_scope)

    if settings.ENVIRONMENT == "development":
        admin_email_list = ["<EMAIL>"]
    else:
        admin_email_list = [
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>"
                            ]

    time_now = datetime.now()
    google_sheet_link = settings.GOOGLE_SHEET_LINK

    spreadsheet = client.open_by_url(google_sheet_link) # Create spreadsheet instance
    worksheet = spreadsheet.get_worksheet(0) # Get the first worksheet
    record_data = worksheet.get_all_records() # Get all records in json format

    df = pd.DataFrame()
    google_sheet = df.from_dict(record_data) # Convert sheet to a dataframe

    over_due_logs_list = []

    for n, row in google_sheet.iterrows():
        customer_account_number = row["Customer Account Number"]
        card_number = row["Card Number"]
        stan = row["STAN"]
        terminal_id = row["Terminal ID"]
        amount = row["AMOUNT"]
        transaction_date = row["DATE"]
        date = row["Date Logged"]
        rrn = row["Retrieval Reference Number"]
        resolved = row["Resolved"]

        try:
            logged_time = datetime.strptime(date, "%m/%d/%Y %H:%M:%S")
            time_since_logged = time_now - logged_time
            number_of_seconds = time_since_logged.total_seconds()
            hours = number_of_seconds // 3600
            minutes = (number_of_seconds % 3600) // 60

            if hours >= 24 and resolved.lower() != "yes":
                over_due_logs_list.append((f"RRN: {rrn} - {int(hours)} hours {int(minutes)} minutes ago"))
        except Exception as e:
            print(f"-----------{e}----------------")
            pass

    # Send overdue logs email notification
    if over_due_logs_list:
        for email in admin_email_list:
            send_pos_agent_email(
                    email=email,
                    files="",
                    template="pos_agent_email.html",
                    email_subject="Overdue Logs",
                    message=f"""
                    <p>
                    The following issue logs are overdue for resolution. <br/>
                    {"<br />".join(over_due_logs_list)}
                    </p>
                    """
                    )

    return "DONE"