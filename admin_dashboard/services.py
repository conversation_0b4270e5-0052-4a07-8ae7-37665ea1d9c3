from accounts.models import (
    Transaction, AccountSystem, WalletSystem, 
    OtherCommissionsRecord, 
    SalesRepCommissionTable, 
    BillsPaymentDumpData)
from accounts.helpers.vfdbank_manager import VFDBank

from main.models import AvailableBalance

from admin_dashboard.models import (
    TerminalTypePrice, BunchOfBalance, 
    TerminalRequest, Stock, Zone, 
    SalesRep, StockRequest, Branch,
    AdminDashboardLogHistory, Dispute
    )
from main.models import AgentProfile
from kyc_app.models import BVNDetail, GuarantorDetail

from django.contrib.auth import get_user_model
User = get_user_model()

from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd
import os, json, calendar

from django.conf import settings
from django.utils import timezone

from django.db.models import Sum, Count, Q, Avg, F

from admin_dashboard.helpers.helpers import (
        send_agent_activity_email_task, 
        get_percentage_diff, 
        date_utility, Paginator, 
        filter_by_date_two
        )

class DateUtility:
    def __init__(self):
        # Date utilities
        filter_date = date_utility(datetime=datetime)
        self.previous_month_start = filter_date.get("previous_month_start")
        self.previous_month_end = filter_date.get("previous_month_end")
        self.start_of_all_users = filter_date.get("start_of_all_transactions")
        self.previous_year_current_month_start = filter_date.get("previous_year_current_month_start")
        self.previous_year_current_month_end = filter_date.get("previous_year_current_month_end")
        self.previous_year_current_previous_month = filter_date.get("previous_year_current_previous_month")
        self.year_end = filter_date.get("year_end")
        self.year_start = filter_date.get("year_start")
        self.init_start = filter_date.get("init_start")
        self.previous_year_current_following_month = filter_date.get("previous_year_current_following_month")
        self.start_of_all_transactions = filter_date.get("start_of_all_transactions")
        self.month_start = filter_date.get("month_start")
        self.today = filter_date.get("today")
        self.previous_day = filter_date.get("previous_day") 
        self.previous_year_end = filter_date.get("previous_year_end") 
        self.previous_year_start = filter_date.get("previous_year_start")
        self.week_start = filter_date.get("week_start")
        self.date_from = filter_date.get("date_from")
        self.date_today = filter_date.get("date_today")
        self.month_start = filter_date.get("month_start")
        self.month_ago = filter_date.get("month_ago")

        # Create an array of the month keys
        self.months_list1 = list(range(self.previous_year_current_month_start.month, 
                        (self.previous_year_current_month_start.month+13-self.previous_year_current_month_start.month)))
        self.months_list2 = list(range(1, 12-(12-self.previous_year_current_month_start.month)+1))
        self.months_list_names = [ 
                            f"{calendar.month_name[month][:3]} {self.previous_year_current_month_start.year}" 
                            for month in self.months_list1
                            ]
        self.months_list2_names = [f"{calendar.month_name[month][:3]} {self.today.year}" for month in self.months_list2]
        self.months_list_names = self.months_list_names + self.months_list2_names
        self.months_list = self.months_list1 + self.months_list2
        self.year_months_tuple_list = [(self.previous_year_current_month_start.year, month) for month in self.months_list1] + \
            [(self.today.year, month) for month in self.months_list2]


all_possible_transaction_list = [
                                "SEND_BANK_TRANSFER",
                                "FUND_BANK_TRANSFER", "FUND_PAYSTACK", "CARD_TRANSACTION_FUND",
                                "CARD_TRANSACTION_FUND_TRANSFER", "CARD_PURCHASE"
                                ]

inflow_list = ["FUND_BANK_TRANSFER", "FUND_PAYSTACK"]
cashout_transaction_type_list = ["CARD_TRANSACTION_FUND_TRANSFER", "CARD_TRANSACTION_FUND"]
send_money_transaction_type_list = ["SEND_BANK_TRANSFER", "CARD_PURCHASE"]


# Generate account statement for agent on request via provided email address
class AccountStatement:
    def request_statement(request, serializer):
        serializer = serializer(data=request.data)

        if serializer.is_valid(raise_exception=True):
            customer_email = serializer.data.get("customer_email")
            start_date = serializer.validated_data.get("start_date")
            end_date = serializer.validated_data.get("end_date")

        tenor = end_date - start_date

        customer = request.user
        account_details = AccountSystem.objects.filter(user=customer, account_type="COLLECTION").last()
        customer_wallet = WalletSystem.objects.filter(user=customer, wallet_type="COLLECTION").first()

        debit_transactions_list = ["SEND_BANK_TRANSFER", "SEND_BUDDY", "CARD_PURCHASE"]
        customer_transactions = Transaction.objects.filter(
                date_created__gte=start_date, 
                date_created__lte=end_date, 
                user=customer,
                status="SUCCESSFUL"
                ).filter(~Q(transaction_leg="COMMISSIONS"))

        customer_debit_transactions_qs = customer_transactions.filter(transaction_type__in=debit_transactions_list)
        customer_credit_transactions_qs = customer_transactions.filter(~Q(transaction_type__in=debit_transactions_list))

        total_debit_amount = customer_debit_transactions_qs.aggregate(Sum("amount"))["amount__sum"]
        total_credit_amount = customer_credit_transactions_qs.aggregate(Sum("amount"))["amount__sum"]

        customer_details = {
            "account_name": account_details.account_name if account_details else "",
            "address": customer.state,
            "bvn": customer.bvn_number,
            "tenor": tenor,
            "period": str(start_date) + ", " + str(end_date),
            "account_number": account_details.account_number if account_details else "",
            "available_balance": customer_wallet.available_balance if customer_wallet else "",
            "total_debit": total_debit_amount,
            "total_credit": total_credit_amount
            }

        transactions_list = []
        sn = 0
        for transaction in customer_transactions:
            sn+=1
            transaction_details = {
                "s/n": sn,
                "transaction_date": transaction.date_created,
                "value_date": transaction.date_created,
                "transaction_details": transaction.transaction_type,
                "debit": transaction.amount if transaction.transaction_type in ["SEND_BANK_TRANSFER", "SEND_BUDDY", "CARD_PURCHASE"] else "",
                "credit": transaction.amount if transaction.transaction_type not in ["SEND_BANK_TRANSFER", "SEND_BUDDY", "CARD_PURCHASE"] else "",
                "balance": transaction.balance_after
                }

            transactions_list.append(transaction_details)
        df = pd.DataFrame
        customer_info = df.from_dict([customer_details])
        
        statement = df.from_dict(transactions_list)
        statement['transaction_date'] = statement['transaction_date'].apply(lambda a: pd.to_datetime(a).to_datetime64())
        statement['value_date'] = statement['value_date'].apply(lambda a: pd.to_datetime(a).date())

        statement_path = os.path.join(settings.BASE_DIR, "media/statement/")
        statement_file = f"{statement_path}/{account_details.account_name}.xlsx"
        previous_statement_file = Path(statement_file)

        if previous_statement_file.is_file():
            os.remove(statement_file)

        statement_excel = statement.to_excel(f"{statement_path}/{account_details.account_name}.xlsx")
        statement_excel_path = os.path.abspath(f"{statement_path}{account_details.account_name}.xlsx")
        statement_path = Path(statement_excel_path)

        with open(statement_path, "rb") as email_file:
            file = email_file.read()
            email_file.close()

        # send email to agent via provided email address
        send_agent_activity_email_task(
            email=customer_email,
            file=file,
            file_name=f"{account_details.account_name}.xlsx",
            message="Dear customer, kindly find your bank statement attached below",
            email_subject="Liberty Account Statement"
            )


class UserChartData:
    def __init__(self):
        self.previous_year_current_month_start = DateUtility().previous_year_current_month_start
        self.months_list = DateUtility().months_list
        self.date_today = DateUtility().date_today
        self.start_of_all_users = DateUtility().start_of_all_users
        self.previous_month_end = DateUtility().previous_month_end  
        self.previous_year_current_month_end = DateUtility().previous_year_current_month_end
        self.previous_year_current_following_month = DateUtility().previous_year_current_following_month
        self.months_list_names = DateUtility().months_list_names
            
    def get_user_chart_data(self, request):
        user_role = request.user.role
        users_qs = User.objects.filter(role=user_role)
        
        # Graph Info
        users_per_month = users_qs.filter(
            date_joined__gt=self.previous_year_current_month_start
            ).values("date_joined__year", "date_joined__month"
            ).annotate(total_users = Count("date_joined__month")
            ).order_by("date_joined__year")
        users_past_year_list = [(obj["date_joined__year"], 
                                 obj["date_joined__month"], 
                                 obj["total_users"]) for obj 
                                 in users_per_month]

        # generate total number of users m-o-m from previous year
        month_number_of_users = []
        for i in self.months_list[:-1]:
            month_count = []
            for users in (
            users_past_year_list[:len(users_past_year_list)-1]
            if users_past_year_list[-1][1]==self.date_today.month
            else users_past_year_list
            ):
                if users[1] == i:
                    month_count.append(users[2])
                else:
                    month_count.append(0)
            month_number_of_users.append(sum(month_count))
            current_month_users = users_past_year_list[-1][2] if users_past_year_list[-1][1]==self.date_today.month else 0
        month_number_of_users.append(current_month_users) 


        # Total Users
        total_users_current = users_qs.filter(date_joined__range=[self.start_of_all_users, datetime.today()]).count()
        total_users_till_previous_month = users_qs.filter(date_joined__lte=self.previous_month_end).count()
        total_users_change = get_percentage_diff(previous = total_users_till_previous_month, current = total_users_current)

        previous_year_current_month_users_count = users_qs.filter(
                            date_joined__range=[self.previous_year_current_month_start, self.previous_year_current_month_end]
                            ).count()

        # new_users = [previous_year_current_month_users_count]
        new_users = []
        # if users_per_month:
        n = self.previous_year_current_following_month
        for month in range(1, 13):
            month_users = []
            if users_per_month:
                for user in users_per_month:
                    if user["date_joined__month"] == month:
                        month_users.append(user["total_users"])
                    else:
                        month_users.append(0)
                    month_sum = sum(month_users)
                new_users.append(month_sum)
            else:
                new_users.append(0)

        response = {
                "overview" : {
                    "total_users": total_users_current,
                    "percentage": str(total_users_change.get("percentage")) if total_users_change else 0.0,
                    "change": total_users_change.get("change"),
                    # "count": len(new_users)
                },
                "chart_labels": self.months_list_names,
                "new_chart_data": month_number_of_users,
                "chart_data": new_users,
            }
        return response


# Total number of terminals onboarded for each month
class TerminalsChart:
    def __init__(self):
        self.previous_year_current_month_start = DateUtility().previous_year_current_month_start
        self.months_list = DateUtility().months_list
        self.date_today = DateUtility().date_today
        self.start_of_all_users = DateUtility().start_of_all_users
        self.previous_month_end = DateUtility().previous_month_end  
        self.previous_year_current_month_end = DateUtility().previous_year_current_month_end
        self.previous_year_current_following_month = DateUtility().previous_year_current_following_month
        self.months_list_names = DateUtility().months_list_names
        self.init_start = DateUtility().init_start
        self.previous_month_end = DateUtility().previous_month_end
        self.previous_year_current_month_start = DateUtility().previous_year_current_month_start

    def get_terminals_chart(self, request):
        user_role = request.user.role
        terminals_overall_qs = User.objects.filter(Q(terminal_id__isnull=False) & ~Q(terminal_id=""), role=user_role)
        terminals_overall_count = terminals_overall_qs.count()
        terminals_overall_previous_month = terminals_overall_qs.filter(
                                            date_joined__lte = self.previous_month_end, 
                                            terminal_id__isnull=False).count()

        active_terminals = terminals_overall_qs.filter(terminal_status="ACTIVE").count()
        inactive_terminals = terminals_overall_qs.filter(terminal_status="INACTIVE").count()
        partially_inactive_terminals = terminals_overall_qs.filter(terminal_status="PARTIALLY_INACTIVE").count()

        terminals_monthly = terminals_overall_qs.filter(
                            date_joined__gte=self.previous_year_current_month_start
                            ).values("date_joined__year", "date_joined__month").annotate(
                            terminals_count = Count("terminal_id")).order_by("date_joined__year")
        terminals_past_year_list = [
                                     (obj["date_joined__year"], obj["date_joined__month"], 
                                     obj["terminals_count"]) for obj in terminals_monthly
                                    ]

        active_terminals_monthly = terminals_overall_qs.filter(
                                   date_joined__gte = self.previous_year_current_month_start, 
                                   terminal_status="ACTIVE").values(
                                   "date_joined__year", 
                                   "date_joined__month"
                                   ).annotate(
                                   terminals_count = Count("terminal_id")
                                   ).order_by("date_joined__year")
        
        active_terminals_past_year_list = [(obj["date_joined__year"], obj["date_joined__month"], 
                                        obj["terminals_count"]) for obj in active_terminals_monthly]

        inactive_terminals_monthly = terminals_overall_qs.filter(
                                     date_joined__gte=self.previous_year_current_month_start, 
                                     terminal_status="INACTIVE").values(
                                     "date_joined__year", "date_joined__month"
                                     ).annotate(terminals_count = Count("terminal_id")
                                    ).order_by("date_joined__year")
        
        inactive_terminals_past_year_list = [(obj["date_joined__year"], obj["date_joined__month"], 
                                            obj["terminals_count"]) for obj in inactive_terminals_monthly]

        try:
            terminals_percentage_active = (active_terminals / terminals_overall_count) * 100
            terminals_percentage_inactive = (inactive_terminals / terminals_overall_count) * 100
            terminals_percentage_partially_inactive = (partially_inactive_terminals / terminals_overall_count) * 100
        except:
            terminals_percentage_active = 0
            terminals_percentage_inactive = 0
            terminals_percentage_partially_inactive = 0

        terminals_change_vs_previous_month = get_percentage_diff(
                                            previous=terminals_overall_previous_month, 
                                            current=terminals_overall_count
                                            )

        terminals_counts = []
        for month in self.months_list[:-1]:
            month_terminals = []
            for terminal in (
                terminals_past_year_list[:len(terminals_past_year_list)-1]
                if terminals_past_year_list[-1][1]==self.date_today.month
                else terminals_past_year_list
                ):
                if terminal[1] == month:
                    month_terminals.append(terminal[2])
                else:
                    month_terminals.append(0)
            month_count_sum = sum(month_terminals)
            terminals_counts.append(month_count_sum)
        current_month_terminals = terminals_past_year_list[-1][2] if \
            terminals_past_year_list[-1][1]==self.date_today.month else 0
        terminals_counts.append(current_month_terminals)

        active_terminals_counts = []
        for month in self.months_list[:-1]:
            month_terminals = []
            for terminal in (
                active_terminals_past_year_list[:len(active_terminals_past_year_list)-1]
                if active_terminals_past_year_list[-1][1] == self.date_today.month
                else active_terminals_past_year_list
                ):
                if terminal[1] == month:
                    month_terminals.append(terminal[2])
                else:
                    pass
            month_count_sum = sum(month_terminals)
            active_terminals_counts.append(month_count_sum)
        current_month_active_terminals = active_terminals_past_year_list[-1][2] if \
            active_terminals_past_year_list[-1][1]==self.date_today.month else 0
        active_terminals_counts.append(current_month_active_terminals)

        inactive_terminals_counts = []
        for month in self.months_list[:-1]:
            month_terminals = []
            if inactive_terminals_past_year_list:
                for terminal in (
                    inactive_terminals_past_year_list[:len(inactive_terminals_past_year_list)-1]
                    if inactive_terminals_past_year_list[-1][1]==self.date_today.month
                    else inactive_terminals_past_year_list
                    ):
                    if terminal[1] == month:
                        month_terminals.append(terminal[2])
                    else:
                        pass
            month_count_sum = sum(month_terminals)
            inactive_terminals_counts.append(month_count_sum)
        current_month_inactive_terminals = inactive_terminals_past_year_list[-1][2] if \
                                           inactive_terminals_past_year_list and \
                                           inactive_terminals_past_year_list[-1][1]==self.date_today.month else 0
        inactive_terminals_counts.append(current_month_inactive_terminals)

        response = {
        # Terminals
            "terminals_overview": {
                "total": terminals_overall_count,
                "active": active_terminals,
                "inactive": inactive_terminals,
                "partially_inactive": partially_inactive_terminals,
                "active_terminal_percentage": terminals_percentage_active,
                "partially_inactive_terminal_percentage": terminals_percentage_partially_inactive,
                "inactive_terminals_percentage": terminals_percentage_inactive,
                "terminals_percentage_change": terminals_change_vs_previous_month.get("percentage"),
                "terminals_change": terminals_change_vs_previous_month.get("change")
                },

            "chart_labels": self.months_list_names,
            "terminals_chart_data": terminals_counts,
            "active_terminals_chart_data": active_terminals_counts,
            "inactive_terminals_chart_data": inactive_terminals_counts,
            "chart_labels": self.months_list_names
            }
        return response


class TransactionAmountChart:
    def __init__(self):
        self.previous_year_current_month_start = DateUtility().previous_year_current_month_start
        self.months_list = DateUtility().months_list
        self.date_today = DateUtility().date_today
        self.start_of_all_users = DateUtility().start_of_all_users
        self.previous_month_end = DateUtility().previous_month_end  
        self.previous_year_current_month_end = DateUtility().previous_year_current_month_end
        self.previous_year_current_following_month = DateUtility().previous_year_current_following_month
        self.months_list_names = DateUtility().months_list_names
        self.init_start = DateUtility().init_start
        self.previous_year_current_month_start = DateUtility().previous_year_current_month_start
    
    def get_transactions_chart(self, request):
        float_user = WalletSystem.get_float_user()
        user_role = request.user.role

        base_transactions = Transaction.objects.filter(~Q(user=float_user, transaction_leg__in = 
                            ["INTERNAL", "COMMISSIONS"])
                            ).filter(user__role=user_role, is_reversed=False)

        all_possible_transactions_qs = base_transactions.filter(
            transaction_type__in=all_possible_transaction_list, status="SUCCESSFUL"
            )

        total_transactions_amount = all_possible_transactions_qs.aggregate(Sum("amount"))["amount__sum"]
        total_transactions_til_previous_month  =  all_possible_transactions_qs.filter(
                                                  date_created__lte=self.previous_month_end
                                                  ).aggregate(Sum("amount"))["amount__sum"]

        total_transactions_amount_monthly_chart = all_possible_transactions_qs.filter(
                                                date_created__gte=self.previous_year_current_month_start
                                                ).values("date_created__year", "date_created__month"
                                                ).annotate(total_amount = Sum("amount")
                                                ).order_by("date_created__year")
        
        transaction_amount_past_year_list = [(obj["date_created__year"], obj["date_created__month"], 
                                            obj["total_amount"]) for obj in total_transactions_amount_monthly_chart]

        total_transactions_change = get_percentage_diff(
                                    previous=total_transactions_til_previous_month,
                                    current=total_transactions_amount
                                    )

        all_month_transactions = []
        for month in self.months_list[:-1]:
            month_transactions = []
            month_sum = 0
            if transaction_amount_past_year_list:
                for transaction in (
                    transaction_amount_past_year_list[:len(transaction_amount_past_year_list)-1]
                    if transaction_amount_past_year_list[-1][1]==self.date_today.month
                    else transaction_amount_past_year_list
                    ):
                    if transaction[1] == month:
                        month_transactions.append(transaction[2])
                    month_sum = sum(month_transactions)
            all_month_transactions.append(month_sum)
        current_month_transaction_amount = transaction_amount_past_year_list[-1][2] if \
        transaction_amount_past_year_list and transaction_amount_past_year_list[-1][1]==self.date_today.month else 0
        all_month_transactions.append(current_month_transaction_amount)

        response = {
                "overview": {
                "total_transactions": total_transactions_amount if total_transactions_amount else 0.00,
                "percentage_change": total_transactions_change.get("percentage"),
                "change": total_transactions_change.get("change")
                },
                "chart_labels": self.months_list_names,
                "chart_data": all_month_transactions,
            }
        return response


class TransactionCountChart:
    def __init__(self):
        self.previous_year_current_month_start = DateUtility().previous_year_current_month_start
        self.months_list = DateUtility().months_list
        self.date_today = DateUtility().date_today
        self.start_of_all_users = DateUtility().start_of_all_users
        self.previous_month_end = DateUtility().previous_month_end  
        self.previous_year_current_month_end = DateUtility().previous_year_current_month_end
        self.previous_year_current_following_month = DateUtility().previous_year_current_following_month
        self.months_list_names = DateUtility().months_list_names
        self.init_start = DateUtility().init_start
        self.previous_year_current_month_start = DateUtility().previous_year_current_month_start

    def get_transaction_count_chart(self, request):
        float_user = WalletSystem.get_float_user()
        user_role = request.user.role
        base_transactions = Transaction.objects.filter(
                            ~Q(user=float_user, transaction_leg__in=["INTERNAL", "COMMISSIONS"])
                            ).filter(user__role = user_role, is_reversed = False)

        all_possible_transactions_qs = base_transactions.filter(
                                        transaction_type__in=all_possible_transaction_list, 
                                        status="SUCCESSFUL"
                                        )
       
        total_transactions_count = all_possible_transactions_qs.count()
        total_transactions_til_previous_month = all_possible_transactions_qs.filter(
                                                date_created__lte=self.previous_month_end
                                                ).count()

        total_transactions_count_monthly_chart = all_possible_transactions_qs.filter(
                                                date_created__gte=self.previous_year_current_month_start, 
                                                transaction_type__in=all_possible_transaction_list
                                                ).values("date_created__year", 
                                                "date_created__month").annotate(total_count=Count("amount")
                                                ).order_by("date_created__year")
        transaction_count_past_year_list = [(obj["date_created__year"], obj["date_created__month"], 
                                            obj["total_count"]) for obj in total_transactions_count_monthly_chart]

        total_transactions_count_change = get_percentage_diff(
                                          previous=total_transactions_til_previous_month, 
                                          current=total_transactions_count
                                          )

        all_month_transactions_count = []
        for month in self.months_list[:-1]:
            month_transactions = []
            month_sum = 0
            if transaction_count_past_year_list:
                for transaction in (
                    transaction_count_past_year_list[:len(transaction_count_past_year_list)-1]
                    if transaction_count_past_year_list[-1][1]==self.date_today.month
                    else transaction_count_past_year_list
                    ):
                    if transaction[1] == month:
                        month_transactions.append(transaction[2])
                month_sum = sum(month_transactions)
            all_month_transactions_count.append(month_sum)
        current_month_transaction_count = transaction_count_past_year_list[-1][2] if \
        transaction_count_past_year_list and transaction_count_past_year_list[-1][1]==self.date_today.month else 0
        all_month_transactions_count.append(current_month_transaction_count)

        response = {
                "overview": {"total_transactions_count": total_transactions_count,
                "percentage_change": total_transactions_count_change.get("percentage"),
                "change": total_transactions_count_change.get("change")
                },
                "chart_labels": self.months_list_names,
                "chart_data": all_month_transactions_count
                }
        return response


class CommissionCharts:
    def __init__(self):
        self.previous_year_current_month_start = DateUtility().previous_year_current_month_start
        self.months_list = DateUtility().months_list
        self.date_today = DateUtility().date_today
        self.start_of_all_users = DateUtility().start_of_all_users
        self.previous_month_end = DateUtility().previous_month_end  
        self.previous_year_current_month_end = DateUtility().previous_year_current_month_end
        self.previous_year_current_following_month = DateUtility().previous_year_current_following_month
        self.months_list_names = DateUtility().months_list_names
        self.init_start = DateUtility().init_start
        self.year_months_tuple_list = DateUtility().year_months_tuple_list
    
    def get_commission_charts(self, request):
        user_role = request.user.role
        all_commissions_qs = OtherCommissionsRecord.objects.filter(Q(transaction_owner="AGENT") | 
                            Q(transaction_owner="LIBERTY"), agent__role=user_role)

        total_commission_amount = all_commissions_qs.values().aggregate(Sum("liberty_profit"))["liberty_profit__sum"]
        total_commision_amount_til_previous_month  =  all_commissions_qs.filter(date_created__lte=self.previous_month_end
                                                    ).aggregate(Sum("liberty_profit"))["liberty_profit__sum"]
        
        total_commisions_in_amount_monthly_chart = all_commissions_qs.filter(
                                                    date_created__gte=self.previous_year_current_month_start
                                                    ).values( "date_created__year", "date_created__month"
                                                    ).annotate(total_commission = Sum("liberty_profit")
                                                    ).order_by("date_created__year")
        
        total_commissions_in_past_year_list = [(obj["date_created__year"], obj["date_created__month"], obj["total_commission"]) for
                                                obj in total_commisions_in_amount_monthly_chart]

        total_cards_commisions_in_amount_monthly_chart = all_commissions_qs.filter(
                                                        date_created__gte=self.previous_year_current_month_start, 
                                                        transaction_type="CASH_OUT").values(
                                                        "date_created__year", "date_created__month"
                                                        ).annotate(total_commission = Sum("liberty_profit")
                                                        ).order_by("date_created__year")
        
        total_cards_commissions_in_past_year_list = [(obj["date_created__year"], obj["date_created__month"], 
                                                obj["total_commission"]) for obj in total_cards_commisions_in_amount_monthly_chart]

        total_commisions_out_amount_monthly_chart = all_commissions_qs.filter(date_created__gte=self.previous_year_current_month_start
                                                    ).values("date_created__year", "date_created__month"
                                                    ).annotate(commissions_out_amount = F("ro_cash_profit") + F("agent_cash_profit")
                                                    ).order_by("date_created__year")
        
        total_commissions_out_past_year_list = [(obj["date_created__year"], obj["date_created__month"], 
                                                obj["commissions_out_amount"]) for
                                                obj in total_commisions_out_amount_monthly_chart]
        past_year_commission_out_list = [
                                        (data["date_created__year"], data["date_created__month"], 
                                         data["commissions_out_amount"]) for 
                                        data in total_commisions_out_amount_monthly_chart
                                       ]
        
        total_cards_commisions_out_amount_monthly_chart = all_commissions_qs.filter(
                                                        date_created__gte=self.previous_year_current_month_start, 
                                                        transaction_type="CASH_OUT").values(
                                                        "date_created__year", 
                                                        "date_created__month").annotate(
                                                        total_commission = Sum("liberty_profit"), 
                                                        commissions_out_amount = F("ro_cash_profit") + F("agent_cash_profit")
                                                        ).order_by("date_created__year")
        
        total_cards_commissions_out_past_year_list = [(obj["date_created__year"], obj["date_created__month"], 
                                                obj["commissions_out_amount"]) for obj in total_cards_commisions_out_amount_monthly_chart]

        total_commission_out_amount = total_commisions_out_amount_monthly_chart.aggregate(total=Sum("commissions_out_amount"))["total"]

        total_commission_amount_change = get_percentage_diff(
                                        previous=total_commision_amount_til_previous_month,
                                        current=total_commission_amount
                                        )

        all_month_commission = []
        for month in self.months_list[:-1]:
            month_commission = []
            month_sum = 0
            if total_commissions_in_past_year_list:
                for commision in (
                    total_commissions_in_past_year_list[:len(total_commissions_in_past_year_list)-1]
                    if total_commissions_in_past_year_list[-1][1]==self.date_today.month
                    else total_commissions_in_past_year_list
                    ):
                    if commision[1] == month:
                        month_commission.append(commision[2])
                month_sum = sum(month_commission)
            all_month_commission.append(month_sum)
        current_month_commission_in = total_commissions_in_past_year_list[-1][2] if \
        total_commissions_in_past_year_list and total_commissions_in_past_year_list[-1][1]==self.date_today.month else 0
        all_month_commission.append(current_month_commission_in)
        
        past_year_commission_out_final_list = []
        for item in self.year_months_tuple_list:
            if item in [(tup[0], tup[1]) for tup in past_year_commission_out_list]:
                for trans in past_year_commission_out_list:
                    if item == (trans[0], trans[1]):
                        past_year_commission_out_final_list.append(trans)
                        break
            else:
                past_year_commission_out_final_list.append((0, 0, 0))
        commission_out_values_list = [trans[2] for trans in past_year_commission_out_final_list]


        all_month_cards_commission_in = []
        for month in self.months_list[:-1]:
            month_commission = []
            month_sum = 0
            if total_cards_commissions_in_past_year_list:
                for commision in (
                    total_cards_commissions_in_past_year_list[:len(total_cards_commissions_in_past_year_list)-1]
                    if total_cards_commissions_in_past_year_list[-1][1]==self.date_today.month
                    else total_cards_commissions_in_past_year_list
                    ):
                    if commision[2] and commision[1] == month:
                        month_commission.append(commision[2])
                month_sum = sum(month_commission)
            all_month_cards_commission_in.append(month_sum)
        current_month_cards_commission_in = total_cards_commissions_in_past_year_list[-1][2] if \
        total_cards_commissions_in_past_year_list and total_cards_commissions_in_past_year_list[-1][1]==self.date_today.month else 0
        all_month_cards_commission_in.append(current_month_cards_commission_in)

        all_month_cards_commission_out = []
        for month in self.months_list[:-1]:
            month_commission = []
            month_sum = 0
            if total_cards_commissions_out_past_year_list:
                for commision in (
                    total_cards_commissions_out_past_year_list[:len(total_cards_commissions_out_past_year_list)-1]
                    if total_cards_commissions_out_past_year_list[-1][1]==self.date_today.month
                    else total_cards_commissions_out_past_year_list):
                    if commision[2] and commision[1] == month:
                        month_commission.append(commision[2])
                month_sum = sum(month_commission)
            all_month_cards_commission_out.append(month_sum)
        current_month_cards_commission_out = total_cards_commissions_out_past_year_list[-1][2] if \
        total_cards_commissions_out_past_year_list and total_cards_commissions_out_past_year_list[-1][1]==self.date_today.month else 0
        all_month_cards_commission_out.append(current_month_cards_commission_out)

        response = {
                "overview": {
                "total_commission_amount": total_commission_amount if total_commission_amount else 0.00,
                "total_commission_out": total_commission_out_amount if total_commission_out_amount else 0.00,
                "percentage_change": total_commission_amount_change.get("percentage"),
                "change": total_commission_amount_change.get("change")
                },
                "chart_labels": self.months_list_names,
                "chart_data": all_month_commission,
                "commission_out_chart": commission_out_values_list,
                "cards_commission_in_chart": all_month_cards_commission_in,
                "cards_commission_out_chart": all_month_cards_commission_out
                }
        return response


class AverageCommissionChart:
    def __init__(self):
        self.previous_year_current_month_start = DateUtility().previous_year_current_month_start
        self.months_list = DateUtility().months_list
        self.date_today = DateUtility().date_today
        self.start_of_all_users = DateUtility().start_of_all_users
        self.previous_month_end = DateUtility().previous_month_end  
        self.previous_year_current_month_end = DateUtility().previous_year_current_month_end
        self.previous_year_current_following_month = DateUtility().previous_year_current_following_month
        self.months_list_names = DateUtility().months_list_names
        self.init_start = DateUtility().init_start
        self.year_months_tuple_list = DateUtility().year_months_tuple_list
    
    def get_average_commission_chart(self, request):
        user_role = request.user.role
        all_commissions_qs = OtherCommissionsRecord.objects.filter(Q(transaction_owner="AGENT") | 
                                Q(transaction_owner="LIBERTY"), agent__role=user_role)

        # Average Commission
        total_average_commissions_in_amount = all_commissions_qs.values().aggregate(
                                              Avg("liberty_profit"))["liberty_profit__avg"]

        total_average_commissions_out_amount = all_commissions_qs.values("agent_cash_profit", "ro_cash_profit"
                                                ).annotate(total_commission = Sum("liberty_profit"), 
                                                commissions_out_amount = F("ro_cash_profit") + F("agent_cash_profit")
                                                ).aggregate(Avg("commissions_out_amount")
                                                )["commissions_out_amount__avg"]

        total_average_commissions_in_amount_till_previous_month = all_commissions_qs.filter(
                                                                date_created__lte=self.previous_month_end
                                                                ).values("agent_cash_profit", "ro_cash_profit"
                                                                ).annotate(total_commission = Sum("liberty_profit"), 
                                                                commissions_out_amount = F("ro_cash_profit") + 
                                                                F("agent_cash_profit")
                                                                ).aggregate(Avg("liberty_profit"))["liberty_profit__avg"]

        total_average_commissions_out_amount_till_previous_month = all_commissions_qs.filter(
                                                                date_created__lte=self.previous_month_end
                                                                ).values("agent_cash_profit", "ro_cash_profit"
                                                                ).annotate(total_commission = Sum("liberty_profit"), 
                                                                commissions_out_amount = F("ro_cash_profit") + 
                                                                F("agent_cash_profit")).aggregate(Avg("commissions_out_amount")
                                                                )["commissions_out_amount__avg"]

        total_avg_commissions_in_amount_monthly_chart = all_commissions_qs.filter(
                                                        date_created__gte=self.previous_year_current_month_start
                                                        ).values("date_created__year", "date_created__month"
                                                        ).annotate(average_commission = Avg("liberty_profit")
                                                        ).order_by("date_created__year")
        
        total_avg_commissions_in_past_year_list = [(obj["date_created__year"], obj["date_created__month"], 
                                                obj["average_commission"]) for obj in total_avg_commissions_in_amount_monthly_chart]

        total_avg_cards_commissions_in_amount_monthly_chart = all_commissions_qs.filter(
                                                              date_created__gte=self.previous_year_current_month_start, 
                                                              transaction_type="CASH_OUT"
                                                                ).values("date_created__year", 
                                                                "date_created__month").annotate(
                                                                average_commission = Avg("liberty_profit")
                                                                ).order_by("date_created__year")
        
        total_avg_cards_commissions_in_past_year_list = [(obj["date_created__year"], obj["date_created__month"], 
                                                          obj["average_commission"]) for obj in total_avg_cards_commissions_in_amount_monthly_chart]

        total_average_commissions_out_amount_monthly_chart = all_commissions_qs.filter(
                                                            date_created__gte=self.previous_year_current_month_start).values(
                                                            "date_created__year", "date_created__month"
                                                            ).annotate(commissions_out_amount = 
                                                            (F("ro_cash_profit") + F("agent_cash_profit"))
                                                            ).annotate(total_out_average = Avg("commissions_out_amount")
                                                            ).values("date_created__year", "date_created__month", "total_out_average"
                                                            ).order_by("date_created__year")
        total_average_commissions_out_past_year_list = [
                                                        (obj["date_created__year"], obj["date_created__month"], 
                                                        obj["total_out_average"]) for obj in 
                                                        total_average_commissions_out_amount_monthly_chart]

        # total_commission_out_amount = total_commisions_out_amount_monthly_chart.aggregate(total=Sum("commissions_out_amount"))["total"]

        total_average_commissions_in_amount_change = get_percentage_diff(
                                        previous=total_average_commissions_in_amount_till_previous_month,
                                        current=total_average_commissions_in_amount
                                        )

        total_average_commissions_out_amount_change = get_percentage_diff(
                                        previous=total_average_commissions_out_amount_till_previous_month,
                                        current=total_average_commissions_out_amount
                                        )

        all_month_avg_commission_in = []
        for month in self.months_list[:-1]:
            month_commission = []
            month_sum = 0
            if total_avg_commissions_in_past_year_list:
                for commision in (
                    total_avg_commissions_in_past_year_list[:len(total_avg_commissions_in_past_year_list)-1]
                    if total_avg_commissions_in_past_year_list[-1][1]==self.date_today.month
                    else total_avg_commissions_in_past_year_list
                    ):
                    if commision[1] == month:
                        avg_commission = commision[2]
                        month_commission.append(avg_commission)
                month_sum = sum(month_commission)
            all_month_avg_commission_in.append(month_sum)
        current_avg_commission_in = total_avg_commissions_in_past_year_list[-1][2] if \
        total_avg_commissions_in_past_year_list and total_avg_commissions_in_past_year_list[-1][1]==self.date_today.month else 0
        all_month_avg_commission_in.append(current_avg_commission_in)

        all_month_avg_commission_out= []
        for month in self.months_list[:-1]:
            month_commission = []
            month_avg = 0
            if total_average_commissions_out_past_year_list:
                for commision in (
                    total_average_commissions_out_past_year_list[:len(total_average_commissions_out_past_year_list)-1]
                    if total_average_commissions_out_past_year_list[-1][1]==self.date_today.month
                    else total_average_commissions_out_past_year_list
                    ):
                    if commision[1] == month:
                        avg_commission = commision[2] if commision[2] else 0
                        month_commission.append(avg_commission)
                month_comm_len = len(month_commission)
                if month_comm_len >= 1:
                    month_avg = sum(month_commission) // len(month_commission)
                else:
                    month_avg = 0
            all_month_avg_commission_out.append(month_avg)
        current_month_avg_commission_out = total_average_commissions_out_past_year_list[-1][2] if \
        total_average_commissions_out_past_year_list and total_average_commissions_out_past_year_list[-1][1]==self.date_today.month else 0
        all_month_avg_commission_out.append(current_month_avg_commission_out)

        all_month_avg_cards_commission_in = []
        for month in self.months_list[:-1]:
            month_commission = []
            month_sum = 0
            if total_avg_cards_commissions_in_past_year_list:
                for commision in (
                    total_avg_cards_commissions_in_past_year_list[:len(total_avg_cards_commissions_in_past_year_list)-1]
                    if total_avg_cards_commissions_in_past_year_list[-1][1]==self.date_today.month
                    else total_avg_cards_commissions_in_past_year_list
                    ):
                    if commision[1] == month:
                        avg_commission = commision[2]
                        month_commission.append(avg_commission)
                month_sum = sum(month_commission)
            all_month_avg_cards_commission_in.append(month_sum)
        current_month_avg_cards_commission_in = total_avg_cards_commissions_in_past_year_list[-1][2] if total_avg_cards_commissions_in_past_year_list and total_avg_cards_commissions_in_past_year_list[-1][1]==date_today.month else 0
        all_month_avg_cards_commission_in.append(current_month_avg_cards_commission_in)


        response = {
                "overview": {
                "total_average_commission_amount": total_average_commissions_in_amount,
                "percentage_change": total_average_commissions_in_amount_change.get("percentage"),
                "change": total_average_commissions_in_amount_change.get("change")
                },

                "average_commissions_out_overview": {
                "total_average_commission_out_amount": total_average_commissions_out_amount,
                "percentage": total_average_commissions_out_amount_change.get("percentage"),
                "change": total_average_commissions_out_amount_change.get("change"),
                },
                "chart_labels": self.months_list_names,
                "chart_data": all_month_avg_commission_in,
                "average_commission_out_chart": all_month_avg_commission_out,
                "average_cards_commission_in_chart": all_month_avg_cards_commission_in
        }
        return response


# Dashboard Data
class DashData:
    def __init__(self):
        self.previous_year_current_month_start = DateUtility().previous_year_current_month_start
        self.months_list = DateUtility().months_list
        self.date_today = DateUtility().date_today
        self.start_of_all_users = DateUtility().start_of_all_users
        self.previous_month_end = DateUtility().previous_month_end  
        self.previous_year_current_month_end = DateUtility().previous_year_current_month_end
        self.previous_year_current_following_month = DateUtility().previous_year_current_following_month
        self.months_list_names = DateUtility().months_list_names
        self.init_start = DateUtility().init_start
        self.year_months_tuple_list = DateUtility().year_months_tuple_list
        self.year_start = DateUtility().year_start
        self.month_start = DateUtility().month_start
        self.week_start = DateUtility().week_start
        self.previous_year_start = DateUtility().previous_year_start
        self.previous_year_end = DateUtility().previous_year_end
        self.today = DateUtility().today
        self.previous_day = DateUtility().previous_day
    
    def get_dashboard_data(self, start_date, end_date, request):
        user_role = request.user.role
        float_user = WalletSystem.get_float_user()
        base_transactions = Transaction.objects.filter(~Q(user = float_user, is_reversed = True)
                            ).filter(user__role = user_role)

        try:
            float_balance = WalletSystem.objects.filter(
                            user=float_user, wallet_type="FLOAT"
                            ).last().available_balance
        except:
            float_balance = 0
        try:
            whisper_balance = WalletSystem.objects.filter(user=float_user, wallet_type="WHISPER").last().available_balance
        except:
            whisper_balance = 0

        # Wallets
        wallet_overall_qs = WalletSystem.objects.filter(
                            ~Q(user = float_user)).filter(user__role = user_role, wallet_type = "COLLECTION")
        wallets_overall = list(wallet_overall_qs.aggregate(Sum('available_balance')).values())[0]
        pos_wallets = list(wallet_overall_qs.filter(user__terminal_id__isnull=False).aggregate(Sum('available_balance')).values())[0]
        mobile_wallets = list(wallet_overall_qs.filter(user__terminal_id__isnull=True).aggregate(Sum('available_balance')).values())[0]

        wallet_number_of_months_count = WalletSystem.objects.values("date_created__month").count()
        overall_wallet_balance_per_month = (wallets_overall if wallets_overall else 0) / \
                                (wallet_number_of_months_count if wallet_number_of_months_count else 1)


        # INFLOWS
        overall_inflow_qs = base_transactions.filter(date_created__range=[start_date, end_date],
                                                    transaction_type__in=inflow_list, 
                                                    status="SUCCESSFUL")
        overall_inflow_today_qs = base_transactions.filter(date_created__date=datetime.now().date(),
                                                    transaction_type__in=inflow_list, 
                                                    status="SUCCESSFUL")
        overall_inflow_year_qs = base_transactions.filter(date_created__gte=self.year_start,
                                                    transaction_type__in=inflow_list, 
                                                    status="SUCCESSFUL")
        overall_inflow_month_qs = base_transactions.filter(date_created__gte=self.month_start,
                                                    transaction_type__in=inflow_list, 
                                                    status="SUCCESSFUL")
        overall_inflow_week_qs = base_transactions.filter(date_created__gte=self.week_start,
                                                    date_created__lte=self.today, 
                                                    transaction_type__in=inflow_list, 
                                                    status="SUCCESSFUL")
        overall_inflow_previous_year_qs = base_transactions.filter(date_created__gte=self.previous_year_start,
                                                            date_created__lte=self.previous_year_end, 
                                                            transaction_type__in=inflow_list, 
                                                            status="SUCCESSFUL")
        overall_inflow_previous_day_qs = base_transactions.filter(date_created__date=self.previous_day.date(),
                                                           transaction_type__in=inflow_list, 
                                                           status="SUCCESSFUL")

        overall_inflow_amount = overall_inflow_qs.aggregate(Sum("amount"))["amount__sum"]
        overall_inflow_amount_year = overall_inflow_year_qs.aggregate(Sum("amount"))["amount__sum"]
        overall_inflow_amount_today = overall_inflow_today_qs.aggregate(Sum("amount"))["amount__sum"]
        overall_inflow_amount_month = overall_inflow_month_qs.aggregate(Sum("amount"))["amount__sum"]
        overall_inflow_amount_week = overall_inflow_week_qs.aggregate(Sum("amount"))["amount__sum"]
        overall_inflow_amount_previous_year = overall_inflow_previous_year_qs.aggregate(Sum("amount"))["amount__sum"]
        overall_inflow_amount_previous_day = overall_inflow_previous_day_qs.aggregate(Sum("amount"))["amount__sum"]

        # Total Users
        total_users_qs = User.objects.filter(date_joined__range=[start_date, end_date], role=user_role)
        total_users_current = total_users_qs.count()

        total_users_previous_qs = total_users_qs.filter(date_joined__lte = self.previous_month_end)
        total_users_previous = total_users_previous_qs.count()

        total_users_change = get_percentage_diff(previous = total_users_previous, current = total_users_current)

        total_users_till_last_month_end = total_users_qs.filter(date_joined__range=[start_date, self.previous_month_end]).count()
        # total_new_users_till_last_month_end = total_users_till_last_month_end - total_users_previous

        # current_month_new_users = total_users_current - total_users_till_last_month_end
        current_month_new_users = total_users_qs.filter(date_joined__gte=self.month_start).count()
        new_users_change = get_percentage_diff(previous=total_users_previous, current=current_month_new_users)

        # Total Sales POS
        total_sales_pos_amount_current = TerminalTypePrice.objects.filter(date_created__range = 
                                        [start_date, end_date]).aggregate(Sum("price"))["price__sum"]
        total_sales_pos_amount_previous_month = TerminalTypePrice.objects.filter(date_created__lte=self.previous_month_end
                                                ).aggregate(Sum("price"))
        total_pos_sales_change = get_percentage_diff(total_sales_pos_amount_previous_month,total_sales_pos_amount_current)

        # KYC Success Rate
        total_kyc_users = total_users_current
        complete_kyc_users = total_users_qs.filter(kyc_level__gte="2").count()
        incomplete_kyc_users = total_users_qs.filter(~Q(kyc_level__gte="2")).count()

        complete_kyc_users_change = float("{:.1f}".format(complete_kyc_users / total_kyc_users * 100))

        terminals_overall_qs = User.objects.filter(Q(date_joined__range=[start_date, end_date], \
                               terminal_id__isnull=False, role=user_role) & ~Q(terminal_id=""))
        terminals_overall = terminals_overall_qs.count()
        active_terminals = terminals_overall_qs.filter(terminal_status="ACTIVE").count()
        partially_inactive_terminals = terminals_overall_qs.filter(terminal_status="PARTIALLY_INACTIVE").count()
        inactive_terminals = terminals_overall_qs.filter(terminal_status="INACTIVE").count()
        lotto_terminals = terminals_overall_qs.filter(type_of_user="LOTTO_AGENT").count()
        agency_banking_terminals = terminals_overall - (lotto_terminals if lotto_terminals else 0)

        if terminals_overall:
            terminals_percentage_active = active_terminals/terminals_overall * 100
            terminals_percentage_inactive = inactive_terminals/terminals_overall * 100
            terminals_percentage_partially_inactive = partially_inactive_terminals/terminals_overall * 100
        else:
            terminals_percentage_active = 0
            terminals_percentage_inactive = 0
            terminals_percentage_partially_inactive = 0

        midnight_float_account_balance = "-"
        midnight_overall_wallet_balance = "-"
        midnight_pos_wallet_balance = "-"
        midnight_mobile_wallet_balance = "-"

        for balance in BunchOfBalance.objects.all():
            if balance.balance_name == "midnight_float_account_balance":
                midnight_float_account_balance = balance.amount

            elif balance.balance_name == "midnight_overall_wallet_balance":
                midnight_overall_wallet_balance = balance.amount

            elif balance.balance_name == "midnight_pos_wallet_balance":
                midnight_pos_wallet_balance = balance.amount

            elif balance.balance_name == "midnight_mobile_wallet_balance":
                midnight_mobile_wallet_balance = balance.amount

        # Average Wallet Balance
        average_wallet_balance = (wallets_overall if wallets_overall else 0) / (total_users_current if total_users_current else 1)
        average_daily_inflow = (overall_inflow_amount_today if overall_inflow_amount_today else 0) / \
                                (total_users_current if total_users_current else 1)

        response = {
            "dashData": {
                # Done
                "float_balance": float_balance,
                "midnight_float_account_balance": midnight_float_account_balance,
                # Done
                "whisper_balance": whisper_balance,
                # Not Done ---> Done
                "totalSalesPOS": {
                    "total": total_sales_pos_amount_current if total_sales_pos_amount_current is not None else 0,
                    "percentage": total_pos_sales_change.get("percentage"),
                    "change": total_pos_sales_change.get("change")
                },

                "totalTerminal":{
                    "total": terminals_overall,
                    "active_terminals": active_terminals,
                    "inactive_terminals": inactive_terminals,
                    "partially_inactive_terminals": partially_inactive_terminals,
                    "active_terminals_percentage": terminals_percentage_active,
                    "inactive_terminals_percentage": terminals_percentage_inactive,
                    "partially_inactive_terminals_percentage": terminals_percentage_partially_inactive,
                    "agency_banking_terminals": agency_banking_terminals,
                    "lotto_agents_terminals": lotto_terminals
                },

                # Done
                "SmsNotificationWalletBalance": {
                    "total": whisper_balance
                },

                # Done
                "overall_wallets_balance": {
                    "overall": wallets_overall if wallets_overall is not None else 0,
                    "pos": pos_wallets if pos_wallets is not None else 0,
                    "mobile": mobile_wallets if mobile_wallets is not None else 0,

                    "midnight_overall_wallet_balance": midnight_overall_wallet_balance,
                    "midnight_pos_wallet_balance": midnight_pos_wallet_balance,
                    "midnight_mobile_wallet_balance": midnight_mobile_wallet_balance,
                    "average_wallet_balance": average_wallet_balance,
                    "average_wallet_balance_per_month": overall_wallet_balance_per_month
                },
                 # Done
                "users": {
                    "total": total_users_current,
                    "total_new_customers": current_month_new_users,
                    "new_users_percentage": str(new_users_change.get("percentage")) if \
                                            type(new_users_change.get("percentage")) is not float else 0.0,
                    "new_users_change": str(new_users_change.get("change")),
                    "percentage": str(total_users_change.get("percentage")),
                    "change": total_users_change.get("change")
                },

                "totalInflow": {
                    "overall_count": overall_inflow_year_qs.count() + overall_inflow_previous_year_qs.count(),
                    "today": overall_inflow_amount_today if overall_inflow_amount_today else 0.0,
                    "this_month": overall_inflow_amount_month if overall_inflow_amount_month else 0.0,
                    "this_year": overall_inflow_amount_year if overall_inflow_amount_year else 0.0,
                    "week": overall_inflow_amount_week if overall_inflow_amount_week else 0.0,
                    "previous_year": overall_inflow_amount_previous_year if overall_inflow_amount_previous_year else 0.0,
                    "yesterday": overall_inflow_amount_previous_day if overall_inflow_amount_previous_day else 0.0,
                    "average_daily_inflow": average_daily_inflow
                },

                # Not Done ---> Done
                "kycSuccessRate": {
                    "total": total_kyc_users,
                    "completed": complete_kyc_users,
                    "incomplete": incomplete_kyc_users,
                    "percentage_complete": complete_kyc_users_change
                },
            },
        }

        return response


class TransactionComparatives:
    def __init__(self, request):
        self.previous_year_current_month_start = DateUtility().previous_year_current_month_start
        self.months_list = DateUtility().months_list
        self.date_today = DateUtility().date_today
        self.start_of_all_users = DateUtility().start_of_all_users
        self.previous_month_end = DateUtility().previous_month_end  
        self.previous_year_current_month_end = DateUtility().previous_year_current_month_end
        self.previous_year_current_following_month = DateUtility().previous_year_current_following_month
        self.months_list_names = DateUtility().months_list_names
        self.init_start = DateUtility().init_start
        self.year_months_tuple_list = DateUtility().year_months_tuple_list
        self.year_start = DateUtility().year_start
        self.month_start = DateUtility().month_start
        self.week_start = DateUtility().week_start
        self.previous_year_start = DateUtility().previous_year_start
        self.previous_year_end = DateUtility().previous_year_end
        self.today = DateUtility().today
        self.previous_day = DateUtility().previous_day
        self.previous_month_start = DateUtility().previous_month_start

        filter_date = filter_by_date_two(user= User, request = request, datetime = datetime)
        self.start_date = filter_date.get("start_date")
        self.end_date = filter_date.get("end_date")

        self.lower_amount = request.GET.get("lower_amount")
        self.upper_amount = request.GET.get("upper_amount")

        self.date_filter = {
            "date_created__gte": self.start_date,
            "date_created__lte": self.end_date
            }
        
        self.date_filter_two = {
            "date_joined__gte": self.start_date,
            "date_joined__lte": self.end_date
            }
        
        if not self.lower_amount:
            self.lower_amount = 0
        if not self.upper_amount:
            self.upper_amount = 0
        
        self.users_qs = User.objects.filter(**self.date_filter_two)
        self.agents_qs = self.users_qs.filter(~Q(terminal_id__isnull = True, terminal_id = ""))

        float_user = WalletSystem.get_float_user()
        user_role = request.user.role
        
        self.transactions_qs = Transaction.objects.filter(
                                            ~Q(user = float_user, 
                                            transaction_leg__in = ["INTERNAL", "COMMISSIONS"])
                                            ).filter(
                                            Q(user__role = user_role, is_reversed = False, 
                                            transaction_type__in = all_possible_transaction_list,
                                            **self.date_filter
                                            ))
        
        self.base_successful_transactions_qs = self.transactions_qs.filter(status = "SUCCESSFUL")
        self.base_failed_transactions_qs = self.transactions_qs.filter(status = "FAILED")

        self.bills_payment_qs = BillsPaymentDumpData.objects.filter(~Q(user = float_user)).filter(
                                status = "SUCCESSFUL", biller__endswith = "DATA", user__role = user_role
                                )
        
        first_transaction_date = self.transactions_qs.first().date_created
        self.number_of_days_since_first_transaction = (timezone.now().date() - first_transaction_date.date()).days


    def get_transaction_comparatives(self):
        # Total Transaction Amount
        total_transaction_amount = list(self.base_successful_transactions_qs.aggregate(Sum("amount")).values())[0]
        total_transaction_amount_previous_month_qs = self.base_successful_transactions_qs.filter(
                                                date_created__gte = self.previous_month_start,
                                                date_created__lte = self.previous_month_end
                                                )
        total_transaction_amount_previous_month = list(
                                                    total_transaction_amount_previous_month_qs.aggregate(
                                                    Sum("amount")).values())[0]

        # Total Agent Amount
        total_agent_transaction_amount_qs = self.base_successful_transactions_qs.filter(
                                user__terminal_id__isnull = False
                                ).filter(~Q(user__terminal_id=""))
        total_agent_transaction_amount = list(total_agent_transaction_amount_qs.aggregate(Sum("amount")).values())[0]

        total_agent_transaction_amount_previous_month_qs = total_transaction_amount_previous_month_qs.filter(
                                                            user__terminal_id__isnull = False)
        total_agent_transaction_amount_previous_month = list(total_agent_transaction_amount_previous_month_qs.aggregate(
                                                        Sum("amount")).values())[0]

        # Total Mobile Amount
        total_transaction_amount_mobile = (
                                         (total_transaction_amount if total_transaction_amount else 0) -
                                         (total_agent_transaction_amount if total_agent_transaction_amount else 1)
                                         )

        total_transaction_amount_change = get_percentage_diff(
                                            previous = total_transaction_amount_previous_month,
                                            current = total_transaction_amount
                                            )

        total_transaction_count = self.base_successful_transactions_qs.count()
        total_transaction_count_pos = total_agent_transaction_amount_qs.count()
        total_transaction_count_mobile = total_transaction_count - total_transaction_count_pos
        total_transaction_count_previous_month = total_transaction_amount_previous_month_qs.count()

        total_transaction_count_change = get_percentage_diff(
                                        previous = total_transaction_count_previous_month,
                                        current = total_transaction_count
                                        )

        # AverageTransactionCOuntPerAgent
        agents_qs = self.agents_qs.filter(Q(**self.date_filter_two, terminal_id__isnull = False) & ~Q(terminal_id = ""))
        total_number_of_agents = agents_qs.count()
        total_number_of_agents_till_previous_month = agents_qs.filter(date_joined__lte = self.previous_month_end).count()

        average_transaction_count_per_agent = (
                                               total_transaction_count_pos / 
                                               (total_number_of_agents if 
                                               total_number_of_agents else 1
                                               ))
        
        average_transaction_count_previous_month = (total_transaction_count_previous_month / 
                                                    (
                                                    total_number_of_agents_till_previous_month if 
                                                    total_number_of_agents_till_previous_month else 1
                                                    ))
        

        average_transaction_per_agent_change = get_percentage_diff(
                                            previous = average_transaction_count_previous_month, 
                                            current = average_transaction_count_per_agent
                                            )

        # Average Agent Transactions
        average_agent_transaction_amount = ((total_agent_transaction_amount if total_agent_transaction_amount else 0) / 
                                            (total_number_of_agents if total_number_of_agents else 1))
        
        average_agent_transaction_amount_previous = (
                                                    (total_agent_transaction_amount_previous_month if 
                                                     total_agent_transaction_amount_previous_month else 0) / 
                                                    (total_number_of_agents_till_previous_month if 
                                                    total_number_of_agents_till_previous_month else 1)
                                                    )

        average_transaction_amount_per_agent_change = get_percentage_diff(
                                                    current = average_agent_transaction_amount,
                                                    previous = average_agent_transaction_amount_previous
                                                    )

        # Average Workdays
        agent_work_days_qs = self.base_successful_transactions_qs.filter(
                            terminal_id__isnull = False).values(
                            "date_created__date").distinct("date_created__date"
                            ).filter(amount__isnull = False)

        average_agent_work_days = agent_work_days_qs.count() / (total_number_of_agents if total_number_of_agents else 1)

        agent_work_days_previous_month = agent_work_days_qs.filter(
                                        date_created__lte = self.previous_month_end
                                        ).count()

        average_agent_work_days_previous_month = (
                                                agent_work_days_previous_month / 
                                                (total_number_of_agents_till_previous_month if 
                                                total_number_of_agents_till_previous_month else 1)
                                                )

        agent_average_work_days_change = get_percentage_diff(
                                        previous = average_agent_work_days_previous_month, 
                                        current = average_agent_work_days
                                        )
        
        failed_cashout_transactions_qs = self.base_failed_transactions_qs.filter(transaction_type__in = cashout_transaction_type_list)
        failed_sendmoney_transactions_qs = self.base_failed_transactions_qs.filter(transaction_type__in = send_money_transaction_type_list)

        failed_cashout_transactions_amount = list(failed_cashout_transactions_qs.aggregate(Sum("amount")).values())[0]
        failed_sendmoney_transactions_amount = list(failed_sendmoney_transactions_qs.aggregate(Sum("amount")).values())[0]

        failed_cashout_transactions_count = failed_cashout_transactions_qs.count()
        failed_sendmoney_transactions_count = failed_sendmoney_transactions_qs.count()

        response = {
                # Total Transactions
                "totalTransactionsAmount": {
                    "total":total_transaction_amount if total_transaction_amount else 0.00,
                    "terminal_transactions": total_agent_transaction_amount if total_agent_transaction_amount else 0.00,
                    "mobile_transactons": total_transaction_amount_mobile if total_transaction_amount_mobile is not None else 0,
                    "percentage": str(total_transaction_amount_change.get("percentage")),
                    "change": total_transaction_amount_change.get("change")
                },

                # Total Transaction Count
                "totalTransactionsCount": {
                    "total":total_transaction_count,
                    "terminal_transactions": total_transaction_count_pos,
                    "mobile_transactions": total_transaction_count_mobile,
                    "percentage": str(total_transaction_count_change.get("percentage")),
                    "change": total_transaction_count_change.get("change")
                },
                # Average Agent Transaction
                "averageAgentTransactions": {
                    "total": average_agent_transaction_amount,
                    "percentage": str(average_transaction_amount_per_agent_change.get("percentage")),
                    "change": average_transaction_amount_per_agent_change.get("change")
                },
                # Done
                "averageWorkDays": {
                    "total": average_agent_work_days,
                    "percentage": str(agent_average_work_days_change.get("percentage")),
                    "change": agent_average_work_days_change.get("change")
                },

                # Done
                "averageTransactionCountPerAgent": {
                    "total": average_transaction_count_per_agent,
                    "percentage": str(average_transaction_per_agent_change.get("percentage")),
                    "change": average_transaction_per_agent_change.get("change")
                },

                "failed_transactions": {
                            "pos_withdrawal": failed_cashout_transactions_amount if failed_cashout_transactions_amount else 0,
                            "send_money": failed_sendmoney_transactions_amount if failed_sendmoney_transactions_amount else 0,
                            "pos_withdrawal_count": failed_cashout_transactions_count if failed_cashout_transactions_count else 0,
                            "send_money_count": failed_sendmoney_transactions_count if failed_sendmoney_transactions_count else 0
                        }
                }
        return response

    
    def get_cashout_comparatives(self):
        # CASHOUT / WITDRAWALS
        cashout_qs = self.base_successful_transactions_qs.filter(transaction_type__in = cashout_transaction_type_list)
        cashout_today_qs = cashout_qs.filter(date_created__date = datetime.now().date())
        cashout_month_qs = cashout_qs.filter(date_created__gte = self.month_start)

        cashout_year_qs = cashout_qs.filter(date_created__gte = self.year_start)
        cashout_week_qs = cashout_qs.filter(date_created__gte=self.week_start)
        cashout_prev_year_qs = cashout_qs.filter(date_created__gte = self.previous_year_start, 
                                                 date_created__lte = self.previous_year_end)
        cashout_prev_day_qs = cashout_qs.filter(date_created__date = self.previous_day.date())

        # Cashout Amount
        cashout_amount_total = cashout_qs.aggregate(Sum("amount"))["amount__sum"]
        cashout_amount_today = cashout_today_qs.aggregate(Sum("amount"))["amount__sum"]
        cashout_amount_month = cashout_month_qs.aggregate(Sum("amount"))["amount__sum"]
        cashout_amount_year = cashout_year_qs.aggregate(Sum("amount"))["amount__sum"]
        cashout_amount_prev_year = cashout_prev_year_qs.aggregate(Sum("amount"))["amount__sum"]
        cashout_amount_week = cashout_week_qs.aggregate(Sum("amount"))["amount__sum"]
        cashout_amount_yesterday = cashout_prev_day_qs.aggregate(Sum("amount"))["amount__sum"]
        
        average_daily_cashout = ((cashout_amount_total if cashout_amount_total else 0) /
                                (self.number_of_days_since_first_transaction if 
                                 self.number_of_days_since_first_transaction else 1))

        data = {"cashout_data": {
                "total_amount": cashout_amount_total,
                "today": cashout_amount_today if cashout_amount_today else 0.0,
                "month": cashout_amount_month if cashout_amount_month else 0.0,
                "year": cashout_amount_year if cashout_amount_year else 0.0,
                "week": cashout_amount_week if cashout_amount_week else 0.0,
                "previous_year": cashout_amount_prev_year if cashout_amount_prev_year else 0.0,
                "yesterday":  cashout_amount_yesterday if cashout_amount_yesterday else 0.0,
                "average_daily_cashout": average_daily_cashout
                }
              }
        return data
    
    def get_comparatives_top10_transactions(self):
        # TOP10 TRANSACTIONS RANGE
        top10_transactions_qs = self.base_successful_transactions_qs.filter()

        top10_1k_5k = top10_transactions_qs.filter(amount__gte=1000, amount__lt=5000).count()
        top10_5k_10k = top10_transactions_qs.filter(amount__gte=5000, amount__lt=10000).count()
        top10_10k_20k = top10_transactions_qs.filter(amount__gte=10000, amount__lt=20000).count()
        top10_20k_50k = top10_transactions_qs.filter(amount__gte=20000, amount__lt=50000).count()
        top10_50k_100k = top10_transactions_qs.filter(amount__gte=50000, amount__lt=100000).count()
        top10_100k_150k = top10_transactions_qs.filter(amount__gte=100000, amount__lt=150000).count()
        top10_150k_200k = top10_transactions_qs.filter(amount__gte=150000, amount__lt=200000).count()
        top10_200k_250k = top10_transactions_qs.filter(amount__gte=200000, amount__lt=250000).count()
        top10_250k_500k = top10_transactions_qs.filter(amount__gte=250000, amount__lt=500000).count()
        top10_500k_1m = top10_transactions_qs.filter(amount__gte=500000, amount__lt=1000000).count()
        top10_lower_upper_amt = top10_transactions_qs.filter(amount__gte = self.lower_amount, amount__lte = self.upper_amount).count()


        data = {
            "top10_range": {
                        "top10_1k_5k": top10_1k_5k,
                        "top10_5k_10k": top10_5k_10k,
                        "top10_10k_20k": top10_10k_20k,
                        "top10_20k_50k": top10_20k_50k,
                        "top10_50k_100k": top10_50k_100k,
                        "top10_100k_150k": top10_100k_150k,
                        "top10_150k_200k": top10_150k_200k,
                        "top10_200k_250k": top10_200k_250k,
                        "top10_250k_500k": top10_250k_500k,
                        "top10_500k_1m": top10_500k_1m,
                        "top10_lower_upper_amt": top10_lower_upper_amt if top10_lower_upper_amt else ""
                        }
                    }
        return data
    

    def get_transaction_comparatives_chart(self):
        # Comparatives
        pos_withdrawals_qs = self.base_successful_transactions_qs.filter(
                            transaction_type__in = cashout_transaction_type_list
                            )
        
        pos_withdrawals = list(pos_withdrawals_qs.aggregate(Sum('amount')).values())[0]
        pos_withdrawals_count = pos_withdrawals_qs.count()

        send_money_trns_qs = self.base_successful_transactions_qs.filter(
                            transaction_type__in = send_money_transaction_type_list, 
                            )

        send_money_trns = send_money_trns_qs.aggregate(Sum('amount'))["amount__sum"]
        send_money_count = send_money_trns_qs.count()

        # AIRTIME - DATA - UTILITIES
        airtime_trns_amount = list(
                                self.base_successful_transactions_qs.filter(
                                transaction_type = "AIRTIME_PIN"
                                ).aggregate(Sum("amount")).values())[0]

        bills_trns_amount = list(
                            self.base_successful_transactions_qs.filter(
                            transaction_type = "BILLS_AND_PAYMENT"
                            ).aggregate(Sum("amount")).values())[0]
        
        data_trns_amount = list(self.bills_payment_qs.aggregate(Sum("amount")).values())[0]

        transaction_comparatives_list = [
                                        pos_withdrawals if pos_withdrawals else 0,
                                        send_money_trns if send_money_trns else 0,
                                        airtime_trns_amount if airtime_trns_amount else 0,
                                        data_trns_amount if data_trns_amount else 0,
                                        bills_trns_amount if bills_trns_amount else 0
                                        ]
        data = {
                "transactionComparatives": {
                    "pos_withdrawals": pos_withdrawals if pos_withdrawals else 0,
                    "send_money": send_money_trns if send_money_trns is not None else 0,
                    "pos_withdrawals_count": pos_withdrawals_count if pos_withdrawals_count is not None else 0,
                    "send_money_count": send_money_count if send_money_count is not None else 0,
                    "airtime_pin": airtime_trns_amount if airtime_trns_amount is not None else 0,
                    "data_bundle": data_trns_amount if data_trns_amount else 0,
                    "utilities": bills_trns_amount if bills_trns_amount is not None else 0
                },
                "chart_data": transaction_comparatives_list
            }

        return data
    
    
    def get_transaction_comparatives_transfer_data(self):
        # TOTAL TRANSFERS
        transfer_qs = self.base_successful_transactions_qs.filter(transaction_type__in = send_money_transaction_type_list)
                                                    
        transfers_today_qs = transfer_qs.filter(date_created__date = datetime.now().date())
        transfers_month_qs = transfer_qs.filter(date_created__gte = self.month_start)
        transfers_year_qs = transfer_qs.filter(date_created__gte = self.year_start)
        transfers_week_qs = transfer_qs.filter(date_created__gte = self.week_start)
        transfers_previous_year_qs = transfer_qs.filter(date_created__gte = self.previous_year_start, 
                                                        date_created__lte = self.previous_year_end)
        transfers_previous_day_qs = transfer_qs.filter(date_created__date = self.previous_day.date())

        total_transfers_amount = list(transfer_qs.aggregate(Sum("amount")).values())[0]
        transfers_amount_today = list(transfers_today_qs.aggregate(Sum("amount")).values())[0]
        transfers_amount_month = list(transfers_month_qs.aggregate(Sum("amount")).values())[0]
        transfers_amount_year = list(transfers_year_qs.aggregate(Sum("amount")).values())[0]
        transfers_amount_week = list(transfers_week_qs.aggregate(Sum("amount")).values())[0]
        transfers_amount_previous_year = list(transfers_previous_year_qs.aggregate(Sum("amount")).values())[0]
        transfers_amount_previous_day = list(transfers_previous_day_qs.aggregate(Sum("amount")).values())[0]

        average_daily_transfer = (
                                (transfers_amount_today if transfers_amount_today else 0) / 
                                (self.number_of_days_since_first_transaction if 
                                 self.number_of_days_since_first_transaction else 1)
                                )

        data = {
                "total_amount": total_transfers_amount if total_transfers_amount else 0.00,
                "today": transfers_amount_today if transfers_amount_today else 0.0,
                "month": transfers_amount_month if transfers_amount_month else 0.0,
                "year": transfers_amount_year if transfers_amount_year else 0.0,
                "week": transfers_amount_week if transfers_amount_week else 0.0,
                "previous_year": transfers_amount_previous_year if transfers_amount_previous_year else 0.0,
                "yesterday": transfers_amount_previous_day if transfers_amount_previous_day else 0.0,
                "average_daily_transfer": average_daily_transfer
                }
        return data


class WalletTransactionDetails:
    def get_wallet_transaction_details(request, start_date, end_date):
        float_user = WalletSystem.get_float_user()
        user_role = request.user.role

        base_transaction_qs = Transaction.objects.filter(transaction_type__in = all_possible_transaction_list, 
                            user__role = user_role).filter(~Q(user = float_user))
        transactions = base_transaction_qs.filter(date_created__range=[start_date, end_date]).order_by("-date_created")
        transactions = Paginator.paginate(request=request, queryset=transactions)

        data = []
        for transaction in list(transactions):
            transaction_detail = {
                "id": transaction.id,
                "description": transaction.transaction_type,
                "amount": transaction.amount,
                "transaction_method": transaction.transaction_mode if transaction.transaction_mode else "",
                "reference_id": transaction.liberty_reference,
                "date_time": transaction.date_created,
                "receiver": transaction.beneficiary_nuban if transaction.beneficiary_nuban else "",
                "status": transaction.status
            }
            data.append(transaction_detail)
        response = {
            "transactions": data,
            "count": len(data)
            }
        return response

class DashDataTwo:
    def get_dashboard_data_two(start_date, end_date, request):
        user_role = request.user.role
        float_user = WalletSystem.get_float_user()


        float_balance = VFDBank.get_vfd_float_balance()

        if float_balance is None:
            last_balance = AvailableBalance.objects.filter(is_active=True).last()

            if last_balance is not None:
                float_balance = last_balance.float_balance
            else:
                float_balance = 0

        base_transactions = Transaction.objects.filter(~Q(user=float_user)).filter(user__role=user_role)

        overall_total_commissions_in_qs = base_transactions.filter(date_created__range=[start_date, end_date]
                                          ).filter(liberty_commission__isnull = False)

        total_commissions_in_amt = list(overall_total_commissions_in_qs.aggregate(Sum('liberty_commission')).values())[0]

        wallet_overall_qs = WalletSystem.objects.filter(~Q(user = float_user)
                            ).filter(wallet_type = "COLLECTION", user__role = user_role)
        wallets_overall = list(wallet_overall_qs.aggregate(Sum('available_balance')).values())[0]
        pos_wallets_overall = list(wallet_overall_qs.filter(user__terminal_id__isnull=False
                            ).aggregate(Sum('available_balance')).values())[0]
        mobile_wallets_overall = list(wallet_overall_qs.filter(user__terminal_id__isnull=True
                                ).aggregate(Sum('available_balance')).values())[0]

        total_transaction_qs = base_transactions.filter(date_created__range=[start_date, end_date],
            transaction_type__in = all_possible_transaction_list, transaction_leg="EXTERNAL", status="SUCCESSFUL")
        
        total_transaction_count = total_transaction_qs.count()
        total_transaction_count_pos = total_transaction_qs.filter(
                                      user__terminal_id__isnull=False, 
                                      user_wallet_type="POS").count()
        total_transaction_count_mobile = total_transaction_qs.filter(
                                         user__terminal_id__isnull=True
                                         ).count()


        try:
            sms_wallet_balance = WalletSystem.objects.filter(user=float_user, wallet_type="WHISPER").last().available_balance
        except:
            sms_wallet_balance = 0

        wallet_number_of_months_count = wallet_overall_qs.values("date_created__month").count()
        overall_wallet_balance_per_month = (wallets_overall if wallets_overall else 0) / \
                            (wallet_number_of_months_count if wallet_number_of_months_count else 1)

        response = {
            "overall_wallet_balance": {
            "overall": wallets_overall if wallets_overall else 0,
            "pos_wallets": pos_wallets_overall if pos_wallets_overall else 0,
            "mobile_wallets": mobile_wallets_overall if mobile_wallets_overall else 0,
            "average_balance_monthly": overall_wallet_balance_per_month
            },
            "float_wallet": float_balance,
            "commissions_in": total_commissions_in_amt if total_commissions_in_amt is not None else "0",
            "SmsNotificationWalletBalance": sms_wallet_balance,
            "TransactionCount": {
                "overall": total_transaction_count,
                "mobile": total_transaction_count_mobile,
                "pos": total_transaction_count_pos
            }
        }
        return response

class TransactionActivities:
    def get_transaction_activites(start_date, end_date, request):                                      
        float_user = WalletSystem.get_float_user()
        user_role = request.user.role

        # OVERALL TRANSACTIONS
        overall_transactions_qs = Transaction.objects.filter(~Q(user = float_user)).filter(date_created__range = [start_date, end_date], 
                                                            transaction_type__in = all_possible_transaction_list, user__role = user_role)
        overall_failed_transactions_qs = overall_transactions_qs.filter(status="FAILED")
        overall_successful_transactions_qs = overall_transactions_qs.filter(status="SUCCESSFUL")
        overall_inprogress_transactions_qs = overall_transactions_qs.filter(status="IN_PROGRESS")
        overall_pending_transactions_qs = overall_transactions_qs.filter(status="PENDING")
        total_transactions_failed_success_qs = overall_transactions_qs.filter(status__in=["SUCCESSFUL", "FAILED"])

        # Inflows
        overall_inflow_qs = Transaction.objects.filter(~Q(user = float_user)).filter(date_created__range = [start_date, end_date], 
                            transaction_type__in = inflow_list, user__role = user_role)

        # AMOUNT TOTAL
        overall_transaction_amount = list(overall_transactions_qs.aggregate(Sum("amount")).values())[0]

        overall_failed_transaction_amount = list(overall_failed_transactions_qs.aggregate(Sum("amount")).values())[0]
        overall_successful_transaction_amount = list(overall_successful_transactions_qs.aggregate(Sum("amount")).values())[0]
        overall_inprogress_transaction_amount = list(overall_inprogress_transactions_qs.aggregate(Sum("amount")).values())[0]
        overall_pending_transaction_amount = list(overall_pending_transactions_qs.aggregate(Sum("amount")).values())[0]
        total_failed_success = list(total_transactions_failed_success_qs.aggregate(Sum("amount")).values())[0]
        total_inflow_amount = list(overall_inflow_qs.aggregate(Sum("amount")).values())[0]
        overall_less_inflow_amount = (overall_transaction_amount if overall_transaction_amount else 0) - \
                                        (total_inflow_amount if total_inflow_amount else 1)
    
        # Inflow count Percentage
        overall_transactions_count = overall_transactions_qs.count()
        overall_inflow_count = overall_inflow_qs.count()
        inflow_trns_count_percentage = overall_inflow_count / (overall_transactions_count 
                                       if overall_transactions_count else 1) * 100

        # AMOUNT PERCENTAGE
        if overall_failed_transaction_amount:
            failed_trans_percentage = overall_failed_transaction_amount / (overall_transaction_amount 
                                      if overall_transaction_amount else 1) * 100
        else:
            failed_trans_percentage = 0

        if overall_failed_transaction_amount:
            successful_trans_percentage = overall_successful_transaction_amount / (overall_transaction_amount 
                                            if overall_transaction_amount else 1) * 100
        else:
            successful_trans_percentage = 0

        if overall_inprogress_transaction_amount:
            inprogress_trans_percentage = overall_inprogress_transaction_amount / \
                        (overall_transaction_amount if overall_transaction_amount else 1) * 100
        else:
            inprogress_trans_percentage = 0

        if overall_pending_transaction_amount:
            pending_trans_percentage = overall_pending_transaction_amount / (overall_transaction_amount 
                                       if overall_transaction_amount else 1) * 100
        else:
            pending_trans_percentage = 0
        
        if total_failed_success and overall_transaction_amount:
            total_failed_success_percentage = total_failed_success / overall_transaction_amount * 100
        else:
            total_failed_success_percentage = 0

        overall_less_inflow_percentage = abs(overall_less_inflow_amount if overall_less_inflow_amount else 0) / \
                                        (overall_transaction_amount if overall_transaction_amount else 1) * 100

        # SEND MONEY BANK TRANSFER 
        overall_sendmoney_qs = overall_transactions_qs.filter(transaction_type__in=send_money_transaction_type_list,
                    transaction_leg__in=["COMMISSIONS", "INTERNAL"])
        overall_failed_sendmoney_qs = overall_sendmoney_qs.filter(status="FAILED")
        overall_successful_sendmoney_qs = overall_sendmoney_qs.filter(status="SUCCESSFUL")
        overall_inprogress_sendmoney_qs = overall_sendmoney_qs.filter(status="IN_PROGRESS")
        overall_pending_sendmoney_qs = overall_sendmoney_qs.filter(status="PENDING")
        total_sendmoney_failed_success_qs = overall_sendmoney_qs.filter(status__in=["SUCCESSFUL", "FAILED"])

        # AMOUNT TOTAL
        overall_sendmoney_amount = list(overall_sendmoney_qs.aggregate(Sum("amount")).values())[0]

        overall_failed_sendmoney_amount = list(overall_failed_sendmoney_qs.aggregate(Sum("amount")).values())[0]
        overall_successful_sendmoney_amount = list(overall_successful_sendmoney_qs.aggregate(Sum("amount")).values())[0]
        overall_inprogress_sendmoney_amount = list(overall_inprogress_sendmoney_qs.aggregate(Sum("amount")).values())[0]
        overall_pending_sendmoney_amount = list(overall_pending_sendmoney_qs.aggregate(Sum("amount")).values())[0]

        total_send_failed_success = list(total_sendmoney_failed_success_qs.aggregate(Sum("amount")).values())[0]

        try:
            sendmoney_less_inflow_amount = overall_sendmoney_amount - total_inflow_amount
        except TypeError:
            sendmoney_less_inflow_amount = 0

        # AMOUNT PERCENTAGE
        if overall_sendmoney_amount and overall_transaction_amount:
            overall_sendmoney_percentage = overall_sendmoney_amount / overall_transaction_amount * 100
        else:
            overall_sendmoney_percentage = 0.0

        if overall_failed_sendmoney_amount and overall_sendmoney_amount:
            failed_send_percentage = overall_failed_sendmoney_amount / overall_sendmoney_amount * 100
        else:
            failed_send_percentage = 0.0

        if overall_successful_sendmoney_amount and overall_sendmoney_amount:
            successful_send_percentage = overall_successful_sendmoney_amount / overall_sendmoney_amount * 100
        else:
            successful_send_percentage = 0

        if overall_inprogress_sendmoney_amount and overall_sendmoney_amount:
            inprogress_send_percentage = overall_inprogress_sendmoney_amount / overall_sendmoney_amount * 100
        else:
            inprogress_send_percentage = 0

        if overall_pending_sendmoney_amount and overall_sendmoney_amount:
            pending_send_percentage = overall_pending_sendmoney_amount / overall_sendmoney_amount * 100
        else:
            pending_send_percentage = 0

        if total_send_failed_success and overall_sendmoney_amount:
            total_send_failed_success_precentage = total_send_failed_success / overall_sendmoney_amount * 100
        else:
            total_send_failed_success_precentage = 0

        sendmoney_less_inflow_percentage = abs(sendmoney_less_inflow_amount if sendmoney_less_inflow_amount else 0) /\
                                            (overall_transaction_amount if overall_transaction_amount else 1) * 100

        # CARD TRANSACTION FUND
        overall_card_transaction_qs = Transaction.objects.filter(~Q(user = float_user)
                                        ).filter(transaction_type__in = cashout_transaction_type_list,
                                        user__role = user_role)
        
        overall_failed_card_transaction_qs = overall_card_transaction_qs.filter(status="FAILED")
        overall_successful_card_transaction_qs = overall_card_transaction_qs.filter(status="SUCCESSFUL")
        overall_inprogress_card_transaction_qs = overall_card_transaction_qs.filter(status="IN_PROGRESS")
        overall_pending_card_transaction_qs = overall_card_transaction_qs.filter(status="PENDING")
        total_card_transaction_failed_success_qs = overall_card_transaction_qs.filter(status__in=["SUCCESSFUL", "FAILED"])

        # AMOUNT TOTAL
        overall_card_transaction_amount = list(overall_card_transaction_qs.aggregate(Sum("amount")).values())[0]

        overall_failed_card_transaction_amount = list(overall_failed_card_transaction_qs.aggregate(Sum("amount")).values())[0]
        overall_successful_card_transaction_amount = list(overall_successful_card_transaction_qs.aggregate(Sum("amount")).values())[0]
        overall_inprogress_card_transaction_amount = list(overall_inprogress_card_transaction_qs.aggregate(Sum("amount")).values())[0]
        overall_pending_card_transaction_amount = list(overall_pending_card_transaction_qs.aggregate(Sum("amount")).values())[0]

        total_card_transaction_failed_success = list(total_card_transaction_failed_success_qs.aggregate(Sum("amount")).values())[0]

        try:
            card_transaction_less_inflow_amount = overall_card_transaction_amount - total_inflow_amount
        except TypeError:
            card_transaction_less_inflow_amount = 0

        # AMOUNT PERCENTAGE
        if overall_card_transaction_amount and overall_transaction_amount:
            overall_card_transaction_percentage = overall_card_transaction_amount / overall_transaction_amount * 100
        else:
            overall_card_transaction_percentage = 0

        if overall_failed_card_transaction_amount and overall_card_transaction_amount:
            failed_card_transaction_percentage = overall_failed_card_transaction_amount / overall_card_transaction_amount * 100
        else:
            failed_card_transaction_percentage = 0

        if overall_failed_card_transaction_amount and overall_card_transaction_amount:
            successful_card_transaction_percentage = overall_successful_card_transaction_amount / overall_card_transaction_amount * 100
        else:
            successful_card_transaction_percentage = 0

        if overall_inprogress_card_transaction_amount and overall_card_transaction_amount:
            inprogress_card_transaction_percentage = overall_inprogress_card_transaction_amount / overall_card_transaction_amount * 100
        else:
            inprogress_card_transaction_percentage = 0

        if overall_pending_card_transaction_amount and overall_card_transaction_amount:
            pending_card_transaction_percentage = overall_pending_card_transaction_amount / overall_card_transaction_amount * 100
        else:
            pending_card_transaction_percentage = 0

        if total_card_transaction_failed_success and overall_card_transaction_amount:
            total_card_transaction_failed_success_precentage = total_card_transaction_failed_success / \
                                                                overall_card_transaction_amount * 100
        else:
            total_card_transaction_failed_success_precentage = 0

        card_transaction_less_inflow_percentage = abs(card_transaction_less_inflow_amount) / \
                        (overall_transaction_amount if overall_transaction_amount else 1) * 100

        # FUND BANK TRANSFER
        overall_fund_bank_transfer_qs = Transaction.objects.filter(~Q(user=float_user)).filter(date_created__range = 
                                        [start_date, end_date], transaction_type="FUND_BANK_TRANSFER")
        overall_failed_fund_bank_transfer_qs = overall_fund_bank_transfer_qs.filter(status="FAILED")
        overall_successful_fund_bank_transfer_qs = overall_fund_bank_transfer_qs.filter(status="SUCCESSFUL")
        overall_inprogress_fund_bank_transfer_qs = overall_fund_bank_transfer_qs.filter(status="IN_PROGRESS")
        overall_pending_fund_bank_transfer_qs = overall_fund_bank_transfer_qs.filter(status="PENDING")
        total_fund_bank_transfer_qs = overall_fund_bank_transfer_qs.filter(status__in=["SUCCESSFUL", "FAILED"])

        # AMOUNT TOTAL
        overall_fund_bank_transfer_amount = list(overall_fund_bank_transfer_qs.aggregate(Sum("amount")).values())[0]

        overall_failed_fund_bank_transfer_amount = list(overall_failed_fund_bank_transfer_qs.aggregate(Sum("amount")).values())[0]
        overall_successful_fund_bank_transfer_amount = list(overall_successful_fund_bank_transfer_qs.aggregate(Sum("amount")).values())[0]
        overall_inprogress_fund_bank_transfer_amount = list(overall_inprogress_fund_bank_transfer_qs.aggregate(Sum("amount")).values())[0]
        overall_pending_fund_bank_transfer_amount = list(overall_pending_fund_bank_transfer_qs.aggregate(Sum("amount")).values())[0]

        total_fund_bank_transfer_failed_success = list(total_fund_bank_transfer_qs.aggregate(Sum("amount")).values())[0]

        try:
            fund_bank_transfer_less_inflow_amount = overall_fund_bank_transfer_amount - total_inflow_amount
        except TypeError:
            fund_bank_transfer_less_inflow_amount = 0

        # AMOUNT PERCENTAGE
        if overall_fund_bank_transfer_amount and overall_transaction_amount:
            overall_fund_bank_transfer_percentage = overall_fund_bank_transfer_amount / \
                                                    overall_transaction_amount * 100
        else:
            overall_fund_bank_transfer_percentage = 0

        if overall_failed_fund_bank_transfer_amount and overall_fund_bank_transfer_amount:
            failed_fund_bank_transfer_percentage = overall_failed_fund_bank_transfer_amount / \
                                                    overall_fund_bank_transfer_amount * 100
        else:
            failed_fund_bank_transfer_percentage = 0

        if overall_failed_fund_bank_transfer_amount and overall_fund_bank_transfer_amount:
            successful_fund_bank_transfer_percentage = overall_successful_fund_bank_transfer_amount / \
                                                    overall_fund_bank_transfer_amount * 100
        else:
            successful_fund_bank_transfer_percentage = 0

        if overall_inprogress_fund_bank_transfer_amount and overall_fund_bank_transfer_amount:
            inprogress_fund_bank_transfer_percentage = overall_inprogress_fund_bank_transfer_amount / \
                                                        overall_fund_bank_transfer_amount * 100
        else:
            inprogress_fund_bank_transfer_percentage = 0

        if overall_pending_fund_bank_transfer_amount and overall_fund_bank_transfer_amount:
            pending_fund_bank_transfer_percentage = overall_pending_fund_bank_transfer_amount / \
                                                    overall_fund_bank_transfer_amount * 100
        else:
            pending_fund_bank_transfer_percentage = 0

        if total_fund_bank_transfer_failed_success and overall_fund_bank_transfer_amount:
            total_fund_bank_transfer_failed_success_precentage = (total_fund_bank_transfer_failed_success / 
                                                                  overall_fund_bank_transfer_amount * 100)
        else:
            total_fund_bank_transfer_failed_success_precentage = 0

        fund_bank_transfer_less_inflow_percentage = (abs(fund_bank_transfer_less_inflow_amount) / 
                                                    (overall_transaction_amount if overall_transaction_amount else 1) * 100)

        # BILLS AND PAYMENT
        overall_bills_payment_qs = Transaction.objects.filter(~Q(user = float_user)).filter(date_created__range = 
                                    [start_date, end_date], transaction_type="BILLS_AND_PAYMENT")
        overall_failed_bills_payment_qs = overall_bills_payment_qs.filter(status="FAILED")
        overall_successful_bills_payment_qs = overall_bills_payment_qs.filter(status="SUCCESSFUL")
        overall_inprogress_bills_payment_qs = overall_bills_payment_qs.filter(status="IN_PROGRESS")
        overall_pending_bills_payment_qs = overall_bills_payment_qs.filter(status="PENDING")
        total_bills_payment_qs = overall_bills_payment_qs.filter(status__in=["SUCCESSFUL", "FAILED"])

        # AMOUNT TOTAL
        overall_bills_payment_amount = list(overall_bills_payment_qs.aggregate(Sum("amount")).values())[0]

        overall_failed_bills_payment_amount = list(overall_failed_bills_payment_qs.aggregate(Sum("amount")).values())[0]
        overall_successful_bills_payment_amount = list(overall_successful_bills_payment_qs.aggregate(Sum("amount")).values())[0]
        overall_inprogress_bills_payment_amount = list(overall_inprogress_bills_payment_qs.aggregate(Sum("amount")).values())[0]
        overall_pending_bills_payment_amount = list(overall_pending_bills_payment_qs.aggregate(Sum("amount")).values())[0]


        total_bills_payment_failed_success = list(total_bills_payment_qs.aggregate(Sum("amount")).values())[0]

        try:
            bills_payment_less_inflow_amount = overall_bills_payment_amount - total_inflow_amount
        except TypeError:
            bills_payment_less_inflow_amount = 0

        # AMOUNT PERCENTAGE
        if overall_bills_payment_amount and overall_transaction_amount:
            overall_bills_payment_percentage = overall_bills_payment_amount / overall_transaction_amount * 100
        else:
            overall_bills_payment_percentage = 0

        if overall_failed_bills_payment_amount and overall_bills_payment_amount:
            failed_bills_payment_percentage = overall_failed_bills_payment_amount / overall_bills_payment_amount * 100
        else:
            failed_bills_payment_percentage = 0

        if overall_failed_bills_payment_amount and overall_bills_payment_amount:
            successful_bills_payment_percentage = overall_successful_bills_payment_amount / overall_bills_payment_amount * 100
        else:
            successful_bills_payment_percentage = 0

        if overall_inprogress_bills_payment_amount and overall_bills_payment_amount:
            inprogress_bills_payment_percentage = overall_inprogress_bills_payment_amount / overall_bills_payment_amount * 100
        else:
            inprogress_bills_payment_percentage = 0

        if overall_pending_bills_payment_amount and overall_bills_payment_amount:
            pending_bills_payment_percentage = overall_pending_bills_payment_amount / overall_bills_payment_amount * 100
        else:
            pending_bills_payment_percentage = 0

        if total_bills_payment_failed_success and overall_bills_payment_amount:
            total_bills_payment_failed_success_precentage = total_bills_payment_failed_success / overall_bills_payment_amount * 100
        else:
            total_bills_payment_failed_success_precentage = 0

        bills_payment_less_inflow_percentage = (abs(bills_payment_less_inflow_amount if bills_payment_less_inflow_amount else 0) / 
                                                (overall_transaction_amount if overall_transaction_amount else 1) * 100)

        # AIRTIME PIN
        overall_airtime_pin_qs = Transaction.objects.filter(~Q(user=float_user)).filter(date_created__range=[start_date, end_date], 
                                transaction_type="AIRTIME_PIN")
        overall_failed_airtime_pin_qs = overall_airtime_pin_qs.filter(status="FAILED")
        overall_successful_airtime_pin_qs = overall_airtime_pin_qs.filter(status="SUCCESSFUL")
        overall_inprogress_airtime_pin_qs = overall_airtime_pin_qs.filter(status="IN_PROGRESS")
        overall_pending_airtime_pin_qs = overall_airtime_pin_qs.filter(status="PENDING")
        total_airtime_pin_qs = overall_airtime_pin_qs.filter(status__in=["SUCCESSFUL", "FAILED"])

        # AMOUNT TOTAL
        overall_airtime_pin_amount = list(overall_airtime_pin_qs.aggregate(Sum("amount")).values())[0]

        overall_failed_airtime_pin_amount = list(overall_failed_airtime_pin_qs.aggregate(Sum("amount")).values())[0]
        overall_successful_airtime_pin_amount = list(overall_successful_airtime_pin_qs.aggregate(Sum("amount")).values())[0]
        overall_inprogress_airtime_pin_amount = list(overall_inprogress_airtime_pin_qs.aggregate(Sum("amount")).values())[0]
        overall_pending_airtime_pin_amount = list(overall_pending_airtime_pin_qs.aggregate(Sum("amount")).values())[0]


        total_airtime_pin_failed_success = list(total_airtime_pin_qs.aggregate(Sum("amount")).values())[0]

        try:
            airtime_pin_less_inflow_amount = overall_airtime_pin_amount - total_inflow_amount
        except TypeError:
            airtime_pin_less_inflow_amount = 0


        # AMOUNT PERCENTAGE
        if overall_airtime_pin_amount and overall_transaction_amount:
            overall_airtime_pin_percentage = overall_airtime_pin_amount / overall_transaction_amount * 100
        else:
            overall_airtime_pin_percentage = 0

        if overall_failed_airtime_pin_amount and overall_airtime_pin_amount:
            failed_airtime_pin_percentage = overall_failed_airtime_pin_amount / overall_airtime_pin_amount * 100
        else:
            failed_airtime_pin_percentage = 0

        if overall_failed_airtime_pin_amount and overall_airtime_pin_amount:
            successful_airtime_pin_percentage = overall_successful_airtime_pin_amount / overall_airtime_pin_amount * 100
        else:
            successful_airtime_pin_percentage = 0

        if overall_inprogress_airtime_pin_amount and overall_airtime_pin_amount:
            inprogress_airtime_pin_percentage = overall_inprogress_airtime_pin_amount / overall_airtime_pin_amount * 100
        else:
            inprogress_airtime_pin_percentage = 0

        if overall_pending_airtime_pin_amount and overall_airtime_pin_amount:
            pending_airtime_pin_percentage = overall_pending_airtime_pin_amount / overall_airtime_pin_amount * 100
        else:
            pending_airtime_pin_percentage = 0

        if total_airtime_pin_failed_success and overall_airtime_pin_amount:
            total_airtime_pin_failed_success_precentage = total_airtime_pin_failed_success / overall_airtime_pin_amount * 100
        else:
            total_airtime_pin_failed_success_precentage = 0

        airtime_pin_less_inflow_percentage = (abs(airtime_pin_less_inflow_amount if airtime_pin_less_inflow_amount else 0) / 
                                              (overall_transaction_amount if overall_transaction_amount else 1) * 100)

        response = {
            # OVERALL TRANSACTIONS
            "transactionActivity" : {
                "total_transaction_sum": overall_transaction_amount if overall_transaction_amount else 0.00,
                "total_transaction_percentage": 100,
                "total_transaction_inflow": inflow_trns_count_percentage,
                "less_inflow_percentage": overall_less_inflow_percentage,
                # "total_inflow_amount": total_inflow_amount if total_inflow_amount else 0,
                "failed": {
                    "sum": overall_failed_transaction_amount if overall_failed_transaction_amount else 0.0,
                    "percentage": failed_trans_percentage,
                },
                "successful": {
                    "sum": overall_successful_transaction_amount if overall_successful_transaction_amount else 0.00,
                    "percentage": successful_trans_percentage
                },
                "in_progress": {
                    "sum": overall_inprogress_transaction_amount if overall_inprogress_transaction_amount else 0.0,
                    "percentage": inprogress_trans_percentage
                },
                "pending": {
                    "sum": overall_pending_transaction_amount if overall_pending_transaction_amount else 0.0,
                    "percentage": pending_trans_percentage
                },
                "total": {
                    "sum": total_failed_success if total_failed_success else 0.00,
                    "percentage": total_failed_success_percentage
                }
            },
            # SEND MONEY TRANSFER
            "SendMoneyBankTransfer" : {
                "total_transaction_sum": overall_sendmoney_amount if overall_sendmoney_amount else 0.0,
                "total_sendmoney_percentage": overall_sendmoney_percentage,
                "total_transactions": overall_sendmoney_percentage,
                "failed": {
                    "sum": overall_failed_sendmoney_amount if overall_failed_sendmoney_amount else 0.0,
                    "percentage": failed_send_percentage,
                },
                "successful": {
                    "sum": overall_successful_sendmoney_amount if overall_successful_sendmoney_amount else 0.00,
                    "percentage": successful_send_percentage
                },
                "in_progress": {
                    "sum": overall_inprogress_sendmoney_amount if overall_inprogress_sendmoney_amount else 0.0,
                    "percentage": inprogress_send_percentage
                },
                "pending": {
                    "sum": overall_pending_sendmoney_amount if overall_pending_sendmoney_amount else 0.0,
                    "percentage": pending_send_percentage
                },
                "total": total_send_failed_success if total_send_failed_success else 0.0,
                "total_success_failed_percentage": total_send_failed_success_precentage,
                "less_inflow": sendmoney_less_inflow_percentage
            },
            # CARD TRANSACTION FUND
            "CardTransactionFund" : {
                "total_transaction_sum": overall_card_transaction_amount if overall_card_transaction_amount else 0.0,
                "total_card_transaction_percentage": overall_card_transaction_percentage,
                "total_transactions": overall_card_transaction_percentage,
                "failed": {
                    "sum": overall_failed_card_transaction_amount if overall_failed_card_transaction_amount else 0.0,
                    "percentage": failed_card_transaction_percentage,
                },
                "successful": {
                    "sum": overall_successful_card_transaction_amount if overall_successful_card_transaction_amount else 0.0,
                    "percentage": successful_card_transaction_percentage
                },
                "in_progress": {
                    "sum": overall_inprogress_card_transaction_amount if overall_inprogress_card_transaction_amount else 0.0,
                    "percentage": inprogress_card_transaction_percentage
                },
                "pending": {
                    "sum": overall_pending_card_transaction_amount if overall_pending_card_transaction_amount else 0.0,
                    "percentage": pending_card_transaction_percentage
                },
                "total": total_card_transaction_failed_success,
                "total_success_failed_percentage": total_card_transaction_failed_success_precentage,
                "less_inflow": card_transaction_less_inflow_percentage
            },
            # FUND BANK TRANSFER
            "FundBankTransfer" : {
                "total_transaction_sum": overall_fund_bank_transfer_amount if overall_fund_bank_transfer_amount else 0.00,
                "total_fund_bank_transfer_percentage": overall_fund_bank_transfer_percentage,
                "total_transactions": overall_fund_bank_transfer_percentage,
                "failed": {
                    "sum": overall_failed_fund_bank_transfer_amount if overall_failed_fund_bank_transfer_amount else 0.00,
                    "percentage": failed_fund_bank_transfer_percentage,
                },
                "successful": {
                    "sum": overall_successful_fund_bank_transfer_amount if overall_successful_fund_bank_transfer_amount else 0.00,
                    "percentage": successful_fund_bank_transfer_percentage
                },
                "in_progress": {
                    "sum": overall_inprogress_fund_bank_transfer_amount if overall_inprogress_fund_bank_transfer_amount else 0,
                    "percentage": inprogress_fund_bank_transfer_percentage
                },
                "pending": {
                    "sum": overall_pending_fund_bank_transfer_amount if overall_pending_fund_bank_transfer_amount else 0.00,
                    "percentage": pending_fund_bank_transfer_percentage
                },
                "total": total_fund_bank_transfer_failed_success if total_fund_bank_transfer_failed_success else 0.00,
                "total_success_failed_percentage": total_fund_bank_transfer_failed_success_precentage,
                "less_inflow": fund_bank_transfer_less_inflow_percentage
            },
            # BILLS AND PAYMENT
            "BillsAndPayment" : {
                "total_transaction_sum": overall_bills_payment_amount if overall_bills_payment_amount else 0.00,
                "total_bills_payment_percentage": overall_bills_payment_percentage,
                "total_transactions": overall_bills_payment_percentage,
                "failed": {
                    "sum": overall_failed_bills_payment_amount if overall_failed_bills_payment_amount else 0.00,
                    "percentage": failed_bills_payment_percentage,
                },
                "successful": {
                    "sum": overall_successful_bills_payment_amount if overall_successful_bills_payment_amount else 0,
                    "percentage": successful_bills_payment_percentage
                },
                "in_progress": {
                    "sum": overall_inprogress_bills_payment_amount if overall_inprogress_bills_payment_amount else 0,
                    "percentage": inprogress_bills_payment_percentage
                },
                "pending": {
                    "sum": overall_pending_bills_payment_amount if overall_pending_bills_payment_amount else 0,
                    "percentage": pending_bills_payment_percentage
                },
                "total": total_bills_payment_failed_success if total_bills_payment_failed_success else 0,
                "total_success_failed_percentage": total_bills_payment_failed_success_precentage,
                "less_inflow": bills_payment_less_inflow_percentage
            },
            # AIRTIME PIN
            "AirtimePin" : {
                "total_transaction_sum": overall_airtime_pin_amount if overall_airtime_pin_amount else 0,
                "total_airtime_pin_percentage": overall_airtime_pin_percentage,
                "total_transactions": overall_airtime_pin_percentage,
                "failed": {
                    "sum": overall_failed_airtime_pin_amount if overall_failed_airtime_pin_amount else 0,
                    "percentage": failed_airtime_pin_percentage,
                },
                "successful": {
                    "sum": overall_successful_airtime_pin_amount if overall_successful_airtime_pin_amount else 0,
                    "percentage": successful_airtime_pin_percentage
                },
                "in_progress": {
                    "sum": overall_inprogress_airtime_pin_amount if overall_inprogress_airtime_pin_amount else 0,
                    "percentage": inprogress_airtime_pin_percentage
                },
                "pending": {
                    "sum": overall_pending_airtime_pin_amount if overall_pending_airtime_pin_amount else 0,
                    "percentage": pending_airtime_pin_percentage
                },
                "total": total_airtime_pin_failed_success if total_airtime_pin_failed_success else 0,
                "total_success_failed_percentage": total_airtime_pin_failed_success_precentage,
                "less_inflow": airtime_pin_less_inflow_percentage
            },
        }
        return response

class TerminalByPerformance:
    def get_terminal_performance(request, start_date, end_date, status_filter):
        user_role = request.user.role

        if not status_filter or status_filter == "":
            status_choice_list = ["ACTIVE", "INACTIVE", "PARTIALLY_INACTIVE",
                                "RECOVERY", "RECOVERED", "SUSPENDED", "DORMANT"]
        else:
            status_choice_list = [status_filter]

        terminals = User.objects.filter(Q(date_joined__range=[start_date, end_date], terminal_id__isnull = False, 
                    terminal_status__in=status_choice_list) & ~Q(terminal_id=""), role=user_role)
        terminals = Paginator.paginate(request=request, queryset=terminals)

        all_data = []
        for terminal in terminals:
            transactions_qs = Transaction.objects.filter(Q(user__terminal_id=terminal.terminal_id) &
                Q(transaction_type__in=all_possible_transaction_list, transaction_leg ="EXTERNAL", status="SUCCESSFUL"))

            transactions = transactions_qs.filter()

            transactions_weekly = transactions_qs.filter().values("date_created__week")

            average_transactions_count_weekly = transactions_weekly.annotate(num_trans_week=Count("date_created__week")).\
                aggregate(Avg("num_trans_week"))["num_trans_week__avg"]

            transaction_count = transactions.count()
        
            data = {
                "id": terminal.id,
                "terminal_id":terminal.terminal_id,
                "name": terminal.first_name + " " + terminal.last_name,
                "status": terminal.terminal_status,
                "transaction_count": transaction_count,
                "average_transaction_per_week": average_transactions_count_weekly,
                "last_login": terminal.last_login if terminal.last_login is not None else ""
            }
            all_data.append(data)

        response = {
            "terminals": all_data,
            "count": len(all_data)
        }
        return response

class MerchantsTransactionsTop:
    def __init__(self):
        self.previous_year_current_month_start = DateUtility().previous_year_current_month_start
        self.months_list = DateUtility().months_list
        self.date_today = DateUtility().date_today
        self.start_of_all_users = DateUtility().start_of_all_users
        self.previous_month_end = DateUtility().previous_month_end  
        self.previous_year_current_month_end = DateUtility().previous_year_current_month_end
        self.previous_year_current_following_month = DateUtility().previous_year_current_following_month
        self.months_list_names = DateUtility().months_list_names
        self.init_start = DateUtility().init_start
        self.year_months_tuple_list = DateUtility().year_months_tuple_list
        self.year_start = DateUtility().year_start
        self.month_start = DateUtility().month_start
        self.week_start = DateUtility().week_start
        self.previous_year_start = DateUtility().previous_year_start
        self.previous_year_end = DateUtility().previous_year_end
        self.today = DateUtility().today
        self.previous_day = DateUtility().previous_day

    def get_merchants_top_transactions(self, request, start_date, end_date):
        user_role = request.user.role

        float_user = WalletSystem.get_float_user()
        base_transactions = Transaction.objects.filter(~Q(user=float_user)
                            ).filter(type_of_user="MERCHANT", user__role=user_role)

        # Overview
        gen_transaction_qs = base_transactions.filter(
                                        transaction_type__in=all_possible_transaction_list, 
                                        status="SUCCESSFUL",
                                        user__terminal_id__isnull=False, 
                                        user__type_of_user="MERCHANT"
                                        )

        transaction_amount_today = gen_transaction_qs.filter(
                                        date_created__range=[self.previous_day, self.today]
                                        ).aggregate(Sum("amount"))["amount__sum"]

        transaction_amount_overall = gen_transaction_qs.aggregate(Sum("amount"))["amount__sum"]


        previous_day_transaction_amount_total = gen_transaction_qs.filter(
                                        date_created__date=self.previous_day.date()
                                        ).aggregate(Sum("amount"))["amount__sum"]

        previous_month_transaction_amount_total = gen_transaction_qs.filter(
                                        date_created__range=[self.init_start, self.previous_month_end]
                                        ).aggregate(Sum("amount"))["amount__sum"]


        transaction_amount_change = get_percentage_diff(current=transaction_amount_overall, 
                                    previous=(previous_month_transaction_amount_total if 
                                    previous_month_transaction_amount_total else 0))

        transaction_amount_change_today = get_percentage_diff(
                                          current=transaction_amount_overall, 
                                          previous=previous_day_transaction_amount_total
                                          )

        transaction_counts_today = gen_transaction_qs.filter(date_created__range=[self.previous_day, self.today]).count()
        previous_day_transaction_count_total = gen_transaction_qs.filter(date_created__date=self.previous_day.date()).count()
        previous_month_transaction_count_total = gen_transaction_qs.filter(date_created__range=
                                                [self.init_start, self.previous_month_end]).count()

        transaction_count_overall = gen_transaction_qs.count()

        transaction_count_change = get_percentage_diff(current=transaction_count_overall, 
                                   previous=previous_month_transaction_count_total)
        transaction_count_change_today = get_percentage_diff(current=transaction_count_overall, 
                                        previous=previous_day_transaction_count_total)

        # Top 10
        discrete_transaction_amounts_overall = gen_transaction_qs.filter(date_created__range=[start_date, end_date]
                                                ).values("user__terminal_id", "user__first_name", "user__last_name", 
                                                "user__email").annotate(total_amounts=(Sum("amount")))

        discrete_transaction_count_overall = gen_transaction_qs.filter(date_created__range=
                                            [start_date, end_date], transaction_type__in=all_possible_transaction_list
                                            ).values("user__terminal_id", "user__first_name", "user__last_name", "user__email"
                                            ).annotate(transaction_counts=Count("amount"))

        amounts_sorted_list = sorted(discrete_transaction_amounts_overall, key=lambda d: d["total_amounts"])[::-1][:10]
        counts_sorted_list = sorted(discrete_transaction_count_overall, key=lambda d: d["transaction_counts"])[::-1][:10]

        top10_amounts = []
        for agent in amounts_sorted_list:
            data = {
                "terminal_id": agent["user__terminal_id"],
                "agent_name": agent["user__first_name"] + " " + agent["user__last_name"],
                "amount": agent["total_amounts"],
                "agent_email": agent["user__email"]
            }
            top10_amounts.append(data)

        top10_counts = []
        for agent in counts_sorted_list:
            data = {
                "terminal_id": agent["user__terminal_id"],
                "agent_name": agent["user__first_name"] + " " + agent["user__last_name"],
                "transaction_count": agent["transaction_counts"],
                "agent_email": agent["user__email"],
                }
            top10_counts.append(data)

        response = {
            "transactionAmount": {
                "total_transactions": transaction_amount_overall if transaction_amount_overall else 0.00,
                "today_transactions": transaction_amount_today if transaction_amount_today else 0,
                "percentage": str(transaction_amount_change.get("percentage")),
                "change": str(transaction_amount_change.get("change")),
                "today_amount_change": {
                    "percentage": str(transaction_amount_change_today.get("percentage")),
                    "change": transaction_amount_change_today.get("change")
                },
                "top10_transactions": top10_amounts,
            },

            "transactionCount": {
                "total_transaction_count": transaction_count_overall if transaction_count_overall else 0.00,
                "today_transaction_count": transaction_counts_today,
                "percentage": str(transaction_count_change.get("percentage")),
                "change": str(transaction_count_change.get("change")),
                "today_count_change": {
                    "percentage": str(transaction_count_change_today.get("percentage")),
                    "change": transaction_count_change_today.get("change")
                },
                "top10_counts": top10_counts
            }
        }
        return response

class CustomerCount():
    def __init__(self):
        self.previous_year_current_month_start = DateUtility().previous_year_current_month_start
        self.months_list = DateUtility().months_list
        self.date_today = DateUtility().date_today
        self.start_of_all_users = DateUtility().start_of_all_users
        self.previous_month_end = DateUtility().previous_month_end  
        self.previous_year_current_month_end = DateUtility().previous_year_current_month_end
        self.previous_year_current_following_month = DateUtility().previous_year_current_following_month
        self.months_list_names = DateUtility().months_list_names
        self.init_start = DateUtility().init_start
        self.year_months_tuple_list = DateUtility().year_months_tuple_list
        self.year_start = DateUtility().year_start
        self.month_start = DateUtility().month_start
        self.week_start = DateUtility().week_start
        self.previous_year_start = DateUtility().previous_year_start
        self.previous_year_end = DateUtility().previous_year_end
        self.today = DateUtility().today
        self.previous_day = DateUtility().previous_day
        self.previous_month_start = DateUtility().previous_month_start

    def get_customer_count(self, start_date, end_date, request):
        user_role = request.user.role
        customers_qs = User.objects.filter(role=user_role)

        # Total Customers
        total_customers_till_last_month_end = customers_qs.filter(date_joined__date__lte=self.previous_month_end).count()
        current_total_customers = customers_qs.all().count()

        # Total Customers Change
        total_customers_change = get_percentage_diff(current=current_total_customers, previous=total_customers_till_last_month_end)

        # New Customers
        last_month_new_customers = customers_qs.filter(date_joined__range=[self.previous_month_start, self.previous_month_end]).count()
        current_month_new_customers = customers_qs.filter(date_joined__gte=self.month_start).count()

        # New Customers Change
        new_customers_change = get_percentage_diff(current=current_month_new_customers, previous=last_month_new_customers)
        
        # # Active Customers
        active_customers_qs = Transaction.objects.filter(~Q(status = "FAILED")).filter(user__id__in=[user.id for user in customers_qs]
                            ).distinct("user")
        active_customers = active_customers_qs.count()
        active_customers_till_previous_month_end = active_customers_qs.filter(user__date_joined__lte = self.previous_month_end).count()

        # # Active customers Change
        active_customers_change = get_percentage_diff(current=active_customers, previous=active_customers_till_previous_month_end)

        # Inactive Customers
        inactive_customers = current_total_customers - active_customers
        inactive_customers_previous_month = total_customers_till_last_month_end - active_customers_till_previous_month_end

        # Inactive Customers Change
        inactive_customers_change = get_percentage_diff(current=inactive_customers, previous=inactive_customers_previous_month)

        # Churn Customers
        churn_customers_qs = customers_qs.filter(Q(last_login__date__lte = datetime.now() - timedelta(days=30)))
        churn_customers = churn_customers_qs.count()
        churn_customers_till_previous_month = churn_customers_qs.filter(date_joined__lte=self.previous_month_end).count()

        # Churn Customers Change
        churn_customers_change = get_percentage_diff(current=churn_customers, previous=churn_customers_till_previous_month)

        response = {
            "all_customers":current_total_customers,
            "new_customers": current_month_new_customers,
            "active_customers": active_customers,
            "Inactive_customers": inactive_customers,
            "churn_customers": churn_customers,
            "customer_changes": {
                "all_customers": {
                    "percentage": str(total_customers_change.get("percentage")),
                    "change": total_customers_change.get("change")
                },
                "new_customers": {
                    "percentage": str(new_customers_change.get("percentage")),
                    "change": new_customers_change.get("change")
                },
                "active_customers": {
                    "percentage": str(active_customers_change.get("percentage")),
                    "change": active_customers_change.get("change")
                },
                "inactive_customers": {
                    "percentage": str(inactive_customers_change.get("percentage")),
                    "change": inactive_customers_change.get("change")
                },
                "churn_customers": {
                    "percentage": str(churn_customers_change.get("percentage")),
                    "change": churn_customers_change.get("change")
                }
            }
        }
        return response

class NewCustomers:
    def __init__(self):
        self.month_start = DateUtility().month_start

    def get_new_customers(self, request, start_date, end_date):
        user_role = request.user.role
        new_customers =  User.objects.filter(date_joined__gte=self.month_start, role=user_role).order_by("-date_joined")
        new_customers = Paginator.paginate(request=request, queryset=new_customers)

        new_customers_list = []
        for data in new_customers:
            data = {
            "id": data.id,
            "unique_id": data.unique_id,
            "last_login": data.last_login,
            "date_joined": data.date_joined,
            # "tag": data.tag,
            "name": data.get_full_name(),
            "email": data.email,
            "kyc_level": "completed" if data.kyc_level > 1 else "not completed",
            "device": "POS" if data.terminal_id else "Mobile"
            }

            new_customers_list.append(data)

        response = {
            "new_customers": new_customers_list,
            "count": len(new_customers_list)
        }
        return response

class ChurnCustomers:
    def __init__(self):
        self.month_ago = DateUtility().month_ago

    def get_churn_customers(self, request, start_date, end_date):
        user_role = request.user.role
        churn_customers =  User.objects.filter(last_login__lt=self.month_ago, role=user_role).order_by("-date_joined")
        churn_customers = Paginator.paginate(request=request, queryset=churn_customers)

        churn_customers_list = []
        for data in churn_customers:
            data = {
            "id": data.id,
            "unique_id": data.unique_id,
            "last_login": data.last_login,
            "date_joined": data.date_joined,
            # "tag": data.tag,
            "name": data.get_full_name(),
            "email": data.email,
            "kyc_level": "completed" if data.kyc_level > 1 else "not completed",
            "device": "POS" if data.terminal_id else "Mobile"
            }

            churn_customers_list.append(data)

        response = {
            "churn_customers": churn_customers_list,
            "count": len(churn_customers_list)
        }
        return response

class AgentCommission:
    def __init__(self):
        self.init_start = DateUtility().init_start
        self.previous_month_end = DateUtility().previous_month_end
        self.today = DateUtility().today

    def get_agent_commission(self, queryset, start_date, end_date):
        # Total Commissions In
        total_commissions_in = queryset.filter(date_created__range=[start_date, end_date]
                            ).aggregate(Sum("liberty_profit"))["liberty_profit__sum"]
        total_commission_in_till_previous_month_end = list(queryset.filter(date_created__range=
                                                    [self.init_start, self.previous_month_end]
                                                    ).aggregate(Sum("liberty_profit")).values())[0]

        total_commissions_in_change = get_percentage_diff(
                                        previous=total_commission_in_till_previous_month_end, 
                                        current=total_commissions_in)

        # Total Commissions Out
        total_commissions_out = queryset.filter(date_created__range=[self.init_start, self.today]).annotate(
                                commissions_out_amount = F("agent_cash_profit") + F("ro_cash_profit")).aggregate(
                                Sum("commissions_out_amount"))["commissions_out_amount__sum"]

        total_commission_out_till_previous_month_end = queryset.filter(date_created__range=[self.init_start, self.previous_month_end]
                                                    ).annotate(
                                                    commissions_out_amount=F("agent_cash_profit") + F("ro_cash_profit")
                                                    ).aggregate(Sum("commissions_out_amount"))["commissions_out_amount__sum"]

        total_commissions_out_change = get_percentage_diff(
                                        previous=total_commission_out_till_previous_month_end, 
                                        current=total_commissions_out)

        # Average Commission
        total_commissions_count = queryset.filter(date_created__range=[start_date, end_date]).count()
        total_commissions_count_till_previous_month = queryset.filter(
                                                      date_created__range=[self.init_start, 
                                                      self.previous_month_end]).count()

        average_commissions_amount_current = (total_commissions_in if total_commissions_in else 0) / \
                                            (total_commissions_count if total_commissions_count else 1)

        average_commissions_amount_previous_month = ((total_commission_in_till_previous_month_end if 
                                                      total_commission_in_till_previous_month_end else 0)
                                                    / (total_commissions_count_till_previous_month if 
                                                    total_commissions_count_till_previous_month else 1))

        average_commissions_change = get_percentage_diff(
                                    previous=average_commissions_amount_previous_month, 
                                    current=average_commissions_amount_current)

        # Average Commission Out
        total_commissions_out_count = queryset.filter(date_created__range=[start_date, end_date]).count()
        total_commissions_out_count_till_previous_month = queryset.filter(date_created__range = 
                                                        [self.init_start, self.previous_month_end]).count()

        average_commissions_out_amount_current = (total_commissions_out if total_commissions_out else 0) / \
                                                (total_commissions_out_count if total_commissions_out_count else 1)

        average_commissions_out_amount_previous_month = ((total_commission_out_till_previous_month_end if 
                                                          total_commission_out_till_previous_month_end else 0)
                                                        / (total_commissions_out_count_till_previous_month if 
                                                           total_commissions_out_count_till_previous_month else 1))

        average_commissions_out_change = get_percentage_diff(
                                         previous=average_commissions_out_amount_previous_month, 
                                         current=average_commissions_out_amount_current)

        response = {
            "totalCommissionsIn": {
                    "total": total_commissions_in if total_commissions_in else 0,
                    "percentage": str(total_commissions_in_change.get("percentage")),
                    "change": total_commissions_in_change.get("change")
                },
                # Done
                "totalCommissionsOut": {
                    "total": total_commissions_out,
                    "percentage": str(total_commissions_out_change.get("percentage")),
                    "change": total_commissions_out_change.get("change")
                },
                # Done
                "averageCommissionPerTransaction": {
                    "total": average_commissions_amount_current,
                    "percentage": str(average_commissions_change.get("percentage")),
                    "change": average_commissions_change.get("change")
                },
                # Done
                "averageCommissionsOutPerTransaction": {
                    "total": average_commissions_out_amount_current,
                    "percentage": str(average_commissions_out_change.get("percentage")),
                    "change": average_commissions_out_change.get("change")
                }

        }
        return response


class TransactionList:
    def __init__(self):
        self.previous_day = DateUtility().previous_day
        self.date_today = DateUtility().date_today
        self.today = DateUtility().today
        self.previous_month_end = DateUtility().previous_month_end
        self.init_start = DateUtility().init_start

    def get_transactions_list(self, serializer_class, request, start_date, end_date):
        float_user = WalletSystem.get_float_user()
        user_role = request.user.role
        base_transaction_qs = Transaction.objects.filter(transaction_type__in = 
                            all_possible_transaction_list, user__role = user_role
                            ).filter(~Q(user=float_user, transaction_leg="INTERNAL"))

        # Total Transactions
        total_transactions_qs = base_transaction_qs
        total_transactions_amount = total_transactions_qs.aggregate(Sum("amount"))["amount__sum"]
        total_transactions_amount_today = total_transactions_qs.filter(date_created__range = 
                                        [self.previous_day, self.date_today]
                                        ).aggregate(Sum("amount"))["amount__sum"]

        previous_day_total_transaction_amount = total_transactions_qs.filter(date_created__range = 
                                              [self.init_start, self.previous_day]
                                              ).aggregate(Sum("amount")).values()
        previous_month_total_transaction_amount = total_transactions_qs.filter(date_created__range = 
                                            [self.init_start, self.previous_month_end]
                                            ).aggregate(Sum("amount")).values()

         # Transaction Counts
        total_transaction_counts = base_transaction_qs.count()
        total_transaction_counts_today = base_transaction_qs.filter(
                                         date_created__range=[self.previous_day, 
                                        self.date_today]).count()

        previous_day_total_transaction_count = base_transaction_qs.filter(
                                                Q(date_created__range=[self.init_start, 
                                                self.previous_day])).count()
        previous_month_total_transaction_count = base_transaction_qs.filter(
                                                Q(date_created__range=[self.init_start, 
                                                self.previous_month_end])).count()

        total_transactions_counts_change = get_percentage_diff(
                                            previous=previous_month_total_transaction_count, 
                                            current=total_transaction_counts)
        total_transaction_counts_change_daily = get_percentage_diff(
                                                previous = previous_day_total_transaction_count, 
                                                current=total_transaction_counts)

        total_transactions_amount_change = get_percentage_diff(
                                            previous = (previous_month_total_transaction_amount if 
                                            previous_month_total_transaction_amount else 0), 
                                            current = total_transactions_amount
                                            )

        total_transactions_amount_change_daily = get_percentage_diff(
                                                previous = previous_day_total_transaction_amount, 
                                                current = total_transactions_amount
                                                )

        overview = {
            #Transactions Amount
            "total_transaction_amount": {
                "amount": total_transactions_amount if total_transactions_amount else 0.00,
                "percentage": str(total_transactions_amount_change.get("percentage")),
                "change": str(total_transactions_amount_change.get("change"))
                },
            "today_transactions": {
                "amount": total_transactions_amount_today if total_transactions_amount_today else 0.00,
                "percentage": str(total_transactions_amount_change_daily.get("percentage")),
                "change": total_transactions_amount_change_daily.get("change")
                },
            "total_transaction_counts": {
                "count": total_transaction_counts,
                "percentage": str(total_transactions_counts_change.get("percentage")),
                "change": total_transactions_counts_change.get("change")
            },
            "today_transaction_counts": {
                "count": total_transaction_counts_today if total_transaction_counts_today else 0,
                "percentage": str(total_transaction_counts_change_daily.get("percentage")),
                "change": total_transaction_counts_change_daily.get("change")
            }
            }

        transactions = base_transaction_qs.order_by("-date_created")
        transactions = Paginator.paginate(request=request, queryset=transactions)

        serializer = serializer_class(transactions, many=True)

        response = {
            "overview": overview,
            "transactions": serializer.data,
            "count": len(serializer.data)
        }
        return response


class OtherCommission:
    def __init__(self):
        self.previous_day = DateUtility().previous_day
        self.date_today = DateUtility().date_today
        self.today = DateUtility().today
        self.previous_month_end = DateUtility().previous_month_end
        self.init_start = DateUtility().init_start

    def get_other_commission(self, queryset, serializer_class, request, start_date, end_date):
        user_role = request.user.role
        queryset = queryset.filter(agent__role = user_role)

        other_commissions_qs = queryset.filter(date_created__range = [start_date, end_date]).order_by("date_created")
        other_commissions_qs = Paginator.paginate(request=request, queryset = other_commissions_qs)

        # Total Commissions In
        total_commissions_in = queryset.filter(date_created__range=[self.init_start, self.date_today]
                                ).aggregate(Sum("liberty_profit"))["liberty_profit__sum"]
        total_commission_in_till_previous_month_end = list(queryset.filter(date_created__range =
                                                    [self.init_start, self.previous_month_end]
                                                    ).aggregate(Sum("liberty_profit")).values())[0]

        total_commissions_in_change = get_percentage_diff(
                                    previous=total_commission_in_till_previous_month_end, 
                                    current=total_commissions_in
                                    )

        # Today Commission In
        today_commissions_in = queryset.filter(date_created__range=[self.previous_day, self.date_today]
                                ).aggregate(Sum("liberty_profit"))["liberty_profit__sum"]
        total_commissions_in_till_previous_day = queryset.filter(date_created__range=[self.init_start, self.previous_day]
                                                ).aggregate(Sum("liberty_profit"))["liberty_profit__sum"]
        total_commissions_in_daily_change = get_percentage_diff(
                                            current=total_commissions_in, 
                                            previous=total_commissions_in_till_previous_day)
        serializer = serializer_class(other_commissions_qs, many=True)

        # Total Commissions Out
        total_commissions_out = queryset.filter(date_created__range=[self.init_start, self.date_today]).annotate(
            commissions_out_amount = F("agent_cash_profit") + F("ro_cash_profit")
                                        ).aggregate(Sum("commissions_out_amount")
                                        )["commissions_out_amount__sum"]

        total_commission_out_till_previous_month_end = queryset.filter(date_created__range=
                                                        [self.init_start, self.previous_month_end]).annotate(
                                                        commissions_out_amount = F("agent_cash_profit") + F("ro_cash_profit")
                                                        ).aggregate(Sum("commissions_out_amount"))["commissions_out_amount__sum"]

        total_commissions_out_change = get_percentage_diff(
                                        previous = total_commission_out_till_previous_month_end, 
                                        current=total_commissions_out)

        # Today Commission Out
        today_commissions_out = queryset.filter(date_created__range=[self.previous_day, self.date_today]).annotate(
                                commissions_out_amount = (F("agent_cash_profit") + F("ro_cash_profit"))
                                ).aggregate(Sum("commissions_out_amount"))["commissions_out_amount__sum"]

        total_commissions_out_till_previous_day = queryset.filter(date_created__range=[self.init_start, self.previous_day]).annotate(
                                                commissions_out_amount= (F("agent_cash_profit") + F("ro_cash_profit"))
                                                ).aggregate(Sum("commissions_out_amount"))["commissions_out_amount__sum"]

        total_commissions_out_daily_change = get_percentage_diff(
                                            current = total_commissions_out, 
                                            previous = total_commissions_out_till_previous_day
                                            )

        overview = {
            "total_commissions_in": {
                "amount": total_commissions_in if total_commissions_in else 0,
                "percentage": total_commissions_in_change.get("percentage"),
                "change": total_commissions_in_change.get("change")
                },
            "today_commissions_in": {
                "amount": today_commissions_in,
                "percentage": total_commissions_in_daily_change.get("percentage"),
                "change": total_commissions_in_daily_change.get("change")
                },

            "total_commissions_out": {
                "amount": total_commissions_out if total_commissions_out else 0,
                "percentage": total_commissions_out_change.get("percentage"),
                "change": total_commissions_out_change.get("change")
                },
            "today_commissions_out": {
                "amount": today_commissions_out if today_commissions_out else 0,
                "percentage": total_commissions_out_daily_change.get("percentage"),
                "change": total_commissions_out_daily_change.get("change")
                }
        }

        commissions_list = []
        for commission in other_commissions_qs:
            data = {
                "id": commission.id,
                "amount": commission.amount,
                "commission_in": commission.liberty_profit,
                "commission_out": (commission.ro_cash_profit if commission.ro_cash_profit else 0) + \
                                (commission.agent_cash_profit if commission.agent_cash_profit else 0),
                "reference_id": commission.liberty_reference,
                "date_time": commission.date_created,
                "status": "success"
            }
            commissions_list.append(data)

        response = {
            "overview": overview,
            "commissions_list": commissions_list,
            "count": len(commissions_list)
        }
        return response


class ActiveTerminals:
    def get_active_terminals_status(request, start_date, end_date):
        user_role = request.user.role

        terminals = User.objects.filter(Q(date_joined__range=[start_date, end_date], 
                                        terminal_id__isnull=False, terminal_status="ACTIVE") 
                                        & ~Q(terminal_id=""), role=user_role)
        terminals = Paginator.paginate(request=request, queryset=terminals)

        all_data = []
        for terminal in terminals:
            transactions_qs = Transaction.objects.filter(user__terminal_id=terminal.terminal_id,
            transaction_type__in=all_possible_transaction_list, transaction_leg ="EXTERNAL", status="SUCCESSFUL")

            transactions_weekly = transactions_qs.filter().values("date_created__week")

            average_transactions_count_weekly = transactions_weekly.annotate(num_trans_week=Count("date_created__week")).\
                aggregate(Avg("num_trans_week"))["num_trans_week__avg"]

            transaction_count = transactions_qs.count()

            data = {
                "id": terminal.id,
                "terminal_id":terminal.terminal_id,
                "name": terminal.first_name + " " + terminal.last_name,
                "status": terminal.terminal_status,
                "transaction_count": transaction_count,
                "average_transaction_per_week": average_transactions_count_weekly if average_transactions_count_weekly else 0,
                "last_login": terminal.last_login if terminal.last_login is not None else ""
            }
            all_data.append(data)

        response = {
            "terminals": all_data,
            "count": len(all_data)
        }
        return response

class TerminalDetails:
    def get_terminal_details(request, start_date, end_date, id):
            agent = User.objects.get(id = id)
            transactions = Transaction.objects.filter(Q(date_created__range = [start_date, end_date], user = agent) &
                Q(transaction_type__in=all_possible_transaction_list, transaction_leg = "EXTERNAL", status="SUCCESSFUL")
                    ).order_by("-date_created")

            transactions_list = list(transactions)
            transactions_list = Paginator.paginate(request = request, queryset = transactions)

            transaction_count = transactions.count()

            all_transactions = []
            for transaction in transactions_list:
                transaction_detail = {
                    "id": transaction.id,
                    "description": transaction.transaction_type,
                    "transaction_amount": transaction.amount,
                    "transaction_method": transaction.transaction_mode,
                    "reference_id": transaction.liberty_reference,
                    "date_time": transaction.date_created,
                    "receiver": transaction.beneficiary_nuban,
                    "status": transaction.status
                }
                all_transactions.append(transaction_detail)

            agent_details = {
                "id": agent.id,
                "username": agent.username,
                "agent_id": agent.unique_id,
                "phone_number": agent.phone_number,
                "terminal_id":agent.terminal_id,
                "name": agent.first_name + " " + agent.last_name,
                "email": agent.email,
                "date_joined": agent.date_joined,
                "last_login": agent.terminal_last_login
            }

            response = {
                "agent_details": agent_details,
                "transactions": all_transactions,
                "transaction_counts": transaction_count,
                "count": len(all_transactions)
            }
            return response

class StockHeadOffice:
    def get_stock_head_office(queryset, start_date, end_date):
        stock_qs = queryset
        total_terminals_in_stock = stock_qs.count()
        last_date_added = stock_qs.last().date_added

        terminal_requests_qs = TerminalRequest.objects.filter(date_created__range = [start_date, end_date])
        terminal_requests_count = terminal_requests_qs.count()

        paid_stock_qs = terminal_requests_qs.filter(Q(status="ASSIGNED") | Q(status="PICKED_UP"))
        delivered_stock_qs = terminal_requests_qs.filter(status = "PICKED_UP")
        delayed_delivery_stock_qs = terminal_requests_qs.filter(~Q(status = "PICKED_UP") & Q(status="ASSIGNED"))

        paid_stock_count = paid_stock_qs.count()
        delivered_stock_count = delivered_stock_qs.count()
        delayed_delivery_count = delayed_delivery_stock_qs.count()

        pick_up_days_qs = terminal_requests_qs.filter(Q(status = "PICKED_UP")).values(f"date_created", "pick_up_date"). \
            annotate(pick_up_days = F("pick_up_date") - F("approved_date"))

        average_pickup_days = pick_up_days_qs.aggregate(Avg("pick_up_days"))["pick_up_days__avg"]

        response = {
            "terminals_in_stock": total_terminals_in_stock,
            "last_date_added": last_date_added,

            "deliveryStatus": {
                "paid": paid_stock_count,
                "received": delivered_stock_count
            },

            "terminal_requests_count": terminal_requests_count,
            "average_delivery_time": f"{average_pickup_days.days * 24}" + " hours" if average_pickup_days else 0,
            "delayed_deliveries": delayed_delivery_count
        }
        return response

class StockProviders:
    def get_stock_providers(queryset, start_date, end_date):
        horizon_pay_qs = queryset.filter(Q(date_added__range = [start_date, end_date]) 
                                         & Q(device_name = "HORIZONPAY") | 
                                         Q(device_name = "HOIRZONPAY"))
        nairapoint_qs = queryset.filter(Q(date_added__range = [start_date, end_date]) & Q(device_name="9NAIRAPOINT"))
        npsb_qs = queryset.filter(Q(date_added__range = [start_date, end_date]) & Q(device_name="NPSB"))
        paydia_qs = queryset.filter(Q(date_added__range = [start_date, end_date]) & Q(device_name="PAYDIA"))
        liberty_pay_qs = queryset.filter(Q(date_added__range = [start_date, end_date]) & Q(device_name="LIBERTYPAY"))

        total_terminals = Stock.objects.count()
        providers_list = [
            {
                "id": 1,
                "device_name": "LibertyPay",
                "number_of_terminals": liberty_pay_qs.count(),
                "available_terminals": liberty_pay_qs.filter(is_assigned=False).count(),
                "last_updated": timezone.now()
            },
            {
                    "id": 2,
                    "device_name": "9nairapoint",
                    "number_of_terminals": nairapoint_qs.count(),
                    "available_terminals": nairapoint_qs.filter(is_assigned=False).count(),
                    "last_updated": timezone.now()
                },

            {
                    "id": 3,
                    "device_name": "LibertyPay",
                    "number_of_terminals": npsb_qs.count(),
                    "available_terminals": npsb_qs.filter(is_assigned=False).count(),
                    "last_updated": timezone.now()
                },

            {
                    "id": 4,
                    "device_name": "Paydia",
                    "number_of_terminals": paydia_qs.count(),
                    "available_terminals": paydia_qs.filter(is_assigned=False).count(),
                    "last_updated": timezone.now()
                },

                {
                    "id": 5,
                    "device_name": "HorizonPay",
                    "number_of_terminals": horizon_pay_qs.count(),
                    "available_terminals": horizon_pay_qs.filter(is_assigned=False).count(),
                    "last_updated": timezone.now()
                }

            ]
        # stock_providers = {
       
        # }
        # providers_list.append(stock_providers)
        response = {
            "overview": {
                "total_terminals": total_terminals,
                "number_of_providers": 5
            },
            "providers": providers_list,
            "count": len(providers_list)
        }
        return response

class DeviceStockDetails:
    def get_device_stock_details(request, queryset, serializer_class, start_date, end_date, id):
        if id == 1:
            stocks = queryset.filter(Q(date_added__range=[start_date, end_date]) & Q(device_name="LIBERTYPAYPLUS") |
                Q(device_name="LIBERTYPAY"))

        elif id == 2:
            stocks = queryset.filter(Q(date_added__range=[start_date, end_date]) & Q(device_name="9NAIRAPOINT"))

        elif id == 3:
            stocks = queryset.filter(Q(date_added__range=[start_date, end_date]) & Q(device_name="NPSB"))

        elif id == 4:
            stocks = queryset.filter(Q(date_added__range=[start_date, end_date]) & Q(device_name="PAYDIA"))

        elif id == 5:
            stocks = queryset.filter(Q(date_added__range=[start_date, end_date]) & Q(device_name="HORIZONPAY") | Q(device_name="HOIRZONPAY"))

        total_terminals = stocks.count()
        available_terminals = stocks.filter(is_assigned=False).count()
        terminal_name = stocks.last()

        stocks = Paginator.paginate(request=request, queryset=stocks)
        serializer = serializer_class(stocks, many=True)

        response = {
            "overview": {
                "device_name": terminal_name.device_name if terminal_name else "",
                "total_terminals": total_terminals,
                "available_stock": available_terminals
            },

            "terminal_details": serializer.data,
            "count": len(serializer.data)
        }
        return response


class ZoneStockDetail:
    def get_zone_stock_detail(request, serializer_class, start_date, end_date, id):
        # try:
        zone = Zone.objects.get(id=id)
        # except Zone.DoesNotExist:
        #     return {"message":"This detail does not exist"}

        zone_branches = zone.branch_set.all()
        stocks = Stock.objects.filter(Q(date_added__range=[start_date, end_date]) & Q(zone=zone) & \
                        Q(terminal_id__isnull=False))
        stocks = Paginator.paginate(request=request, queryset=stocks)
        stocks_count = Stock.objects.filter(Q(date_added__range=[start_date, end_date]) & Q(zone=zone) & \
                        Q(terminal_id__isnull=False)).count()
        serializer = serializer_class(stocks, many=True)

        terminals_list = []
        for terminal in serializer.data:
            data = {
                "id": terminal["id"],
                "provider": terminal["device_name"],
                "serial_number": terminal["serial_number"],
                "model_number": terminal["model_number"],
                "terminal_id": terminal["terminal_id"],
                "stock_location": terminal["stock_location"],
                "payment_status": "paid" if terminal["payment_status"]=="PAID" else "not paid",
                "is_assigned": "Assigned" if terminal["is_assigned"] else "Re-assign"
            }
            terminals_list.append(data)

        response = {
            "overview": {
                "state": zone.name,
                "number_of_branches": zone_branches.count(),
                "terminal_in_stock": stocks_count
            },
            "stock_list": terminals_list,
            "count": len(serializer.data)
        }
        return response

class StateAllTerminals:
    def get_state_all_terminals(request, serializer_class, zone, start_date, end_date):
        branch_set = zone.branch_set.all()
        stocks = Stock.objects.filter(Q(date_added__range=[start_date, end_date]) & \
                Q(zone=zone) & Q(terminal_id__isnull=False))

        delivered_stocks_count = stocks.filter(stock_location="HEAD_OFFICE").count()
        terminals_in_stock = stocks.count()

        stocks = Paginator.paginate(request=request, queryset=stocks)
        serializer = serializer_class(stocks, many=True)

        response = {
            "overview": {
                "state": zone.name,
                "number_of_branch": branch_set.count(),
                "terminals_in_stock": terminals_in_stock,
                "number_of_delivered": delivered_stocks_count
            },
            "stock_list": serializer.data,
            "count": len(serializer.data)
        }
        return response

class IndividualTerminalTrail:
    def get_individual_terminal_trail(serializer_class, stock):
        serializer = serializer_class(stock)
        sales_rep = SalesRep.objects.filter(branch=stock.branch).last()

        at_headoffice_count = 0
        with_ro_count = 0
        with_agent_count = 0
        at_zone_count = 0
        at_branch_count = 0
        in_transit_count = 0
    
        if stock.stock_location=="HEAD_OFFICE":
            at_headoffice_count+=1
        elif stock.stock_location=="WITH_RO":
            with_ro_count+=1
        elif stock.stock_location=="WITH_AGENT":
            with_agent_count+=1
        elif stock.stock_location=="IN_STATE":
            at_zone_count+=1
        elif stock.stock_location=="AT_BRANCH":
            at_branch_count+=1
        elif stock.stock_location=="IN_TRANSIT":
            in_transit_count+=1

        trail_details = {
            "head_office": {
                "quantity": at_headoffice_count,
                "date_arrived": str(stock.date_added)
            },
            "in_transit": {
                "quantity": in_transit_count,
                "date_arrived": str(timezone.now())
            },
            "zone": {
                "quantity": at_zone_count,
                "date_arrived": str(timezone.now())
            },
            "branch": {
                "quantity": at_branch_count,
                "date_arrived": str(timezone.now())
            },
            "with_ro": {
                "quantity": with_ro_count,
                "date_arrived": str(timezone.now())
            },
            "delivered_to_agent": {
                "quantity": with_ro_count,
                "date_arrived": str(timezone.now())
            }
        }
        
        details = {
            "zone": stock.zone.name,
            "branch": stock.branch.branch_name if stock.branch else "",
            "salesrep": sales_rep.sales_rep.get_full_name() if sales_rep else "",
            "terminal_id": stock.terminal_id,
            "status": stock.status,
            "date_paid": "",
            "trail_date_start": stock.date_added,
            "date_delivered": stock.date_assigned if stock.date_assigned else ""
        }
        response = {
            "overview": details,
            "trail_tree": trail_details
            }
        return response

class ZoneHeadBranches:
    def get_zone_head_branches(zone, start_date, end_date):
        zone_stock_qs = Stock.objects.filter(zone=zone)
        total_terminals_in_stock = zone_stock_qs.count()

        stock_requests_qs = StockRequest.objects.filter(branch__zone=zone)
        total_stock_requests = stock_requests_qs.count()

        delayed_terminals = Stock.objects.filter(Q(payment_status="PAID") & Q(stock_location="HEAD_OFFICE")).count()

        zone_branches_qs = Branch.objects.filter(zone=zone)
        number_of_branches = zone_branches_qs.count()

        branch_list = []

        for branch in zone_branches_qs:
            number_of_terminals = Stock.objects.filter(date_added__range=[start_date, end_date], branch=branch).count()
            number_of_sales_reps = SalesRep.objects.filter(created_at__range=[start_date, end_date], branch=branch).count()
            branch_name = branch.branch_name

            data = {
                "id": branch.id,
                "branch_name": branch_name,
                "number_of_terminals": number_of_terminals,
                "number_of_sales_reps": number_of_sales_reps
            }

            branch_list.append(data)

        response = {
            "overview": {
                "terminals_in_stock": total_terminals_in_stock,
                "terminal_requests": total_stock_requests,
                "delayed_terminals": delayed_terminals,
                "state": zone.name,
                "number_of_branches": number_of_branches
            },

            "branches": branch_list,
            "count": len(branch_list)
        }
        return response

class StockHistoryDetails:
    def get_stock_history_detail(stock_qs, start_date, end_date):
        total_terminals_in_stock = stock_qs.count()
        last_date_added = stock_qs.last().date_added

        terminal_requests_qs = TerminalRequest.objects.filter(date_created__range=[start_date, end_date])
        terminal_requests_count = terminal_requests_qs.count()

        paid_stock_qs = terminal_requests_qs.filter(Q(status="ASSIGNED") | Q(status="PICKED_UP"))
        delivered_stock_qs = terminal_requests_qs.filter(status = "PICKED_UP")
        delayed_delivery_stock_qs = terminal_requests_qs.filter(~Q(status = "PICKED_UP") & Q(status="ASSIGNED"))

        paid_stock_count = paid_stock_qs.count()
        delivered_stock_count = delivered_stock_qs.count()
        delayed_delivery_count = delayed_delivery_stock_qs.count()

        pick_up_days_qs = terminal_requests_qs.filter(Q(status = "PICKED_UP")).values(f"date_created", "pick_up_date"). \
            annotate(pick_up_days = F("pick_up_date") - F("approved_date"))

        average_pickup_days = pick_up_days_qs.aggregate(Avg("pick_up_days"))["pick_up_days__avg"]

        response = {
            "terminals_in_stock": total_terminals_in_stock,
            "last_date_added": last_date_added,

            "deliveryStatus": {
                "paid": paid_stock_count,
                "received": delivered_stock_count
            },

            "terminal_requests_count": terminal_requests_count,
            "average_delivery_time": f"{average_pickup_days.days * 24}" + " hours" if average_pickup_days else 0,
            "delayed_deliveries": delayed_delivery_count
            }
        return response

class StockHistoryDetail:
    def get_stockhistory_detail(queryset, start_date, end_date):
        if id == "1":
            stocks = queryset.filter(Q(date_added__range=[start_date, end_date]) & Q(device_name__in=["LIBERTYPAY", "LIBERTYPAYPLUS"]))
        elif id == "2":
            stocks = queryset.filter(Q(date_added__range=[start_date, end_date]) & Q(device_name="9NAIRAPOINT"))
        elif id == "3":
            stocks = queryset.filter(Q(date_added__range=[start_date, end_date]) & Q(device_name="NPSB"))
        elif id == "4":
            stocks = queryset.filter(Q(date_added__range=[start_date, end_date]) & Q(device_name="PAYDIA"))
        elif id == "5":
            stocks = queryset.filter(Q(date_added__range=[start_date, end_date]) & Q(device_name="HORIZONPAY") | Q(device_name="HOIRZONPAY"))
        else:
            response = {"errror": "View does not exist"}
            return response


        branches = Branch.objects.all()
        terminals_list = []
        for branch in branches:
            terminals = stocks.filter(branch=branch)
            for terminal in terminals:
                data  = {
                    "id": terminal.id,
                    "device_name": terminal.device_name,
                    "branch": branch.branch_name,
                    "last_updated": terminal.date_added,
                    "number_of_terminals": len(terminals)
                    }
                terminals_list.append(data)

        response = {
            "stocks": terminals_list,
            "count": len(terminals_list)
        }
        return response

class BranchStockDetails:
    def get_branch_stock_details(request, queryset, branch, start_date, end_date):
        stocks = queryset.filter(date_added__range=[start_date, end_date], branch=branch)
        stocks = Paginator.paginate(request=request, queryset=stocks)

        sn = 0
        terminals_list = []
        for terminal in stocks:
            sn+=1
            data  = {
                "s/n": sn,
                "id": terminal.id,
                "branch": branch.branch_name,
                "date_added": terminal.date_added,
                "serial_number": terminal.serial_number,
                "terminal_id": terminal.terminal_id,
                "model_number": terminal.model_number,
                }
            terminals_list.append(data)

        response = {
            "stocks": terminals_list,
            "count": len(terminals_list)
        }
        return response


class ZoneStockRequestHistoryDet:
    def get_zone_stock_history(request, queryset, zone, start_date, end_date):
        branches =  Branch.objects.filter(zone=zone)
        branches = Paginator.paginate(request=request, queryset=branches)

        branch_terminals_list = []
        for branch in branches:
            stocks = queryset.filter(date_created__range=[start_date, end_date], branch=branch)
            stock_requests = queryset.filter(branch=branch)
            salesrep = SalesRep.objects.filter(branch=branch)
         
            data  = {
                "id": branch.id,
                "branch": branch.branch_name,
                "number_of_terminals": len(stocks),
                "number_of_salesrep": len(salesrep),
                "last_updated": datetime.strftime(timezone.now(), "%Y-%m-%d")
                }
            branch_terminals_list.append(data)

        response = {
            "overview": {
                "zone": zone.name,
                "total_requests": len(stock_requests) if branches else 0
            },
            "stocks": branch_terminals_list,
            "count": len(branch_terminals_list)
            }
        return response


class StockHistoryDetailMore:
    def get_stockhistory_detail_more(serializer_class, stock_history):
        serializer = serializer_class(stock_history, many=False)

        terminals =  serializer.data["stocks"]
        # terminals_list = ast.literal_eval(terminals)
        if terminals:
            terminals_list = json.loads(terminals)

            sn = 0
            terminal_details = []
            for terminal in terminals_list: 
                agent = User.objects.filter(terminal_id=terminal).last()
                sales_rep = SalesRep.objects.filter(sales_rep=agent).last()
                sn+=1   
                data = {
                    "sn": sn,
                    "id": terminal,
                    "device_name": "LibertyPay",
                    "terminal_id": terminal,
                    "paid_date": datetime.strftime(timezone.now(), "%Y-%m-%d"),
                    "delivery_date": datetime.strftime(timezone.now(), "%Y-%m-%d"),
                    "sales_rep": sales_rep.sales_rep.get_fullname() if sales_rep else "",
                    "status": "received"
                    }
                terminal_details.append(data)
        else:
            terminal_details = []
            terminals_list = []

        response = {
                "terminals": terminal_details,
                "count": len(terminals_list)
                }
        return response


class TransactionAmountDay:
    def __init__(self):
        self.previous_day = DateUtility().previous_day
        self.date_today = DateUtility().date_today
        self.today = DateUtility().today
        self.previous_month_end = DateUtility().previous_month_end
        self.init_start = DateUtility().init_start

    def get_transaction_amount_daily(self, request, start_date, end_date):
        float_user = WalletSystem.get_float_user()
        user_role = request.user.role
        base_transactions = Transaction.objects.filter(~Q(user = float_user, transaction_leg__in = ["INTERNAL", "COMMISSIONS"])).filter(
            transaction_type__in = all_possible_transaction_list, status="SUCCESSFUL", user__terminal_id__isnull=False, 
            user__role=user_role, is_reversed = False)

        # Overview
        transaction_amount_today = base_transactions.filter(Q(date_created__range=[self.previous_day, self.today])
                                        ).aggregate(Sum("amount"))["amount__sum"]

        transaction_amount_overall = base_transactions.aggregate(Sum("amount"))["amount__sum"]

        previous_day_transaction_amount_total = base_transactions.filter(
                                        date_created__range=[self.init_start, self.previous_day]
                                        ).aggregate(Sum("amount"))["amount__sum"]

        previous_month_transaction_amount_total = base_transactions.filter(
                                        date_created__range=[self.init_start, self.previous_month_end]
                                        ).aggregate(Sum("amount"))["amount__sum"]

        transaction_amount_change = get_percentage_diff(
                                    current = transaction_amount_overall, 
                                    previous = (previous_month_transaction_amount_total 
                                    if previous_month_transaction_amount_total else 0))

        transaction_amount_change_today = get_percentage_diff(
                                        current = transaction_amount_overall, 
                                        previous = previous_day_transaction_amount_total
                                        )

        transaction_counts_today = base_transactions.filter(
                                        date_created__range=[self.previous_day, self.today]
                                        ).count()

        previous_day_transaction_count_total = base_transactions.filter(
                                        date_created__range=[self.init_start, self.previous_day]
                                        ).count()

        previous_month_transaction_count_total = base_transactions.filter(
                                        date_created__range=[self.init_start, self.previous_month_end]
                                        ).count()

        transaction_count_overall = base_transactions.count()

        transaction_count_change = get_percentage_diff(
                                   current = transaction_count_overall, 
                                   previous = previous_month_transaction_count_total)
        
        transaction_count_change_today = get_percentage_diff(
                                         current=transaction_count_overall, 
                                         previous=previous_day_transaction_count_total)

        # Top 3
        discrete_transaction_amounts_overall = list(base_transactions.filter(date_created__range=[start_date, end_date]
                                                ).values("user__terminal_id", "user__first_name", "user__last_name", 
                                                "user__email").annotate(total_amounts=(Sum("amount"))))

        discrete_transaction_count_overall = list(base_transactions.filter(date_created__range=[start_date, end_date]
                                            ).values("user__terminal_id", "user__first_name", "user__last_name", 
                                            "user__email").annotate(transaction_counts=Count("amount")))
        
        amounts_sorted_list = sorted(discrete_transaction_amounts_overall, key=lambda d: d["total_amounts"])[::-1][:10]
        counts_sorted_list = sorted(discrete_transaction_count_overall, key=lambda d: d["transaction_counts"])[::-1][:10]

        top3_amounts = []
        for agent in amounts_sorted_list:
            data = {
                "terminal_id": agent["user__terminal_id"],
                "agent_name": agent["user__first_name"] + " " + agent["user__last_name"],
                "amount": agent["total_amounts"],
                "agent_email": agent["user__email"]
            }
            top3_amounts.append(data)

        top3_counts = []
        for agent in counts_sorted_list:
            data = {
                "terminal_id": agent["user__terminal_id"],
                "agent_name": agent["user__first_name"] + " " + agent["user__last_name"],
                "transaction_count": agent["transaction_counts"],
                "agent_email": agent["user__email"],
                }
            top3_counts.append(data)

        response = {
            "transactionAmount": {
                "total_transactions": transaction_amount_overall if transaction_amount_overall else 0.00,
                "today_transactions": transaction_amount_today if transaction_amount_today else 0.00,
                "percentage": str(transaction_amount_change.get("percentage")),
                "change": str(transaction_amount_change.get("change")),
                "today_amount_change": {
                    "percentage": str(transaction_amount_change_today.get("percentage")),
                    "change": transaction_amount_change_today.get("change")
                },
                "top3_transactions": top3_amounts,
            },

            "transactionCount": {
                "total_transaction_count": transaction_count_overall,
                "today_transaction_count": transaction_counts_today,
                "percentage": str(transaction_count_change.get("percentage")),
                "change": str(transaction_count_change.get("change")),
                "today_count_change": {
                    "percentage": str(transaction_count_change_today.get("percentage")),
                    "change": transaction_count_change_today.get("change")
                },
                "top3_counts": top3_counts
            }
        }
        return response


class PosAgentsDetails:
    def get_pos_agent_details(request, start_date, end_date, jresponse, status, status_filter):
        float_user = WalletSystem.get_float_user()
        user_role = request.user.role
        base_agents_transactions = Transaction.objects.filter(~Q(transaction_leg__in=["INTERNAL", "COMMISSIONS"], 
                                    user=float_user)).filter(
                                    transaction_type__in = all_possible_transaction_list, 
                                    status = "SUCCESSFUL", 
                                    is_reversed = False)

        if not status_filter or status_filter == "":
            status_choice_list = ["ACTIVE", "INACTIVE", "PARTIALLY_INACTIVE",
                                "RECOVERY", "RECOVERED", "SUSPENDED", "DORMANT"]
        else:
            status_choice_list = [status_filter]

        agents = User.objects.filter(Q(date_joined__range=[start_date,end_date], 
                terminal_status__in=status_choice_list, role=user_role) & 
                Q(terminal_id__isnull=False) & ~Q(terminal_id=""))
        if not agents:
            response = {
                "agent_details": [],
                "count": 0
            }
            return jresponse(response, status=status.HTTP_404_NOT_FOUND)
        agents = Paginator.paginate(request=request, queryset=agents)

        all_agents = []
        for agent in list(agents):
            agent_transactions_count = base_agents_transactions.filter(user__terminal_id=agent.terminal_id).distinct().count()
            agent_transactions_amount = base_agents_transactions.filter(user__terminal_id=agent.terminal_id
                                        ).distinct().aggregate(Sum("amount"))["amount__sum"]

            number_of_days_since_joined = (timezone.now() - agent.date_joined).days

            average_transaction_per_day = agent_transactions_count / (number_of_days_since_joined if number_of_days_since_joined else 1)  #zero division error fixed

            agent_detail = {
                "id": agent.id,
                "terminal_id": agent.terminal_id,
                "name": agent.first_name + " " + agent.last_name,
                "status": "suspended" if agent.is_suspended or agent.terminal_suspended else agent.terminal_status,
                "email": agent.email,
                "date_joined": agent.date_joined,
                "average_transaction_per_day": average_transaction_per_day,
                "transaction_count": agent_transactions_count,
                "total_transaction": agent_transactions_amount if agent_transactions_amount else 0.00,
            }
            all_agents.append(agent_detail)

        response = {
            "agent_details": all_agents,
            "count": len(all_agents)
        }
        return response


class PosAgentWallet:
    def get_pos_agent_wallet(id):
        agent = User.objects.get(id=id)
        agent_transactions_count = Transaction.objects.filter(~Q(transaction_leg__in=["INTERNAL", "COMMISSIONS"], is_reversed=True)
                                    ).filter(user__terminal_id = agent.terminal_id, 
                                    transaction_type__in = all_possible_transaction_list, status="SUCCESSFUL").count()

        # Agent SalesRep Details
        salesrep_upline_code = agent.sales_rep_upline_code
        if salesrep_upline_code is not None:
            agent_salesrep = SalesRep.objects.filter(sales_rep_code=salesrep_upline_code).first()
            agent_salesrep_name = agent_salesrep.sales_rep.first_name + " " + agent_salesrep.sales_rep.last_name
        else:
            agent_salesrep_name = ""

        agent_profile = AgentProfile.objects.filter(user=agent).last()
        bills_utilities_commission = WalletSystem.objects.filter(Q(user=agent) & Q(wallet_type="COMMISSIONS"))

        #KYC PERCENTGE COMPLETED CALCULATIONS
        kyc_level = agent.kyc_level
        kyc_status = kyc_level/3 * 100

        #AGENT BANK ACCOUNT DETAILS
        agent_account = list(AccountSystem.get_default_provider_accounts(user=agent).values())[0]
        wallet_balance = WalletSystem.objects.filter(user=agent, wallet_type="COLLECTION").last().available_balance

        # agent_kyc = KYCTable.objects.filter(user=agent)
        agent_bvn_num = BVNDetail.objects.get(kyc__user=agent).bvn_number

        response = []
        data = {
            "id": agent.id,
            "terminal_id":agent.terminal_id,
            "name": agent.first_name + " " + agent.last_name,
            "username": agent.username,
            "email": agent.email,
            "agent_id": agent.unique_id,
            "phone_number": agent.phone_number,
            "status": "suspended" if agent.terminal_suspended else agent.terminal_status,
            "date_joined": agent.date_joined,
            "last_login": agent.last_login,
            "transaction_counts": agent_transactions_count,
            "Address": {
                "state": agent.state if agent.state else "",
                "city": agent_profile.city if agent_profile and agent_profile.city else "",
                "lga": agent.lga if agent.lga else "",
                "street": agent.street if agent.street else ""
            },
            "agent_map_address": agent.lga if agent.lga else agent.street + ", " + agent.state if agent.state else "",

            "gender": agent.gender if agent.gender else "",
            "marital_status": agent.marital_status if agent.marital_status else "",
            "bvn": agent_bvn_num if agent_bvn_num else "",
            "agent_salesrep": agent_salesrep_name,

            "AccountDetails": {
                "bank_name": agent_account["bank_name"],
                "account_number": agent_account["account_number"]
            },

            "kyc_status_percentage": kyc_status
            }

        general = {
            "Wallets": wallet_balance,
            "bills_utilities_commission": bills_utilities_commission.available_balance if not AccountSystem.DoesNotExist else 0,
        }

        response = {
            "agent_details": data,
            "general": general
        }
        return response


class PosAgentTransactions:
    def get_pos_agent_transactions(request, queryset, start_date, end_date, id):
        agent = queryset.get(id=id)
        transactions = Transaction.objects.filter(~Q(transaction_leg__in = ["INTERNAL", "COMMISSIONS"])).filter(
                                                  date_created__range = [start_date, end_date], 
                                                  transaction_type__in = all_possible_transaction_list, 
                                                  user__terminal_id__isnull = False, status="SUCCESSFUL", 
                                                  user = agent, is_reversed = False).order_by("-date_created")

        transaction_count = transactions.count()

        # Agent SalesRep Details
        salesrep_upline_code = agent.sales_rep_upline_code
        if salesrep_upline_code is not None:
            agent_salesrep = SalesRep.objects.filter(sales_rep_code=salesrep_upline_code).first()
            agent_salesrep_name = agent_salesrep.sales_rep.first_name + " " + agent_salesrep.sales_rep.last_name
        else:
            agent_salesrep_name = ""

        transactions = Paginator.paginate(request=request, queryset=transactions)
        all_transactions = []

        for transaction in list(transactions):
            transaction_detail = {
                "id": transaction.id,
                "description": transaction.transaction_type,
                "amount": transaction.amount,
                "transaction_method": transaction.transaction_mode,
                "reference_id": transaction.liberty_reference,
                "date_time": transaction.date_created,
                "receiver": transaction.beneficiary_nuban,
                "status": transaction.status
            }
            all_transactions.append(transaction_detail)

        data = {"agentDetails": {
            "id": agent.id,
            "username": agent.username,
            "agent_id": agent.unique_id,
            "phone_number": agent.phone_number,
            "terminal_id":agent.terminal_id,
            "name": agent.first_name + " " + agent.last_name,
            "email": agent.email,
            "date_joined": agent.date_joined,
            "last_login": agent.last_login,
            "agent_salesrep": agent_salesrep_name
        }
        }
        response = {
            "agent_details": data,
            "transactions_list": all_transactions,
            "count": len(all_transactions)
        }
        return response


class PosAgentsActiveSessions:
    def get_agent_active_sessions(request, queryset, start_date, end_date, id):
        agent = queryset.get(id=id)
        user_role = agent.role

        logs = AdminDashboardLogHistory.objects.filter(action_time__range=[start_date,end_date], 
                                        user_id=agent.id, user__role=user_role).distinct()
        logs = Paginator.paginate(request=request, queryset=logs)

        all_logs = []
        for log in logs:
            last_login = agent.last_login
            action_time =  log.action_time
            if last_login and action_time:
                session_duration = action_time - last_login
                hours, remainder = divmod(session_duration.seconds, 3600)
                session_duration = f"{hours} hours, {remainder} seconds"
            else:
                session_duration = 0
            data = {
                "log_time": log.action_time,
                "session_duration": session_duration,
                "device": "POS",
                "location": agent.street if agent.street else " " + agent.lga if \
                            agent.lga else " " + agent.state if agent.state else " "
            }
            all_logs.append(data)

        response = {
            "agent_logs": all_logs,
            "count": len(all_logs)
        }
        return response


class PosAgentsDisputesList:
    def get_pos_agents_disputes_list(request, queryset, start_date, end_date, id):
        agent = queryset.get(id=id)
        disputes = Dispute.objects.filter(Q(date_created__range=[start_date, end_date]) & Q(user_id=agent.id))
        disputes = Paginator.paginate(request=request, queryset=disputes)

        all_disputes = []
        for dispute in disputes:
            data = {
                "id": dispute.id,
                "support_id": dispute.support_id,
                "dispute_type": dispute.dispute_type,
                "disputed_on": dispute.date_created,
                "respond_time": dispute.respond_time,
                "resolved_by": dispute.resolved_by,
                "status": "Resolved" if dispute.is_resolved else "Need Response"
            }
            all_disputes.append(data)
        response = {
            "agent_disputes": all_disputes,
            "count": len(all_disputes)
        }
        return response


class PosAgentFormDetails:
    def get_pos_agent_form_details(request, queryset, id):
        agent = queryset.get(id=id)
        agent_profile = agent
        # agent_profile = User.objects.get(user=agent)
        agent_transactions_count = Transaction.objects.filter(~Q(transaction_leg__in = ["INTERNAL", "COMMISSIONS"])
                                    ).filter(user__terminal_id=agent.terminal_id,
                                    transaction_type__in=all_possible_transaction_list).count()

        bvn_details = BVNDetail.objects.get(kyc__user=agent)
        guarantor_details = GuarantorDetail.objects.get(kyc__user=agent)

        agent_detail = {
            "id": agent.id,
            "personal_info": {
            "username": agent.username,
            "firstname": agent.first_name,
            "lastname": agent.last_name,
            "phone_number": agent.phone_number,
            "email": agent.email,
            "marital_status": agent.marital_status,
            "date_of_birth": bvn_details.bvn_birthdate,
            "address": agent_profile.street,
            "state": agent_profile.state,
            "lga": agent_profile.lga,
            "agent_id": agent.unique_id,
            "last_login": agent.last_login,
            "terminal_id": agent.terminal_id,
            "status": "Suspended" if agent.is_suspended else "Active"
            },

            "transaction_counts": agent_transactions_count,

            "next_of_kin": {
                    "name": guarantor_details.next_of_kin_name,
                    "address": guarantor_details.next_of_kin_address,
                    "relationship": guarantor_details.next_of_kin_relationship,
            },

            "guarantor_information": {
                "name": guarantor_details.guarantor_name,
                "address": guarantor_details.guarantor_address,
                "phone_number": guarantor_details.guarantor_phone_number,
                "email": guarantor_details.guarantor_email,
                "occupation": guarantor_details.guarantor_occupation,
                "valid_identity_card": ""
            }
            }

        response = {
            "agent_details": agent_detail
        }
        return response


class ZonesDeliveryRequest:
    def get_zone_delivery_request(request, queryset, serializer_class, start_date, end_date):
        zones =  queryset.all()
        serializer = serializer_class(zones, many=True)
        zones = Paginator.paginate(request=request, queryset=zones)        
        
        stock_request_details = []
        for zone in zones:
            stock_request = StockRequest.objects.filter(date_created__range=[start_date, end_date], zone=zone)
            data =    {
                        "id": zone.id,
                        "zone": zone.name,
                        "number_of_devices": stock_request.count(),
                        "last_updated": stock_request.latest("date_created").date_created if stock_request else ""
                    }
            stock_request_details.append(data)
        response = {
            "requests_list": stock_request_details,
            "count": len(stock_request_details)
        }
        return response

class ZoneDeliveryRequestView:
    def get_zone_delivery_request(request, queryset, serializer_class, start_date, end_date, zone):
        zone_request = queryset.filter(date_added__range=[start_date, end_date], zone=zone, delivery_requested=True)
        all_zone_requests = queryset.filter(zone=zone)
        zone_request = Paginator.paginate(request=request, queryset=zone_request)
        serializer = serializer_class(zone_request, many=True)

        stock_request_details = []
        for stock_request in serializer.data:
            zone = Zone.objects.get(id=stock_request["zone"])
            data = {
                    "id": stock_request["id"],
                    "provider": stock_request["device_name"],
                    "serial_number": stock_request["serial_number"],
                    "model_number": stock_request["model_number"],
                    "payment_status":" ".join(stock_request["payment_status"].split("_")).capitalize(),
                    "action": "Assign" if stock_request["is_assigned"] == "false" else "Assigned",
                    "status": " ".join(stock_request["stock_location"].split("_")).capitalize()
                    }
            stock_request_details.append(data)
        response = {
            "overview": {
                "state": zone.name,
                "number_of_terminals": all_zone_requests.count()
                },
            "requests_list": stock_request_details,
            "count": len(stock_request_details)
        }
        return response


class ZoneStockInRequest:
    def get_zone_stockin_request(queryset, serializer_class, zone):
        branches_qs = Branch.objects.filter(zone=zone)
        terminal_request = queryset.filter(zone=zone).first()
        terminal_requests_qs = queryset.filter(zone=zone)
        serializer = serializer_class(terminal_request)

        assigned_terminals_qs = Stock.objects.filter(zone=zone)
        sold_terminals_qs = assigned_terminals_qs.filter(stock_location="WITH_AGENT")
        pickedup_terminals_qs = assigned_terminals_qs.filter(stock_location="IN_STATE")
        active_terminals_qs = assigned_terminals_qs.filter(status="ACTIVE")
        inactive_terminals_qs = assigned_terminals_qs.filter(status="INACTIVE")
        suspended_terminals_qs = assigned_terminals_qs.filter(status="SUSPENED")

        try:
            sold_terminals_percentage = sold_terminals_qs.count() / assigned_terminals_qs.count() * 100
        except ZeroDivisionError:
            sold_terminals_percentage = 0
        try:
            active_terminals_percentage = active_terminals_qs.count() / assigned_terminals_qs.count() * 100
        except ZeroDivisionError:
            active_terminals_percentage = 0
        try:
            inactive_terminals_percentage = inactive_terminals_qs.count() / assigned_terminals_qs.count() * 100
        except ZeroDivisionError:
            inactive_terminals_percentage = 0
        try:
            suspended_terminals_percentage = suspended_terminals_qs.count() / assigned_terminals_qs.count() * 100
        except ZeroDivisionError:
            suspended_terminals_percentage = 0

        request_details = {
            "requested_by": zone.name,
            "number_of_branches": branches_qs.count(),
            "date": terminal_request.date_created if terminal_request else ""
            }

        requested_devices = {
            "libertypay": serializer.data["liberty_pay_plus"] if serializer.data["liberty_pay_plus"] else 0,
            "9nairapoint": serializer.data["nine_naira_point"] if serializer.data["nine_naira_point"] else 0,
            "npsb": serializer.data["npsb"] if serializer.data["npsb"] else 0,
            "paydia": serializer.data["paydia"] if serializer.data["paydia"] else 0,
            "horizonpay": serializer.data["hoirzon_pay"] if serializer.data["hoirzon_pay"] else 0
            }
        
        analytics = {
            "assigned_terminal_ids": assigned_terminals_qs.count(),
            "pickedup_terminal_ids": pickedup_terminals_qs.count(),
            "sold_terminals": sold_terminals_percentage,
            "active_terminals": active_terminals_percentage,
            "inactive_terminals": inactive_terminals_percentage,
            "suspended_terminals": suspended_terminals_percentage
            }

        stock_request_details = []
        for branch in branches_qs:
            branch_stock_request = queryset.filter(zone=zone, branch=branch)
            approved_branch_stock_request = queryset.filter(zone=zone, branch=branch, is_approved=True)
            branch_serializer = serializer_class(branch_stock_request, many=True)

            data = {
                    "id": branch.id,
                    "branch": branch.branch_name,
                    "number_of_terminals": "",
                    "approved_devices": len(approved_branch_stock_request),
                    # "stock_request_location": stock_request["stock_request_location"],
                    "request_time": branch_stock_request.last().date_created if branch_stock_request else " ",
                    # "zone": zone.name,
                    # "request_id": stock_request["request_id"]
                    }
            stock_request_details.append(data)
        response = {
            "request_details": request_details,
            "requested_devices": requested_devices,
            "analytics": analytics,
            "branch_details": stock_request_details,
            "count": len(stock_request_details)
            }
        return response


class BranchStockInRequest:
    def get_branch_stockin_request(queryset, serializer_class, branch):
        # branches_qs = Branch.objects.filter(zone=zone)
        terminal_request = queryset.filter(branch=branch).last()
        serializer = serializer_class(terminal_request)

        assigned_terminals_qs = Stock.objects.filter(branch=branch)
        sold_terminals_qs = assigned_terminals_qs.filter(stock_location="WITH_AGENT")
        pickedup_terminals_qs = assigned_terminals_qs.filter(stock_location="IN_STATE")
        active_terminals_qs = assigned_terminals_qs.filter(status="ACTIVE")
        inactive_terminals_qs = assigned_terminals_qs.filter(status="INACTIVE")
        suspended_terminals_qs = assigned_terminals_qs.filter(status="SUSPENED")

        try:
            sold_terminals_percentage = sold_terminals_qs.count() / assigned_terminals_qs.count() * 100
        except ZeroDivisionError:
            sold_terminals_percentage = 0
        try:
            active_terminals_percentage = active_terminals_qs.count() / assigned_terminals_qs.count() * 100
        except ZeroDivisionError:
            active_terminals_percentage = 0
        try:
            inactive_terminals_percentage = inactive_terminals_qs.count() / assigned_terminals_qs.count() * 100
        except ZeroDivisionError:
            inactive_terminals_percentage = 0
        try:
            suspended_terminals_percentage = suspended_terminals_qs.count() / assigned_terminals_qs.count() * 100
        except ZeroDivisionError:
            suspended_terminals_percentage = 0

        request_details = {
            "requested_by": branch.branch_name if branch else "",
            "date": terminal_request.date_created if terminal_request else "",
            "note": terminal_request.request_msg if terminal_request else ""
            }

        requested_devices = {
            "libertypay": serializer.data.get("liberty_pay_plus", 0) if serializer.data.get("liberty_pay_plus", 0) else 0,
            "9nairapoint": serializer.data.get("nine_naira_point", 0) if serializer.data.get("nine_naira_point", 0) else 0,
            "npsb": serializer.data.get("npsb", 0) if serializer.data.get("npsb", 0) else 0,
            "paydia": serializer.data.get("paydia", 0) if serializer.data.get("paydia", 0) else 0,
            "horizonpay": serializer.data.get("hoirzon_pay", 0) if serializer.data.get("hoirzon_pay", 0) else 0
            }
        
        analytics = {
            "assigned_terminal_ids": assigned_terminals_qs.count(),
            "pickedup_terminal_ids": pickedup_terminals_qs.count(),
            "sold_terminals": sold_terminals_percentage,
            "active_terminals": active_terminals_percentage,
            "inactive_terminals": inactive_terminals_percentage,
            "suspended_terminals": suspended_terminals_percentage
            }

        response = {
            "request_details": request_details,
            "requested_devices": requested_devices,
            "analytics": analytics
            }
        return response

class MerchantDetails:
    def get_merchant_details(request, start_date, end_date, status_choice_list, status, jresponse):
        user_role = request.user.role

        float_user = WalletSystem.get_float_user()
        base_transactions = Transaction.objects.filter(
                            ~Q(user=float_user, transaction_leg__in=["INTERNAL", "COMMISSIONS"])
                            ).filter(type_of_user="MERCHANT", 
                            transaction_type__in = all_possible_transaction_list, is_reversed=False)

        agents = User.objects.filter(Q(date_joined__range=[start_date, end_date], terminal_status__in=status_choice_list) & \
                        Q(terminal_id__isnull=False, type_of_user="MERCHANT") & ~Q(terminal_id=""), role=user_role)
        
        agents = Paginator.paginate(request=request, queryset=agents)

        all_agents = []
        for agent in list(agents):
            agent_transactions_count = base_transactions.filter(user__terminal_id=agent.terminal_id).count()

            agent_transactions_amount = base_transactions.filter(user__terminal_id=agent.terminal_id
                                        ).aggregate(Sum("amount"))["amount__sum"]

            number_of_days_since_joined = (timezone.now() - agent.date_joined).days
            average_transaction_per_day = agent_transactions_count // number_of_days_since_joined  #zero division error fixed

            agent_detail = {
                "id": agent.id,
                "terminal_id": agent.terminal_id,
                "name": agent.first_name + " " + agent.last_name,
                "status": "suspended" if agent.is_suspended else agent.terminal_status,
                "email": agent.email,
                "date_joined": agent.date_joined,
                "average_transaction_per_day": average_transaction_per_day,
                "transaction_count": agent_transactions_count,
                "total_transaction": agent_transactions_amount if agent_transactions_amount else 0.0,
            }
            all_agents.append(agent_detail)
        response = {
            "agent_details": all_agents,
            "count": len(all_agents)
        }
        return response

    
class ServiceQuality:
    def get_service_quality(start_date, end_date, request):
        float_user = WalletSystem.get_float_user()
        user_role = request.user.role

        base_transactions_qs = Transaction.objects.filter(~Q(user=float_user, transaction_leg__in=["INTERNAL", "COMMISSIONS"])
                                ).filter(user__role=user_role, is_reversed=False)

        # DATA PURCHASE
        data_trns_qs = BillsPaymentDumpData.objects.filter(~Q(user=float_user)).filter(
            date_added__range = [start_date, end_date], biller__endswith = "DATA"
            )
        
        data_trns_amount_successful = data_trns_qs.filter(status="SUCCESSFUL").aggregate(Sum("amount"))["amount__sum"]
        data_trns_amount_all = data_trns_qs.aggregate(Sum("amount"))["amount__sum"]
        data_trans_success_rate = (data_trns_amount_successful if data_trns_amount_successful else 0) / \
                                 (data_trns_amount_all if data_trns_amount_all else 1) * 100

        # POS Withdrawals
        cashout_transaction_type_list = ["CARD_TRANSACTION_FUND_TRANSFER", "CARD_TRANSACTION_FUND"]
        pos_withdrawals_qs = base_transactions_qs.filter(date_created__range=[start_date, end_date],
                        transaction_type__in=cashout_transaction_type_list, status__in=["SUCCESSFUL", "FAILED"])        
        pos_withdrawal_amount = pos_withdrawals_qs.aggregate(Sum("amount"))["amount__sum"]
        successful_pos_withdrawal_amount = pos_withdrawals_qs.filter(status="SUCCESSFUL").aggregate(Sum("amount"))["amount__sum"]
        pos_withdrawal_success_rate = ((successful_pos_withdrawal_amount if successful_pos_withdrawal_amount else 0) / 
                                       (pos_withdrawal_amount if pos_withdrawal_amount else 1)) * 100

        # POS Withdrawals USSD
        pos_ussd_withdrawals_qs = base_transactions_qs.filter(date_created__range=[start_date, end_date], transaction_type = "USSD_WITHDRAW")
        pos_ussd_withdrawal_amount = pos_ussd_withdrawals_qs.aggregate(Sum("amount"))["amount__sum"]
        successful_pos_ussd_withdrawal_amount = pos_ussd_withdrawals_qs.filter(status="SUCCESSFUL").aggregate(Sum("amount"))["amount__sum"]
        pos_ussd_withdrawal_success_rate = (successful_pos_ussd_withdrawal_amount if successful_pos_ussd_withdrawal_amount else 0) / \
                                          (pos_ussd_withdrawal_amount if successful_pos_ussd_withdrawal_amount else 1) * 100

        # SendMoney Transactions
        send_money_transaction_type_list = ["SEND_BANK_TRANSFER", "SEND_BUDDY"]
        sendmoney_transactions_qs = base_transactions_qs.filter(date_created__range=[start_date, end_date], 
                                    transaction_type__in=send_money_transaction_type_list) \
                                    .filter(~Q(transaction_leg__in=["COMMISSIONS", "INTERNAL"]))
        sendmoney_transactions_amount = sendmoney_transactions_qs.aggregate(Sum("amount"))["amount__sum"]
        successful_sendmoney_transactions_amount = sendmoney_transactions_qs.filter(status="SUCCESSFUL"
                                                ).aggregate(Sum("amount"))["amount__sum"]
        sendmoney_success_rate = (successful_sendmoney_transactions_amount if successful_sendmoney_transactions_amount else 0) / \
                                 (sendmoney_transactions_amount if sendmoney_transactions_amount else 1) * 100

        # Buy Airtime
        airtime_transactions_qs = base_transactions_qs.filter(date_created__range=[start_date, end_date], transaction_type="AIRTIME_PIN")
        airtime_transactions_amount = airtime_transactions_qs.aggregate(Sum("amount"))["amount__sum"]
        successful_airtime_transactions_amount = airtime_transactions_qs.filter(status="SUCCESSFUL").aggregate(Sum("amount"))["amount__sum"]
        airtime_success_rate = (successful_airtime_transactions_amount if successful_airtime_transactions_amount else 0) / \
                                (airtime_transactions_amount if airtime_transactions_amount else 1) * 100

        # Bills And Utilities
        bills_transactions_qs = base_transactions_qs.filter(date_created__range=[start_date, end_date], transaction_type="BILLS_AND_PAYMENT")
        bills_transactions_amount = bills_transactions_qs.aggregate(Sum("amount"))["amount__sum"]
        successful_bills_transactions_amount = bills_transactions_qs.filter(status="SUCCESSFUL").aggregate(Sum("amount"))["amount__sum"]
        bills_transactions_success_rate = (successful_bills_transactions_amount if successful_bills_transactions_amount else 0) / \
                                          (bills_transactions_amount if bills_transactions_amount else 1) * 100

        response = {
            "pos_withdrawal_success_rate": pos_withdrawal_success_rate,
            "pos_withdrawal_ussd_success_rate": pos_ussd_withdrawal_success_rate,
            "card_withdrawal_nfc_success_rate": 0.00,
            "sendmoney_success_rate": sendmoney_success_rate,
            "buy_airtime_success_rate": airtime_success_rate,
            "buy_data_success_rate": data_trans_success_rate,
            "pay_bills_cable_tv": bills_transactions_success_rate,
            "pay_bills_electricity": 0.00
            }
        return response


# SALES AND SALESREP

class SalesTable:

    def __init__(self, terminal_serial_table, constant_table, sales_rep_table, stock_table, user_role):
        self.terminal_serial_table = terminal_serial_table
        self.constant_table = constant_table
        self.sales_rep_table = sales_rep_table
        self.stock_table = stock_table
        self.user_role = user_role

        self.all_terminal_table = terminal_serial_table.objects.filter(user__role=user_role)
        self.constant = constant_table.get_constant_table_instance()
        self.stock_price = self.constant.stock_price if self.constant else 0
        self.number_of_sales_rep = sales_rep_table.objects.filter(sales_rep__role=user_role).count()
        self.number_of_pos = stock_table.objects.count()
        self.number_of_pos_sold = self.all_terminal_table.count()
        self.total_pos_sales_amount = self.stock_price * self.number_of_pos_sold

        self.previous_year_current_month_start = DateUtility().previous_year_current_month_start
        self.months_list = DateUtility().months_list
        self.months_list_names = DateUtility().months_list_names
        self.date_today = DateUtility().date_today


    def get_sales_overview(self):
        response = {
            "number_of_sales_reps": self.number_of_sales_rep,
            "number_of_pos_devices": self.number_of_pos,
            "total_pos_sold": {
                "count": self.number_of_pos_sold,
                "percentage": 20,
                "change": "up"
                },
            "total_pos_sales": {
                "amount": self.total_pos_sales_amount,
                "percentage": 33,
                "change": "up"
                }
            }
        return response


    def get_sales_table(self, request):
        sales_table_qs = self.all_terminal_table.filter(user__isnull=False)
        sales_table_qs = Paginator.paginate(queryset=sales_table_qs, request=request)

        sales_table = []
        for terminal in sales_table_qs:
            stock_instance = self.stock_table.objects.filter(terminal_id=terminal.terminal_id).last()
            team = stock_instance.sold_by.sales_rep.first_name if stock_instance and stock_instance.sold_by and stock_instance.sold_by else ""
            name = "{} {}".format(terminal.user.first_name if terminal.user.first_name else "", 
                    terminal.user.last_name if terminal.user.last_name else "")

            sales_table.append({
                'name': name,
                'amount': self.stock_price,
                'order': "Android",
                'team': team,
                'date': terminal.date_created,
                "payment_method": stock_instance.payment_method if stock_instance and stock_instance.payment_method else "",
                "payment_status": "successful" if stock_instance and stock_instance.payment_status else "failed"
            })
        return sales_table


    def get_monthly_sales_chart(self, request):
        all_terminals = self.all_terminal_table.filter(
            date_assigned__gt=self.previous_year_current_month_start).values("date_assigned__year", "date_assigned__month"
            ).annotate(total_terminals = Count("date_assigned__month")
            ).order_by("date_assigned__year")
        terminals_past_year_list = [(obj["date_assigned__year"], obj["date_assigned__month"],
                                   obj["total_terminals"]) for obj in all_terminals]

        # generate total number of users m-o-m from previous year
        month_number_of_terminals = []
        for i in self.months_list[:-1]:
            month_count = []
            for terminal in (
            terminals_past_year_list[:len(terminals_past_year_list)-1]
            if terminals_past_year_list[-1][1]==self.date_today.month
            else terminals_past_year_list
            ):
                if terminal[1] == i:
                    month_count.append(terminal[2] * self.stock_price)
                else:
                    month_count.append(0)
            month_number_of_terminals.append(sum(month_count))
            current_month_terminals = terminals_past_year_list[-1][2] * self.stock_price if\
                                      terminals_past_year_list[-1][1]==self.date_today.month else 0
        month_number_of_terminals.append(current_month_terminals) 
        
        resp = {
            'total_pos_sales_amount': self.total_pos_sales_amount,
            "chart_label": self.months_list_names,
            'chart_data': month_number_of_terminals,
        }
        return resp


class SalesBar:
    def __init__(self, all_terminals, sales_reps, terminal_users, transactions):
        self.all_terminals = all_terminals
        self.sales_reps = sales_reps
        self.terminal_users = terminal_users
        self.transactions = transactions

    def get_total_pos_sold_chart(self):
        # Sales rep bar chart
        
        sales_rep_bar = {}
        max_count = 0
        if len(self.sales_reps) > 0: # should always happen
            for sl in self.sales_reps:
                sales_rep_code = sl.sales_rep_code
                rep_agents_count = self.terminal_users.filter(sales_rep_upline_code=sales_rep_code).count()
                name = sl.sales_rep.first_name + " " + sl.sales_rep.last_name
                sales_rep_bar[name] = rep_agents_count
                max_count = rep_agents_count if rep_agents_count > max_count else max_count
        sales_rep_bar_tuples = list(sales_rep_bar.items())
        sales_rep_bar_sorted = sorted(sales_rep_bar_tuples, key=lambda a: a[1], reverse=True)
        sales_rep_bar = dict(sales_rep_bar_sorted)

        # Top3 Salesreps count
        top3_sales_rep_bar = [{"initials": k[0], "name":k, "count": v} for k,v in sales_rep_bar_sorted[:3]]

        # actual sales rep 
        if max_count == 0:
            names = []
            values = []
        else:
            slr_bar = {k: v for  k, v in sales_rep_bar.items()}
            names = slr_bar.keys()
            values = slr_bar.values()
        response = {
            'total_terminals_sold': self.terminal_users.count(),
            "top3": top3_sales_rep_bar,
            'data': {
                'names': names,
                'values': values
            }
        }
        return response

    def get_total_transaction_count(self):
        # Sales rep bar chart
        
        sales_rep_bar = {}
        max_count = 0
        if len(self.sales_reps) > 0: # should always happen
            for sl in self.sales_reps:
                sales_rep_code = sl.sales_rep_code
                rep_agents__transaction_count = self.transactions.filter(user=sl.sales_rep).count()
                name = sl.sales_rep.first_name + " " + sl.sales_rep.last_name
                sales_rep_bar[name] = rep_agents__transaction_count
                max_count = rep_agents__transaction_count if rep_agents__transaction_count > max_count else max_count
        sales_rep_bar_tuples = list(sales_rep_bar.items())
        sales_rep_bar_sorted = sorted(sales_rep_bar_tuples, key=lambda a: a[1], reverse=True)
        sales_rep_bar = dict(sales_rep_bar_sorted)

        # Top3 Salesreps count
        top3_sales_rep_bar = [{"initials": k[0], "name":k, "count": v} for k,v in sales_rep_bar_sorted[:3]]

        # actual sales rep 
        if max_count == 0:
            names = []
            values = []
        else:
            slr_bar = {k: v for  k, v in sales_rep_bar.items()}
            names = slr_bar.keys()
            values = slr_bar.values()
        response = {
            'total_transaction_count': self.transactions.count(),
            "top3": top3_sales_rep_bar,
            'data': {
                'names': names,
                'values': values
            }
        }
        return response


    def get_total_transaction_amount(self):
        # Sales rep bar chart
        
        sales_rep_bar = {}
        max_count = 0
        if len(self.sales_reps) > 0: # should always happen
            for sl in self.sales_reps:
                sales_rep_code = sl.sales_rep_code
                rep_agents__transaction_amount = self.transactions.filter(user=sl.sales_rep
                                                ).aggregate(Sum("amount"))["amount__sum"]
                name = sl.sales_rep.first_name + " " + sl.sales_rep.last_name
                sales_rep_bar[name] = rep_agents__transaction_amount if rep_agents__transaction_amount else 0
                max_count = rep_agents__transaction_amount if rep_agents__transaction_amount and  \
                            rep_agents__transaction_amount > max_count else max_count
        sales_rep_bar_tuples = list(sales_rep_bar.items())
        sales_rep_bar_sorted = sorted(sales_rep_bar_tuples, key=lambda a: a[1], reverse=True)
        sales_rep_bar = dict(sales_rep_bar_sorted)

        # Top3 Salesreps count
        top3_sales_rep_bar = [{"initials": k[0], "name":k, "amount": v} for k,v in sales_rep_bar_sorted[:3]]

        # actual sales rep 
        if max_count == 0:
            names = []
            values = []
        else:
            slr_bar = {k: v for  k, v in sales_rep_bar.items()}
            names = slr_bar.keys()
            values = slr_bar.values()
        response = {
            'total_transaction_amount': self.transactions.aggregate(Sum("amount"))["amount__sum"],
            "top3": top3_sales_rep_bar,
            'data': {
                'names': names,
                'values': values
            }
        }
        return response


class RepresentativeData:
    def get_representative_data(request):
        user_role = request.user.role

        sales_rep_users =  User.objects.select_related("sales_rep").filter(terminal_id__isnull=False, role=user_role)
        sales_rep_transactions = Transaction.objects.select_related("user").filter(status="SUCCESSFUL", 
                                transaction_type__in=all_possible_transaction_list)
        sales_reps =  SalesRep.objects.prefetch_related("sales_rep").filter(sales_rep__role=user_role)
        sales_reps = Paginator.paginate(request=request, queryset=sales_reps)

        rep_data_list = []
        for salesrep in sales_reps:
            devices_sold =  sales_rep_users.filter(salesrep=salesrep).count()
            active =  sales_rep_users.filter(salesrep=salesrep, terminal_status = "ACTIVE").count()
            inactive =  sales_rep_users.filter(salesrep=salesrep, terminal_status = "INACTIVE").count()
            suspended =  sales_rep_users.filter(salesrep=salesrep, terminal_suspended=True).count()
            transactions = sales_rep_transactions.filter(user__email=salesrep)
            data = {
                "name": salesrep.sales_rep.get_full_name(),
                'sales_rep_id': salesrep.sales_rep.unique_id if salesrep.sales_rep.unique_id else "",
                    'device_sold': devices_sold,
                    'active': active,
                    'inactive': inactive,
                    'suspended': suspended,
                    'transaction_amount': transactions.aggregate(Sum("amount"))["amount__sum"] if transactions else 0,
                    'transaction_count': transactions.count() if transactions else 0
                }
            rep_data_list.append(data)

        return {
            'status': 'success',
            'message': '',
            'data':  {
                "representative_data": rep_data_list,
                "count": len(rep_data_list)
            }
        }
    

class SalesRepDetail:
    def get_salesrep_detail(request, jresponse, status):
        user = request.user
        sales_rep_instance = SalesRep.objects.filter(sales_rep=user).last()
        sales_rep_commission_qs = SalesRepCommissionTable.objects.filter(sale_rep=user).last()
        # sales_rep_commission_sum = sales_rep_commission_qs.last("date_created")

        if not sales_rep_instance:
            return jresponse({"error": "Only salesrep or Branch heads can access this view"},
                    status=status.HTTP_404_NOT_FOUND)

        monthly_pos_sales = User.objects.filter(
            sales_rep_upline_code = sales_rep_instance.sales_rep_code
            )

        number_of_branch_sales_reps = SalesRep.objects.filter(branch=sales_rep_instance.branch).count()

        # Branch Head stocks
        branch_stocks_qs = Stock.objects.filter(branch=sales_rep_instance.branch)
        pending_assignments = branch_stocks_qs.filter(is_assigned=False).count()
        assigned = branch_stocks_qs.filter(is_assigned=True).count()
        delivered_terminals = branch_stocks_qs.filter(is_assigned=True, stock_location="WITH_AGENT").count()
        role = "Branch Head" if sales_rep_instance.is_branch_head else "Sales Representative"

        ref = {
            "ussd": sales_rep_instance.ussd_code,
            "form_link": ""
            }

        all_agents = User.objects.filter(sales_rep_upline_code=sales_rep_instance.sales_rep_code)
        total_terminals = {
            "total_terminals": len(all_agents),
            'sold_terminals': len(all_agents),
            'active_terminals': 0,
            'inactive_terminals': 0,
            'suspended_terminals': 0
            }

        terminal_requests = {
            "pending_assignments": 0,
            "assigned_terminal_id": 0,
            "picked_up_terminals": 0
            }

        stocks = {
            "pending_assignments": pending_assignments,  
            "assigned": assigned,
            "delivered_terminals": delivered_terminals,
            "delayed_deliveries": 0  
            }
        
        response = {
            "sales_rep_details": {
                "name": user.get_full_name(),
                "role": role,
                "branch": str(sales_rep_instance.branch) if sales_rep_instance.branch else "",
                "branch_sales_representatives": number_of_branch_sales_reps,
                "code_link": ref,
                "earnings_balance": sales_rep_commission_qs.balance_after if sales_rep_commission_qs else 0.00
                },
            "terminal_requests": terminal_requests,
                # "sales_rep_bar_graph": "",
            "total_terminals": total_terminals,
            "sales_rep_bar_chart": {
                # "names": names,
                # "values": values
                },
            "stocks": stocks
            }           
        return response