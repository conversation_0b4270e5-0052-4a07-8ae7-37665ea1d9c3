from PIL import Image
from io import BytesIO
import re, json, base64
import requests
from django.conf import settings


def create_transaction(payload):
    url = "https://payment-api.cyberpay.ng/api/v1/payments"
    resp = requests.post(url, json=payload)
    response = json.loads(resp.text)
    if response['succeeded']:
        return response['data']
    return None


def getI420FromBase64(codec, filename):
    base64_data = re.sub('^data:image/.+;base64,', '', codec)
    byte_data = base64.b64decode(base64_data)
    image_data = BytesIO(byte_data)
    img = Image.open(image_data)
    img.save(filename, "PNG")


def generate_qr(transaction_reference):
    url = f"https://payment-api.cyberpay.ng/api/v1/payments/generateQRcode/{transaction_reference}"
    resp = requests.get(url)
    response = json.loads(resp.text)
    if response['succeeded']:
        filename = f"{response['data']['reference']}.png"
        qr = response['data']['data']['codeUrl']
        getI420FromBase64(qr, filename)


if __name__ == "__main__":
    load = {
        "Currency": "NGN",
        "MerchantRef": "8403434430087847332644",
        "Amount": 10000,
        "Description": "as discuss",
        "CustomerId": "37485930902920102",
        "CustomerName": "Some Name",
        "CustomerEmail": "<EMAIL>",
        "CustomerMobile": "2347039555295",
        "IntegrationKey": '2b8b0faf91134039b24883649532b276',
        "ReturnUrl": "http://www.*******.com",
        "WebhookUrl": "http: // yourwebookurl.com",
        "ProductCode": "",
        "Channel": "QRCode"
    }
    transaction = create_transaction(load)
    if transaction:
        ref = transaction['transactionReference']
        generate_qr(ref)