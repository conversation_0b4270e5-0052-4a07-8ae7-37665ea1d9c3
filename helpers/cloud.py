from django.conf import settings
from firebase_admin import credentials, messaging
import json
import requests
import firebase_admin



class CloudMessaging:
    def __init__(self, base_dir):
        path = f"{base_dir}/main/liberty-pay-agency-356418-firebase-adminsdk-vvf0z-41c433032f.json"
        cred = credentials.Certificate(path)

        default_app = firebase_admin.initialize_app(cred)

    def send_broadcast(self, token, title, body,data={}):
        # See documentation on defining a message payload.
        from accounts.tasks import handle_cloud_notification_send_task

        handle_cloud_notification_send_task.apply_async(
            queue="sms_queue",
            kwargs={
                "title": title,
                "body": body,
                "data": data,
                "token": token
            }
        )

        return


        # try:
        #     message = messaging.Message(
        #         notification=messaging.Notification(
        #             title=title,
        #             body=body,
        #         ),
        #         data=data,
        #         token=token,
        #     )


        #     # Send a message to the device corresponding to the provided
        #     # registration token.

        #     response = messaging.send(message)
        #     # Response is a message ID string.
        # except Exception as e:
        #     pass

class NotifyMessaging:

    def send_broadcast(self, token, title, body, data={}):

        try:

            serverToken = settings.NOTIFY_APP_FBK_SERVER_TOKEN

            headers = {
                    'Content-Type': 'application/json',
                    'Authorization': 'key=' + serverToken,
                }

            body = {
                        'notification': {
                            'title': title,
                            'body': body
                        },
                        'to': token,
                        'priority': 'high',
                        'data': data,
            }
            response = requests.post("https://fcm.googleapis.com/fcm/send",headers = headers, data=json.dumps(body))

        except Exception as e:
            pass
