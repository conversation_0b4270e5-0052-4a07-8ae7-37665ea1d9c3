DEBUG=1
ENVIRONMENT=development
DATABASE_NAME=postgres
DATABASE_USER=postgres
DATABASE_PASSWORD=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432
EMEKA_ADMIN_PASSWORD=xxx

SECRET_KEY=django-insecure-lg_oop^*kbx+n@=&nqhv0u!5025t3&hp79z^y+#z288k(+hx!c
HADADA_SECRET_KEY=xxx
BASE_URL=http://localhost:8000
CARD_WITHDRAW_MERCHANT_CHARGE=0.5
CARD_WITHDRAW_PAYBOX_MERCHANT_CHARGE=1.5
CARD_WITHDRAW_AGENT_CHARGE=0.5

OTP_TOKEN=xxx
OTP_APP_ID=xxx
OTP_MESSAGE_ID=xxx
ONLY_SMS_OTP_APP_ID=xxx
ONLY_SMS_OTP_MESSAGE_ID=xxx

PROVIDUS_SIGNATURE=xxx
PROVIDUS_CLIENT_ID=xxx
POS_AUTH_TOKEN=xxx
LIBERTY_MARKETING_URL=https://libertypay.live
MARKETING_USSD_CODE=*347*180#

SENDGRID_API_KEY=xxx
HORIZON_COOKIE_TOKEN=xxx
HORIZON_BEARER=xxx
HORIZON_PAY_URL=xxx
LOANDISK_AUTH_TOKEN=xxx
LOANDISK_PUBLIC_KEY=1111

WOVEN_API_KEY_LIVE=xxx
WOVEN_PAYOUT_PIN=0000
WHISPER_KEY=xxx
WHISPER_CHARGE=2.99

LIBERTY_AGENCY_RESET_PINS=xxx
LIBERTY_AGENCY_CREDIT_DEBIT_ALERT=xxx
LIBERTY_PAY_ACCOUNT_NUMBER_CREATED_SMS=xxx

AGENT_ACTIVITIES_SMS_TEMPLATE=xxx
REFUNDED_CLAIMS_SMS_TEMPLATE=xxx
LIBERTY_VAS_AUTH_USERNAME=xxx
LIBERTY_VAS_AUTH_PASSWORD=xxx
OTHERS_SERVICES_AUTH_PERMISSION=xxx
OTHERS_SERVICES_SUPERTOKEN=xxx

VFD_WEBHOOK_USERNAME=xxx
VFD_WEBHOOK_PASSWORD=xxx
VFD_WEBHOOK_AUTH=xxx
VFD_ACCESS_TOKEN_TEST=xxx
VFD_ACCESS_TOKEN_LIVE=xxx
VFD_WALLET_CREDENTIALS_TEST=xxx
VFD_WALLET_CREDENTIALS_LIVE=xxx
VFD_BASE_URL_TEST=https://xxx.vfdbank.systems/vtech-wallet/api/v1.1/
VFD_BASE_URL_LIVE=https://xxx.vfdbank.systems/vtech-wallet/api/v1.1/
VFD_ACCOUNT_CREATION_BASE_URL_LIVE=
VFD_ACCOUNT_CREATION_BASE_URL_LIVE_TEST=

PARALLEX_BASE_URL=xxx
PARALLEX_KEY=xxx
PARALLEX_IV=xxx
PARALLEX_USERNAME=xxx
PARALLEX_PASSWORD=xxx
PARALLEX_AGENT_ID=xxx


HERITAGE_API_KEY=xxx
HERITAGE_SECRET_KEY=xxx

PRINT_AIRTIME_BASE_URL=https://a.com
TRANSACTION_VERIFICATION_BACEKEND_URL=http://20
VERIFY_TRANSFER_LOCALLY=no

FRESHWORKS_API_KEY=xxx
METAMAP_CLIENT_ID=xxx
METAMAP_CLIENT_SECRET=xxx
METAMAP_WEBHOOK_SECRET=xxx
METAMAP_WEBHOOK_SECRET_HASH=xxx

WEMA_BANK_ACCOUNT_PASSWORD=xxx
WEMA_PASSWORD=xxx
WEMA_BANK_AES_KEY=xxx
WEMA_BANK_AES_IV=xxx

LIBERTY_LOANS_TOKEN=xxx
LOTTO_GUARANTOR_VERF_URL=https://
UVERIFY_TOKEN=xxx
CORAL_PAY_BASE_URL=http://
PICKY_ASSIST_TOKEN=xxx

CARD_LOAD_TOK=xxx
CARD_LOAD_TOKONE=xxx

MAILGUN_API_KEY=xxx
MARKETING_PAYSTACK_URL=http://bdd7-102-89-33-142.ngrok.io

TEST_AWS_S3_ACCESS_KEY_ID=xxxx
TEST_AWS_S3_SECRET_ACCESS_KEY=xxxx
TEST_AWS_STORAGE_BUCKET_NAME=xxxx
TEST_AWS_S3_ENDPOINT_URL=xxxx

AWS_S3_ACCESS_KEY_ID=xxx
AWS_S3_SECRET_ACCESS_KEY=xxx
AWS_STORAGE_BUCKET_NAME=xxx
AWS_S3_ENDPOINT_URL=xxx

GUARANTOR_FORM_LINK=https://app.libertypay.live/employment-forms/guarantor-form
GUARANTOR_FORM_ADMIN_VERIFICATION_EMAIL=
GUARANTOR_CANCEL_LINK=xxx
GUARANTOR_SMS_TEMPLATE=xxx
WHISPER_URL=https://whispersms.xyz/transactional/send

FLOAT_USER_EMAIL=xxx
CARDS_USER_EMAIL=xxx
LOTTO_SERVICE_USER=xxx
LIBERTY_CREDI_USER=xxx
LIBERTY_ASSURED_USER=xxx
PAYSTACK_API_SECRET=xxx

TRANSACTION_REWARD_PERCENTAGE=0.1
WEEKLY_REWARD_TARGET_COIN=108
DAILY_REWARD_TARGET_COIN=17


NOTIFY_APP_FBK_SERVER_TOKEN=xxx
SUDO_API_KEY_TEST=xxx
SUDO_API_KEY=xxx
SUDO_FUNDING_SOURCE_ID=xxx
SUDO_DEBIT_ACCOUNT_ID=xxx
SUDO_LIVE_BASE_URL=https://api.sudo.africa
SUDO_AUTH_HEADER=xxx
VGS_USERNAME=xxx
VGS_PASSWORD=xxx


IDENTITYPASS_TEST_BASE_URL=https://
IDENTITYPASS_TEST_SECRET_KEY=xxx
IDENTITYPASS_LIVE_BASE_URL=https://api.myidentitypay.com/api/v2
IDENTITYPASS_LIVE_SECRET_KEY=xxx
IDENTITYPASS_APP_ID=xxx


CYBER_PAY_INTEGRATION_KEY_TEST=xxx
CYBERPAY_RETURN_URL_TEST=https://dev.libertypayng.com
CYBERPAY_WEBHOOK_URL_TEST=/accounts/call_back/
CYBERPAY_BASE_URL_TEST=https://payment-api.staging.cyberpay.ng/api/v1
CYBER_PAY_INTEGRATION_KEY=xxx
CYBERPAY_RETURN_URL=https://12ea-102-67-1-66.eu.ngrok.io
CYBERPAY_WEBHOOK_URL=/accounts/call_back/
CYBERPAY_BASE_URL=https://payment-api.cyberpay.ng/api/v1

CARDS_APP_BASE_URL_DEV=http://
CARDS_APP_BASE_URL_PROD=http://

CARDS_APP_LOGIN_USERNAME=xxx
CARDS_APP_LOGIN_PASSWORD=xxx

AJO_BASE_URL=http://
AJO_MULTI_AUTH=xxx

CORALPAY_WITHDRAWAL_USSD_MERCHANT_ID=xxx
CORALPAY_WITHDRAWAL_USSD_TERMINAL_ID=xxx
CORALPAY_WITHDRAWAL_USSD_TOKEN=xxx
CORALPAY_WITHDRAWAL_USSD_USERNAME=xxx
CORALPAY_WITHDRAWAL_USSD_PASSWORD=xxx
CORALPAY_WITHDRAWAL_USSD_KEY=xxx

USSD_SESSION_KEY_KEY=xxx
PARALLEX_NOTIFY_KEY=xxx
PARALLEX_NOTIFY_IV=xxx
CORE_BANKING_TOKEN=xxx
CORE_BANKING_BASE_URL=https://banking.libertypayng.com
CORE_BANKING_BASE_URL_TEST=https://bankingdev.libertypayng.com
CORE_BANKING_EMAIL=xxx
CORE_BANKING_PASSWORD=xxx
GOOGLE_SHEET_CREDENTIALS=xxx
GOOGLE_SHEET_LINK=xxx
GETRESPONSE_API_KEY=xxx

ALLOWED_HOSTS=.localhost, .127.0.0.1,
AWS_ACCESS_KEY_ID=xxx
AWS_SECRET_ACCESS_KEY=xxx
AWS_REGION=us-east-1
BILLS_USER_EMAIL=
BILLS_USER_PASSWORD=
BILLS_USER_PIN=
LIB_PAY_API_KEY=
WHISPER_ONBOARDING_KEY=
DOJAH_APP_ID=xxx
DOJAH_SECRET_KEY=xxxx
LIBERTY_DRAW_BASE_URL=xxx
TERMINAL_PAYMENT_USER=
GUARANTOR_FORM_LINK_AGENT=
GUARANTOR_TEST_FIRST_NAME=
GUARANTOR_TEST_LAST_NAME=

