name: libertypay

on:
  push:
    branches: [ development-1 ]
  pull_request:
    branches: [ development-1 ]

jobs:
  build:

    runs-on: ubuntu-latest
    strategy:
      max-parallel: 4
      matrix:
        python-version: [3.9]

    steps:
    - uses: actions/checkout@v3
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v3
      with:
        python-version: ${{ matrix.python-version }}

    - name: Set up env
      run: |
        cp .env.sample .env

    - name: Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Run coverage report
      run: |
        coverage run manage.py  test
        coverage report

    - name: Run Tests
      run: |
        pytest
