name: Production Auto Deployment

on:
  push:
    branches: ["production"]

jobs:
  pre_check:
    name: Pre-deployment Checks
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:latest
        env:
          POSTGRES_DB: postgres
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
        ports:
          - 5432:5432
        options: >-
          --health-cmd "pg_isready -U postgres -d postgres"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.10"

      - name: Install Dependencies
        run: |
          python -m venv venv
          source venv/bin/activate
          pip install --upgrade pip
          pip install -r requirements.txt

      - name: Copy Environment Variables
        run: cp .env.sample .env

      - name: Run Django System Check
        run: |
          source venv/bin/activate
#          python manage.py runserver
#          python manage.py makemigrations accounts admin_dashboard agent_dashboard apiCalls
#          cards coral horizon_pay kyc_app loan_DB main pos_onboarding retail send_money singlelogin ussd
#          python manage.py migrate
#          python manage.py check

  deploy:
    name: Production Deployment
    runs-on: ubuntu-latest
    needs: pre_check

    steps:
      - name: Create SSH key file and directory
        run: |
          mkdir -p ~/.ssh
          echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          eval "$(ssh-agent -s)"
          ssh-add ~/.ssh/id_rsa
        env:
          SSH_PRIVATE_KEY: ${{ secrets.PROD_SSH_KEY }}

      - name: Checkout
        uses: actions/checkout@v3

      - name: SSH into Server, and deploy
        run: |
          ssh -v -o "StrictHostKeyChecking=no" -o "ForwardAgent=no" -i ~/.ssh/id_rsa ${{ secrets.SSH_USERNAME }}@${{ secrets.PROD_IP }} << 'EOF'
            cd /home/<USER>/AgencyBanking
            git pull https://${{ secrets.GIT_TOKEN }}@github.com/LibertytechX/LIBERTY-PAY-POS.git production
            echo "Activating environment..."
            source /home/<USER>/AgencyBanking/venv/bin/activate
            echo "Installing Dependencies..."
            pip install -r requirements.txt
            echo "Running migrations..."
            python manage.py makemigrations
            echo "Migrating..."
            python manage.py migrate
            echo "Restarting services..."
            systemctl restart gunicorn.service
            echo "Deployment complete..."
          EOF
#          systemctl restart celery.service celery1.service celery2.service celery3.service celery4.service
#          systemctl restart celery5.service celery6.service celery7.service celery8.service celery9.service celery10.service
#          systemctl restart celery11.service celery12.service celery13.service celerybeat.service

