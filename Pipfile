[[source]]
url = "https://pypi.python.org/simple"
verify_ssl = true
name = "pypi"

[packages]
asgiref = "==3.5.0"
certifi = "==2021.10.8"
cffi = "==1.15.0"
charset-normalizer = "==2.0.12"
coreapi = "==2.3.3"
coreschema = "==0.0.4"
cryptography = "==36.0.1"
defusedxml = "==0.7.1"
Django = "==4.0.3"
django-import-export = "==2.7.1"
django-templated-mail = "==1.1.1"
djangorestframework = "==3.13.1"
djangorestframework-simplejwt = "==4.8.0"
djoser = "==2.1.0"
drf-yasg = "==1.20.0"
environs = "==9.5.0"
idna = "==3.3"
inflection = "==0.5.1"
itypes = "==1.2.0"
Jinja2 = "==3.0.3"
MarkupSafe = "==2.1.0"
marshmallow = "==3.14.1"
oauthlib = "==3.2.0"
pycparser = "==2.21"
PyJWT = "==2.3.0"
pyparsing = "==3.0.7"
python-dotenv = "==0.19.2"
python3-openid = "==3.2.0"
pytz = "==2021.3"
requests = "==2.27.1"
requests-oauthlib = "==1.3.1"
"ruamel.yaml" = "==0.17.21"
"ruamel.yaml.clib" = "==0.2.6"
six = "==1.16.0"
social-auth-app-django = "==4.0.0"
social-auth-core = "==4.2.0"
sqlparse = "==0.4.2"
uritemplate = "==4.1.1"
urllib3 = "==1.26.8"

[dev-packages]
pytest = "*"
pytest-django = "*"
pytest-watch = "*"
mock = "*"
mixer = "*"

[requires]
python_version = "3.8"
