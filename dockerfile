# The first instruction is what image we want to base our container on
# We Use an official Python runtime as a parent image
FROM python:3.10

# The enviroment variable ensures that the python output is set straight
# to the terminal with out buffering it first
ENV PYTHONUNBUFFERED 1

# create root directory for our project in the container
RUN mkdir /home/<USER>

# Set the working directory to /AgencyBanking
WORKDIR /home/<USER>

# Copy the current directory contents into the container at /AgencyBanking
ADD . /home/<USER>/

# Install any needed packages specified in requirements.txt
RUN apt install libpq-dev
RUN pip install psycopg2
RUN pip install -r requirements.txt
RUN sysctl vm.overcommit_memory=1